<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdatePolicyAddressTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testUpdatePolicyAddress()
    {
        $data = 
        [
            'address_id' => '40051',
            'policy_id' => '9931922',
        ];
        $response = $this->json('POST','/api/v1/update-policy-address',$data);

        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Address Updated.',
                ]
            ]);
    }
    public function testUpdatePolicyAddressNullDataValidation()
    {
        $data = 
        [
            'address_id' => '',
            'policy_id' => '',
        ];
        $response = $this->json('POST','/api/v1/update-policy-address',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'address_id'=> 
                    [
                        'Address ID is required.'
                    ],
                    'policy_id'=> 
                    [
                        'Policy ID is required.'
                    ],
                ]
            ]);
    }
        public function testUpdatePolicyAddressDataTypeValidation()
    {
        $data = 
        [
            'address_id' => 'abc',
            'policy_id' => 'swd',
        ];
        $response = $this->json('POST','/api/v1/update-policy-address',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'address_id'=> 
                    [
                        'Address ID must be numeric.'
                    ],
                    'policy_id'=> 
                    [
                        'Policy ID must be numeric.'
                    ],
                ]
            ]);
    }
}
