<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EditPolicyGroupTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testEditGroupForPolicy()
    {
        $data = [
                 'gid' => '1073',
                 'policy_id' => '9931922',
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/edit-policy-group',$data);

        $response
            ->assertStatus(200)
            ->assertJsonFragment([
                'type' => 'success',
                'message' => 'Group Updated.',
            ]);
    }

    public function testEditGroupForPolicyNullGid()
    {
        $data = [
                 'gid' => '',
                 'policy_id' => '9931922',
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/edit-policy-group',$data);

        $response
            ->assertStatus(422)
            ->assertSeeText(
                'Group ID is required'
            );   
    }
    public function testEditGroupForPolicyGidNotExists()
    {
        $data = [
                 'gid' => '1073333',
                 'policy_id' => '9931922',
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/edit-policy-group',$data);

        $response
            ->assertStatus(200)
            ->assertSeeText(
                'Group does not exist.'
            );   
    }
    public function testEditGroupForPolicyNullPolicyId()
    {
        $data = [
                 'gid' => '1073',
                 'policy_id' => '',
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/edit-policy-group',$data);

        $response
            ->assertStatus(422)
            ->assertSeeText(
                'Policy ID is required'
            );   
    }
    public function testEditGroupForPolicyNullUserId()
    {
        $data = [
                 'gid' => '1073',
                 'policy_id' => '9931922',
                 'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/edit-policy-group',$data);

        $response
            ->assertStatus(422)
            ->assertSeeText(
                'User ID is required'
            );   
    }
}
