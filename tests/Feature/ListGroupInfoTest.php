<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ListGroupInfoTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testListGroupInfo()
    {
        $this->withoutExceptionHandling();
        $response = $this->json('GET','/api/v1/list-group-info');                
        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*'=>[
                        'gid',
                        'gname'
                    ]
                ]
            ]);
    }
}
