<?php

namespace Tests\Feature\PlanPolicy;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ActivePlanTermedTest extends TestCase
{
    /** @test */
    public function active_plan_termed_requires_values()
    {
        $this->post('/api/v1/active-plan-termed')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'planPolicyId' => ['Plan Policy Id is required.'],
                        'termDate' => ['Termination Date is required.'],
                        'reason' => ['Reason is required.'],
                        'loginUserName' => ['Logged In User is required.'],
                    ]
            ]);
    }

    /** @test */
    public function active_plan_termed_plan_policy_id_must_be_numeric()
    {
        $data = [
            'planPolicyId' => 'string',
            'termDate' => "2021-03-31",
            'reason' => "tcr",
            'notes' => "string",
            'loginUserName' => 'sting'
        ];
        $this->post('/api/v1/active-plan-termed', $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'planPolicyId' => ['Plan Policy must be numeric.'],
                    ]
            ]);
    }

    /** @test */
    public function active_plan_termed_term_date_must_be_date()
    {
        $data = [
            'planPolicyId' => 73300,
            'termDate' => "string",
            'reason' => "tcr",
            'notes' => "string",
            'loginUserName' => 'sting'
        ];
        $this->post('/api/v1/active-plan-termed', $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'termDate' => ['The term date is not a valid date.'],
                    ]
            ]);
    }

    /** @test */
    public function active_plan_termed_where_plan_policy_does_not_exists()
    {
        $data = [
            'planPolicyId' => 7330034343434333,
            'termDate' => "2021-03-31",
            'reason' => "tcr",
            'notes' => "string",
            'loginUserName' => 'sting'
        ];
        $this->post('/api/v1/active-plan-termed', $data)
            ->assertStatus(404)
            ->assertExactJson([
                'success' => false,
                'code' => 404,
                'message' => 'No Plan Policy Found.',
            ]);
    }

    /** @test */
    public function active_plan_termed_if_already_termed()
    {
        $data = [
            'planPolicyId' => 262,
            'termDate' => "2021-03-31",
            'reason' => "tcr",
            'notes' => "string",
            'loginUserName' => 'sting'
        ];
        $this->post('/api/v1/active-plan-termed', $data)
            ->assertStatus(409)
            ->assertExactJson([
                'success' => false,
                'code' => 409,
                'message' => 'This plan is already termed.',
            ]);
    }

    /** @test */
    public function active_plan_termed_can_termed()
    {
        $data = [
            'planPolicyId' => 73300,
            'termDate' => "2021-03-31",
            'reason' => "tcr",
            'notes' => "string",
            'loginUserName' => 'sting'
        ];
        $this->post('/api/v1/active-plan-termed', $data)
            ->assertStatus(200)
            ->assertExactJson([
                'success' => true,
                'code' => 200,
                'message' => 'Plan (L713 Elite PPO Advantage 5000) has been termed.',
                'data' => [
                    "planPolicyId" => 73300,
                    "policyId" => 9952697,
                    "status" => "termed"
                ]
            ]);
    }
}
