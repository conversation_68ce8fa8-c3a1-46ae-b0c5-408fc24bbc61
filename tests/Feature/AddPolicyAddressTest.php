<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AddPolicyAddressTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testAddPolicyAddress()
    {
        $data = 
        [
            'a_userid' => '107437',
            'address1' => 'test -GL-aaa-history-include-unit-testing',
            'address2' => '',
            'city' => 'Dhn',
            'state' => 'AK',
            'zip' => '44466',
            'type' => 'R',
            'policy_id' => '9931922',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/add-policy-address',$data);

        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Address Updated.',
                ]
            ]);
    }
    public function testAddPolicyAddressUserNotExists()
    {
        $data = 
        [
            'a_userid' => '107437111',
            'address1' => 'test -GL-aaa-history-include-unit-testing',
            'address2' => '',
            'city' => 'Dhn',
            'state' => 'AK',
            'zip' => '44466',
            'type' => 'R',
            'policy_id' => '9931922',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/add-policy-address',$data);

        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'error',
                    'message' => 'Address not updated. User not found.',
                ]
            ]);
    }
    public function testAddPolicyAddressUserNullDataValidation()
    {
        $data = 
        [
            'a_userid' => '',
            'address1' => '',
            'address2' => '',
            'city' => '',
            'state' => '',
            'zip' => '',
            'type' => '',
            'policy_id' => '',
            'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/add-policy-address',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'policy_id'=> 
                    [
                        'Policy ID is required.'
                    ],
                    'a_userid'=> 
                    [
                        'User ID is required.'
                    ],
                    'address1'=> 
                    [
                        'Address is required.'
                    ],
                    'city'=> 
                    [
                        'City is required.'
                    ],
                    'state'=> 
                    [
                        'State is required.'
                    ],
                    'zip'=>
                    [
                        'Zip is required. Only 5 digits.'
                    ],
                    'type'=> 
                    [
                        'Type is required.'
                    ],
                    'aid'=>
                    [
                        'User ID is required.'
                    ]
                ]
            ]);
    }
    public function testAddPolicyAddressDataTypeValidation()
    {
        $data = 
        [
            'a_userid' => 'abc',
            'address1' => 'test -GL-aaa-history-include-unit-testing',
            'address2' => '',
            'city' => 'Dhn',
            'state' => 'AK',
            'zip' => '44466',
            'type' => 'R',
            'policy_id' => 'def',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/add-policy-address',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'a_userid'=> 
                    [
                        'User ID must be numeric.'
                    ],
                    'policy_id'=> 
                    [
                        'Policy ID must be numeric.'
                    ],
                ]
            ]);
    }
}
