<?php

namespace Tests\Feature\ManageGroup;

use App\GroupBillingAddress;
use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AddGroupAddressTest extends TestCase
{

    public const SUCCESS_MESSAGE = "Group address added successfully.";
    public const FAILED_MESSAGE = "Failed to add group address.";
    const URL = "/api/v2/group/add-address";

    /** @test */
    public function can_create_group_address()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    /** @test */
    public function add_address_requires_values()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group is required.'],
                        'state' => ['The state field is required.'],
                        'address1' => ['Primary address is required.'],
                        'city' => ['City is required.'],
                        'zip' => ['Zip is required.'],
                        'type' => ['Type is required.'],
                    ]
            ]);
    }

    public function add_address_require_is_primary_integer(){
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "is_primary"=>"is primary",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'is_primary' => ['The is primary must be an integer.'],
                    ]
            ]);
    }

    public function add_address_require_usps_verified_integer(){
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "usps_verified"=> "integer",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'usps_verified' => ['The usps verified must be an integer.'],
                    ]
            ]);
    }

    public function add_address_require_group_id_integer(){
        $data = [
            "group_id" => "hello",
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "usps_verified"=> "integer",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group Id should be integer.'],
                    ]
            ]);
    }


    public function add_address_require_exact_type(){
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => "testing",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'type' => ['Type should be either business or billing.'],
                    ]
            ]);
    }

    /** @test */
    public function add_address_must_have_valid_group_id()
    {
        $data = [
            "group_id" => 1234556789,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
        ];

        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }
}
