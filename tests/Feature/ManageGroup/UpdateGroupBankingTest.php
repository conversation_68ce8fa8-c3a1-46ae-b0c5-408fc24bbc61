<?php

namespace Tests\Feature\ManageGroup;

use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateGroupBankingTest extends TestCase
{
    const SUCCESS_MESSAGE = "Group banking details updated successfully.";
    const SUCCESS_PAYMENT_UPDATE_MESSAGE = "Group payment method updated successfully.";
    const FAILED_MESSAGE = "Failed to update group banking details.";
    const FAILED_PAYMENT_MESSAGE = "Failed to update payment method.";
    const URL = "/api/v2/group/update-web-display-details";

    /** @test */
    public function can_update_group_banking_detail_data()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "ach_bank_routing" => "*********",
            "ach_bank_name" => "US BANK NA",
            "ach_bank_account_name" => null,
            "ach_bank_account" => **********,
            "payment_type" => "eft",
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    /** @test */
    public function can_update_group_banking_payment_data()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "payment_type" => "stmt",
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_PAYMENT_UPDATE_MESSAGE,
                'data' => []
            ]);
    }

    public function update_group_detail_required_ach_values_if_payment_type_is_eft()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "payment_type" => "eft",
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' => [
                    "ach_bank_account_name" => [
                        "The ach bank account name field is required when payment type is eft."
                    ],
                    "ach_bank_name" => [
                        "The ach bank name field is required when payment type is eft."
                    ],
                    "ach_bank_routing" => [
                        "The ach bank routing field is required when payment type is eft."
                    ],
                    "ach_bank_account" => [
                        "The ach bank account field is required when payment type is eft."
                    ]
                ]
            ]);
    }

    public function update_group_detail_required_valid_group_id()
    {
        $data = [
            "group_id" => *************,
            "ach_bank_routing" => "*********",
            "ach_bank_name" => "US BANK NA",
            "ach_bank_account_name" => null,
            "ach_bank_account" => **********,
            "payment_type" => "eft",
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }

    public function update_group_detail_required_routing_integer()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "ach_bank_routing" => *********.343,
            "ach_bank_name" => "US BANK NA",
            "ach_bank_account_name" => null,
            "ach_bank_account" => **********,
            "payment_type" => "eft",
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'ach_bank_routing' => ['Routing number should be in number.'],
                    ]
            ]);
    }

    public function update_group_detail_required_routing_nine_digits()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "ach_bank_routing" => *********,
            "ach_bank_name" => "US BANK NA",
            "ach_bank_account_name" => null,
            "ach_bank_account" => **********,
            "payment_type" => "eft",
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'routing' => ['Routing number should be of 9 digits.'],
                    ]
            ]);
    }


    public function update_group_detail_required_account_integer()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "ach_bank_routing" => *********,
            "ach_bank_name" => "US BANK NA",
            "ach_bank_account_name" => null,
            "ach_bank_account" => **********.3333,
            "payment_type" => "eft",
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'ach_bank_account' => ['The account must be an integer.'],
                    ]
            ]);
    }


}
