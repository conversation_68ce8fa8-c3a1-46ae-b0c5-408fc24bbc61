<?php

namespace Tests\Feature\ManageGroup;

use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SuspendGroupTest extends TestCase
{
    const URL = "/api/v2/group/suspend-account";

    /** @test */
    public function can_suspend_account()
    {
        $groupInfo = GroupInfo::query()
            ->where('gstatus', '=', GroupInfo::STATUS_ACTIVE)
            ->first();
        $data = [
            "group_id" => $groupInfo->gid,
            "status" => 0,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Group Account Suspended.',
                'data' => []
            ]);
    }

    public function suspend_account_required_valid_group_id()
    {
        $data = [
            "group_id" => 3243423434343434343,
            "status" => 1,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }

    public function suspend_account_cannot_suspend_having_status_false()
    {
        $groupInfo = GroupInfo::query()
            ->where('gstatus', '=', GroupInfo::STATUS_INACTIVE)
            ->first();
        $data = [
            "group_id" => $groupInfo->gid,
            "status" => 1,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertJson([
                'success' => false,
                'code' => 409,
                'message' => 'Failed to suspend account.',
                'data' => []
            ]);
    }

}
