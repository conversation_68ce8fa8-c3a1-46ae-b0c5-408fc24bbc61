<?php

namespace Tests\Feature\ManageGroup;

use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class GroupAddressTest extends TestCase
{
    /** @test */
    public function admin_can_retrieve_group_addresses()
    {
        $groupId = GroupInfo::first()->gid;
        $this->get('/group/' . $groupId . '/addresses')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    public function failed_to_fetch_if_valid_group_id_does_not_exist(){
        $groupId = 1234213412343214324123423412343214123;
        $this->get('/group' . $groupId . '/addresses')
            ->assertStatus(400)
            ->assertJson([
                'success' => false,
                'code' => 400,
                'message' => 'Group does not exist.',
            ]);
    }
}
