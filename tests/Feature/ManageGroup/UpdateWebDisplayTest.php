<?php

namespace Tests\Feature\ManageGroup;

use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateWebDisplayTest extends TestCase
{
    const SUCCESS_MESSAGE = "Group web display details updated successfully.";
    const FAILED_MESSAGE = "Failed to update group web display details.";
    const URL = "/api/v2/group/update-web-display-details";

    /** @test */
    public function can_update_web_display_data()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "web_display_name"=>" Ruden Medical Services LLP",
            "web_display_number"=>**********,
            "web_display_email"=>"",
            "web_display_sa_fees"=>"",
            "web_access"=>"",
            "web_access_dental"=>"Ruden Medical Services LLP",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    public function update_web_display_required_valid_group_id()
    {
        $data = [
            "group_id" => 3243423434343434343,
            "web_display_name"=>" Ruden Medical Services LLP",
            "web_display_number"=>**********,
            "web_display_email"=>"",
            "web_display_sa_fees"=>"",
            "web_access"=>"",
            "web_access_dental"=>"Ruden Medical Services LLP",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }

    public function update_web_display_number_required_valid_digit()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->id,
            "web_display_name"=>" Ruden Medical Services LLP",
            "web_display_number"=>33,
            "web_display_email"=>"",
            "web_display_sa_fees"=>"",
            "web_access"=>"",
            "web_access_dental"=>"Ruden Medical Services LLP",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'web_display_number' => ['Web display number must have 10 digits.'],
                    ]
            ]);
    }

    public function update_web_display_number_required_valid_num_verify()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->id,
            "web_display_name"=>" Ruden Medical Services LLP",
            "web_display_number"=>**********,
            "web_display_email"=>"",
            "web_display_sa_fees"=>"",
            "web_access"=>"",
            "web_access_dental"=>"Ruden Medical Services LLP",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'web_display_number' => ['Phone is invalid - Validated using ClearoutPhone'],
                    ]
            ]);
    }

    public function update_web_display_email_required_valid_neverbounce_email()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->id,
            "web_display_name"=>" Ruden Medical Services LLP",
            "web_display_number"=>**********,
            "web_display_email"=>"<EMAIL>",
            "web_display_sa_fees"=>"",
            "web_access"=>"",
            "web_access_dental"=>"Ruden Medical Services LLP",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'web_display_email' => ['Email is invalid - Validated using Neverbounce API'],
                    ]
            ]);
    }

    public function update_web_display_sa_fees_values_required_greater_than_zero()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->id,
            "web_display_name"=>" Ruden Medical Services LLP",
            "web_display_number"=>**********,
            "web_display_email"=>"<EMAIL>",
            "web_display_sa_fees"=>-3,
            "web_access"=>"",
            "web_access_dental"=>"Ruden Medical Services LLP",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'web_display_sa_fees' => ['Web display fee must be greater than 0.'],
                    ]
            ]);
    }
}
