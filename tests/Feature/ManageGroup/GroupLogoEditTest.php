<?php

namespace Tests\Feature\ManageGroup;

use App\GroupInfo;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class GroupLogoEditTest extends TestCase
{
    const SUCCESS_MESSAGE = "Group logo upload successfully.";
    const FAILED_MESSAGE = "Failed to upload logo.";
    const URL = "/api/v2/edit-group-logo";

    public function can_edit_logo()
    {
        $groupInfo = GroupInfo::first();
        Storage::fake('corenroll');
        $logo = UploadedFile::fake()->create('test-img.png', 1025);
        $data = [
            'group_id' => $groupInfo->group_id,
            'group_logo' => $logo,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' =>self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    public function edit_logo_must_be_exact_type(){
        $groupInfo = GroupInfo::first();
        Storage::fake('corenroll');
        $logo = UploadedFile::fake()->create('test-img.txt', 20481);
        $data = [
            'group_id' => $groupInfo->group_id,
            'group_logo' => $logo,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_logo' => ['Group ogo must be type of .jpeg,.jpg or .png format.'],
                    ]
            ]);
    }


    public function edit_logo_requires_less_than_20_mb(){
        $groupInfo = GroupInfo::first();
        Storage::fake('corenroll');
        $logo = UploadedFile::fake()->create('test-img.png', 20481);
        $data = [
            'group_id' => $groupInfo->group_id,
            'group_logo' => $logo,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_logo' => ['Group ogo must be less than 20 MB.'],
                    ]
            ]);
    }

    public function edit_logo_requires_valid_group_id()
    {
        $data = [
            "group_id" => 3143433243,
            "group_logo" => "",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }
}
