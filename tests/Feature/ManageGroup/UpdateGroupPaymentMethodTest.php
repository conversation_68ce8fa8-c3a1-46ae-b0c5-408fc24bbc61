<?php

namespace Tests\Feature\ManageGroup;

use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateGroupPaymentMethodTest extends TestCase
{
    const SUCCESS_MESSAGE = "Payment method updated successfully.";
    const FAILED_MESSAGE = "Failed to update payment method.";
    const URL = "/api/v2/group/update-payment-method";

    /** @test */
    public function can_update_group_payment_method()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "payment_method" => "stmt",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }


    /** @test */
    public function update_group_payment_method_requires_values()
    {
        $this->put(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group is required.'],
                        'payment_method' => ['Payment Method is required.'],
                    ]
            ]);
    }

    public function  update_group_payment_method_require_exact_type()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "payment_method" => 'randome',
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'payment_method' => [ "Payment Method must be either eft or stmt or list."],
                    ]
            ]);
    }
}
