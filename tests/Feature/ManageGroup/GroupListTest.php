<?php

namespace Tests\Feature\ManageGroup;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class GroupListTest extends TestCase
{
    /** @test */
    public function admin_can_retrieve_paginated_groups()
    {
        $this->get('/api/v2/get-groups')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }
}
