<?php

namespace Tests\Feature\ManageGroup;

use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateGroupBillingDetailTest extends TestCase
{
    const SUCCESS_MESSAGE = "Group billing details updated successfully.";
    const FAILED_MESSAGE = "Failed to update group billing details.";
    const URL = "/api/v2/group/update-billing-details";

    /** @test */
    public function can_update_group_billing_detail_data()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "contact_first_name"=>"Mary",
            "contact_last_name"=>"Monong",
            "phone"=>7182689612,
            "email"=>"<EMAIL>",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    public function update_group_billing_detail_required_valid_group_id()
    {
        $data = [
            "group_id" => 43432434343434,
            "contact_first_name"=>"Mary",
            "contact_last_name"=>"Monong",
            "phone"=>7182689612,
            "email"=>"<EMAIL>",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }

    public function update_group_billing_detail_number_required_valid_digit()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "contact_first_name"=>"Mary",
            "contact_last_name"=>"Monong",
            "phone"=>71826896123333,
            "email"=>"<EMAIL>",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'phone"' => ['Web display number must have 10 digits.'],
                    ]
            ]);
    }

    public function update_group_billing_detail_number_required_valid_num_verify()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "contact_first_name"=>"Mary",
            "contact_last_name"=>"Monong",
            "phone"=>1234567895,
            "email"=>"<EMAIL>",
            "fax"=>"",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'phone' => ['Phone is invalid - Validated using ClearoutPhone'],
                    ]
            ]);
    }

    public function update_group_billing_detail_email_required_valid_neverbounce_email()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "contact_first_name"=>"Mary",
            "contact_last_name"=>"Monong",
            "phone"=>7182689612,
            "email"=>"<EMAIL>",
            "fax"=>"",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'email' => ['Email is invalid - Validated using Neverbounce API'],
                    ]
            ]);
    }
}
