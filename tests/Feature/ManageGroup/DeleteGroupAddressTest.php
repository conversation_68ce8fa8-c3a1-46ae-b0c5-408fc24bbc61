<?php

namespace Tests\Feature\ManageGroup;

use App\GroupAddress;
use App\GroupBillingAddress;
use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DeleteGroupAddressTest extends TestCase
{
    public const FAILED_MESSAGE = "Failed to delete address.";
    const URL = "/api/v2/group/delete-address";

    /** @test */
    public function can_delete_group_address()
    {
        $groupId = GroupInfo::first()->gid;
        $addressId = GroupAddress::first()->id;
        $data = [
            "id" => $addressId,
            "type" => GroupAddress::GROUP_ADDRESS_TYPE_BUSINESS,
            "group_id"=>$groupId,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Business address deleted successfully.',
                'data' => []
            ]);
    }

    /** @test */
    public function can_delete_billing_group_address()
    {
        $groupId = GroupInfo::first()->gid;
        $addressId = GroupBillingAddress::first()->id;
        $data = [
            "id" => $addressId,
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "group_id"=>$groupId,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Billing address deleted successfully.',
                'data' => []
            ]);
    }

    /** @test */
    public function delete_group_address_requires_values()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group is required.'],
                        'id' => ['Group Address Id is required.'],
                        'type' => ['Type is required.'],
                    ]
            ]);
    }

    public function delete_group_address_require_integer()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "id" => "test",
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "group_id"=>$groupId,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Group Address Id should be integer.'],
                    ]
            ]);
    }

    public function delete_group_address_require_exact_type()
    {
        $groupId = GroupInfo::first()->gid;
        $addressId = GroupBillingAddress::first()->id;
        $data = [
            "id" => $addressId,
            "type" => "test",
            "group_id"=>$groupId,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'type' => ['Type should be either business or billing.'],
                    ]
            ]);
    }
}
