<?php

namespace Tests\Feature\ManageGroup;

use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateGroupPaymentEftMethodWithEftBankTest extends TestCase
{
    public const SUCCESS_MESSAGE = "Group Payment method updated and ach payment added successfully.";
    public const FAILED_MESSAGE = "Failed to update agent payment method with ach.";
    const URL = '/api/v2/agent/update-payment-method-with-ach-payment';

    /** @test */
    public function can_update_payment_method_with_eft_bank()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "payment_method" => "eft",
            "group_id" => $groupId,
            "bank_name" => "US BANK NA",
            "bank_routing" => *********,
            "bank_account" => *********,
            "bank_accountname"=>"test",
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    /** @test */
    public function update_payment_method_with_eft_bank_requires_values()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'payment_method' => ['Payment method is required.'],
                        'group_id' => ['Group is required.'],
                        'bank_accountname' => ['Bank Account Name is required.'],
                        'bank_name' => ['Bank name is required.'],
                        'bank_routing' => ['The bank_routing field is required.'],
                        'bank_account' => ['The bank_account field is required.'],
                        'account_type' => ['Account type is required.'],
                        'account_holder_type' => ['Account holder type is required.'],
                    ]
            ]);
    }

    /** @test */
    public function update_payment_method_with_eft_bank_must_have_valid_group_id()
    {
        $data = [
            "payment_method" => "eft",
            "group_id" => **********,
            "bank_name" => "US BANK NA ",
            "bank_routing" => *********,
            "bank_account" => *********,
            "bank_accountname"=>"test",
            "account_type" => "savings",
            "account_holder_type" => "companyss",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];

        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }


    public function update_payment_method_with_eft_bank_require_bank_routing_integer()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "payment_method" => "eft",
            "group_id" => $groupId,
            "bank_name" => "US BANK NA ",
            "bank_accountname"=>"test",
            "bank_routing" => *********.77,
            "bank_account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'bank_routing' => ['Routing number should be in number.'],
                    ]
            ]);
    }

    public function update_payment_method_with_eft_bank_require_bank_routing_nine_digits()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "payment_method" => "eft",
            "group_id" => $groupId,
            "bank_name" => "US BANK NA ",
            "bank_accountname"=>"test",
            "bank_routing" => ***********,
            "bank_account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'bank_routing' => ['Routing number should be of 9 digits.'],
                    ]
            ]);
    }


    public function update_payment_method_with_eft_bank_require_bank_account_integer()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "payment_method" => "eft",
            "group_id" => $groupId,
            "bank_name" => "US BANK NA ",
            "bank_accountname"=>"test",
            "bank_routing" => *********,
            "bank_account" => *********.33,
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'bank_account' => ['The bank_account must be an integer.'],
                    ]
            ]);
    }

    public function update_payment_method_with_eft_bank_require_exact_account_type()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "payment_method" => "eft",
            "group_id" => $groupId,
            "bank_name" => "US BANK NA ",
            "bank_accountname"=>"test",
            "bank_routing" => *********,
            "bank_account" => *********,
            "account_type" => "savingsss",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'account_type' => ['Account type should be either checking or savings.'],
                    ]
            ]);
    }

    public function update_payment_method_with_eft_bank_require_exact_account_holder_type()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "payment_method" => "eft",
            "group_id" => $groupId,
            "bank_name" => "US BANK NA ",
            "bank_accountname"=>"test",
            "bank_routing" => *********,
            "bank_account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "companyss",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'account_holder_type' => ['Account holder type should be either individual or company.'],
                    ]
            ]);
    }

    public function update_payment_method_with_eft_bank_require_exact_payment_method_type_eft()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "payment_method" => "efts",
            "group_id" => $groupId,
            "bank_name" => "US BANK NA ",
            "bank_routing" => *********,
            "bank_account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'payment_method' => ["Payment Method must be eft."],
                    ]
            ]);
    }
}
