<?php

namespace Tests\Feature\ManageGroup;

use App\AgentInfo;
use App\GroupInfo;
use Tests\TestCase;

class UpdateGroupMainAgentTest extends TestCase
{
    const SUCCESS_MESSAGE = "Main Agent updated successfully.";
    const FAILED_MESSAGE = "Failed to update main agent.";
    const URL = "/api/v2/group/update-main-agent";

    /** @test */
    public function can_update_main_agent()
    {
        $groupInfo = GroupInfo::first();
        $mainAgent = AgentInfo::query()
            ->where('agent_id','!=', $groupInfo->gagent_code)
            ->where('agent_status', '=', AgentInfo::STATUS_APPROVED)
            ->first();
        $data = [
            "group_id" => $groupInfo->gid,
            "gagent_code" => $mainAgent->agent_id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }


    /** @test */
    public function update_main_agent_requires_values()
    {
        $this->put(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group is required.'],
                        'gagent_code' => ['Main Agent is required.'],
                    ]
            ]);
    }

    public function update_main_agent_requires_valid_group_id()
    {
        $groupInfo = GroupInfo::first();
        $mainAgent = AgentInfo::query()
            ->where('agent_id','!=', $groupInfo->gagent_code)
            ->where('agent_status', '=', AgentInfo::STATUS_APPROVED)
            ->first();
        $data = [
            "group_id" => 134321434343,
            "gagent_code" => $mainAgent->agent_id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }

    public function update_main_agent_requires_valid_gagent_code()
    {
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "gagent_code" => 24233434343,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'gagent_code' => ['Main Agent not found.'],
                    ]
            ]);
    }

    public function update_main_agent_requested_gagent_code_exist(){
        $groupInfo = GroupInfo::first();
        $data = [
            "group_id" => $groupInfo->gid,
            "gagent_code" => $groupInfo->mainAgent->agent_id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(409)
            ->assertJson([
                'success' => true,
                'code' => 409,
                'message' => 'This main agent already exist.',
                'data' => []
            ]);
    }
}
