<?php

namespace Tests\Feature\ManageGroup;

use App\GroupBillingAddress;
use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SetGroupPrimaryAddressTest extends TestCase
{
    public const FAILED_MESSAGE = "Failed to set primary address.";
    const URL = "/api/v2/group/set-primary-address";

    /** @test */
    public function can_set_primary_group_address()
    {
        $groupId = GroupInfo::first()->gid;
        $addressId = GroupBillingAddress::first()->id;
        $data = [
            "group_id" => $groupId,
            "id" => $addressId,
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => "Billing address successfully set to primary.",
                'data' => []
            ]);
    }


    /** @test */
    public function add_address_requires_values()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group is required.'],
                        'id' => ['Group Address Id is required.'],
                        'type' => ['Type is required.'],
                    ]
            ]);
    }

    public function set_primary_address_require_exact_type()
    {
        $groupId = GroupInfo::first()->gid;
        $addressId = GroupBillingAddress::first()->id;
        $data = [
            "group_id" => $groupId,
            "id" => $addressId,
            "type" => "test",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'type' => ['Type should be either business or billing.'],
                    ]
            ]);
    }

    public function set_primary_address_require_group_id_integer()
    {
        $addressId = GroupBillingAddress::first()->id;
        $data = [
            "group_id" => 1234556789,
            "id" => $addressId,
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }

    public function set_primary_address_require_valid_group_id()
    {
        $addressId = GroupBillingAddress::first()->id;
        $data = [
            "group_id" => "test",
            "id" => $addressId,
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group Id should be integer.'],
                    ]
            ]);
    }

    public function set_primary_address_require_id_integer()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "id" => "test",
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Group Address Id should be integer.'],
                    ]
            ]);
    }

    public function set_primary_address_check_if_address_is_already_primary()
    {
        $groupId = GroupInfo::first()->gid;
        $addressId = GroupBillingAddress::query()
            ->where('gid', '=', $groupId)
            ->where('is_primary', '=', 1)
            ->first();
        $data = [
            "group_id" => $groupId,
            "id" => $addressId,
            "type" => GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(409)
            ->assertJson([
                'success' => true,
                'code' => 409,
                'message' => 'This address is already set to primary.',
            ]);
    }
}
