<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EditPersonalInfoTest extends TestCase
{
    const SUCCESS_MESSAGE = "Personal information updated successfully.";
    const FAILED_MESSAGE = "Failed to update personal information.";
    const URL = "/api/v2/edit-agent-image";

    public function can_edit_personal_info()
    {
        $agentInfo = AgentInfo::first();
        $data = [
            "agent_id" =>$agentInfo->agent_id,
            "agent_fname" => "Test",
            "agent_lname" => "Test",
            "agent_email" => "<EMAIL>",
            "agent_phone1" => 6135550176,
            "dob" => "1995-01-01",
            "agent_ssn" => 123123000,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' =>self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }


    public function edit_personal_info_valid_agent_id()
    {
        $data = [
            "agent_id" =>3143433243,
            "agent_fname" => "Test",
            "agent_lname" => "Test",
            "agent_email" => "<EMAIL>",
            "agent_phone1" => 6135550176,
            "dob" => "1995-01-01",
            "agent_ssn" => 123123000,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];

        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent not found.'],
                    ]
            ]);
    }
}
