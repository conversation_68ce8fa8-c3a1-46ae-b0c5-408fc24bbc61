<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EditAgentImageTest extends TestCase
{
    const SUCCESS_MESSAGE = "Image upload successfully.";
    const FAILED_MESSAGE = "Failed to upload image.";
    const URL = "/api/v2/edit-agent-image";

    public function can_edit_image()
    {
        $agentInfo = AgentInfo::first();
        Storage::fake('corenroll');
        $image = UploadedFile::fake()->create('test-img.png', 1025);
        $data = [
            'agent_id' => $agentInfo->agent_id,
            'agent_img' => $image,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' =>self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    public function edit_image_must_be_exact_type(){
        $agentInfo = AgentInfo::first();
        Storage::fake('corenroll');
        $image = UploadedFile::fake()->create('test-img.txt', 20481);
        $data = [
            'agent_id' => $agentInfo->agent_id,
            'agent_img' => $image,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_img' => ['Agent Image must be type of .jpeg,.jpg or .png format.'],
                    ]
            ]);
    }


    public function edit_image_requires_less_than_20_mb(){
        $agentInfo = AgentInfo::first();
        Storage::fake('corenroll');
        $image = UploadedFile::fake()->create('test-img.png', 20481);
        $data = [
            'agent_id' => $agentInfo->agent_id,
            'agent_img' => $image,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_img' => ['Agent Image must be less than 20 MB.'],
                    ]
            ]);
    }

    public function edit_image_requires_valid_agent_id()
    {
        $data = [
            "agent_id" => 3143433243,
            "agent_img" => "",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent not found.'],
                    ]
            ]);
    }
}
