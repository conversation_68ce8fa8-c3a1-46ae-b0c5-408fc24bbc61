<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AgentAddressTest extends TestCase
{
    /** @test */
    public function admin_can_retrieve_group_addresses()
    {
        $agentId = AgentInfo::first()->agentId;
        $this->get('/agent/' . $agentId . '/addresses')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    public function failed_to_fetch_if_valid_group_id_does_not_exist(){
        $groupId = 1234213412343214324123423412343214123;
        $this->get('/agent/' . $groupId . '/addresses')
            ->assertStatus(400)
            ->assertJson([
                'success' => false,
                'code' => 400,
                'message' => 'Agent does not exist.',
            ]);
    }
}
