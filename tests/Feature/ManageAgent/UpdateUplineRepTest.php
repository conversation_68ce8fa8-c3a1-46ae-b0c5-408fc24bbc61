<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateUplineRepTest extends TestCase
{
    const SUCCESS_MESSAGE = "Upline Rep updated successfully.";
    const FAILED_MESSAGE = "Failed to update upline rep.";
    const URL = "/api/v2/agent/update-upline-rep";

    /** @test */
    public function can_update_upline_req()
    {
        $agentInfo = AgentInfo::first();
        $uplineRep = $agentInfo->getAgentGa;
        $agentGa = AgentInfo::query()
            ->where('agent_level', '>', $agentInfo->agent_level)
            ->where('agent_status', '=', AgentInfo::STATUS_APPROVED)
            ->where('agent_id', '!=', $agentInfo->agent_id)
            ->where('agent_id', '!=', $uplineRep->agent_id)
            ->first();
        $data = [
            "agent_id" => $agentInfo->agent_id,
            "agent_ga" => $agentGa->agent_id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }


    /** @test */
    public function update_upline_rep_requires_values()
    {
        $this->put(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent is required.'],
                        'agent_ga' => ['Upline Rep is required.'],
                    ]
            ]);
    }

    public function update_upline_rep_requires_valid_agent_id()
    {
        $agentInfo = AgentInfo::first();
        $uplineRep = $agentInfo->getAgentGa;
        $agentGa = AgentInfo::query()
            ->where('agent_level', '>', $agentInfo->agent_level)
            ->where('agent_status', '=', AgentInfo::STATUS_APPROVED)
            ->where('agent_id', '!=', $agentInfo->agent_id)
            ->where('agent_id', '!=', $uplineRep->agent_id)
            ->first();
        $data = [
            "agent_id" => 134321434343,
            "agent_ga" => $agentGa->agent_id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent not found.'],
                    ]
            ]);
    }

    public function update_upline_rep_requires_valid_agent_ga()
    {
        $agentInfo = AgentInfo::first();
        $data = [
            "agent_id" => $agentInfo->agent_id,
            "agent_ga" => 24233434343,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Upline Rep not found.'],
                    ]
            ]);
    }

    public function update_upline_rep_requires_agent_level_higher_than_current()
    {
        {
            $agentInfo = AgentInfo::first();
            $uplineRep = $agentInfo->getAgentGa;
            $agentGa = AgentInfo::query()
                ->where('agent_level', '<=', $agentInfo->agent_level)
                ->where('agent_status', '=', AgentInfo::STATUS_APPROVED)
                ->where('agent_id', '!=', $agentInfo->agent_id)
                ->where('agent_id', '!=', $uplineRep->agent_id)
                ->first();
            $data = [
                "agent_id" => $agentInfo->agent_id,
                "agent_ga" => $agentGa->agent_id,
            ];
            $this->put(self::URL, $data)
                ->assertStatus(409)
                ->assertJson([
                    'success' => true,
                    'code' => 409,
                    'message' => "Selected Upline Rep level must be greater than current rep level.",
                ]);
        }
    }

    public function update_upline_rep_requires_agent_must_be_active()
    {
        {
            $agentInfo = AgentInfo::first();
            $uplineRep = $agentInfo->getAgentGa;
            $agentGa = AgentInfo::query()
                ->where('agent_level', '>', $agentInfo->agent_level)
                ->where('agent_status', '!=', AgentInfo::STATUS_APPROVED)
                ->where('agent_id', '!=', $agentInfo->agent_id)
                ->where('agent_id', '!=', $uplineRep->agent_id)
                ->first();
            $data = [
                "agent_id" => $agentInfo->agent_id,
                "agent_ga" => $agentGa->agent_id,
            ];
            $this->put(self::URL, $data)
                ->assertStatus(409)
                ->assertJson([
                    'success' => true,
                    'code' => 409,
                    'message' => "Selected Upline Rep level must be active.",
                ]);
        }
    }

}
