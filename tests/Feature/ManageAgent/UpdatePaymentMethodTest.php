<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdatePaymentMethodTest extends TestCase
{
    const SUCCESS_MESSAGE = "Agent payment method updated successfully.";
    const FAILED_MESSAGE = "Failed to update payment method.";
    const URL = "/api/v2/update-payment-method";

    /** @test */
    public function can_update_agent_payment_method()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_id" => $agentId,
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_ACH,
            "send_email"=>0,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }


    /** @test */
    public function update_agent_payment_method_requires_values()
    {
        $this->put(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent is required.'],
                        'payment_method' => ['Payment Method is required.'],
                    ]
            ]);
    }

    public function  update_agent_payment_method_require_exact_type()
    {
        $agentId = AgentInfo::first()->agent_id;
        $paymentAch = AgentInfo::AGENT_PAYMENT_METHOD_ACH;
        $paymentCheck = AgentInfo::AGENT_PAYMENT_METHOD_CHECK;
        $data = [
            "agent_id" => $agentId,
            "payment_method" => 'randome',
            "send_email"=>0,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'payment_method' => [ "Payment Method should be either {$paymentAch} or {$paymentCheck}."],
                    ]
            ]);
    }
}
