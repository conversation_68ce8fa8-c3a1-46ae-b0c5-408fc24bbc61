<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ManageAgentTest extends TestCase
{
    /** @test */
    public function admin_can_retrieve_paginated_agents()
    {
        $this->get('/api/v2/get-agents')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    /** @test */
    public function admin_can_retrieve_contract_actions()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/api/v2/get-contract-actions/' . $agentId)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    /** @test */
    public function admin_can_retrieve_contract_levels()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/api/v2/get-contract-levels/' . $agentId)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    /** @test */
    public function can_get_agent_detail()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/detail')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }


    /** @test */
    public function can_get_agent_paginated_licenses()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/licenses')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    /** @test */
    public function can_get_agent_paginated_downline_reps()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/downline-reps')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    /** @test */
    public function can_get_upline_reps()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/upline-reps')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    /** @test */
    public function if_upline_reps_empty()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/upline-reps')
            ->assertStatus(400)
            ->assertJson([
                'success' => false,
                'code' => 400,
                'message' => 'No Upline Reps Found.',
            ]);
    }

    /** @test */
    public function fetch_groups_for_agent_which_are_not_associated()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/get-groups')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }


    /** @test */
    public function fetch_agent_user_activity()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/activity')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    /** @test */
    public function agent_user_activity_if_data_does_not_exist()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/activity')
            ->assertStatus(400)
            ->assertJson([
                'success' => false,
                'code' => 400,
                'message' => 'No Agent Activity found.',
            ]);
    }

    /** @test */
    public function agent_user_activity_if_agent_does_not_exist()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/agent/' . $agentId . '/activity')
            ->assertStatus(400)
            ->assertJson([
                'success' => false,
                'code' => 400,
                'message' => 'No Agent found.',
            ]);
    }

}
