<?php

namespace Tests\Feature\ManageAgent;

use App\AgentBusinessAddress;
use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AddAgentAddressTest extends TestCase
{
    public const SUCCESS_MESSAGE = "Agent address added successfully.";
    public const FAILED_MESSAGE = "Failed to add agent address.";
    const URL = '/api/v2/add-agent-address';
    /** @test */
    public function can_create_agent_address()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_id" => $agentId,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    /** @test */
    public function add_address_requires_values()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent is required.'],
                        'state' => ['The state field is required.'],
                        'address1' => ['Primary address is required.'],
                        'city' => ['City is required.'],
                        'zip' => ['Zip is required.'],
                        'type' => ['Type is required.'],
                    ]
            ]);
    }

    public function add_address_require_is_primary_integer(){
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_id" => $agentId,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS,
            "is_primary"=>"is primary",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'is_primary' => ['The is primary must be an integer.'],
                    ]
            ]);
    }

    public function add_address_require_usps_verified_integer(){
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_id" => $agentId,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS,
            "usps_verified"=> "integer",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'usps_verified' => ['The usps verified must be an integer.'],
                    ]
            ]);
    }

    public function add_address_require_agent_id_integer(){
        $data = [
            "agent_id" => "hello",
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS,
            "usps_verified"=> "integer",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent Id should be integer.'],
                    ]
            ]);
    }


    public function add_address_require_exact_type(){
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_id" => $agentId,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => "testing",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'type' => ['Type should be either personal , business or payment.'],
                    ]
            ]);
    }

    /** @test */
    public function add_address_must_have_valid_agent_id()
    {
        $data = [
            "agent_id" => 1234556789,
            "state" => "AZ",
            "address1" => "Primary New TT",
            "city" => "Kathmandhu Primary",
            "zip" => "44607",
            "type" => AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS,
        ];

        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent not found.'],
                    ]
            ]);
    }
}
