<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SendContractEmailTest extends TestCase
{
    /** @test */
    public function send_contract_email_requires_values()
    {
        $this->post('/api/v2/agent/send-contract-email')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent Id is required.'],
                        'agent_level_alc' => ['Ancillary/LifeStyle contracts level is required.'],
                        'agent_level_pec' => ['Premier/Elite contracts level is required.'],
                        'agent_level_bex' => ['BEX contracts level is required.'],
                    ]
            ]);
    }


    /** @test */
    public function send_contract_email_requires_integer()
    {
//        This validation rule does not verify that the input is of the "integer" variable type
        $data = [
            "agent_level_alc" => 'test',
            "agent_level_pec" => 'test',
            "agent_level_bex" => 'test',
            "agent_id" => 'test'
        ];
        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent Id must be integer.'],
                        'agent_level_alc' => ['Ancillary/LifeStyle contracts level must be integer.'],
                        'agent_level_pec' => ['Premier/Elite contracts level must be integer.'],
                        'agent_level_bex' => ['BEX contracts level must be integer.'],
                    ]
            ]);
    }

    /** @test */
    public function send_contract_email_must_have_valid_agent_id()
    {
        $data = [
            "agent_level_alc" => 3,
            "agent_level_pec" => 3,
            "agent_level_bex" => 3,
            "agent_id" => 1234556789
        ];

        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(400)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent Id is invalid.'],
                    ]
            ]);
    }

    /** @test */
    public function send_contract_email_should_not_have_alc_greater_than_equal_to_seven()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_level_alc" => 8,
            "agent_level_pec" => 3,
            "agent_level_bex" => 3,
            "agent_id" => $agentId
        ];

        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(400)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_level_alc' => ['Ancillary/LifeStyle contracts level must be less than or equal 7.'],
                    ]
            ]);
    }

    /** @test */
    public function send_contract_email_should_not_have_pec_greater_than_equal_to_seven()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_level_alc" => 2,
            "agent_level_pec" => 9,
            "agent_level_bex" => 3,
            "agent_id" => $agentId
        ];

        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(400)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_level_pec' => ['Premier/Elite level must be less than or equal 7.'],
                    ]
            ]);
    }

    /** @test */
    public function send_contract_email_should_not_have_bex_greater_than_equal_to_five()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_level_alc" => 2,
            "agent_level_pec" => 2,
            "agent_level_bex" => 6,
            "agent_id" => $agentId
        ];

        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(400)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_level_bex' => ['BEX contracts level must be less than or equal 5.'],
                    ]
            ]);
    }

    /** @test */
    public function can_send_contract_email()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_level_alc" => 2,
            "agent_level_pec" => 2,
            "agent_level_bex" => 2,
            "agent_id" => $agentId
        ];
        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(400)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Representative Contracts Sent.',
                'data' => []
            ]);
    }

    /** @test */
    public function failed_to_send_contract_email()
    {
        //if sending email failed
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_level_alc" => 2,
            "agent_level_pec" => 2,
            "agent_level_bex" => 2,
            "agent_id" => $agentId
        ];
        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(400)
            ->assertJson([
                'success' => false,
                'code' => 400,
                'message' => 'Failed to send representative contracts.',
            ]);
    }
}
