<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use App\AgentPersonalAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SetAgentPrimaryAddressTest extends TestCase
{
    const FAILED_MESSAGE = "Failed to set primary address.";
    const URL = "/api/v2/agent/add-address";

    /** @test */
    public function can_set_primary_group_address()
    {
        $agentId = AgentInfo::first()->agent_id;
        $addressId = AgentPersonalAddress::first()->id;
        $data = [
            "agent_id" => $agentId,
            "id" => $addressId,
            "type" => AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Personal successfully set to primary.',
                'data' => []
            ]);
    }


    /** @test */
    public function add_address_requires_values()
    {
        $this->put(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent is required.'],
                        'id' => ['Agent Address Id is required.'],
                        'type' => ['Type is required.'],
                    ]
            ]);
    }

    public function set_primary_address_require_exact_type()
    {
        $agentId = AgentInfo::first()->gid;
        $addressId = AgentPersonalAddress::first()->id;
        $data = [
            "agent_id" => $agentId,
            "id" => $addressId,
            "type" => "test",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'type' => ['Type should be either personal , business or payment.'],
                    ]
            ]);
    }

    public function set_primary_address_require_agent_id_integer()
    {
        $addressId = AgentPersonalAddress::first()->id;
        $data = [
            "agent_id" => 1234556789,
            "id" => $addressId,
            "type" => AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent not found.'],
                    ]
            ]);
    }

    public function set_primary_address_require_valid_agent_id()
    {
        $addressId = AgentPersonalAddress::first()->id;
        $data = [
            "agent_id" => "test",
            "id" => $addressId,
            "type" => AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent Id should be integer.'],
                    ]
            ]);
    }

    public function set_primary_address_require_id_integer()
    {
        $agentId = AgentInfo::first()->gid;
        $data = [
            "agent_id" => $agentId,
            "id" => "test",
            "type" => AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Agent Address Id should be integer.'],
                    ]
            ]);
    }

    public function set_primary_address_check_if_address_is_already_primary()
    {
        $agentId = AgentInfo::first()->gid;
        $addressId = AgentPersonalAddress::query()
            ->where('agent_id', '=', $agentId)
            ->where('is_primary', '=', 1)
            ->first();
        $data = [
            "agent_id" => $agentId,
            "id" => $addressId,
            "type" => AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL
        ];
        $this->put(self::URL, $data)
            ->assertStatus(409)
            ->assertJson([
                'success' => true,
                'code' => 409,
                'message' => 'This address is already set to primary.',
            ]);
    }
}
