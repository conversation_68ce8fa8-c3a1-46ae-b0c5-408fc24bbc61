<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class DeleteAgentTest extends TestCase
{
    const SUCCESS_MESSAGE = "Agent deleted successfully.";
    const FAILED_MESSAGE = "Failed to delete agent.";
    const URL = "/api/v2/delete-agent";

    public function can_delete_agent()
    {
        $agentInfo = AgentInfo::first();
        $data = [
            'agent_id' => $agentInfo->agent_id,
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' =>self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    public function failed_to_delete_test_agent(){
        $agentInfo = AgentInfo::where([
            'agent_fname'=>'test',
            'agent_lname'=>'test'
        ]);
        $data = [
            'agent_id' => $agentInfo->agent_id,
        ];
        $this->post(self::URL, $data)
            ->assertStatus(400)
            ->assertJson([
                'success' => false,
                'code' => 400,
                'message' =>'Failed to delete test agent.',
            ]);
    }
}
