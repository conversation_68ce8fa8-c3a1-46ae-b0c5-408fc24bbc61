<?php

namespace Tests\Feature\ManageAgent;

use App\AgentInfo;
use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AddAgentGroupTest extends TestCase
{

    const SUCCESS_MESSAGE = "group(s) added successfully.";
    const FAILED_MESSAGE = "Failed to add group(s).";
    const URL = "/api/v2/agent/add-group";

    /** @test */
    public function can_add_agent_group()
    {
        $agentInfo = AgentInfo::first();
        $agentGroupIds = $agentInfo->getGroup()->pluck('gid');
        $gIds = GroupInfo::query()
            ->whereNotIn('gid', $agentGroupIds)
            ->where('gstatus', '=', GroupInfo::STATUS_ACTIVE)
            ->take(2)
            ->pluck('gid')
            ->toArray();
        $data = [
            "agent_id" => $agentInfo->agent_id,
            "gids" => $gIds,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => "2 " . self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    /** @test */
    public function add_agent_group_requires_value()
    {

        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent is required.'],
                        'gids' => ['At least one group Id must be selected.'],
                    ]
            ]);
    }

    public function add_agent_group_requires_valid_agent_id()
    {
        $agentInfo = AgentInfo::first();
        $agentGroupIds = $agentInfo->getGroup()->pluck('gid');
        $gIds = GroupInfo::query()
            ->whereNotIn('gid', $agentGroupIds)
            ->where('gstatus', '=', GroupInfo::STATUS_ACTIVE)
            ->take(2)
            ->pluck('gid')
            ->toArray();
        $data = [
            "agent_id" => 3143433243,
            "gids" => $gIds,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent not found.'],
                    ]
            ]);
    }
}
