<?php

namespace Tests\Feature\ManageAgent;

use App\AgentBusinessAddress;
use App\AgentInfo;
use App\AgentPersonalAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DeleteAgentAddressTest extends TestCase
{
    public const FAILED_MESSAGE = "Failed to delete address.";
    const URL = "/api/v2/agent/delete-address";

    /** @test */
    public function can_delete_group_address()
    {
        $agentId = AgentInfo::first()->agent_id;
        $addressId = AgentPersonalAddress::first()->id;
        $data = [
            "agent_id" => $agentId,
            "id" => $addressId,
            "type" => AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Personal address deleted successfully.',
                'data' => []
            ]);
    }

    /** @test */
    public function can_delete_billing_group_address()
    {
        $agentId = AgentInfo::first()->agent_id;
        $addressId = AgentBusinessAddress::first()->id;
        $data = [
            "agent_id" => $agentId,
            "id" => $addressId,
            "type" => AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Business address deleted successfully.',
                'data' => []
            ]);
    }

    /** @test */
    public function delete_group_address_requires_values()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Agent Address Id is required.'],
                        'type' => ['Type is required.'],
                    ]
            ]);
    }

    public function delete_group_address_require_integer()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "agent_id" => $agentId,
            "id" => "test",
            "type" => AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Agent Address Id should be integer.'],
                    ]
            ]);
    }

    public function delete_group_address_require_exact_type()
    {
        $agentId = AgentInfo::first()->agent_id;
        $addressId = AgentPersonalAddress::first()->id;
        $data = [
            "agent_id" => $agentId,
            "id" => $addressId,
            "type" => "test",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'type' => ['Type should be either personal , business or payment.'],
                    ]
            ]);
    }
}
