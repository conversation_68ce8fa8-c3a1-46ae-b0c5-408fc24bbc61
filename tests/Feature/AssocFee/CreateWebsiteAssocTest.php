<?php

namespace Tests\Feature\AssocFee;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CreateWebsiteAssocTest extends TestCase
{
    /** @test */
    public function website_assoc_fee_requires_values()
    {
        $this->post('/api/v1/create-website-association-fee')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'website' => ['Website is required.'],
                        'plans' => ['At least one plan must be selected.'],
                        'assocPlanId' => ['Association Fee must be required.']
                    ]
            ]);
    }

    /** @test */
    public function website_assoc_fee_already_exists()
    {
        $data = [
            'website' => 'premier.com',
            'plans' => [22,58,63],
            'assocPlanId' => 607,
        ];
        $this->post('/api/v1/create-website-association-fee', $data)
            ->assertStatus(409)
            ->assertExactJson([
                'success' => false,
                'code' => 409,
                'message' => 'Association fee already assigned for this plan and group.',
            ]);
    }

    /** @test */
    public function website_assoc_fee_can_create()
    {
        $data = [
            'website' => 'teletherapy123.com',
            'plans' => [79],
            'assocPlanId' => 607,
        ];
        $this->post('/api/v1/create-website-association-fee', $data)
            ->assertStatus(201)
            ->assertExactJson([
                'success' => true,
                'code' => 201,
                'message' => '1 Website Association Fees Created.',
                'data' => []
            ]);
    }
}
