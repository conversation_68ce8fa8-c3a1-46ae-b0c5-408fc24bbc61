<?php

namespace Tests\Feature\AssocFee;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateGroupAssocTest extends TestCase
{
    /** @test */
    public function group_assoc_fee_requires_values()
    {
        $this->post('/api/v1/group-association-fee/9/update')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'gId' => ['Group is required.'],
                        'planId' => ['Plan must be required.'],
                        'assocPlanId' => ['Association Fee must be required.']
                    ]
            ]);
    }

    /** @test */
    public function group_assoc_fee_does_not_exist()
    {
        $this->post('/api/v1/group-association-fee/44454545/update')
            ->assertStatus(400)
            ->assertExactJson([
                'success' => false,
                'message' => 'Data not found'
            ]);
    }

    /** @test */
    public function group_assoc_fee_can_update()
    {
        $data = [
            'website' => 'premier.com',
            'planId' => 252,
            'assocPlanId' => 607,
        ];
        $this->post('/api/v1/group-association-fee/9/update', $data)
            ->assertStatus(200)
            ->assertExactJson([
                'success' => true,
                'message' => 'Updated',
                'data' => []
            ]);
    }
}
