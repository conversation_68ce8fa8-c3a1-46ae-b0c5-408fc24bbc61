<?php

namespace Tests\Feature\AssocFee;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CreateGroupAssocTest extends TestCase
{
    /** @test */
    public function group_assoc_fee_requires_values()
    {
        $this->post('/api/v1/create-group-association-fee')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'gId' => ['Group is required.'],
                        'plans' => ['At least one plan must be selected.'],
                        'assocPlanId' => ['Association Fee must be required.']
                    ]
            ]);
    }

    /** @test */
    public function group_assoc_fee_already_exists()
    {
        $data = [
            'gid' => 1926,
            'plans' => [22,58,63],
            'assocPlanId' => 607,
        ];
        $this->post('/api/v1/create-group-association-fee', $data)
            ->assertStatus(409)
            ->assertExactJson([
                'success' => false,
                'code' => 409,
                'message' => 'This Group Association Fees are already created.',
            ]);
    }


    /** @test */
    public function group_assoc_fee_can_create()
    {
//        $this->withoutExceptionHandling();
        $data = [
            'groupId' => 1929,
            'plans' => [79],
            'assocPlanId' => 690,
        ];
        $this->post('/api/v1/create-website-association-fee', $data)
            ->assertStatus(201)
            ->assertExactJson([
                'success' => true,
                'code' => 201,
                'message' => '1 Group Association Fees Created.',
                'data' => []
            ]);
    }
}
