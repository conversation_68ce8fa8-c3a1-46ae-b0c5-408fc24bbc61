<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class GetEnrollmentReceiptTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testGetEnrollmentReceipt()
    {
        $this->withoutExceptionHandling();
        $data = [
            'policy_id' => '9900112',
        ];
        $response = $this->json('POST','/api/v1/get-enrollment-receipt',$data);                
        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'planOverview'=>[
                        '*'=>[
                            'policy_id',
                            'cemail',
                            'userid'
                        ]    
                    ],
                    'userInfoPolicyAddress'=>[
                        '*'=>[
                            'userid'
                        ]                            
                    ]                    
                ]
            ]);
    }

}
