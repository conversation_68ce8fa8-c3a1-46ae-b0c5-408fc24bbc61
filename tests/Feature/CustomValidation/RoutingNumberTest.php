<?php

namespace Tests\Feature\CustomValidation;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RoutingNumberTest extends TestCase
{
    public const SUCCESS_MESSAGE = "Routing number successfully validated.";
    public const FAILED_MESSAGE = "Failed to validate routing number.";
    const URL = "/api/v2/validate-routing-number";

    /** @test */
    public function can_validate_routing_number()
    {
        $data = [
            "routing_number" => *********,
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }


    /** @test */
    public function routing_number_required()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'routing_number' => ['Routing number is required.'],
                    ]
            ]);
    }

    public function routing_number_must_be_integer(){
        $data = [
            "routing_number" => "routing number",
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'routing_number' => ['Routing number must be in number.'],
                    ]
            ]);
    }

    public function routing_number_must_be_nine_digits(){
        $data = [
            "routing_number" => ********,
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'routing_number' => ['Routing number should be of 9 digits.'],
                    ]
            ]);
    }
}
