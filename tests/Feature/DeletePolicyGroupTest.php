<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DeletePolicyGroupTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testDeletePolicyGroup()
    {
        $data = [
                 'policy_id' => '9931922',
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/delete-policy-group',$data);

        $response
            ->assertStatus(200)
            ->assertJsonFragment([
                'type' => 'success',
                'message' => 'Group Removed for policy.',
            ]);
    }
    public function testDeleteGroupForPolicyNullPolicyId()
    {
        $data = [
                 'policy_id' => '',
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/delete-policy-group',$data);

        $response
            ->assertStatus(422)
            ->assertSeeText(
                'Policy ID is required.'
            );
    }
    public function testDeleteGroupForPolicyNullUserId()
    {
        $data = [
                 'policy_id' => '9931922',
                 'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/delete-policy-group',$data);

        $response
            ->assertStatus(422)
            ->assertSeeText(
                'User ID is required.'
            );
    }
}
