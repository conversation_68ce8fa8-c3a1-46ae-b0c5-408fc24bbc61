<?php

namespace Tests\Feature\AgentAchPayment;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdatePaymentMethodWithAchTest extends TestCase
{
    public const SUCCESS_MESSAGE = "Agent Payment method updated and ach payment added successfully.";
    public const FAILED_MESSAGE = "Failed to update agent payment method with ach.";
    const URL = '/api/v2/agent/update-payment-method-with-ach-payment';

    /** @test */
    public function can_update_payment_method_with_ach_payment()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_ACH,
            "agent_id" => $agentId,
            "bank_name" => "Test primary new new ",
            "payee_name" => "Test",
            "routing" => *********,
            "account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    /** @test */
    public function update_payment_method_with_ach_payment_requires_values()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'payment_method' => ['Payment method is required.'],
                        'agent_id' => ['Agent is required.'],
                        'payee_name' => ['Payee name is required.'],
                        'bank_name' => ['Bank name is required.'],
                        'routing' => ['The routing field is required.'],
                        'account' => ['The account field is required.'],
                        'account_type' => ['Account type is required.'],
                        'account_holder_type' => ['Account holder type is required.'],
                    ]
            ]);
    }

    /** @test */
    public function update_payment_method_with_ach_payment_must_have_valid_agent_id()
    {
        $data = [
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_ACH,
            "agent_id" => **********,
            "bank_name" => "Test primary new new ",
            "payee_name" => "Test",
            "routing" => *********,
            "account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "companyss",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];

        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agent_id' => ['Agent not found.'],
                    ]
            ]);
    }


    public function update_payment_method_with_ach_payment_require_routing_integer()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_ACH,
            "agent_id" => $agentId,
            "bank_name" => "Test primary new new ",
            "payee_name" => "Test",
            "routing" => *********.77,
            "account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'routing' => ['Routing number should be in number.'],
                    ]
            ]);
    }

    public function update_payment_method_with_ach_payment_require_routing_nine_digits()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_ACH,
            "agent_id" => $agentId,
            "bank_name" => "Test primary new new ",
            "payee_name" => "Test",
            "routing" => ***********,
            "account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'routing' => ['Routing number should be of 9 digits.'],
                    ]
            ]);
    }


    public function update_payment_method_with_ach_payment_require_account_integer()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_ACH,
            "agent_id" => $agentId,
            "bank_name" => "Test primary new new ",
            "payee_name" => "Test",
            "routing" => *********,
            "account" => *********.33,
            "account_type" => "savings",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'account' => ['The account must be an integer.'],
                    ]
            ]);
    }

    public function update_payment_method_with_ach_payment_require_exact_account_type()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_ACH,
            "agent_id" => $agentId,
            "bank_name" => "Test primary new new ",
            "payee_name" => "Test",
            "routing" => *********,
            "account" => *********,
            "account_type" => "savingsss",
            "account_holder_type" => "company",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'account_type' => ['Account type should be either checking or savings.'],
                    ]
            ]);
    }

    public function update_payment_method_with_ach_payment_require_exact_account_holder_type()
    {
        $agentId = AgentInfo::first()->agent_id;
        $data = [
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_ACH,
            "agent_id" => $agentId,
            "bank_name" => "Test primary new new ",
            "payee_name" => "Test",
            "routing" => *********,
            "account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "companyss",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'account_holder_type' => ['Account holder type should be either individual or company.'],
                    ]
            ]);
    }

    public function update_payment_method_with_ach_payment_require_exact_payment_method_type_ach()
    {
        $agentId = AgentInfo::first()->agent_id;
        $paymentAch = AgentInfo::AGENT_PAYMENT_METHOD_ACH;
        $data = [
            "payment_method" => AgentInfo::AGENT_PAYMENT_METHOD_CHECK,
            "agent_id" => $agentId,
            "bank_name" => "Test primary new new ",
            "payee_name" => "Test",
            "routing" => *********,
            "account" => *********,
            "account_type" => "savings",
            "account_holder_type" => "companyss",
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'account_holder_type' => ["Payment Method must be {$paymentAch}."],
                    ]
            ]);
    }
}
