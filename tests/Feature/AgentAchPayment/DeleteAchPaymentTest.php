<?php

namespace Tests\Feature\AgentAchPayment;

use App\AgentAchPaymentBankDetail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DeleteAchPaymentTest extends TestCase
{
    public const SUCCESS_MESSAGE = "Ach Payment deleted successfully.";
    public const FAILED_MESSAGE = "Failed to delete this ach payment.";
    const URL = "/api/v2/agent/delete-ach-payment";

    /** @test */
    public function can_set_delete_ach_payment()
    {
        $id = AgentAchPaymentBankDetail::first()->id;
        $data = [
            "id" => $id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }


    /** @test */
    public function can_set_delete_ach_payment_requires_value()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Ach Payment Id is required.'],
                    ]
            ]);
    }

    /** @test */
    public function can_set_delete_ach_payment_require_integer()
    {
        $id = AgentAchPaymentBankDetail::first()->id;
        $data = [
            "id" => $id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Ach Payment should be integer.'],
                    ]
            ]);
    }

    /** @test */
    public function can_set_delete_check_if_delete()
    {
        $model = AgentAchPaymentBankDetail::query()
            ->where([
                'achpayment_default' => 1
            ])->first();
        $data = [
            "id" => $model->id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(409)
            ->assertJson([
                'success' => true,
                'code' => 409,
                'message' => self::FAILED_MESSAGE,
            ]);
    }
}
