<?php

namespace Tests\Feature\AgentAchPayment;

use App\AgentAchPaymentBankDetail;
use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SetPrimaryAchPaymentTest extends TestCase
{
    public const SUCCESS_MESSAGE = "Ach Payment successfully set to primary.";
    public const FAILED_MESSAGE = "Failed to set ach payment primary.";
    const URL = "/api/v2/agent/set-primary-ach-payment";

    /** @test */
    public function can_set_primary_ach_payment()
    {
        $id = AgentAchPaymentBankDetail::first()->id;
        $data = [
            "id" => $id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }


    /** @test */
    public function can_set_primary_ach_payment_requires_value()
    {
        $this->put(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Ach Payment Id is required.'],
                    ]
            ]);
    }

    /** @test */
    public function can_set_primary_ach_payment_require_integer()
    {
        $id = AgentAchPaymentBankDetail::first()->id;
        $data = [
            "id" => $id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Ach Payment should be integer.'],
                    ]
            ]);
    }

    /** @test */
    public function can_set_primary_check_if_primary()
    {
        $model = AgentAchPaymentBankDetail::query()
            ->where([
                'id' => 1
            ])->first();
        $data = [
            "id" => $model->id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->put(self::URL, $data)
            ->assertStatus(409)
            ->assertJson([
                'success' => true,
                'code' => 409,
                'message' => 'This ach payment is already set to primary.',
            ]);
    }
}
