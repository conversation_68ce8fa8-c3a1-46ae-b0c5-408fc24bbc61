<?php

namespace Tests\Feature\AgentAchPayment;

use App\AgentInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AchPaymentTest extends TestCase
{
    /** @test */
    public function can_fetch_ach_payments()
    {
        $agentId = AgentInfo::first()->agentId;
        $this->get('/agent/' . $agentId . '/ach-payments')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => []
            ]);
    }

    public function failed_to_fetch_if_agent_does_not_exist(){
        $agentId = 123456789;
        $this->get('/agent/' . $agentId . '/ach-payments')
            ->assertStatus(400)
            ->assertJson([
                'success' => false,
                'code' => 400,
                'message' => "Agent not found.",
            ]);
    }
}
