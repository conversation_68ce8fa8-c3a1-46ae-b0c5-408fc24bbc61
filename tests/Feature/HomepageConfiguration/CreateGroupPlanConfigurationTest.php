<?php

namespace Tests\Feature\HomepageConfiguration;

use App\GroupInfo;
use App\Plan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CreateGroupPlanConfigurationTest extends TestCase
{
    /** @test */
    public function homepage_configuration_group_plan_requires_values()
    {
        $this->post('/api/v1/create-group-homepage-configuration', [])
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'groupId' => ['Group Id is required.'],
                        'website' => ['Website Id is required.'],
                        'plans' => ['At least one plan must be selected.'],
                    ]
            ]);
    }

    /** @test */
    public function admin_can_create_homepage_configuration_group_plan()
    {
        $groupId = GroupInfo::first()->gid;
        $plans = Plan::query()
            ->where([
                'forsale' => true,
                'available' => true,
                'is_assoc' => false
            ])
            ->whereNotNull('web_display_name')
            ->groupBy('plan_name_system')
            ->orderBy('web_display_name')
            ->pluck('pid')
            ->take(2)
            ->toArray();
        $data = [
            "groupId" => $groupId,
            "website" => "mybenefitbuilder.com",
            "plans" => $plans
        ];
        $this->post('/api/v1/create-group-homepage-configuration', $data)
            ->assertStatus(201)
            ->assertExactJson([
                'success' => true,
                'code' => 201,
                'message' => 'Homepage Configuration Group Plan Created.',
                'data' => []
            ]);
    }

    /** @test */
    public function admin_cannot_create_homepage_configuration_group_plan_if_group_does_not_exist(){
        $groupId = 42454235435243535353535353553;
        $plans = Plan::query()
            ->where([
                'forsale' => true,
                'available' => true,
                'is_assoc' => false
            ])
            ->whereNotNull('web_display_name')
            ->groupBy('plan_name_system')
            ->orderBy('web_display_name')
            ->pluck('pid')
            ->take(2)
            ->toArray();
        $data = [
            "groupId" => $groupId,
            "website" => "mybenefitbuilder.com",
            "plans" => $plans
        ];
        $this->post('/api/v1/create-group-homepage-configuration', $data)
            ->assertStatus(400)
            ->assertExactJson([
                'success' => false,
                'code' => 400,
                'message' => 'Cannot add plan for this group.',
            ]);
    }
}
