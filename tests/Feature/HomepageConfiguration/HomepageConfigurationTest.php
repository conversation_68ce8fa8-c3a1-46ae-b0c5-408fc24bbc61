<?php

namespace Tests\Feature\HomepageConfiguration;

use App\AgentInfo;
use App\GroupInfo;
use App\HomepageConfiguration;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class HomepageConfigurationTest extends TestCase
{
    /** @test */
    public function admin_can_retrieve_paginated_homepage_configurations_from_agent()
    {
        $agentId = AgentInfo::first()->agent_id;
        $this->get('/api/v1/agent-homepage-configuration/' . $agentId . '/plans')
            ->assertStatus(200)
            ->assertJson([
                "success" => true,
                "code" => 200,
                "message" => "Success",
                "data" => []
            ]);
    }

    /** @test */
    public function admin_can_retrieve_paginated_homepage_configurations_from_group()
    {
        $groupId = GroupInfo::first()->gid;
        $this->get('/api/v1/agent-homepage-configuration/' . $groupId . '/plans')
            ->assertStatus(200)
            ->assertJson([
                "success" => true,
                "code" => 200,
                "message" => "Success",
                "data" => []
            ]);
    }

    /** @test */
    public function admin_can_retrieve_homepage_configuration_options()
    {
        $this->get('/api/v1/get-homepage-configuration-options')
            ->assertStatus(200)
            ->assertJson([
                "success" => true,
                "code" => 200,
                "message" => "Success",
                "data" => []
            ]);
    }

    /** @test */
    public function admin_can_delete_homepage_configuration()
    {
        $model = HomepageConfiguration::first();
        $this->delete('/api/v1/homepage-configuration/' . $model->id . '/delete')
            ->assertStatus(200)
            ->assertExactJson([
                'success' => true,
                'code' => 200,
                'message' => 'Homepage Configuration Deleted.',
                'data' => []
            ]);
    }

    /** @test */
    public function admin_can_toggle_is_featured_column()
    {
        $model = HomepageConfiguration::first();
        $this->put('/api/v1/homepage-configuration/' . $model->id . '/toggle-is-featured')
            ->assertStatus(200)
            ->assertExactJson([
                'success' => true,
                'code' => 200,
                'message' => 'Homepage Configuration Updated.',
                'data' => []
            ]);
    }

}
