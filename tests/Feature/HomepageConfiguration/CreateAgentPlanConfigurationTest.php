<?php

namespace Tests\Feature\HomepageConfiguration;

use App\AgentInfo;
use App\Plan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CreateAgentPlanConfigurationTest extends TestCase
{
    /** @test */
    public function homepage_configuration_agent_plan_requires_values()
    {
        $this->post('/api/v1/create-agent-homepage-configuration', [])
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'agentId' => ['Agent Id is required.'],
                        'website' => ['Website Id is required.'],
                        'plans' => ['At least one plan must be selected.'],
                    ]
            ]);
    }

    /** @test */
    public function admin_can_create_homepage_configuration_agent_plan()
    {
        $agentId = AgentInfo::first()->agent_id;
        $plans = Plan::query()
            ->where([
                'forsale' => true,
                'available' => true,
                'is_assoc' => false
            ])
            ->whereNotNull('web_display_name')
            ->groupBy('web_display_name')
            ->orderBy('web_display_name')
            ->pluck('pid')
            ->take(2)
            ->toArray();
        $data = [
            "agentId" => $agentId,
            "website" => "mybenefitbuilder.com",
            "plans" => $plans
        ];
        $this->post('/api/v1/create-agent-homepage-configuration', $data)
            ->assertStatus(201)
            ->assertExactJson([
                'success' => true,
                'code' => 201,
                'message' => '2 Homepage Configuration Agent Plan Created.',
                'data' => []
            ]);
    }

    /** @test */
    public function admin_cannot_create_homepage_configuration_agent_plan_if_agent_does_not_exist(){
        $agentId = 24545234534524353245234543543;
        $plans = Plan::query()
            ->where([
                'forsale' => true,
                'available' => true,
                'is_assoc' => false
            ])
            ->whereNotNull('web_display_name')
            ->groupBy('web_display_name')
            ->orderBy('web_display_name')
            ->pluck('pid')
            ->take(2)
            ->toArray();
        $data = [
            "agentId" => $agentId,
            "website" => "mybenefitbuilder.com",
            "plans" => $plans
        ];
        $this->post('/api/v1/create-agent-homepage-configuration', $data)
            ->assertStatus(400)
            ->assertExactJson([
                'success' => false,
                'code' => 400,
                'message' => 'Cannot add plan for this agent.',
            ]);
    }
}
