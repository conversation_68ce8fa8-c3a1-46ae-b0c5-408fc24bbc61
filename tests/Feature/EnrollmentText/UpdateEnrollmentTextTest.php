<?php

namespace Tests\Feature\EnrollmentText;

use App\EnrollmentText;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateEnrollmentTextTest extends TestCase
{
    /** @test */
    public function enrollment_text_requires_values()
    {
        $this->post('/api/v1/enrollment-text/create')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'textHead' => ['Heading is required.'],
                        'textBody' => ['Body is required.'],
                        'details' => ['Detail is required.']
                    ]
            ]);
    }

    /** @test */
    public function admin_can_update_enrollment_text()
    {
        $model = EnrollmentText::first();
        $data = [
            'textHead' => 'Update Accident DI Member Agreements"',
            'textBody' => '<div> I understand that I am purchasing a membership in a consumer benefit association.</div> <div> &nbsp;</div> ',
            'details' => "Update WBA Accident DI Agreements",
            'makeRequiredText' => "Required",
            'makeRequiredCheck' => 1,
        ];
        $this->put('/api/v1/enrollment-text/'.$model->etid.'/update', $data)
            ->assertStatus(200)
            ->assertExactJson([
                'success' => true,
                'code' => 200,
                'message' => 'Enrollment Text Updated.',
                'data' => []
            ]);
    }
}
