<?php

namespace Tests\Feature\EnrollmentQuestion;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CreateEnrollmentTextTest extends TestCase
{
    /** @test */
    public function enrollment_text_requires_values()
    {
        $this->post('/api/v1/enrollment-text/create')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'textHead' => ['Heading is required.'],
                        'textBody' => ['Body is required.'],
                        'details' => ['Detail is required.']
                    ]
            ]);
    }

    /** @test */
    public function enrollment_text_make_required_check_must_be_boolean()
    {
        $data = [
            'textHead' => 'New Accident DI Member Agreements"',
            'textBody' => '<div> I understand that I am purchasing a membership in a consumer benefit association.</div> <div> &nbsp;</div> ',
            'details' => "New WBA Accident DI Agreements",
            'makeRequiredText' => "Required",
            'makeRequiredCheck' => "String",
        ];
        $this->post('/api/v1/enrollment-text/create', $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'makeRequiredCheck' => ['Make Required Check must be boolean.']
                    ]
            ]);
    }

    /** @test */
    public function admin_can_create_enrollment_text()
    {
        $data = [
            'textHead' => 'New Accident DI Member Agreements"',
            'textBody' => '<div> I understand that I am purchasing a membership in a consumer benefit association.</div> <div> &nbsp;</div> ',
            'details' => "New WBA Accident DI Agreements",
            'makeRequiredText' => "Required",
            'makeRequiredCheck' => 1,
        ];
        $this->post('/api/v1/enrollment-text/create', $data)
            ->assertStatus(201)
            ->assertExactJson([
                'success' => true,
                'code' => 201,
                'message' => 'Enrollment Text Created.',
                'data' => []
            ]);
    }
}
