<?php

namespace Tests\Feature\EnrollmentText;

use App\EnrollmentText;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EnrollmentTextTest extends TestCase
{
    /** @test */
    public function enrollment_text_does_not_exists()
    {
        $this->get('/api/v1/enrollment-text/1124343434343434343/show')
            ->assertStatus(404)
            ->assertExactJson([
                'success' => false,
                'code' => 404,
                'message' => 'No Enrollment Text found.',
            ]);
    }

    /** @test */
    public function admin_can_retrieve_paginated_enrollment_texts()
    {
        $this->get('/api/v1/get-enrollment-texts')
            ->assertStatus(200);
    }

    /** @test */
    public function admin_can_retrieve_one_enrollment_text()
    {
        $model = EnrollmentText::first();
        $this->get('/api/v1/enrollment-text/'.$model->etid.'/show')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => [
                    'etId' => $model->etid,
                    'textHead' => $model->enrollment_text_head,
                    'textBody' => $model->enrollment_text_body,
                    'details' => $model->details,
                    'makeRequiredCheck' => $model->make_required_chk,
                    'makeRequiredText' => $model->make_required_text,
                    'added' => $model->added,
                    'addedDate' => date('Y-m-d', $model->added),
                    'deleted' => $model->deleted,
                ]
            ]);
    }

    /** @test */
    public function admin_can_delete_enrollment_text()
    {
        $model = EnrollmentText::first();
        $this->put('/api/v1/enrollment-text/'.$model->etid.'/delete')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Enrollment Text Deleted.',
                'data' => []
            ]);
    }
}
