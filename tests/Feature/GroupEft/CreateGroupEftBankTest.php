<?php

namespace Tests\Feature\GroupEft;

use App\GroupInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CreateGroupEftBankTest extends TestCase
{
    const SUCCESS_MESSAGE = "Ach Payment added successfully.";
    const FAILED_MESSAGE = "Failed to add ach payment.";
    const URL = '/api/v2/group/add-eft-bank';

    /** @test */
    public function can_create_group_bank()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "bank_name" => "Test primary new new ",
            "bank_routing" => *********,
            "bank_account" => *********,
            "bank_accountname"=>"Tes",
            "account_type" => "savings",
            "account_holder_type" => "company",
            "is_primary" => 1,
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    /** @test */
    public function add_eft_bank_must_have_valid_group_id()
    {
        $data = [
            "group_id" => **********,
            "bank_name" => "Test primary new new ",
            "bank_routing" => *********,
            "bank_account" => *********,
            "bank_accountname"=>"Tes",
            "account_type" => "savings",
            "account_holder_type" => "company",
            "is_primary" => 1,
            "loginUserId" => null,
            "loginUserName" => null
        ];

        $this->post('/api/v2/agent/send-contract-email', $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'group_id' => ['Group not found.'],
                    ]
            ]);
    }

    public function add_eft_bank_require_is_primary_integer()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "bank_name" => "Test primary new new ",
            "bank_routing" => *********,
            "bank_account" => *********,
            "bank_accountname"=>"Tes",
            "account_type" => "savings",
            "account_holder_type" => "company",
            "is_primary" => "text",
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'is_primary' => ['The is primary must be an integer.'],
                    ]
            ]);
    }

    public function add_eft_bank_require_routing_integer()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "bank_name" => "Test primary new new ",
            "bank_routing" => *********.3434,
            "bank_account" => *********,
            "bank_accountname"=>"Tes",
            "account_type" => "savings",
            "account_holder_type" => "company",
            "is_primary" => 1,
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'bank_routing' => ['Routing number should be in number.'],
                    ]
            ]);
    }

    public function add_eft_bank_require_routing_nine_digits()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "bank_name" => "Test primary new new ",
            "bank_routing" => 34343,
            "bank_account" => *********,
            "bank_accountname"=>"Tes",
            "account_type" => "savings",
            "account_holder_type" => "company",
            "is_primary" => 1,
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'bank_routing' => ['Routing number should be of 9 digits.'],
                    ]
            ]);
    }


    public function add_eft_bank_require_account_integer()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "bank_name" => "Test primary new new ",
            "bank_routing" => *********,
            "bank_account" => *********.22,
            "bank_accountname"=>"Tes",
            "account_type" => "savings",
            "account_holder_type" => "company",
            "is_primary" => 1,
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'bank_account' => ['The account must be an integer.'],
                    ]
            ]);
    }

    public function add_eft_bank_require_exact_account_type()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "bank_name" => "Test primary new new ",
            "bank_routing" => *********,
            "bank_account" => *********,
            "bank_accountname"=>"Tes",
            "account_type" => "savingsssssss",
            "account_holder_type" => "company",
            "is_primary" => 1,
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'account_type' => ['Account type should be either checking or savings.'],
                    ]
            ]);
    }

    public function add_eft_bank_require_exact_account_holder_type()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "bank_name" => "Test primary new new ",
            "bank_routing" => *********,
            "bank_account" => *********,
            "bank_accountname"=>"Tes",
            "account_type" => "savings",
            "account_holder_type" => "compasny",
            "is_primary" => 1,
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'account_holder_type' => ['Account holder type should be either individual or company.'],
                    ]
            ]);
    }

    public function add_eft_bank_require_min_5_digits()
    {
        $groupId = GroupInfo::first()->gid;
        $data = [
            "group_id" => $groupId,
            "bank_name" => "Test primary new new ",
            "bank_routing" => *********,
            "bank_account" => 9999,
            "bank_accountname"=>"Tes",
            "account_type" => "savings",
            "account_holder_type" => "company",
            "is_primary" => 1,
            "loginUserId" => null,
            "loginUserName" => null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'bank_account' => ['Account number must be at least 5 digits.'],
                    ]
            ]);
    }
}
