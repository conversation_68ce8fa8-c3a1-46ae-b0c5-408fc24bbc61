<?php

namespace Tests\Feature\AgentAchPayment;

use App\GroupEft;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DeleteGroupEftBankTest extends TestCase
{
    public const SUCCESS_MESSAGE = "Eft bank deleted successfully.";
    public const FAILED_MESSAGE = "Failed to delete this eft bank.";
    const URL = "/api/v2/agent/delete-ach-payment";

    /** @test */
    public function can_set_delete_eft_bank()
    {
        $id = GroupEft::first()->bank_id;
        $data = [
            "bank_id" => $id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => self::SUCCESS_MESSAGE,
                'data' => []
            ]);
    }

    /** @test */
    public function can_set_primary_eft_bank_requires_valid_group_id()
    {
        $data = [
            "bank_id" => ********,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'bank_id' => ['Bank Id not found.'],
                    ]
            ]);
    }

    /** @test */
    public function can_set_delete_eft_bank_requires_value()
    {
        $this->post(self::URL)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Bank Id is required.'],
                    ]
            ]);
    }

    /** @test */
    public function can_set_delete_eft_bank_require_integer()
    {
        $id = GroupEft::first()->bank_id;
        $data = [
            "bank_id" => $id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'id' => ['Bank Id should be integer.'],
                    ]
            ]);
    }

    /** @test */
    public function can_set_delete_check_if_primary()
    {
        $model = GroupEft::query()
            ->where([
                'is_primary' => 1
            ])->first();
        $data = [
            "id" => $model->id,
            "loginUserId"=>null,
            "loginUserName"=>null
        ];
        $this->post(self::URL, $data)
            ->assertStatus(409)
            ->assertJson([
                'success' => true,
                'code' => 409,
                'message' => 'Cannot delete this primary eft bank.',
            ]);
    }
}
