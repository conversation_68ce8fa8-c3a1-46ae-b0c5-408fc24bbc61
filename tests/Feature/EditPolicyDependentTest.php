<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EditPolicyDependentTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testEditPolicyDependent()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'did' => '71837',
            'd_fname' => 'test -GL--unit-testing',
            'd_mname' => '',
            'd_lname' => 'test',
            'd_dob' => '1995-05-02',
            'd_relate' => 'S',
            'd_gender' => '1',
            'policy_id' => '9931922',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/edit-policy-dependent',$data);

        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Dependent Updated.',
                ]
            ]);
    }
    public function testEditPolicyDependentUserNullDataValidation()
    {
        $data = 
        [
            'did' => '',
            'd_fname' => '',
            'd_mname' => '',
            'd_lname' => '',
            'd_dob' => '',
            'd_relate' => '',
            'd_gender' => '',
            'policy_id' => '',
            'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/edit-policy-dependent',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'did'=>
                    [
                        'Dependent ID is required.'
                    ],
                    'd_fname'=> 
                    [
                        'First name is required.'
                    ],
                    'd_lname'=> 
                    [
                        'Last name is required.'
                    ],
                    'd_relate'=> 
                    [
                        'Relation is required.'
                    ],
                    'd_gender'=>
                    [
                        'Gender is required.'
                    ],
                    'policy_id'=> 
                    [
                        'Policy ID is required.'
                    ],
                    'aid'=>
                    [
                        'User ID is required.'
                    ]
                ]
            ]);
    }
}
