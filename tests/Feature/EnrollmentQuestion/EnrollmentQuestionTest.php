<?php

namespace Tests\Feature\EnrollmentQuestion;

use App\EnrollmentQuestion;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EnrollmentQuestionTest extends TestCase
{
    /** @test */
    public function enrollment_question_does_not_exists()
    {
        $this->get('/api/v1/enrollment-question/1125525445354524/show')
            ->assertStatus(404)
            ->assertExactJson([
                'success' => false,
                'code' => 404,
                'message' => 'No Enrollment Question found.',
            ]);
    }

    /** @test */
    public function admin_can_retrieve_paginated_enrollment_questions()
    {
        $this->get('/api/v1/get-enrollment-questions')
            ->assertStatus(200);
    }

    /** @test */
    public function admin_can_retrieve_one_enrollment_question()
    {
        $model = EnrollmentQuestion::first();
        $this->get('/api/v1/enrollment-question/'.$model->qid.'/show')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Success',
                'data' => [
                    'qId' => $model->qid,
                    'question' => $model->question,
                    'errorMessage' => $model->error_message,
                    'trueCondition' => $model->true_condition,
                    'acceptanceCondition' => array_search($model->true_condition, EnrollmentQuestion::$trueConditions),
                    'status' => $model->status,
                    'formattedStatus' => array_search($model->status, EnrollmentQuestion::$statuses),
                    'deleted' => $model->deleted,
                ]
            ]);
    }

    /** @test */
    public function admin_can_delete_enrollment_question()
    {
        $model = EnrollmentQuestion::first();
        $this->put('/api/v1/enrollment-question/'.$model->qid.'/delete')
            ->assertStatus(200)
            ->assertJson([
                'success' => true,
                'code' => 200,
                'message' => 'Enrollment Question Deleted.',
                'data' => []
            ]);
    }
}
