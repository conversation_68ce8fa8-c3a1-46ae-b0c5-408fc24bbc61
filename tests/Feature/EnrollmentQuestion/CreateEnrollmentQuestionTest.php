<?php

namespace Tests\Feature\EnrollmentQuestion;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CreateEnrollmentQuestionTest extends TestCase
{

    /** @test */
    public function enrollment_question_requires_values()
    {
        $this->post('/api/v1/enrollment-question/create')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'question' => ['Question is required.'],
                        'errorMessage' => ['Error Message is required.'],
                        'trueCondition' => ['Acceptance Condition is required.']
                    ]
            ]);
    }

    /** @test */
    public function admin_can_create_enrollment_question()
    {
        $data = [
            'question' => 'This is Test Question ',
            'errorMessage' => 'This is Error Message',
            'trueCondition' => 1,
            'status' => 1,
        ];
        $this->post('/api/v1/enrollment-question/create', $data)
            ->assertStatus(201)
            ->assertExactJson([
                'success' => true,
                'code' => 201,
                'message' => 'Enrollment Question Created.',
                'data' => []
            ]);
    }
}
