<?php

namespace Tests\Feature\EnrollmentQuestion;

use App\EnrollmentQuestion;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UpdateEnrollmentQuestionTest extends TestCase
{


    /** @test */
    public function enrollment_question_requires_values()
    {
        $this->post('/api/v1/enrollment-question/create')
            ->assertStatus(422)
            ->assertExactJson([
                'errors' =>
                    [
                        'question' => ['Question is required.'],
                        'errorMessage' => ['Error Message is required.'],
                        'trueCondition' => ['Acceptance Condition is required.']
                    ]
            ]);
    }


    /** @test */
    public function admin_can_update_enrollment_question()
    {
        $model = EnrollmentQuestion::first();
        $data = [
            'question' => 'This is Test Question ',
            'errorMessage' => 'This is Error Message',
            'trueCondition' => 1,
            'status' => 1,
        ];
        $this->put('/api/v1/enrollment-question/'.$model->qid.'/update', $data)
            ->assertStatus(200)
            ->assertExactJson([
                'success' => true,
                'code' => 200,
                'message' => 'Enrollment Question Updated.',
                'data' => []
            ]);
    }
}
