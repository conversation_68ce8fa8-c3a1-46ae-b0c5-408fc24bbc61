<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AddPolicyDependentTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testAddPolicyDependent()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'userid' => '107437',
            'd_fname' => 'test -GL-aaa-history-include-unit-testing',
            'd_mname' => '',
            'd_lname' => 'test',
            'd_dob' => '1999-05-02',
            'd_relate' => 'C',
            'd_gender' => '0',
            'd_ssn' => 'c5LlLkUbHI4n/VCGnyThm/Ja4zP9VvUHC/wf63QVLR8=',
            'd_ssn4' => '4545',
            'policy_id' => '9931922',
            'aid' => '107437',
        ];
        $response = $this->json('POST','/api/v1/add-policy-dependent',$data);

        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Dependent Added.',
                ]
            ]);
    }
    public function testAddPolicyDependentUserNullDataValidation()
    {
        $data = 
        [
            'userid' => '',
            'd_fname' => '',
            'd_mname' => '',
            'd_lname' => '',
            'd_dob' => '',
            'd_relate' => '',
            'd_gender' => '',
            'd_ssn' => '',
            'd_ssn4' => '',
            'policy_id' => '',
        ];
        $response = $this->json('POST','/api/v1/add-policy-dependent',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'policy_id'=>
                    [
                        'Policy ID is required.'
                    ],
                    'userid'=> 
                    [
                        'User ID is required.'
                    ],
                    'd_fname'=> 
                    [
                        'First name is required.'
                    ],
                    'd_lname'=> 
                    [
                        'Last name is required.'
                    ],
                    'd_ssn'=> 
                    [
                        'SSN is required.'
                    ],
                    'd_ssn4'=> 
                    [
                        'Last 4 digit of SSN is required.'
                    ],
                    'd_relate'=> 
                    [
                        'Relation is required.'
                    ],
                    'd_gender'=>
                    [
                        'Gender is required.'
                    ],
                    'aid'=>
                    [
                        'User ID is required.'
                    ]
                ]
            ]);
    }
}
