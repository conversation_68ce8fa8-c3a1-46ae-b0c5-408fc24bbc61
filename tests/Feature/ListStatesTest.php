<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ListStatesTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testListStatesTest()
    {
        $this->withoutExceptionHandling();
        $response = $this->json('GET','/api/v1/list-states');                
        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*'=>[
                        'id',
                        'name',
                        'abbrev',
                        'status'
                    ]
                ]
            ]);
    }
}
