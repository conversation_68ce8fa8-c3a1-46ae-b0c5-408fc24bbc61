<?php

namespace Tests\Unit\BillingInformationTest;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class BillingInformationTest extends TestCase
{
    use DatabaseTransactions;
    /**
     * A basic test example.
     *
     * @return void
     */
    public function testGetBillingInformationChangeDetailsFetch()
    {
        $response = $this->post('api/v1/get-billing-information-change-details/71960');
        $response->assertStatus(200);
        $response->assertJsonStructure(["data"]);
    }
    public function testChangeBillingAccountToCC()
    {
        $data = [
            "credit_type"=> "Visa",
            "credit_num"=> "**********",
            "credit_expmonth"=> "04",
            "credit_expyear"=> "2011",
            "credit_date"=> "**********",
            "credit_status"=> "A",
            "credit_userid"=> 71960,
            "firstname"=> "sooraz",
            "lastname"=> "kunwar",
            "credit_shipaddress"=> 9898,
            "charge_status"=> "saving",
            "credit_cvc"=> "555",
            "weburl"=> "9anime.ru",
            "address1"=> "address1",
            "address2"=> "address2",
            "city"=> "city",
            "state"=> "statename",
            "zip"=> "5400"
        ];
        $response = $this->post('api/v1/change-billing-information-cc',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["message"=>"Successfully Added Billing Information CC"]]);
        $response->assertJsonStructure(["data"=>["message"]]);
    }
    public function testChangeBillingAccountToCCInputValidation()
    {
        $data = [
            "credit_type"=> "",
            "credit_num"=> "**********",
            "credit_expmonth"=> "",
            "credit_expyear"=> "2011",
            "credit_date"=> "**********",
            "credit_status"=> "A",
            "credit_userid"=> 71960,
            "firstname"=> "sooraz",
            "lastname"=> "kunwar",
            "credit_shipaddress"=> 9898,
            "charge_status"=> "saving",
            "credit_cvc"=> "555",
            "weburl"=> "9anime.ru",
            "address1"=> "address1",
            "address2"=> "address2",
            "city"=> "city",
            "state"=> "statename",
            "zip"=> "5400"
        ];
        $response = $this->post('api/v1/change-billing-information-cc',$data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors"=>["credit_type","credit_expmonth"]]);
    }
    public function testChangeBillingAccountToEFT()
    {
        $data = [
            "bank_accountname" => "bankName Billing",
            "bank_name" => "Billin Bank Name",
            "bank_routing" => *********,
            "bank_account" => *********,
            "userid" => 2
        ];
        $response = $this->post('api/v1/change-billing-information-eft',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["message"=>"Successfully Added Billing Information EFT"]]);
        $response->assertJsonStructure(["data"=>["message"]]);
    }
    public function testChangeBillingAccountToEFTInputValidation()
    {
        $data = [
            "bank_accountname" => "",
            "bank_name" => "Billin Bank Name",
            "bank_routing" => 6789,
            "bank_account" => *********,
            "userid" => 2
        ];
        $response = $this->post('api/v1/change-billing-information-eft',$data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors"=>["bank_routing","bank_accountname"]]);
    }
}
