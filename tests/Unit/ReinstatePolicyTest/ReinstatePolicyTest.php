<?php

namespace Tests\Unit\ReinstatePolicyTest;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class ReinstatePolicyTest extends TestCase
{
    use DatabaseTransactions;
    /**
     * A basic test example.
     *
     * @return void
     */
    public function testGetDataOfReinstatePolicyDetails()
    {
        $response = $this->post('api/v1/get-policy-reinstate-details/9933261');
        $response->assertStatus(200);
        $response->assertJsonStructure(["data"=>["briefinfo"=>["user_policy_info","policy_overview"],"plan_overview_data"]]);
    }
    public function testUpdateUserPolicyInformation()
    {
        $data = [
            "reason"=>"Reason.",
            "policyID"=> "9933261",
            "aid"=>"22",
            "substartdate"=>"2020-02-03"
        ];
        $response = $this->post('api/v1/set-policy-reinstate-details',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added RPC"]]);
    }
    public function testUpdatingUserPolicyInformationInputValidation()
    {
        $data = [
            "reason"=>"Reason.",
             "policyID"=> "",
             "aid"=>"",
            "substartdate"=>"2020-02-03"
        ];
        $response = $this->post('api/v1/set-policy-reinstate-details',$data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors"=>["policyID","aid"]]);
    }
}
