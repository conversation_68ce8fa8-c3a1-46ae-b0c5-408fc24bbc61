<?php

namespace Tests\Unit\AgentUpdate;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class UpdateAgentStatusTest extends TestCase
{
	use DatabaseTransactions;
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testUpdateAgentStatus()
    {
        $this->withoutExceptionHandling();
        Mail::fake();
        $data = 
        [
            'agent_id' => '47',
            'status' => 'S',
        ];
        $response = $this->json('POST','/api/v1/update-agent-status',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Updated agent status. Email notification sent.',
                ]
            ]);    
    }
    public function testUpdateAgentStatusNotExists()
    {
        $this->withoutExceptionHandling();
        Mail::fake();        
        $data = 
        [
            'agent_id' => '8200000',
            'status' => 'S',
        ];
        $response = $this->json('POST','/api/v1/update-agent-status',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'error',
                    'message' => 'Agent not found.',
                ]
            ]);    
    }
    public function testUpdateAgentStatusNullDataValidation()
    {
        $this->withoutExceptionHandling();
        Mail::fake();        
        $data = 
        [
            'agent_id' => '',
            'status' => '',
        ];
        $response = $this->json('POST','/api/v1/update-agent-status',$data);                
        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'agent_id'=> 
                    [
                        'Agent Id is required.'
                    ],
                    'status'=> 
                    [
                        'Status is required.'
                    ]                    
                ]
            ]);   
    }
}
