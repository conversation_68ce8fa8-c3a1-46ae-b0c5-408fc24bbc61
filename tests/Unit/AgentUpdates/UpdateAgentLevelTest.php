<?php

namespace Tests\Unit\AgentUpdates;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class UpdateAgentLevelTest extends TestCase
{
	use DatabaseTransactions;
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testUpdateAgentLevel()
    {
        $this->withoutExceptionHandling();
        Mail::fake();
        $data = 
        [
            'agent_id' => '47',
            'agent_gid' => '10',
            'level'=>'2'
        ];
        $response = $this->json('POST','/api/v1/update-agent-level',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Updated agent Level. Email notification sent.',
                ]
            ]);    
    }
    public function testUpdateAgentLevelNotExists()
    {
        $this->withoutExceptionHandling();
        Mail::fake();        
        $data = 
        [
            'agent_id' => '8200000',
            'agent_gid' => '360363625',
            'level'=>'1'
        ];
        $response = $this->json('POST','/api/v1/update-agent-level',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'error',
                    'message' => 'Agent not found.',
                ]
            ]);    
    }
    public function testUpdateAgentLevelNullDataValidation()
    {
        $this->withoutExceptionHandling();
        Mail::fake();        
        $data = 
        [
            'agent_id' => '',
            'agent_gid' => '',
            'level'=>''
        ];
        $response = $this->json('POST','/api/v1/update-agent-level',$data);                
        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'agent_id'=> 
                    [
                        'Agent Id is required.'
                    ],
                    'agent_gid'=> 
                    [
                        'Agent Group Id is required.'
                    ],
                    'level'=> 
                    [
                        'Agent Level is required.'
                    ]
                ]
            ]);   
    }
}
