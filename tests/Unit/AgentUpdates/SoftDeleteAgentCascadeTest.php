<?php

namespace Tests\Unit\AgentUpdates;

use App\AgentAchpayment;
use App\AgentAchPaymentBankDetail;
use App\AgentBusiness;
use App\AgentBusinessAddress;
use App\AgentCheckpayment;
use App\AgentChequeAddress;
use App\AgentGroup;
use App\AgentInfo;
use App\AgentLicense;
use App\AgentPersonalAddress;
use App\Repositories\Agents\ManageAgents;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class SoftDeleteAgentCascadeTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * Agent is soft deleted
     * @test
     * @return void
     */
    public function agentIsSoftDeleted()
    {
        $agentList = AgentInfo::latest()->take(10)->get();

        foreach ($agentList as $each) {
            $each->delete();
            $this->assertSoftDeleted('agent_info', [
                'agent_id' => $each->agent_id
            ]);
        }
    }

    /**
     * Agent down-line is not changed
     * @test
     */
    public function noChangeToDownline()
    {
        $agentList = AgentInfo::latest()->take(10)->get();

        foreach ($agentList as $each) {
            $old_agent_ga = $each->agent_ga;
            $each->delete();
            $new_agent_ga = $each->agent_ga;
            $this->assertEquals($old_agent_ga, $new_agent_ga);
        }
    }

    /**
     * Ensure affected tables are soft-deleted
     * and their status are updated accordingly
     * @test
     * @return void
     */
    public function softDeleteIsCascade()
    {
        $agentList = AgentInfo::latest()->take(50)->get();

        foreach ($agentList as $each) {
            $agent_id = $each->agent_id;

            $group_exist = isset($each->agentGroups);
            $license_exist = isset($each->getLicense);
            $personal_address_exist = isset($each->personalAddresses);
            $business_exist = isset($each->agentBusines);
            $business_add_exist = isset($each->businessAddresses);
            $check_exist = isset($each->agentCheckPayment);
            $check_add_exist = isset($each->chequeAddresses);
            $ach_exist = isset($each->achPayment);
            $ach_bank_exist = isset($each->achPaymentBankDetails);

            $manage_agents_obj = new ManageAgents();
            $manage_agents_obj->deleteAgent(['agent_id' => $agent_id]);


            // AGENT INFO
            $this->assertDatabaseHas('agent_info', [
                'agent_id' => $agent_id,
                'agent_status' => 'D'
            ]);
            $agent = AgentInfo::withTrashed()->find($agent_id);
            $this->assertNotNull($agent->deleted_at);

            // AGENT GROUP
            if ($group_exist) {
                $groups = AgentGroup::withTrashed()->where('agent_id', $agent_id)->get();
                foreach ($groups as $eachGroup) {
                    $this->assertEquals($eachGroup->ag_status, 'D');
                    $this->assertNotNull($eachGroup->deleted_at);
                }
            }

            // AGENT LICENSE
            if ($license_exist) {
                $licenses = AgentLicense::withTrashed()->where('license_agent_id', $agent_id)->get();
                foreach ($licenses as $eachLicense) {
                    $this->assertEquals($eachLicense->license_deleted, 1);
                    $this->assertEquals($eachLicense->license_status, 'D');
                    $this->assertNotNull($eachLicense->deleted_at);
                }
            }

            // PERSONAL ADDRESS
            if ($personal_address_exist) {
                $addresses = AgentPersonalAddress::withTrashed()->where('agent_id', $agent_id)->get();
                foreach ($addresses as $eachAddress) {
                    $this->assertEquals($eachAddress->active, 0);
                    $this->assertNotNull($eachAddress->deleted_at);
                }
            }

            // BUSINESS
            if ($business_exist) {
                $businesses = AgentBusiness::withTrashed()->where('business_agent_id', $agent_id)->get();
                foreach ($businesses as $eachBusiness) {
                    $this->assertNotNull($eachBusiness->deleted_at);
                }
            }

            // BUSINESS ADDRESS
            if ($business_add_exist) {
                $business_addresses = AgentBusinessAddress::withTrashed()->where('agent_id', $agent_id);
                foreach ($business_addresses as $eachBusinessAdd) {
                    $this->assertNotNull($eachBusinessAdd->deleted_at);
                }
            }

            // CHEQUE
            if ($check_exist) {
                $cheques = AgentCheckpayment::withTrashed()->where('checkpaymentagent_id', $agent_id)->get();
                foreach ($cheques as $eachCheque) {
                    $this->assertNotNull($eachCheque->deleted_at);

                }
            }

            // CHEQUE ADD
            if ($check_add_exist) {
                $cheque_addresses = AgentChequeAddress::withTrashed()->where('agent_id', $agent_id)->get();
                foreach ($cheque_addresses as $eachChequeAdd) {
                    $this->assertNotNull($eachChequeAdd->deleted_at);
                }
            }

            // ACH
            if ($ach_exist) {
                $achs = AgentAchpayment::withTrashed()->where('achagent_id', $agent_id)->get();
                foreach ($achs as $eachAch) {
                    $this->assertNotNull($eachAch->deleted_at);
                }
            }

            // ACH BANK
            if ($ach_bank_exist) {
                $ach_banks = AgentAchPaymentBankDetail::withTrashed()->where('agent_id', $agent_id)->get();
                foreach ($ach_banks  as $eachAchBank) {
                    $this->assertNotNull($eachAchBank->deleted_at);
                }
            }
        }
    }
}
