<?php

namespace Tests\Unit\AgentUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ViewAgentLicenseTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testViewAgentLicense()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'agent_id' => '100366',
        ];
        $response = $this->json('POST','/api/v1/view-agent-license',$data);
        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*'=>[
                        'license_id',
                        'license_agent_id',
                        'license_state',
                        'license_number'
                    ]    
                ]
            ]);
    }
}
