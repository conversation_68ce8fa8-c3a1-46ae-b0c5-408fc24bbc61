<?php

namespace Tests\Unit\AgentUpdates;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class UpdateAgentGroupTest extends TestCase
{
	use DatabaseTransactions;
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testUpdateAgentGroup()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'agent_id' => '47',
            'admin_only'=>'1',
            'idilusplan' => 'H',
            'enaplan' => 'L',
            'amgplan' => 'H',
            'eliteblue' => '1',
            'agency_id' => [
            	'34',
            	'35'
            ]
        ];
        $response = $this->json('POST','/api/v1/update-agent-group',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Agent Group Information Updated',
                ]
            ]);    
    }
}
