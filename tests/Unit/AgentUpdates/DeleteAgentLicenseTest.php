<?php

namespace Tests\Unit\AgentUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class DeleteAgentLicenseTest extends TestCase
{
	use DatabaseTransactions;
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testDeleteAgentLicense()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
        	'license_id' => '5518',
            'agent_id' => '100540'            
        ];
        $response = $this->json('POST','/api/v1/remove-agent-license',$data);
        $response
            ->assertStatus(200)
            ->assertExactJson([
            	'data'=>[
                    'type' => 'success',
                    'message' => 'Agent License Deleted.'
                ]    
            ]);
    }
    public function testDeleteAgentLicenseNullDataValidation()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
        	'license_id' => '',
            'agent_id' => ''            
        ];
        $response = $this->json('POST','/api/v1/remove-agent-license',$data);
        $response
            ->assertStatus(422)
            ->assertExactJson([
            	'errors'=>[
                    'license_id'=> 
                    [
                        'License ID is required.'
                    ],                    
                    'agent_id'=> 
                    [
                        'Agent ID is required.'
                    ],
                ]    
            ]);
    }
}
