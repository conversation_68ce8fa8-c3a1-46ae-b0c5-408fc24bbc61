<?php

namespace Tests\Unit\AgentUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class EditAgentLicenseTest extends TestCase
{
	use DatabaseTransactions;
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testEditAgentLicenseTest()
    {
    	$this->withoutExceptionHandling();
    	Mail::fake();
        Storage::fake('nuerabenefits');    	
        $data = [
        	     'license_id'=>'5518',
                 'agent_id' => '100540',
                 'license_number' => '3456',
                 'license_exp_date' => '2021-02-02',
                 'license_state' => 'TE',
                 'license_status' => 'D',                 
                 'license_resident' => '0',
                 'file'=>UploadedFile::fake()->image('test1-glglgl.jpg'),
        ];
        $response = $this->json('POST','/api/v1/update-agent-license',$data);

        $response
            ->assertStatus(200)
            ->assertJsonFragment([
                'type' => 'success',
                'message' => 'License Added Successfully, Document Uploaded and email notification sent.',
            ]);
    }
    public function testEditAgentLicenseTestNullDataValidation()
    {
    	$this->withoutExceptionHandling();
    	Mail::fake();
        Storage::fake('nuerabenefits');    	
        $data = [
        	     'license_id'=>'',
                 'agent_id' => '',
                 'license_number' => '',
                 'license_exp_date' => '2021-02-02',
                 'license_state' => '',
                 'license_status' => '',                 
                 'license_resident' => '',
                 'file'=>UploadedFile::fake()->image('test1-glglgl.jpg')
        ];
        $response = $this->json('POST','/api/v1/update-agent-license',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'license_id'=> 
                    [
                        'License ID is required.'
                    ],                    
                    'agent_id'=> 
                    [
                        'Agent ID is required.'
                    ],
                    'license_number'=> 
                    [
                        'License No is required.'
                    ],
                    'license_state'=> 
                    [
                        'License State is required.'
                    ],            
                    'license_status'=> 
                    [
                        'License status is required.'
                    ],                    
                    'license_resident'=> 
                    [
                        'License resident is required.'
                    ]                    
                ]
            ]);
    }
}
