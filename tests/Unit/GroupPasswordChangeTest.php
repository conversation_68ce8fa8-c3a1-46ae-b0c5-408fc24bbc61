<?php

namespace Tests\Unit;

// use PHPUnit\Framework\TestCase;

use App\GroupUser;
use App\SsoUsers;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class GroupPasswordChangeTestCase extends TestCase
{   
    use DatabaseTransactions;
    // use RefreshDatabase;
    /**
     * 
     * @test
     * @runInSeparateProcess
     */

    public function testDatabase()
    {
        //old passwords
        $user_old =SsoUsers::where([['email','<EMAIL>'],['user_type','G']])->value('password');
        $group_old = GroupUser::where('groupuser_email', '<EMAIL>')->value('groupuser_password');

        $url = '/api/v2/change-group-password';
        $data = [
            "new_password" =>"1111",
            "confirm_password" => "1111",
            "group_email" => "<EMAIL>",
        ];
        $response = $this->put($url, $data);
        $response->assertStatus(200)->assertSeeText("Password updated successfully.")->assertJsonStructure(["success","code","message","data"]);

        $user_new = SsoUsers::where([['email',$data['group_email']],['user_type','G']])->value('password');
        $group_new = GroupUser::where('groupuser_email',$data['group_email'] )->value('groupuser_password');

        $this->assertNotEquals($user_old,$user_new,'the password must be different after changing');
        $this->assertNotEquals($group_old,$group_new,'the password must be different after changing');
        $this->assertEquals($user_new, $group_new,'password must be same in both tables');


        // var_dump($group);
    }
}