<?php

namespace Tests\Unit\Commission;

use App\AgentInfo;
use App\NbCommissionDataSummary;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class AgentCommissionTest extends TestCase
{
    use DatabaseTransactions;

    private $agent_id;
    private $year;
    private $month;
    private $url;

    public function __construct($name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);
        $this->agent_id = 211;
        $this->year = 2022;
        $this->month = 5;
        $this->set_url($this->agent_id, $this->year, $this->month);
    }

    private function set_url($agent_id, $year, $month)
    {
        $this->url = "/api/v2/agent/{$agent_id}/commission/{$year}/{$month}";
    }

    /**
     * @test
     * @runInSeparateProcess
     */
    public function check_json_structure()
    {
        $response = $this->get($this->url);
        $response->assertJsonStructure([
            'status', 'statusCode', 'data' => [
                'direct' => [
                    'commission' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
                    'delta' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
                ],
                'downline' => [
                    'commission' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
                    'delta' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
                ],
                'special' => [
                    'commission' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
                    'delta' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
                ],
                'association' => [
                    'commission' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
                    'delta' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
                ],
                'onetime' => [
                    'commission' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
                    'delta' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
                ],
                'override' => [
                    'commission' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
                    'delta' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
                ],
                'affiliate' => [
                    'commission' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
                    'delta' => [
                        'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
                ],
                'month' => [
                    'commission', 'delta', 'paid', 'unpaid'
                ]
            ]
        ]);
    }

    /**
     * @test
     * @runInSeparateProcess
     */
    public function check_validation()
    {
        $this->agent_id = AgentInfo::pluck('agent_id')->last() + 1;
        $this->year = date('Y') + 1;
        $this->month = 13;
        $this->set_url($this->agent_id, $this->year, $this->month);
        $response = $this->get($this->url);
        $response->assertSeeText('Invalid Agent ID. The Agent ID does not exist.');
        $response->assertSeeText('The year must be a date before or equal to 2023.');
        $response->assertSeeText('Month must be in \'MM\' format.');
        $response->assertStatus(422);
    }

    /**
     * @test
     * @runInSeparateProcess
     * test long AF
     */
    public function verify_commission_delta()
    {
        $agent_id = AgentInfo::orderBy('agent_id', 'desc')->take(500)->pluck('agent_id');

        foreach (NbCommissionDataSummary::$commission_category as $eachCategory) {
            foreach ($agent_id as $each_id) {
                $this->set_url($each_id, $this->year, $this->month);
                $response = $this->get($this->url)->json();

                $dec_21 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2021-12%')->pluck($eachCategory)->first();
                $jan_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-01%')->pluck($eachCategory)->first();
                if (isset($dec_21) && $dec_21 != 0 && $dec_21 != null) {
                    $jan_delta = ($jan_22 - $dec_21) / $dec_21 * 100;
                } else {
                    $jan_delta = 0;
                }
                $this->assertEquals($jan_delta, $response['data'][$eachCategory]['delta']['jan']);

                $feb_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-02%')->pluck($eachCategory)->first();
                if (isset($jan_22) && $jan_22 != 0 && $jan_22 != null) {
                    $feb_delta = ($feb_22 - $jan_22) / $jan_22 * 100;
                } else {
                    $feb_delta = 0;
                }
                $this->assertEquals($feb_delta, $response['data'][$eachCategory]['delta']['feb']);

                $mar_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-03%')->pluck($eachCategory)->first();
                if (isset($feb_22) && $feb_22 != 0 && $feb_22 != null) {
                    $mar_delta = ($mar_22 - $feb_22) / $feb_22 * 100;
                } else {
                    $mar_delta = 0;
                }
                $this->assertEquals($mar_delta, $response['data'][$eachCategory]['delta']['mar']);

                $apr_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-04%')->pluck($eachCategory)->first();
                if (isset($mar_22) && $mar_22 != 0 && $mar_22 != null) {
                    $apr_delta = ($apr_22 - $mar_22) / $mar_22 * 100;
                } else {
                    $apr_delta = 0;
                }
                $this->assertEquals($apr_delta, $response['data'][$eachCategory]['delta']['apr']);

                $may_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-05%')->pluck($eachCategory)->first();
                if (isset($apr_22) && $apr_22 != 0 && $apr_22 != null) {
                    $may_delta = ($may_22 - $apr_22) / $apr_22 * 100;
                } else {
                    $may_delta = 0;
                }
                $this->assertEquals($may_delta, $response['data'][$eachCategory]['delta']['may']);

                $jun_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-06%')->pluck($eachCategory)->first();
                if (isset($may_22) && $may_22 != 0 && $may_22 != null) {
                    $jun_delta = ($jun_22 - $may_22) / $may_22 * 100;
                } else {
                    $jun_delta = 0;
                }
                $this->assertEquals($jun_delta, $response['data'][$eachCategory]['delta']['jun']);

                $jul_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-07%')->pluck($eachCategory)->first();
                if (isset($jun_22) && $jun_22 != 0 && $jun_22 != null) {
                    $jul_delta = ($jul_22 - $jun_22) / $jun_22 * 100;
                } else {
                    $jul_delta = 0;
                }
                $this->assertEquals($jul_delta, $response['data'][$eachCategory]['delta']['jul']);

                $aug_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-08%')->pluck($eachCategory)->first();
                if (isset($jul_22) && $jul_22 != 0 && $jul_22 != null) {
                    $aug_delta = ($aug_22 - $jul_22) / $jul_22 * 100;
                } else {
                    $aug_delta = 0;
                }
                $this->assertEquals($aug_delta, $response['data'][$eachCategory]['delta']['aug']);

                $sep_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-09%')->pluck($eachCategory)->first();
                if (isset($aug_22) && $aug_22 != 0 && $aug_22 != null) {
                    $sep_delta = ($sep_22 - $aug_22) / $aug_22 * 100;
                } else {
                    $sep_delta = 0;
                }
                $this->assertEquals($sep_delta, $response['data'][$eachCategory]['delta']['sep']);

                $oct_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-10%')->pluck($eachCategory)->first();
                if (isset($sep_22) && $sep_22 != 0 && $sep_22 != null) {
                    $oct_delta = ($oct_22 - $sep_22) / $sep_22 * 100;
                } else {
                    $oct_delta = 0;
                }
                $this->assertEquals($oct_delta, $response['data'][$eachCategory]['delta']['oct']);

                $nov_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-11%')->pluck($eachCategory)->first();
                if (isset($oct_22) && $oct_22 != 0 && $oct_22 != null) {
                    $nov_delta = ($nov_22 - $oct_22) / $oct_22 * 100;
                } else {
                    $nov_delta = 0;
                }
                $this->assertEquals($nov_delta, $response['data'][$eachCategory]['delta']['nov']);

                $dec_22 = NbCommissionDataSummary::where('agent_id', $each_id)->where('paid_through_date', 'LIKE', '2022-12%')->pluck($eachCategory)->first();
                if (isset($nov_22) && $nov_22 != 0 && $nov_22 != null) {
                    $dec_delta = ($dec_22 - $nov_22) / $nov_22 * 100;
                } else {
                    $dec_delta = 0;
                }
                $this->assertEquals($dec_delta, $response['data'][$eachCategory]['delta']['dec']);

                // sleep 1 sec
                sleep(1);
            }
            sleep(2);
        }
    }
}
