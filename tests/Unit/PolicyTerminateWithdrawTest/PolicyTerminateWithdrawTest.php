<?php

namespace Tests\Unit\PolicyTerminateWithdrawTest;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class PolicyTerminateWithdrawTest extends TestCase
{
    use DatabaseTransactions;
    /**
     * A basic test example.
     *
     * @return void
     */
    public function testGetDataForTerminateWithdrawPolicy()
    {
        $response = $this->post('api/v1/terminate-and-withdraw-details/9933261');
        $response->assertStatus(200);
        $response->assertJsonStructure(["data"=>["user_policy_info","policy_overview"]]);
    }
    public function testGetUserPolicyInformationDetails()
    {
        $response = $this->post('api/v1/get-user-policy-information-change-details/72017');
        $response->assertStatus(200);
        $response->assertJsonStructure(["data"]);
    }
    public function testUpdateUpdatingUserPolicyInformation()
    {
        $data = [
            "userid"=> 72017,
            "cfname"=> "Anita sss",
            "clname"=> "<PERSON>",
            "cmname"=> "D",
            "cemail"=> "<EMAIL>",
            "cgender"=> "1",
            "cdob"=> "1972-03-18",
            "cssn"=> "76767676",
            "cssn4"=> "1111",
            "employed"=> 1,
            "phone1"=> "6784225393",
            "aid"=>"7",
            "policyID"=>"77"
        ];
        $response = $this->post('api/v1/update-user-policy-information',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added PIC"]]);
    }
    public function testUpdateUpdatingUserPolicyInformationInputValidation()
    {
        $data = [
            "userid"=> "",
            "cfname"=> "Anita sss",
            "clname"=> "Riley",
            "cmname"=> "D",
            "cemail"=> "<EMAIL>",
            "cgender"=> "1",
            "cdob"=> "1972-03-18",
            "cssn"=> "76767676",
            "cssn4"=> "1111",
            "employed"=> 1,
            "phone1"=> "6784225393",
            "aid"=>"7",
            "policyID"=>""
        ];
        $response = $this->post('api/v1/update-user-policy-information',$data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors"=>["userid","policyID"]]);
    }
    public function testTerminatePolicyTNP()
    {
        $data = [
            "tdate"=>"2019-02-14",
            "reason"=> "this is test reason",
            "policy_id"=> "9933261",
            "aid"=> "39856",
            "sendEmail"=>"0",
            "action"=>"tnp",
        ];
        $response = $this->post('api/v1/terminate-policy',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added TNP"]]);
    }
    public function testTerminatePolicyTMO()
    {
        $data = [
            "tdate"=>"2019-02-14",
            "reason"=> "this is test reason",
            "policy_id"=> "9933261",
            "aid"=> "39856",
            "sendEmail"=>"0",
            "action"=>"tmo",
        ];
        $response = $this->post('api/v1/terminate-policy',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added TMO"]]);
    }
    public function testTerminatePolicyTCR()
    {
        $data = [
            "tdate"=>"2019-02-14",
            "reason"=> "this is test reason",
            "policy_id"=> "9933261",
            "aid"=> "39856",
            "sendEmail"=>"0",
            "action"=>"tcr",
        ];
        $response = $this->post('api/v1/terminate-policy',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added TCR"]]);
    }
    public function testWithdrawPolicyWNP()
    {
        $data = [
            "tdate"=>"2019-02-14",
            "reason"=> "this is wnp policy",
            "policy_id"=> "9933261",
            "aid"=> "39856",
            "sendEmail"=>"0",
            "action"=>"wnp",
        ];
        $response = $this->post('api/v1/withdraw-policy',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added WNP"]]);
    }
    public function testWithdrawPolicyWDR()
    {
        $data = [
            "tdate"=>"2019-02-14",
            "reason"=> "this is WDR policy",
            "policy_id"=> "9933261",
            "aid"=> "39856",
            "sendEmail"=>"0",
            "action"=>"wdr",
        ];
        $response = $this->post('api/v1/withdraw-policy',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added WDR"]]);
    }
    public function testWithdrawPolicyWCP()
    {
        $data = [
            "tdate"=>"2019-02-14",
            "reason"=> "this is wcp policy",
            "policy_id"=> "9933261",
            "aid"=> "39856",
            "sendEmail"=>"0",
            "action"=>"wcp",
        ];
        $response = $this->post('api/v1/withdraw-policy',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added WCP"]]);
    }
    public function testWithdrawPolicyWNO()
    {
        $data = [
            "tdate"=>"2019-02-14",
            "reason"=> "this is  wno polict",
            "policy_id"=> "9933261",
            "aid"=> "39856",
            "sendEmail"=>"0",
            "action"=>"wno",
        ];
        $response = $this->post('api/v1/withdraw-policy',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Successfully Added WNO"]]);
    }

    public function testWithdrawTerminationPolicyEffectiveDateComparisonToTerminationDateValidation()
    {
        $data = [
            "tdate"=>"2017-02-14",
            "reason"=> "this is effective date validation polict",
            "policy_id"=> "9933261",
            "aid"=> "39856",
            "sendEmail"=>"0",
            "action"=>"tcr",
        ];
        $response = $this->post('api/v1/terminate-policy',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["error"=>"Termination date should be higher than effective date"]]);
    }
    public function testWithdrawTerminationPolicyInputRequiredValidation()
    {
        $data = [
            "tdate"=>"",
            "reason"=> "",
            "policy_id"=> "",
            "aid"=> "",
            "sendEmail"=>"",
            "action"=>"",
        ];
        $response = $this->post('api/v1/terminate-policy',$data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors"=>["tdate","reason","policy_id","aid","sendEmail","action"]]);
    }
}
