<?php

namespace Tests\Unit\PolicyRider;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class PolicyRiderTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * A basic test example.
     *
     * @return void
     */

    public function testRiderPlanList()
    {
        $response = $this->get('api/v1/get-policy-rider-plan/9800014');
        $response->assertStatus(200);
        $response->assertJsonStructure(["data" => ["plan_list"]]);
    }

    public function testGetRiderPlanListWithInvalidPolicyID()
    {
        $response = $this->get('api/v1/get-policy-rider-plan/71960999');
        $response->assertStatus(200);
        $response->assertExactJson(["data" => ["error" => "Policy details not found."]]);
    }

    public function testAddPolicyRiderTestWithEmptyField()
    {
        $data = [
            "policy_id" => '',
            "reason" => "This is test reason",
            "plan_pricing_id" => '',
            "agent_id" => 114
        ];
        $response = $this->post('api/v1/add-policy-rider', $data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors" => ["policy_id", "plan_pricing_id"]]);
    }

    public function testTerminateRiderPolicyWithEmptyFiled()
    {
        $data = [
            "policy_id" => '',
            "reason" => "this is reason ok ok ok",
            "plan_policy_id" => '' ,
            "agent_id" => 144,
            "term_date" => "2020-08-31",
            "sendEmail" => 1
        ];
        $response = $this->post('api/v1/terminate-policy-rider', $data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors" => ["policy_id", "plan_policy_id"]]);
    }

    public function testTerminateRiderPolicyWithInvalidPolicyID()
    {
        $data = [
            "policy_id" => '999999999',
            "reason" => "this is reason ok ok ok",
            "plan_policy_id" => '194' ,
            "agent_id" => 144,
            "term_date" => "2020-08-31",
            "sendEmail" => 1
        ];
        $response = $this->post('api/v1/terminate-policy-rider', $data);
        $response->assertStatus(200);
        $response->assertExactJson(["data" => ["error" => "Policy not exist or not active."]]);
    }
}
