<?php

namespace Tests\Unit;

use App\SsoUsers;
use App\UserLogin;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class MemberPasswordChangeTestCase extends TestCase
{
    use DatabaseTransactions;
    /**
     * @runInSeparateProcess
     */
    public function testBasicTest()
    {
        $SSO_OldPassword = SsoUsers::where('email', '<EMAIL>')->value('password');
        $UserOldPassword = UserLogin::where('username', '<EMAIL>')->value('password');
        $attributes = [
            "new_password" => "1121qq#",
            "confirm_password" => "1121qq#",
            "member_email" => "<EMAIL>"
        ];
        $response = $this->json('PUT','/api/v2/change-member-password',$attributes);
        $response->assertStatus(200)
                ->assertJsonStructure(["success", "code", "message", "data"])
                ->assertJson([
                    "success" => 'true',
                    "code" => 200,
                    "message" => "Password updated successfully.",
                ]);
        $SSO_NewPassword = SsoUsers::where('email', '<EMAIL>')->value('password');
        $UserNewPassword = UserLogin::where('username', '<EMAIL>')->value('password');
        $this->assertNotEquals($SSO_OldPassword, $SSO_NewPassword, 'The old and new passwords must be different.');
        $this->assertNotEquals($UserOldPassword, $UserNewPassword, 'The old and new passwords must be different.');
    }
}
