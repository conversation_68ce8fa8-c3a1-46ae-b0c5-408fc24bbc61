<?php

namespace Tests\Unit\DefaultAndRecurringTest;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class DefaultAndRecurringTest extends TestCase
{
    use DatabaseTransactions;
    /**
     * A basic test example.
     *
     * @return void
     */
    public function testSettingRecurringPolicy()
    {
        $data = [
            "type"=>"eft",
            "recurring"=> "1",
            "policy_id"=> "9933261",
            "payid"=> "39856",
            "user_id"=>"101911",
            "aid"=>"11",
        ];
        $response = $this->post('api/v1/set-default-recurring-method',$data);
        $response->assertStatus(200);
        $response->assertJsonStructure(["data"=>["success"]]);
    }
    public function testSettingListElistSTMTPolicy()
    {
        $data = [
            "type"=>"list",
            "recurring"=> "1",
            "policy_id"=> "9933261",
            "payid"=> "39856",
            "user_id"=>"101911",
            "aid"=>"11",
        ];
        $response = $this->post('api/v1/set-default-recurring-method',$data);
        $response->assertStatus(200);
        $response->assertJsonStructure(["data"=>["success"]]);
    }
    public function testSettingEFTPolicy()
    {
        $data = [
            "type"=>"eft",
            "recurring"=> "0",
            "policy_id"=> "9933261",
            "payid"=> "39856",
            "user_id"=>"101911",
            "aid"=>"11",
        ];
        $response = $this->post('api/v1/set-default-recurring-method',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Update Policy Payment method changed to eft"]]);
    }
    public function testSettingCCPolicy()
    {
        $data = [
            "type"=>"cc",
            "recurring"=> "0",
            "policy_id"=> "9933261",
            "payid"=> "86",
            "user_id"=>"101911",
            "aid"=>"11",
        ];
        $response = $this->post('api/v1/set-default-recurring-method',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Update Policy Payment method changed to cc"]]);
    }
    public function testPolicyDefaultRecurringValidationErrorTest()
    {
        $data = [
            "type"=>"eft",
            "recurring"=> "0",
            "policy_id"=> "",
            "payid"=> "39856",
            "user_id"=>"101911",
            "aid"=>"11",
        ];
        $response = $this->post('api/v1/set-default-recurring-method',$data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors"=>["policy_id"]]);
    }
}
