<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AddPolicyEnrollmentNotesTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testAddPolicyEnrollmentNotes()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'policy_id' => '9900290',
            'anuser' => '1',
            'annote' => 'this is for testing purpose',
            'status' => '6',
        ];
        $response = $this->json('POST','/api/v1/add-policy-enrollment-notes',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Enrollment Notes Added.',
                ]
            ]);
    }
    public function testAddPolicyEnrollmentNotesDataValidation()
    {
        $data = 
        [
            'policy_id' => '',
            'anuser' => '',
            'annote' => '',
            'status' => '',
        ];
        $response = $this->json('POST','/api/v1/add-policy-enrollment-notes',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'policy_id'=> 
                    [
                        'Policy Id is required.'
                    ],
                    'anuser'=> 
                    [
                        'User Id is required.'
                    ],
                    'annote'=> 
                    [
                        'Note is required.'
                    ],
                    'status'=> 
                    [
                        'Status is required.'
                    ]
                ]
            ]);
    }
    public function testAddPolicyEnrollmentNotesDataTypeValidation()
    {
        $data = 
        [
            'policy_id' => 'abc',
            'anuser' => '1',
            'annote' => 'this is for testing purpose',
            'status' => '6',
        ];
        $response = $this->json('POST','/api/v1/add-policy-enrollment-notes',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'policy_id'=> 
                    [
                        'Policy Id must be numeric.'
                    ],
                ]
            ]);
    }
}
