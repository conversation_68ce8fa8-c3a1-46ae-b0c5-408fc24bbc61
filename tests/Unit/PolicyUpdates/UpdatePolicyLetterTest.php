<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Storage;
use Tests\TestCase;

class UpdatePolicyLetterTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testAddPolicyApps()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
        	'file_id' => '14',
            'title' => 'test edit testing ex',
            'details' => 'test',
            'policy_id' => '1',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/update-policy-letter',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Document Updated.',
                ]
            ]);
    }
    public function testUpdatePolicyLetterDataValidation()
    {
        $data = 
        [
        	'file_id' => '',
            'title' => '',
            'details' => '',
            'policy_id' => '',
            'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/update-policy-letter',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'file_id'=> 
                    [
                        'File Id is required.'
                    ],
                    'title'=> 
                    [
                        'Title is required.'
                    ],
                    'details'=> 
                    [
                        'Details is required.'
                    ],
                    'policy_id'=> 
                    [
                        'Policy Id is required.'
                    ],
                    'aid'=> 
                    [
                        'User Id is required.'
                    ],
                ]
            ]);
    }
    public function testUpdatePolicyLetterDataTypeValidation()
    {
        $data = 
        [
        	'file_id' => 'abc',
            'title' => 'test',
            'details' => 'test',
            'policy_id' => 'abc',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/update-policy-letter',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                	'file_id'=> 
                    [
                        'File Id must be numeric.'
                    ],
                    'policy_id'=> 
                    [
                        'Policy Id must be numeric.'
                    ],
                ]
            ]);
    }
}
