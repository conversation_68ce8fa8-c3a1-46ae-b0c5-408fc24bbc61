<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EditPolicyEnrollmentNotesTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
     public function testEditPolicyEnrollmentNotes()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'anID' => '2899',
            'annote' => 'this is for testing purpose- edited',
            'status' => '5',
        ];
        $response = $this->json('POST','/api/v1/update-policy-enrollment-notes',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Enrollment Notes Updated.',
                ]
            ]);
    }
    public function testEditPolicyEnrollmentNotesDataValidation()
    {
        $data = 
        [
            'anID' => '',
            'annote' => '',
            'status' => '',
        ];
        $response = $this->json('POST','/api/v1/update-policy-enrollment-notes',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'anID'=> 
                    [
                        'ID is required.'
                    ],
                    'annote'=> 
                    [
                        'Note is required.'
                    ],
                    'status'=> 
                    [
                        'Status is required.'
                    ]
                ]
            ]);
    }
    public function testEditPolicyEnrollmentNotesDataTypeValidation()
    {
        $data = 
        [
            'anID' => 'abc',
            'annote' => 'this is for testing purpose- edited',
            'status' => '5',
        ];
        $response = $this->json('POST','/api/v1/update-policy-enrollment-notes',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'anID'=> 
                    [
                        'ID must be numeric.'
                    ],
                ]
            ]);
    }
}
