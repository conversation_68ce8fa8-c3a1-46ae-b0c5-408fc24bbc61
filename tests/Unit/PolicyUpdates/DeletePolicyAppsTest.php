<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DeletePolicyAppsTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testDeletePolicyApps()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'file_id' => '170005',
            'policy_id' => '1',
            'aid'=>'web',
        ];
        $response = $this->json('POST','/api/v1/delete-policy-apps',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Document Deleted.',
                ]
            ]);
    }
    public function testDeletePolicyAppsFileIdNotExist()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'file_id' => '170005',
            'policy_id' => '1',
            'aid'=>'web',
        ];
        $response = $this->json('POST','/api/v1/delete-policy-apps',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'error',
                    'message' => 'Document Not Deleted.',
                ]
            ]);
    }
    public function testDeletePolicyAppsDataValidation()
    {
    	$this->withoutExceptionHandling();
        $data = 
        [
            'file_id' => '',
            'policy_id' => '1',
            'aid'=>'web',            
        ];
        $response = $this->json('POST','/api/v1/delete-policy-apps',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'file_id'=> 
                    [
                        'File ID is required'
                    ],
                ]
            ]);
    }
    public function testDeletePolicyAppsDataTypeValidation()
    {
    	$this->withoutExceptionHandling();
        $data = 
        [
            'file_id' => 'ABC',
            'policy_id' => '1',
            'aid'=>'web',            
        ];
        $response = $this->json('POST','/api/v1/delete-policy-apps',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'file_id'=> 
                    [
                        'File ID must be numeric'
                    ],
                ]
            ]);
    }
}
