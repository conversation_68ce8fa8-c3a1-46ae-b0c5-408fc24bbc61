<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RemovePolicyTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testRemovePolicy()
    {
        $data = [
                 'policy_id' =>[
                    '9938613'
                    ],
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/remove-policy',$data);

        $response
            ->assertStatus(200)
            ->assertJsonFragment([
                        'policy_id'=> '9938613',
                        'type' => 'success',
                        'message' => 'Policy Deleted.',
            ]);
    }
    public function testRemovePolicyNotExist()
    {
        $data =
        [
            'policy_id' =>
            [
                '9938613'
            ],
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/remove-policy',$data);

        $response
            ->assertStatus(200)
            ->assertJsonFragment([
                        'policy_id'=> '9938613',
                        'type' => 'error',
                        'message' => 'Either Policy does not exist or is not valid.',
            ]);
    }
    public function testRemovePolicyNullPolicyId()
    {
        $data = [
                 'policy_id' => '',
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/remove-policy',$data);

        $response
            ->assertStatus(422)
            ->assertSeeText(
                'Policy ID is required.'
            );
    }
    public function testRemovePolicyNullUserId()
    {
        $data = [
                 'policy_id' => '9931922',
                 'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/remove-policy',$data);

        $response
            ->assertStatus(422)
            ->assertSeeText(
                'User ID is required.'
            );
    }
}
