<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class GetEnrollmentNotesTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testViewPolicyEnrollmentNotes()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'id' => '9900290',
            'type' => 'c',
        ];
        $response = $this->json('POST','/api/v1/get-policy-enrollment-notes',$data);                
        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*'=>[
                        'anID',
                        'ants',
                        'anuser',
                        'annote'
                    ]
                ]
            ]);
    }
}
