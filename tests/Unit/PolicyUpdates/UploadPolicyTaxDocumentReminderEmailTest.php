<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;

class UploadPolicyTaxDocumentReminderEmailTest extends TestCase
{
	use DatabaseTransactions;
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testUploadPolicyTaxDocumentReminderEmail()
    {
    	$this->withoutExceptionHandling();
    	Mail::fake();
        $data = [
                 'policy_id' => '9938613',
                 'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/upload-policy-tax-document-reminder-email',$data);

        $response
            ->assertStatus(200)
            ->assertJsonFragment([
                'type' => 'success',
                'message' => 'Email sent successfully.',
            ]);
    }
    public function testUploadPolicyTaxDocumentReminderEmailDataValidation()
    {
        $this->withoutExceptionHandling();
        $data = [
                 'policy_id' => '',
                 'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/upload-policy-tax-document-reminder-email',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'policy_id'=> 
                    [
                        'Policy ID is required.'
                    ],
                    'aid'=> 
                    [
                        'User ID is required.'
                    ],
                ]
            ]);   
    }
}
