<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DeletePolicyEnrollmentNotesTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testDeletePolicyEnrollmentNotes()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'anID' => '2903',
        ];
        $response = $this->json('POST','/api/v1/remove-policy-enrollment-notes',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'error',
                    'message' => 'Enrollment Notes not Deleted.',
                ]
            ]);
    }
    public function testDeletePolicyEnrollmentNotesDataValidation()
    {
        $data = 
        [
            'anID' => '',
        ];
        $response = $this->json('POST','/api/v1/remove-policy-enrollment-notes',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'anID'=> 
                    [
                        'ID is required.'
                    ],
                ]
            ]);
    }
    public function testDeletePolicyEnrollmentNotesDataTypeValidation()
    {
        $data = 
        [
            'anID' => 'ABC',
        ];
        $response = $this->json('POST','/api/v1/remove-policy-enrollment-notes',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'anID'=> 
                    [
                        'ID must be numeric.'
                    ],
                ]
            ]);
    }
}
