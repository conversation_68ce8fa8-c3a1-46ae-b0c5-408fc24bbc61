<?php

namespace Tests\Unit\PolicyUpdates;

use Tests\TestCase;

class UpdateDependentTtest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function createDependentTest()
    {
        $data =  [
            "d_fname" => "Sujan",
            "d_mname" => "P",
            "d_lname" => "Poudel",
            "d_relate" => "S",
            "d_gender" => "0",
            "d_ssn" => "123456789",
            "d_dob" => "09/13/2000",
            "policy_id" => "9955496",
            "d_notes" => "test",
            "dhrq" => "5",
            "dhrq2" => "5",
            "dwrq" => "100",
            "additional_notes" => "hi doctor",
            "healthQuestionnaires" => [
                [
                    "med_id" => 382808,
                    "question_id" => 63,
                    "question" => "A. Heart attack, brain tumor, stroke, heart disease or heart problems?",
                    "status" => 1,
                    "selectedAnswer" => 0,
                    "medMedications" => [],
                    "medConditions" => [
                        "health_conditionss" => "",
                        "date_of_onset" => "",
                        "date_of_recovery" => "",
                        "is_treatment" => "",
                        "is_medicate" => "",
                        "d_last_seen" => "",
                        "symptoms" => ""
                    ],
                    "type" => "condition",
                    "additionalNotes" => "Health question notes 11622"
                ],
                [
                    "med_id" => 382809,
                    "question_id" => 64,
                    "question" => "B. Cancer, tumor, lymphoma, or any type of transplant?",
                    "status" => 1,
                    "selectedAnswer" => 0,
                    "medMedications" => [],
                    "medConditions" => [
                        "health_condition" => "",
                        "date_of_onset" => "",
                        "date_of_recovery" => "",
                        "is_treatment" => "",
                        "is_medicate" => "",
                        "d_last_seen" => "",
                        "symptoms" => ""
                    ],
                    "type" => "condition",
                    "additionalNotes" => "Health question notes 11622"
                ],
                [
                    "med_id" => 382810,
                    "question_id" => 65,
                    "question" => "C. Any surgery or hospitalization in the last 5 years, OR any currently pending, planned or
          recommended?",
                    "status" => 1,
                    "selectedAnswer" => 0,
                    "medMedications" => [],
                    "medConditions" => [
                        "health_condition" => "",
                        "date_of_onset" => "",
                        "date_of_recovery" => "",
                        "is_treatment" => "",
                        "is_medicate" => "",
                        "d_last_seen" => "",
                        "symptoms" => ""
                    ],
                    "type" => "condition",
                    "additionalNotes" => "Health question notes 11622"
                ],
                [
                    "med_id" => 382811,
                    "question_id" => 66,
                    "question" => "D. Emphysema or COPD?",
                    "status" => 1,
                    "selectedAnswer" => 0,
                    "medMedications" => [],
                    "medConditions" => [
                        "health_condition" => "",
                        "date_of_onset" => "",
                        "date_of_recovery" => "",
                        "is_treatment" => "",
                        "is_medicate" => "",
                        "d_last_seen" => "",
                        "symptoms" => ""
                    ],
                    "type" => "condition",
                    "additionalNotes" => "Health question notes 11622"
                ],
                [
                    "med_id" => 382812,
                    "question_id" => 67,
                    "question" => "E. Kidney failure, dialysis, or disorder of the liver, stomach, pancreas, colon or bladder?",
                    "status" => 1,
                    "selectedAnswer" => 0,
                    "medMedications" => [],
                    "medConditions" => [
                        "health_condition" => "",
                        "date_of_onset" => "",
                        "date_of_recovery" => "",
                        "is_treatment" => "",
                        "is_medicate" => "",
                        "d_last_seen" => "",
                        "symptoms" => ""
                    ],
                    "type" => "condition",
                    "additionalNotes" => "Health question notes 11622"
                ],
                [
                    "med_id" => 382813,
                    "question_id" => 68,
                    "question" => "F. Seizures, epilepsy, hemophilia, Sleep Apnea or blood disorder?",
                    "status" => 1,
                    "selectedAnswer" => 0,
                    "medMedications" => [],
                    "medConditions" => [
                        "health_condition" => "",
                        "date_of_onset" => "",
                        "date_of_recovery" => "",
                        "is_treatment" => "",
                        "is_medicate" => "",
                        "d_last_seen" => "",
                        "symptoms" => ""
                    ],
                    "type" => "condition",
                    "additionalNotes" => "Health question notes 11622"
                ],
                [
                    "med_id" => 382814,
                    "question_id" => 69,
                    "question" => "G. Diabetes, endocrine,Auto Immune, Chron's Disease or Arthritis, or pituitary disorder, growth disorder, lupus, MS, AIDS, or HIV+?",
                    "status" => 1,
                    "selectedAnswer" => 0,
                    "medMedications" => [],
                    "medConditions" => [
                        "health_condition" => "",
                        "date_of_onset" => "",
                        "date_of_recovery" => "",
                        "is_treatment" => "",
                        "is_medicate" => "",
                        "d_last_seen" => "",
                        "symptoms" => ""
                    ],
                    "type" => "condition",
                    "additionalNotes" => "Health question notes 11622"
                ],
                [
                    "med_id" => 382815,
                    "question_id" => 70,
                    "question" => "H. Currently pregnant, premature delivery, or multiple births?",
                    "status" => 1,
                    "selectedAnswer" => 1,
                    "medMedications" => [],
                    "medConditions" => [
                        "health_condition" => "test",
                        "date_of_onset" => "09/06/2022",
                        "date_of_recovery" => "09/10/2022",
                        "is_treatment" => true,
                        "is_medicate" => true,
                        "d_last_seen" => "09/04/2022",
                        "symptoms" => "fever, headache"
                    ],
                    "type" => "condition",
                    "additionalNotes" => "Health question notes 11622"
                ],
                [
                    "med_id" => 382816,
                    "question_id" => 113,
                    "question" => "<B>I. Are you taking or have you taken any medications in the last 12 months?  If yes, you must list them below.</B>",
                    "status" => 1,
                    "selectedAnswerMed" => 1,
                    "medMedications" => [
                        [
                            "dosage" => "1",
                            "medical_condition" => "all good",
                            "medication" => "covid-19-1"
                        ],
                        [
                            "dosage" => "1",
                            "medical_condition" => "all good",
                            "medication" => "covid-19-2"
                        ]
                    ],
                    "medConditions" => null,
                    "type" => "medication",
                    "additionalNotes" => "Health question notes 11622"
                ]
            ],
            "login_user_id" => 16
        ];
        $response = $this->json('POST', '/api/v1/policy/create-dependent', $data);

        $response
            ->assertStatus(200)
            ->assertJsonFragment([
                'success' => 'true',
                'code' => 200,
                'message' => 'Dependent Created.',
                'data'=> [],
            ]);
    }
}
