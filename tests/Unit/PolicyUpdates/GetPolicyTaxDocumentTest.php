<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Storage;
use Tests\TestCase;

class GetPolicyTaxDocumentTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testGetPolicyTaxDocument()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'policy_id' => '1',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/get-policy-tax-document',$data);                
        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*'=>[
                        'file_id',
                        'policy_id'
                    ]
                ]
            ]);
    }
}
