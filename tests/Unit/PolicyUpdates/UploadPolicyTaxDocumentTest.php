<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class UploadPolicyTaxDocumentTest extends TestCase
{
	use DatabaseTransactions;
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testUploadTaxDocumentSuccess()
    {
    	$this->withoutExceptionHandling();
        Storage::fake('corenroll');
        $uploads = [
            UploadedFile::fake()->image('test1-glglgl.jpg'),
            UploadedFile::fake()->create('test2-glglglg.pdf',1025),
            UploadedFile::fake()->create('test3-glglglglg.docx')
        ];
        $data = [
            'policy_id' => '1',
            'file'=>$uploads,
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/upload-policy-tax-document',$data);


        //Storage::disk('corenroll')->assertExists($uploads[0]->hashName());
        //Storage::disk('corenroll')->assertExists('test2-glglglg.pdf');
        //Storage::disk('corenroll')->assertExists('test3-glglglglg.docx');        
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Document Uploaded.',
                ]
            ]);
    }
    public function testUploadTaxDocumentDataValidation()
    {
        $data = 
        [
            'policy_id' => '',
            'file'=>[
            '',
            ],
            'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/upload-policy-tax-document',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'policy_id'=> 
                    [
                        'Policy Id is required.'
                    ],
                    'file.0'=> 
                    [
                        'The file.0 field is required.'
                    ],
                    'aid'=> 
                    [
                        'User Id is required.'
                    ],
                ]
            ]);
    }
    public function testUploadTaxDocumentDataTypeValidation()
    {
        $data = 
        [
            'policy_id' => 'abc',
            'file' => [
            	'abcdde'
            ],
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/upload-policy-tax-document',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'policy_id'=> 
                    [
                        'Policy Id must be numeric.'
                    ],
                ]
            ]);
    }
}
