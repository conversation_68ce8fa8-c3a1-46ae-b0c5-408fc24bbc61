<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ViewPolicyAppsTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testViewPolicyApps()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'policy_id' => '9935102',
        ];
        $response = $this->json('POST','/api/v1/view-policy-apps',$data);
        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'dataPolicyDocument' => [
                         '*'=>[
                            'doc_id',
                            'license_id',
                            'file_name',
                            'app_desc'
                        ]
                    ],
                    'dataPolicy'=>[
                        '*' => [

                        ]
                    ]    
                ]
            ]);
    }
}
