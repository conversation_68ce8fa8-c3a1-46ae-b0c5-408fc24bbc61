<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Storage;
use Tests\TestCase;

class UploadPolicyLetterTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function testUploadPolicyLetterDataValidation()
    {
        $data = 
        [
            'title' => '',
            'details' => '',
            'policy_id' => '',
            'file_upload'=>'',
            'aid' => '',
        ];
        $response = $this->json('POST','/api/v1/upload-policy-letter',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'title'=> 
                    [
                        'Title is required.'
                    ],
                    'details'=> 
                    [
                        'Details is required.'
                    ],
                    'policy_id'=> 
                    [
                        'Policy Id is required.'
                    ],
                    'file_upload'=> 
                    [
                        'File is required'
                    ],
                    'aid'=> 
                    [
                        'User Id is required.'
                    ],
                ]
            ]);
    }
    public function testUploadPolicyLetterDataTypeValidation()
    {
        $data = 
        [
            'title' => 'test',
            'details' => 'test',
            'policy_id' => 'abc',
            'file_upload' => 'abcdde',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/upload-policy-letter',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'policy_id'=> 
                    [
                        'Policy Id must be numeric.'
                    ],
                    'file_upload'=> 
                    [
                        'File must be .xlsx or .xls or .pdf'
                    ],
                ]
            ]);
    }
}
