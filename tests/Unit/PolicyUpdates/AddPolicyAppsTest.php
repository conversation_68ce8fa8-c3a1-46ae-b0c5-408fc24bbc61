<?php

namespace Tests\Unit\PolicyUpdates;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Storage;
use Tests\TestCase;

class AddPolicyAppsTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    /*public function testAddPolicyApps()
    {
        $this->withoutExceptionHandling();
        $data = 
        [
            'agent_id' => '0',
            'app_name' => 'test',
            'app_desc' => 'test-null',
            'pid' => '1',
            'file_upload'=>UploadedFile::fake()->image('DocForUnitTesting.jpg'),
        ];
        $response = $this->json('POST','/api/v1/add-policy-apps',$data);                
        $response
            ->assertStatus(200)
            ->assertExactJson([
                'data'=>
                [
                    'type' => 'success',
                    'message' => 'Document Uploaded.',
                ]
            ]);
    }*/
    public function testAddPolicyAppsDataValidation()
    {
        $data = 
        [
            'agent_id' => '0',
            'app_name' => 'test',
            'app_desc' => 'test-null',
            'policy_id' => '1',
            'file_upload'=>'',
            'aid' => 'web',
        ];
        $response = $this->json('POST','/api/v1/add-policy-apps',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'file_upload'=> 
                    [
                        'File is required'
                    ]
                ]
            ]);
    }
    public function testAddPolicyAppsDataTypeValidation()
    {
        $data = 
        [
            'agent_id' => '0',
            'app_name' => 'test',
            'app_desc' => 'test-null',
            'policy_id' => '1',
            'file_upload'=>'this is',
            'aid' =>'web'
        ];
        $response = $this->json('POST','/api/v1/add-policy-apps',$data);

        $response
            ->assertStatus(422)
            ->assertExactJson([
                'errors'=>
                [
                    'file_upload'=> 
                    [
                        'File must be .jpeg or .jpg or .pdf'
                    ]
                ]
            ]);
    }
}
