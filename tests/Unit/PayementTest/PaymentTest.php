<?php

namespace Tests\Unit\PaymentTest;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class PaymentTest extends TestCase
{
    use DatabaseTransactions;
    /**
     * A basic test example.
     *
     * @return void
     */

    public function testBankDetailsTest()
    {
        $response = $this->get('api/v1/get-bank-details/36');
        $response->assertStatus(200);
    }
    public function testNonExistBankAccount()
    {
        $response = $this->get('api/v1/get-bank-details/**************');
        $response->assertStatus(200);
        $response->assertExactJson(array("data"=>["error"=>"Bank Account Not Found"]));
    }
    public function testAddBankAccount()
    {
        $this->withoutExceptionHandling();
        $data = [
            "bank_accountname"=>"bankAccountName Test",
            "bank_name"=> "BankName",
            "bank_routing"=> "*********",
            "bank_account"=> "**********",
            "bank_id"=>"",
        ];
        $response = $this->post('api/v1/bank-info-add-update',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Bank Account Added Successfully."]]);
        $response->assertJsonStructure(["data"=>["success"]]);
    }
    public function testUpdateBankAccount()
    {
        $data = [
            "bank_accountname"=>"bankAccountName",
            "bank_name"=> "BankName",
            "bank_routing"=> "*********",
            "bank_account"=> "**********",
            "bank_id"=>"36",
        ];
        $response = $this->post('api/v1/bank-info-add-update',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["success"=>"Bank Details Updated Successfully."]]);
        $response->assertJsonStructure(["data"=>["success"]]);
    }
    public function testBankDataAddUpdateValidationFailCheckAccount()
    {
        $data = [
            "bank_accountname"=>"bankAccountName",
            "bank_name"=> "",
            "bank_routing"=> "1187767",
            "bank_account"=> "**********",
            "bank_id"=>"36",
        ];
        $response = $this->post('api/v1/bank-info-add-update',$data);
        $response->assertStatus(422);
        $response->assertExactJson(["errors"=>["bank_name"=>["Bank Name is required."],"bank_routing"=>["The bank routing must be 9 digits."]]]);
        $response->assertJsonStructure(["errors"=>["bank_name","bank_routing"]]);
    }
    public function testPaymentBankUsersDetailsByPolicy()
    {
        $this->withoutExceptionHandling();
        $data = [
            "pid"=>"9933261",
        ];
        $response = $this->post('api/v1/get-payment-page-details',$data);
        $response->assertStatus(200);
        $response->assertJsonStructure(["data"=>["user","payCC","paymentEFT"]]);
    }
    public function testPaymentBankUsersDetailsByPolicyNonExistDataValidationTest()
    {
        $this->withoutExceptionHandling();
        $data = [
            "pid"=>"**********",
        ];
        $response = $this->post('api/v1/get-payment-page-details',$data);
        $response->assertStatus(200);
        $response->assertExactJson(["data"=>["error"=>"No Any User Found"]]);
        $response->assertJsonStructure(["data"=>["error"]]);
    }
    public function invalidPolicyIdTestOnPaymentDetails()
    {
        $this->withoutExceptionHandling();
        $data = [
            "pid"=>"jhuhuhuh",
        ];
        $response = $this->post('api/v1/get-payment-page-details',$data);
        $response->assertStatus(422);
        $response->assertJsonStructure(["errors"=>["pid"=>["Policy ID should be number."]]]);
    }
}
