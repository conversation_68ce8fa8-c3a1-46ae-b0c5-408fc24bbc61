<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAcronymAbbreviationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('acronym_abbreviation', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('code', 16)->comment('short-form | abbreviation');
            $table->string('fullform', 64)->nullable()
                ->comment('full-form | long-form');
            $table->string('type', 64)->nullable()
                ->comment('type of abbreviation like plan_category or plan_type');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('acronym_abbreviation');
    }
}
