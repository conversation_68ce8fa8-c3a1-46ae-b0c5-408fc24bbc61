<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsEmailSentColumnInPlanPoliciesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('plan_policies', function (Blueprint $table) {
            $table->enum('is_email_sent', ['0','Y', 'N'])->default('0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('plan_policies', function (Blueprint $table) {
            $table->dropColumn('is_email_sent');
        });
    }
}
