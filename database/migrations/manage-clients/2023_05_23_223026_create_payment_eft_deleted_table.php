<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentEftDeletedTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_eft_deleted', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('bank_name');
            $table->string('bank_accountname');
            $table->unsignedInteger('bank_routing');
            $table->string('bank_account');
            $table->unsignedSmallInteger('bank_account4');
            $table->string('bank_date')->comment('unix date');
            $table->unsignedInteger('bank_userid');
            $table->char('bank_status', 1)->nullable()
                ->comment('A: Active->Default, N: Not Active');
            $table->string('bankbranchloc')->nullable();
            $table->string('is_approved')->nullable();
            $table->string('is_action_taken')->nullable();
            $table->string('reason_to_reject')->nullable();
            $table->string('request_type')->nullable();
            $table->string('reason_to_accept')->nullable();
            $table->date('accepted_date')->nullable();
            $table->date('rejected_date')->nullable();
            $table->timestamp('created_date');
            $table->timestamp('delete_request_date')->nullable();
            $table->string('account_type')->comment('saving | checking');
            $table->string('account_holder_type')->comment('individual | company');
            $table->timestamps();
            $table->softDeletes()->nullable();
            $table->string('sign')->nullable();
            $table->boolean('is_primary')->default(0);
            $table->string('bank_sign')->nullable()
                ->comment('bank sign image filename');
            $table->string('funding_source_id')->nullable();
            $table->string('customer_id')->nullable();
            $table->enum('is_verified', ['Y', 'P', 'N'])->default('N')
                ->nullable()->comment('Y: Yes, P: Pending, N: No');
            $table->string('unique_identification_number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_eft_deleted');
    }
}
