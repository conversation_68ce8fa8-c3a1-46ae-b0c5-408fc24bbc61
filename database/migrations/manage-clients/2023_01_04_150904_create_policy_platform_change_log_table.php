<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePolicyPlatformChangeLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('policy_platform_change_log', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('policy_id');
            $table->string('weburl_old')->nullable();
            $table->string('weburl_new')->nullable();
            $table->mediumText('comment')->nullable();
            $table->integer('update_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('policy_platform_change_log');
    }
}
