<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentCcMerchantTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_cc_merchant', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('cc_id')
                ->comment('references `cc_id` column in `payment_cc` table');
            $table->foreign('cc_id')->references('cc_id')->on('payment_cc');
            $table->enum('merchant', ['nuera', 'corenroll'])
                ->comment('store the merchant that verifies the card record');
            $table->string('payer_id', 64)
                ->comment('payer id received from merchant')->nullable();
            $table->string('card_id', 64)
                ->comment('card id received from merchant')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_cc_merchant');
    }
}
