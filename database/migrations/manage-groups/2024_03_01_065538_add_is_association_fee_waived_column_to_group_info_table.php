<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsAssociationFeeWaivedColumnToGroupInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('group_info', function (Blueprint $table) {
            $table->boolean('is_association_fee_waived')->default(false)
                ->after('waive_status')->comment('association fee of members is waived');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('group_info', function (Blueprint $table) {
            $table->dropColumn(['is_association_fee_waived']);
        });
    }
}
