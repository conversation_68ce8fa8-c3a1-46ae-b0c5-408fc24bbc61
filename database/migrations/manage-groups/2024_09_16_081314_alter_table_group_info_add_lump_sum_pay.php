<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableGroupInfoAddLumpSumPay extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('group_info', function (Blueprint $table) {
            $table->boolean('lump_sum_pay_fl')->nullable()->default(null);
            $table->string('bill_date', 20)->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('group_info', function (Blueprint $table) {
            $table->dropColumn('lump_sum_pay_fl');
            $table->dropColumn('bill_date');
        });
    }
}
