<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateKnowledgeFeedsExternalContractsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql2')->create('knowledge_feeds_external_contracts', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('knowledge_feed_external_id')->unsigned();
            $table->string('publication_type')->comment('all_reps, level_based, contract_based');
            $table->string('published_agent_level')->nullable();
            $table->string('published_contract_type')->nullable();
            $table->string('published_contract_level')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('knowledge_feeds_external_contracts');
    }
}
