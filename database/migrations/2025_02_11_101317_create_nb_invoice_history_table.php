<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNbInvoiceHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('nb_invoice_history', function (Blueprint $table) {
            $table->bigIncrements('nb_invoice_history_id'); 
            $table->integer('invoice_id');
            $table->string('invoice_type', 25)->nullable();
            $table->string('payment_type', 10)->nullable();
            $table->integer('invoice_policy_id')->nullable();
            $table->string('payment_method', 10)->nullable();
            $table->date('invoice_date')->nullable();
            $table->date('invoice_due_date')->nullable();
            $table->date('invoice_start_date')->nullable();
            $table->date('invoice_end_date')->nullable();
            $table->string('invoice_efee', 20)->nullable();
            $table->string('invoice_status', 10)->nullable();
            $table->string('invoice_payment_status', 45)->nullable();
            $table->string('invoice_total', 10)->nullable();
            $table->string('invoice_late_fee', 10)->nullable();
            $table->string('invoice_bounce_fee', 10)->nullable();
            $table->string('invoice_stmt_fee', 10)->nullable();
            $table->integer('invoice_user_id')->nullable();
            $table->string('invoice_prev_due', 45)->nullable();
            $table->string('invoice_due_amount', 45)->nullable();
            $table->string('invoice_due_amount_paid', 45)->nullable();
            $table->integer('invoice_agent_id')->nullable();
            $table->integer('invoice_group_id')->nullable();
            $table->integer('ena_bill2')->nullable();
            $table->integer('invoice_created_user_id')->nullable();
            $table->dateTime('invoice_create_dttm')->nullable();
            $table->string('security_token', 100)->nullable();
            $table->boolean('dispute_fl')->nullable();
            $table->string('dispute_notes', 255)->nullable();
            $table->boolean('prev_dispute_fl')->nullable();
            $table->boolean('failed_fl')->nullable();
            $table->string('failed_at', 45)->nullable();
            $table->string('payment_party', 45)->nullable();
            $table->string('payment_party_status', 45)->nullable();
            $table->date('payment_party_processing_date')->nullable();
            $table->integer('payment_processing_count')->nullable();
            $table->string('processing_amount', 45)->nullable();
            $table->string('payment_id', 45)->nullable();
            $table->date('processing_date')->nullable();
            $table->timestamp('clearing_date')->nullable();
            $table->string('bank_id', 45)->nullable();
            $table->string('payer_id', 45)->nullable();
            $table->string('card_id', 45)->nullable();
            $table->text('comments')->nullable();
            $table->integer('comments_by')->nullable();
            $table->string('bank_payment_type', 45)->nullable();
            $table->integer('bank_payment_id')->nullable();
            $table->boolean('onetime_fl')->nullable();
            $table->boolean('onetime_type')->nullable();
            $table->timestamps(); // includes created_at and updated_at
            $table->boolean('is_paystand_processed')->nullable();
            $table->string('group_payment_notes', 255)->nullable();
            $table->boolean('group_payment_status')->nullable();
            $table->text('invoice_notes')->nullable();
            $table->boolean('is_recurring_self_payment')->nullable();
            $table->boolean('bulk_payment_notification_email_send')->nullable();
            $table->boolean('stmt_payment_notification_email_send')->nullable();
            $table->string('ccfees', 20)->nullable();
            $table->bigInteger('email_log_id')->nullable();
            $table->enum('merchant', ['corenroll', 'nuera'])->nullable();
            $table->enum('pay_type', ['annual', 'monthly']);
            $table->string('updated_by', 191)->nullable();
            $table->boolean('lump_sum_pay_fl')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('nb_invoice_history');
    }
}
