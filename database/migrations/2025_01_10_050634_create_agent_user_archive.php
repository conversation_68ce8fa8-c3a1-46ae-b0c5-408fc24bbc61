<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgentUserArchive extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agent_user_archive', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('agent_id');
            $table->string('username');
            $table->string('password')->nullable();
            $table->integer('is_root')->default(0);
            $table->string('last_attempt')->nullable();
            $table->string('agent_code')->nullable();
            $table->string('a1')->nullable();
            $table->string('a2')->nullable();
            $table->string('status')->nullable();
            $table->integer('q2')->nullable();
            $table->integer('q1')->nullable();
            $table->string('cookie_token')->nullable();
            $table->string('verification_code')->nullable();
            $table->string('phone')->nullable();
            $table->string('password_verification_code')->nullable();
            $table->integer('is_accepted')->nullable();
            $table->integer('is_decided')->nullable();
            $table->string('registeration_verification_code')->nullable();
            $table->integer('is_temp_password')->nullable();
            $table->integer('disphone')->nullable();
            $table->integer('disemail')->nullable();
            $table->text('tagline')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->enum('is_email_valid', ['YES', 'NO'])->nullable();
        });
    }

    public function down()
    {
        Schema::dropIfExists('agent_user_archive');
    }

}
