<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlanPricingZipsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('plan_pricing_zips', function (Blueprint $table) {
            $table->bigIncrements('plan_pricing_id');
            $table->integer('plan_id');
            $table->integer('age1')->nullable();
            $table->integer('age2')->nullable();
            $table->string('price_male_nons')->nullable();
            $table->string('price_male_s')->nullable();
            $table->string('price_female_nons')->nullable();
            $table->string('price_female_s')->nullable();
            $table->boolean('pricing_status')->default(true);
            $table->string('pricing_date');
            $table->string('wholesale_price_male_nons')->nullable();
            $table->string('wholesale_price_male_s')->nullable();
            $table->string('wholesale_price_female_nons')->nullable();
            $table->string('wholesale_price_female_s')->nullable();
            $table->string('commissionable_price_male_nons')->nullable();
            $table->string('commissionable_price_male_s')->nullable();
            $table->string('commissionable_price_female_nons')->nullable();
            $table->string('commissionable_price_female_s')->nullable();
            $table->string('net_price_male_nons')->nullable();
            $table->string('net_price_male_s')->nullable();
            $table->string('net_price_female_nons')->nullable();
            $table->string('net_price_female_s')->nullable();
            $table->string('nuera_fee')->nullable();
            $table->string('fee1')->nullable();
            $table->string('fee2')->nullable();
            $table->string('fee3')->nullable();
            $table->string('ena_fee')->nullable();
            $table->string('nce_fee')->nullable();
            $table->string('iba_fee')->nullable();
            $table->string('multiplan_fee')->nullable();
            $table->string('agent_comm')->nullable();
            $table->string('union_dues')->nullable();
            $table->string('peo_fees')->nullable();
            $table->string('pricing_start_Date')->nullable();
            $table->text('zip');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plan_pricing_zips');
    }
}
