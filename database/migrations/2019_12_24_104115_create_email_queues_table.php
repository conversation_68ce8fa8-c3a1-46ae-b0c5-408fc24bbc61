<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailQueuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_queues', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('to_email', 60);
            $table->string('to_name', 60);
            $table->string('from_email', 60);
            $table->string('from_name', 60);
            $table->string('subject', 500);
            $table->string('cc', 255);
            $table->string('bcc', 255);
            $table->longText('body');
            $table->string('attach_file_path', 255);
            $table->boolean('sent_status');
            $table->date('send_date')->nullable;
            $table->time('send_time')->nullable;
            $table->string('weburl',250);
            $table->timestamps();
            $table->softDeletes();
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_queues');
    }
}
