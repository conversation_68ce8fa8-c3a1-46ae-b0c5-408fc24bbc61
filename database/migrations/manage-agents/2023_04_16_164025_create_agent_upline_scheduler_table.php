<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgentUplineSchedulerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agent_upline_scheduler', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('agent_id');
            $table->foreign('agent_id')
                ->references('agent_id')->on('agent_info');
            $table->integer('agent_ga');
            $table->foreign('agent_ga')
                ->references('agent_id')->on('agent_info');
            $table->date('effective_date_start')
                ->comment('date agent_id becomes agent_ga\'s downline');
            $table->string('note', 128)->nullable()
                ->comment('note/reason of failure');
            $table->softDeletes()->comment('column populated after upline change is implemented');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('agent_upline_scheduler');
    }
}
