<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgentUplineHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agent_upline_history', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('agent_id');
            $table->foreign('agent_id')
                ->references('agent_id')->on('agent_info');
            $table->integer('agent_ga');
            $table->foreign('agent_ga')
                ->references('agent_id')->on('agent_info');
            $table->date('effective_date_end')
                ->comment('date the agent_id ended being agent_ga\'s downline');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('agent_upline_history');
    }
}
