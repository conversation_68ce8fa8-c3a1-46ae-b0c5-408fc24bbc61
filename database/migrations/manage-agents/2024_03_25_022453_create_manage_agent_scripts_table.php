<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateManageAgentScriptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql2')->create('manage_agent_scripts', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('agent_id');
            $table->text('agent_downlines')->nullable();
            $table->enum('type',['include','exclude']);
            $table->date('pt_date');
            $table->boolean('already_processed')->default(0);
            $table->string('updated_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('manage_agent_scripts');
    }
}
