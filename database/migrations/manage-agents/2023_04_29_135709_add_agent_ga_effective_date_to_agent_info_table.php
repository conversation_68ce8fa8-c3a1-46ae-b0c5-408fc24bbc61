<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAgentGaEffectiveDateToAgentInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('agent_info', function (Blueprint $table) {
            $table->addColumn('date','agent_ga_effective_date')
                ->after('agent_ga')
                ->comment('effective date when the upline(agent_ga) has been set')
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('agent_info', function (Blueprint $table) {
            $table->dropColumn('agent_ga_effective_date');
        });
    }
}
