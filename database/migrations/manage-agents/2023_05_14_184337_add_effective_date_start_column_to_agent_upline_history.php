<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEffectiveDateStartColumnToAgentUplineHistory extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('agent_upline_history', function (Blueprint $table) {
            $table->addColumn('date','effective_date_start')
                ->after('agent_ga')
                ->comment('effective date when the upline(agent_ga) was set')
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('agent_upline_history', function (Blueprint $table) {
            $table->dropColumn('effective_date_start');
        });
    }
}
