<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGroupUserArchive extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */

    public function up()
    {
        Schema::create('group_user_archive', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id');
            $table->integer('gid');
            $table->string('groupuser_lname')->nullable();
            $table->string('groupuser_fname')->nullable();
            $table->string('groupuser_email')->nullable();
            $table->string('groupuser_status')->nullable();
            $table->string('groupuser_signupdate')->nullable();
            $table->string('groupuser_username')->nullable();
            $table->string('groupuser_password')->nullable();
            $table->string('groupuser_notes')->nullable();
            $table->string('verification_code')->nullable();
            $table->string('cookie_token')->nullable();
            $table->string('last_attempt')->nullable();
            $table->integer('q1')->default(0);
            $table->integer('q2')->default(0);
            $table->string('a1')->nullable();
            $table->string('a2')->nullable();
            $table->string('admin_type')->nullable();
            $table->string('phone')->nullable();
            $table->string('ipAddress')->nullable();
            $table->string('last_login_datetime')->nullable();
            $table->string('password_verification_code')->nullable();
            $table->string('registeration_verification_code')->nullable();
            $table->integer('is_decided')->default(1)->nullable();
            $table->integer('is_root')->default(0)->nullable();
            $table->integer('is_temp_password')->nullable();
            $table->string('posted_date')->nullable();
            $table->integer('disphone')->nullable();
            $table->integer('disemail')->nullable();
            $table->text('tagline')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->enum('is_email_valid', ['YES', 'NO'])->nullable();
        });
    }

    public function down()
    {
        Schema::dropIfExists('group_user_archive');
    }

}
