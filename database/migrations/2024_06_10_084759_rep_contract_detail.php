<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RepContractDetail extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('rep_contract_details', function (Blueprint $table) {
            $table->bigIncrements('detail_id');
            $table->bigInteger('contract_id')->unsigned();
            $table->string('contract_item_display_name')->nullable();
            $table->string('contract_item_name');
            $table->string('contract_level');
            $table->timestamps();

            $table->foreign('contract_id')->references('contract_id')->on('rep_contract');
        });
    }

    public function down()
    {
        Schema::dropIfExists('rep_contract_details');
    }
}