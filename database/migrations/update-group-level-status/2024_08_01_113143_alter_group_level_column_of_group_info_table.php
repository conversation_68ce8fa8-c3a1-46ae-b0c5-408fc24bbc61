<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterGroupLevelColumnOfGroupInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('group_info', function (Blueprint $table) {
            // Drop the existing column
            $table->dropColumn('group_level');
        });

        Schema::table('group_info', function (Blueprint $table) {
            // Add the new column with nullable attribute
            $table->string('group_level')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('group_info', function (Blueprint $table) {
            // Drop the new column
            $table->dropColumn('group_level');
        });

        Schema::table('group_info', function (Blueprint $table) {
            // Add the old column back with default attribute
            $table->integer('group_level')->nullable();
        });
    }
}
