<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterGroupGuaranteedColumnOfGroupInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('group_info', function (Blueprint $table) {
            // Drop the existing column
            $table->dropColumn('group_guaranteed');
        });

        Schema::table('group_info', function (Blueprint $table) {
            // Add the new column with nullable attribute
            $table->boolean('group_guaranteed')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('group_info', function (Blueprint $table) {
            // Drop the new column
            $table->dropColumn('group_guaranteed');
        });

        Schema::table('group_info', function (Blueprint $table) {
            // Add the old column back with default attribute
            $table->boolean('group_guaranteed')->default(false);
        });
    }
}
