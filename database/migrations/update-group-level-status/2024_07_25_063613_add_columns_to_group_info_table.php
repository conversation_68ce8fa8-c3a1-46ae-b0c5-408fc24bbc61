<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToGroupInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('group_info', function (Blueprint $table) {
            $table->integer('group_level')->nullable();
            $table->boolean('group_guaranteed')->default(false); //->nullable()
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('group_info', function (Blueprint $table) {
            $table->dropColumn('group_level');
            $table->dropColumn('group_guaranteed');
        });
    }
}
