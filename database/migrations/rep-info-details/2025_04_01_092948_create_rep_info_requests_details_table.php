<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRepInfoRequestsDetailsTable extends Migration
{
    public function up()
    {
        Schema::create('rep_info_requests_details', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('info_request_id');
            $table->string('card_id');
            $table->string('payer_id');
            $table->decimal('amount', 10, 2);
            $table->string('payment_id')->nullable();
            $table->string('status')->nullable();
            $table->string('payment_description')->nullable();
            $table->timestamp('clearing_date')->nullable();
            $table->timestamp('created_date')->nullable();
            $table->string('invoice_id')->nullable();

            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('rep_info_requests_details');
    }
}
