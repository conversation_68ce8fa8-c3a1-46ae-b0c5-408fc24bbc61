<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSsoLoginArchiveTable extends Migration
{

    public function up()
    {
        Schema::connection('sso')->create('sso_login_archive', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->enum('user_type', ['A','M','G'])->default('A')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('verification_code')->nullable();
            $table->string('payment_verification_code')->nullable();
            $table->string('remember_token')->nullable();
            $table->string('deviceid')->nullable();
            $table->string('macid')->nullable();
            $table->tinyInteger('device_verified')->default(0)->nullable();
            $table->timestamp('device_verified_at')->nullable();
            $table->string('device_token')->nullable();
            $table->string('access_token')->nullable();
            $table->string('refresh_token')->nullable();
            $table->timestamp('verification_code_expiry_date')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
        });
    }

    public function down()
    {
        Schema::dropIfExists('sso_login_archive');
    }
}
