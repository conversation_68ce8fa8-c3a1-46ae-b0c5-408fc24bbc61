<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddContractType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('rep_contract_act', function (Blueprint $table) {
            $table->string('contract_type')
            ->nullable()
            ->after('contracts');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('rep_contract_act', function (Blueprint $table) {
            $table->dropColumn('contract_type');
        });
    }
}
