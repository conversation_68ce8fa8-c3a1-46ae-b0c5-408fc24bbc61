<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdatePolicyUpdate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('policy_updates', function (Blueprint $table) {
            $table->string('origin')->nullable()->comment('Origin or refer of the requested api host');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('policy_updates', function (Blueprint $table) {
            $table->dropColumn('origin');
        });
    }
}
