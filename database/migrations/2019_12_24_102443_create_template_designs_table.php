<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTemplateDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('template_design', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->text('categoryname');
            $table->text('templatedesign')->nullable();
            $table->integer('status')->unsigned();
            $table->string('subject', 250);
            $table->timestamps();
            $table->softDeletes();
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('template_design');
    }
}
