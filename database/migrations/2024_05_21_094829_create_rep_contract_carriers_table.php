<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRepContractCarriersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rep_contract_carriers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('contract_act_id');
            $table->unsignedBigInteger('agent_id');
            $table->unsignedBigInteger('userid')->nullable();
            $table->string('ipaddress');
            $table->string('contract',255);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rep_contract_carriers');
    }
}
