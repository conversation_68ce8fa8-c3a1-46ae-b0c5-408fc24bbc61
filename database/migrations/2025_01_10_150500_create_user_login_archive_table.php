<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserLoginArchiveTable extends Migration
{

    public function up()
    {
        Schema::create('user_login_archive', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('userid');
            $table->string('username');
            $table->string('password');
            $table->integer('q1')->nullable();
            $table->integer('q2')->nullable();
            $table->integer('a1')->nullable();
            $table->integer('a2')->nullable();
            $table->string('udate');
            $table->string('temp_pass')->nullable();
            $table->integer('status')->default(1);
            $table->string('ip')->nullable();
            $table->integer('blocked')->default(0);
            $table->string('password_validity')->nullable();
            $table->integer('subscribe')->default(1);
            $table->string('last_attempt')->nullable();
            $table->string('verification_code')->nullable();
            $table->string('cookie_token')->nullable();
            $table->string('phone')->nullable();
            $table->string('ipAddress')->nullable();
            $table->string('last_login_datetime')->nullable();
            $table->string('password_verification_code')->nullable();
            $table->string('weburl')->nullable();
            $table->enum('is_email_valid', ['YES', 'NO'])->nullable();
            $table->timestamps();
            $table->softDeletes();
            
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_login_archive');
    }
}
