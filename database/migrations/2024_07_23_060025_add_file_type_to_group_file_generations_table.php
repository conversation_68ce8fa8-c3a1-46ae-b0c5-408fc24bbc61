<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFileTypeToGroupFileGenerationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('group_file_generations', function (Blueprint $table) {
            $table->string('file_type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *database/migrations/2024_07_23_060025_add_is_john_report_flag_to_group_file_generations_table.php
     * @return void
     */
    public function down()
    {
        Schema::table('group_file_generations', function (Blueprint $table) {
            $table->dropColumn('file_type');
        });
    }
}
