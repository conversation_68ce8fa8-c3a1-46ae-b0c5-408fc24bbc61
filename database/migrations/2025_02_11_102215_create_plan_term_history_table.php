<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlanTermHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('plan_term_history', function (Blueprint $table) {
            $table->bigIncrements('plan_term_history_id'); 
            $table->integer('p_ai');
            $table->bigInteger('policy_num');
            $table->integer('plan_id');
            $table->string('date', 255)->nullable();
            $table->date('peffective_date')->nullable();
            $table->date('pterm_date')->nullable();
            $table->decimal('pefees', 10, 2)->nullable();
            $table->decimal('penroll_fee_discount', 10, 2)->nullable();
            $table->string('ppptype', 255)->nullable();
            $table->string('pstatus', 10)->nullable();
            $table->decimal('idilus_surcharge', 10, 2)->nullable();
            $table->integer('pram_bpa_id')->nullable();
            $table->timestamps(); 
            $table->softDeletes(); 
            $table->boolean('is_approved')->nullable();
            $table->char('is_email_sent', 1)->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plan_term_history');
    }
}
