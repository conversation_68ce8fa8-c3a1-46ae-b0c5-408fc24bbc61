<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNbInvoiceItemHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('nb_invoice_item_history', function (Blueprint $table) {
            $table->bigIncrements('nb_invoice_item_history_id'); 
            $table->integer('invoice_item_id');
            $table->integer('invoice_id')->nullable();
            $table->integer('policy_id')->nullable();
            $table->date('eff_date')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('plan_id')->nullable();
            $table->string('amount', 45)->nullable();
            $table->string('efee', 45)->nullable();
            $table->string('tier', 4)->nullable();
            $table->integer('invoice_item_created_user_id')->nullable();
            $table->dateTime('invoice_item_create_dttm')->nullable();
            $table->string('ppid', 250)->nullable();
            $table->enum('is_ptc', ['1', '0']); 
            $table->timestamps(); 
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('nb_invoice_item_history');
    }
}
