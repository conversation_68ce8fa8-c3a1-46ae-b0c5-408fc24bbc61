<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgentFileGenerationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agent_file_generations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('file_path');
            $table->string('file_name');
            $table->string('file_generation_started');
            $table->string('file_generation_ended');
            $table->integer('no_of_record');
            $table->string('added_by');
            $table->string('added_by_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('agent_file_generations');
    }
}