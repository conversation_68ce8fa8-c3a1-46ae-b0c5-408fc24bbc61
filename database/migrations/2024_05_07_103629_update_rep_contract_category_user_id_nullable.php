<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateRepContractCategoryUserIdNullable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('rep_contract_catagory', function (Blueprint $table) {
            $table->unsignedBigInteger('userid')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('rep_contract_catagory', function (Blueprint $table) {
            $table->unsignedBigInteger('userid')->nullable(false)->change();
        });
    }
}
