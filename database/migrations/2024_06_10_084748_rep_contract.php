<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RepContract extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rep_contract', function (Blueprint $table) {
            $table->bigIncrements('contract_id');
            $table->bigInteger('agent_id')->unsigned();
            $table->string('contract_type')->comment('Values: ancillary, premier, bexPremier, bexAncillary, fegliAncillary, patriotAncillary, categoryL7, categoryL9, carrier');
            $table->string('contract_display_name')->nullable();
            $table->enum('is_completed', ['yes', 'no']);
            $table->string('screenshot')->nullable();
            $table->timestamp('clicked_at')->nullable();
            $table->timestamps();

        });

    }

    public function down()
    {
        Schema::dropIfExists('rep_contract');
    }
}