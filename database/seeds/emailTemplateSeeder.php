<?php

use Illuminate\Database\Seeder;
use \Carbon\Carbon as Carbon;

class emailTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $c=DB::table('email_templates')->truncate();
        if($c==0)
        {
            DB::table('email_templates')->insert([
            'id' =>'1',
            'name' =>'CORENROLL',
            'categoryname' =>'CORENROLL',
            'template' =>'<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
            <html xmlns="http://www.w3.org/1999/xhtml"
            xmlns:v="urn:schemas-microsoft-com:vml"
            xmlns:o="urn:schemas-microsoft-com:office:office">
            <head>
              <!--[if gte mso 9]>
              <xml>
                  <o:OfficeDocumentSettings>
                      <o:AllowPNG/>
                      <o:PixelsPerInch>96</o:PixelsPerInch>
                  </o:OfficeDocumentSettings>
              </xml>
              <![endif]-->
              <!-- fix outlook zooming on 120 DPI windows devices -->
              <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <!-- So that mobile will display zoomed in -->
              <meta http-equiv="X-UA-Compatible" content="IE=edge">
              <!-- enable media queries for windows phone 8 -->
              <meta name="format-detection" content="date=no">
              <!-- disable auto date linking in iOS 7-9 -->
              <meta name="format-detection" content="telephone=no">
              <!-- disable auto telephone linking in iOS 7-9 -->
              <title>Elevate Wellness - </title>
              <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/css/bootstrap.min.css">
              <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.0/jquery.min.js"></script>
              <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/js/bootstrap.min.js"></script>
            </head>
            <body>
              <br>
              <br>
              <br>
              <span v-show="emailconfig.content_status==0">
                  <div class="container" style="background-color: #F0F0F0;width: 759px;">
                      <div class="container" style="background-color:#fefefe;width: 759px;">
                          <div class="row">
                              <div class="col-sm-4">
            
            </div>
                              <div class="col-sm-4">
            
            </div>
                          </div>
                          <div class="row">
                              <div class="col-sm-4">
                                  <br>
            
            
            
                              </div>
                              <div class="col-sm-4"></div>
                              <div class="col-sm-4">
                                  <span>
                                      <img src="https://www.purenroll.com/images/elevate-logo-main.png" class="img-rounded" alt="Cinque Terre" width="39%" height="92px">
            
            
            
                                  </span>
                              </div>
                          </div>
                          <hr>
                          <div class="row">
                              <div class="col-sm-12">
                                  <p>
                                      <b>Dear &nbsp;#firstname#&nbsp;&nbsp;#lastname#,</b>
                                  </p>
            
            #Content#
                                  <div></div>
                              </div>
                          </div>
                          <hr>
                          <div class="row">
                              <span style="font-size: smaller;">
            Last Updated: #current_year#
            
                                  <br>
                                  Problems or questions? Call us at (888) 243-4011
            
                              </span>
                          </div>
                      </div>
                      <hr>
                      <span>
                          <br>
                          <p style="font-size: smaller;">
                              <strong>Elevate Wellness, Inc.</strong>
                          </p>
                          <p style="font-size: smaller;">Elevate Wellness</p>
                          <p style="font-size: smaller;">20 Madison Avenue</p>
                          <p style="font-size: smaller;">Valhalla, NY 10595</p>
                          <p style="font-size: smaller;">Phone (*************</p>
                          <p style="font-size: smaller;">Fax (*************</p>
                      </span>
                  </div>
              </span>
            </body>
            </html>',
            'company'=>'ALL',
            'template_id'=>'1',
            'created_at'=>Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'=>Carbon::now()->format('Y-m-d H:i:s'),
        ]);  
    }
    }
    
}
