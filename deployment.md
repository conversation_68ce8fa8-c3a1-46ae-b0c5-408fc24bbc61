## Nuera API deployment with Capistrano

### Install Ruby
*sudo apt update*
*sudo apt install ruby-full*
*ruby --version*

### Install Capistrano
*gem install capistrano*
*cap --version*

### Install Slakistrano
*gem install slackistrano*


### Key based authentication needed with github and aws (ec2 instance)
#### GITHUB (copy your device's public key and add it to github)

#### AWS 
*cat ~/.ssh/id_rsa.pub | ssh -i <ec2-instance-pem-key> user@ip-address “cat - >> ~/.ssh/authorized_keys”*

### Add Host and Keys sshconfig
*nano ~/.ssh/config*
**Host nuera-api
HostName ***********
User ubuntu
IdentityFile ~/.ssh/id_rsa**


### Deploy
#### Goto Project Root
*cd /path/to/project*

#### Deploy to Staging Server
*cap staging deploy*

#### Deploy to Production Server
*cap production deploy*


