namespace :laravel do

    desc "Configure .env file"
    task :configure_dot_env do
        dotenv_file = fetch(:laravel_dotenv_file)
        on roles (:laravel) do
            execute :cp, "#{dotenv_file} #{release_path}/.env"
        end
    end

    desc "Run Laravel Artisan migrate task."
    task :migrate do
        on roles(:laravel) do
            within release_path do
                execute :php, "artisan migrate --force"
            end
        end
    end


    desc "Optimize Laravel Class Loader"
    task :optimize do
        on roles(:laravel) do
            within release_path do
                execute :php, "artisan clear-compiled"
                execute :php, "artisan optimize"
            end
        end
    end

    desc "Seeding Database"
    task :seed do
        on roles(:laravel) do
            within release_path do
                execute :php, "artisan db:seed"
            end
        end
    end

    desc "Clearing Caches"
    task :clearcache do
        on roles(:laravel) do
            within release_path do
                execute :php, "artisan view:clear"
                execute :php, "artisan config:cache"
                execute :php, "artisan cache:clear"
                execute :php, "artisan config:clear"
            end
        end
    end
end