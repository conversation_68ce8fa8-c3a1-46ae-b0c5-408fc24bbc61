<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentBusinessAddress extends Model
{
    use SoftDeletes;

    protected $table = 'agent_business_address';
    protected $primaryKey = 'id';
    protected $appends = ['type'];
    protected $guarded = ['id'];
    protected $dates = ['deleted_at'];

    const AGENT_ADDRESS_TYPE_BUSINESS = 'business';

    public function getTypeAttribute()
    {
        return self::AGENT_ADDRESS_TYPE_BUSINESS;
    }

    public function restore()
    {
        parent::restore();
    }
}
