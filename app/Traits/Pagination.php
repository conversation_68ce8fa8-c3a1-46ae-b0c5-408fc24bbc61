<?php

namespace App\Traits;

use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

trait Pagination
{
    private function paginate($items, $perPage = 5, $page = 1, $options = [])
    {
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }

    private function paginatedData($paginateData, Request $request, $additionalData = [])
    {
        $toArrayPagination = $paginateData->toArray();
        return [
                'path' => $request->url(),
                'per_page' => (int)$paginateData->perPage(),
                'total_records' => $paginateData->total(),
                'total_pages' => $paginateData->lastPage(),
                'current_page' => $paginateData->currentPage(),
                'record_range' => array($toArrayPagination['from'], $toArrayPagination['to']),
                "next" =>  $this->validateUrl($paginateData->nextPageUrl()) ?: ($paginateData->nextPageUrl() != null ? $request->url() . ltrim($paginateData->nextPageUrl(), '/') : null),
                "previous" => $this->validateUrl($paginateData->previousPageUrl()) ?: ($paginateData->previousPageUrl() != null ? $request->url() . ltrim($paginateData->previousPageUrl(), '/') : null),
                "first" => $this->validateUrl($toArrayPagination['first_page_url']) ?: ($toArrayPagination['first_page_url'] != null ? $request->url() . ltrim($toArrayPagination['first_page_url'], '/') : null),
                "last" => $this->validateUrl($toArrayPagination['last_page_url']) ?: ($toArrayPagination['last_page_url'] != null ? $request->url() . ltrim($toArrayPagination['last_page_url'], '/') : null),
                'records' => $toArrayPagination['data'],
            ] + $additionalData;
    }


    private function validateUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL);
    }

    private function paginateNewData($paginateData, $records, Request $request, $additionalData = [])
    {
        /**
         * @var $paginateData LengthAwarePaginator
         */
        $toArrayPagination = $paginateData->toArray();
        return [
                'path' => $request->url(),
                'per_page' => $paginateData->perPage(),
                'total_records' => $paginateData->total(),
                'total_pages' => $paginateData->lastPage(),
                'current_page' => $paginateData->currentPage(),
                'record_range' => array($toArrayPagination['from'], $toArrayPagination['to']),
                "next" =>  $this->validateUrl($paginateData->nextPageUrl()) ?: ($paginateData->nextPageUrl() != null ? $request->url() . ltrim($paginateData->nextPageUrl(), '/') : null),
                "previous" => $this->validateUrl($paginateData->previousPageUrl()) ?: ($paginateData->previousPageUrl() != null ? $request->url() . ltrim($paginateData->previousPageUrl(), '/') : null),
                "first" => $this->validateUrl($toArrayPagination['first_page_url']) ?: ($toArrayPagination['first_page_url'] != null ? $request->url() . ltrim($toArrayPagination['first_page_url'], '/') : null),
                "last" => $this->validateUrl($toArrayPagination['last_page_url']) ?: ($toArrayPagination['last_page_url'] != null ? $request->url() . ltrim($toArrayPagination['last_page_url'], '/') : null),
                'records' => $records,
            ] + $additionalData;
    }
}
