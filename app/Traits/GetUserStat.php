<?php


namespace App\Traits;


use GuzzleHttp\Client;

trait GetUserStat
{
    private function userTotalLogin()
    {
        if (!cache()->has('user_stat')) {
            $client = new Client();
            $request = $client->request('GET', config('notification.PUSH_NOTIFICATION_URL') . "api/v1/user-stats/new-login-count-device-wise", [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ]
            ]);
            $body = $request->getBody()->getContents();
            cache()->add('user_stat', json_decode(($body), true), 14000); /* cache will expire every 4 hours */
        }
        return cache()->get('user_stat');
    }

}
