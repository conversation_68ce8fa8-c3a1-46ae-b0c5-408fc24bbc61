<?php


namespace App\Traits;


use App\EmailRecipients;
use GuzzleHttp\Client;

trait EmailRecipient
{
    private function addAddtionalCC($CCEmail, $agentID)
    {
        $emailReceipients = EmailRecipients::where('agent_id',$agentID)->first();
        if(!$emailReceipients instanceof EmailRecipients || config("testemail.TEST_EMAIL")) return $CCEmail;
        return array_merge(array_filter($CCEmail), explode(',', $emailReceipients->cc_email));
    }

    private function addAddtionalBCC($BCCEmail, $agentID)
    {
        $emailReceipients = EmailRecipients::where('agent_id',$agentID)->first();
        if(!$emailReceipients instanceof EmailRecipients || config("testemail.TEST_EMAIL")) return $BCCEmail;
        return array_merge(array_filter($BCCEmail), explode(',', $emailReceipients->bcc_email));
    }

}
