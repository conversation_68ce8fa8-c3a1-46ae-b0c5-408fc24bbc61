<?php

namespace App\Traits;

use App\SubGroup;
use App\GroupInfo;
use Illuminate\Support\Facades\Log;


trait SubGroupEmailTrait
{
    
    protected function getRootGroupEmail(int $groupId): ?string
    {
        Log::channel('root_group')->info('Starting root group email lookup', ['group_id' => $groupId]);

        $subGroup = SubGroup::query()
            ->select(['gid', 'sub_group_id'])
            ->where('sub_group_id', $groupId)
            ->where('status', 1)
            ->latest('created_at')
            ->first();

        if ($subGroup === null) {
            Log::channel('root_group')->warning('No active subgroup found', [
                'group_id' => $groupId,
                'action' => 'getRootGroupEmail'
            ]);
            return null;
        }

        $rootGroup = GroupInfo::query()
            ->select(['gid', 'gemail'])
            ->find($subGroup->gid);

        if ($rootGroup === null) {
            Log::channel('root_group')->error('Root group not found', [
                'group_id' => $groupId,
                'root_group_id' => $subGroup->gid
            ]);
            return null;
        }

        if (empty($rootGroup->gemail)) {
            Log::channel('root_group')->warning('Root group email not found', [
                'group_id' => $groupId,
                'root_group_id' => $subGroup->gid
            ]);
            return null;
        }

        Log::channel('root_group')->info('Successfully retrieved root group email', [
            'group_id' => $groupId,
            'root_group_id' => $subGroup->gid,
            'has_email' => !empty($rootGroup->gemail)
        ]);

        return $rootGroup->gemail;
    }

 
    protected function processEmailAddresses(array $emailData, int $groupId): array
    {   
        $oldData = $emailData;
        Log::channel('root_group')->info('Starting email data processing', [
            'group_id' => $groupId,
            'email_data' => $emailData
        ]);

        $rootEmail = $this->getRootGroupEmail($groupId);
        
        if ($rootEmail === null) {
            Log::channel('root_group')->warning('Cannot proceed with email replacement - no root email found', [
                'group_id' => $groupId,
                'email_data' => $emailData
            ]);
            return $emailData;
        }

        $emailFields = ['toAddress', 'ccAddress', 'bccAddress'];
        foreach ($emailFields as $field) {
            if (isset($emailData[$field])) {
                $emailData[$field] = $rootEmail;
            }
        }

        Log::channel('root_group')->info('Successfully processed email data', [
            'old_email_data' => $oldData,
           'new_email_data'=> $emailData
        ]);

        return $emailData;
    }
}
