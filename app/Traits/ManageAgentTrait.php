<?php


namespace App\Traits;

use App\AgentInfo;
use App\Repositories\Agents\Agents;
trait ManageAgentTrait
{
    private $benId = 1901;

    public function formatContractType($contractType)
    {
        $formattedType = ucwords(strtolower(trim(preg_replace('/(?<=\w)([A-Z])/', ' $1', $contractType))));
        return $formattedType;
    }
    public function isBex($agent_id){
        if((int)$agent_id === $this->benId){
            return true;
        }
        else{
            $agent = new Agents();
            $bexDownlines = $agent->getDownlines($this->benId);
            return in_array($agent_id, $bexDownlines);
        }

    }
}
