<?php


namespace App\Traits;

use App\AgentInfo;
use App\Helpers\DecryptEcryptHelper;
use App\SsoUsers;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\DB;

trait SyncRep
{
    public function syncRepSSO($email, $phone , $existingEmail)
    {
        $sso_users = SsoUsers::where('email', $existingEmail)->where('user_type','A')->first();
        if($sso_users){
            $sso_users->phone = $phone;
            $sso_users->email = $email;
            return $sso_users->update();
        }
        return false;
    }

    public function checkAlreadyExists($agent_email){
        return SsoUsers::where('email', $agent_email)->where('user_type','A')->exists();
    }

    public function removeRepSSO($agentId)
    {
        try {
            $client = new Client();
            $request = $client->request('POST', config('notification.PUSH_NOTIFICATION_URL') . "api/v1/remove-sso-user", [
                'json' => [
                    "agent_id" => $agentId,
                    "user_type" => "A"
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ]

            ]);

            $body = $request->getBody()->getContents();
            return (object)json_decode(($body), true);
        } catch (\Exception $e) {
            $response = $e->getMessage();
            return (object)json_decode(($response), true);
        }
    }

    public function addRepSSO($agentId)
    {
        $agentDetails = AgentInfo::where('agent_id',$agentId)
            ->with(['agentUser'])
            ->first();
        try {
            $client = new Client();
            $request = $client->request('POST', config('notification.PUSH_NOTIFICATION_URL') . "api/v1/sync-user", [
                'json' => [
                    "userId" => $agentDetails->agentUser->id,
                    "userPhone" => $agentDetails->agent_phone2 ?: $agentDetails->agent_phone1,
                    "userEmail" => $agentDetails->agentUser->username,
                    "userFullName" => $agentDetails->getFullNameAttribute(),
                    "userPassword" => DecryptEcryptHelper::decryptInfo($agentDetails->agentUser->password),
                    "actionType" => 'insert',
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ]
            ]);
            $body = $request->getBody()->getContents();
            return (object)json_decode(($body), true);
        } catch (\Exception $e) {
            $response = $e->getMessage();
            return (object)json_decode(($response), true);
        }
    }
}
