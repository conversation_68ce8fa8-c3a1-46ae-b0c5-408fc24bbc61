<?php


namespace App\Traits;


use GuzzleHttp\Client;

trait SyncMember
{
    public function syncMemberSSO($user,$newEmail)
    {
        $requestData = [
            "memberID" => $user->userid,
            "old_email" => $user->username,
            "new_email" => $newEmail,
        ];
        $client = new Client();
        $request = $client->request('POST', config('notification.PUSH_NOTIFICATION_URL')."api.member/v1/update-member", [
            'json' => $requestData,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);
        $body = $request->getBody()->getContents();
        return $body;
    }
}
