<?php


namespace App\Traits\FileUpload;

use Illuminate\Support\Facades\Storage;

trait FileUpload
{
    private function uploadToS3($file_local, string $directory, $filename = '')
    {
        $fileName = $filename ?: $file_local->hashName();
        return Storage::disk('s3-third')->put($directory . $fileName, file_get_contents($file_local)) ? $fileName : '';
    }

    private function getS3Url(string $directory, $image)
    {
        return $image ? config('filesystems.disks.s3-third.url') . $directory . $image : '';
    }

    private function existsInS3(string $directory, string $filename)
    {
        return Storage::disk('s3-third')->exists($directory . $filename);
    }

    private function deleteFromS3(string $directory, string $filename)
    {
        return Storage::disk('s3-third')->delete($directory . $filename);
    }
}
