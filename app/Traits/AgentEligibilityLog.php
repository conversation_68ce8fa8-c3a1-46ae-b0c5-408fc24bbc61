<?php


namespace App\Traits;


use App\AgentUpdate;

trait AgentEligibilityLog
{
    private $eligibilityAgentId;
    private $eligibilityCommnt;
    private $eligibilityAction;

    protected function createAgentEligibilityLogs($request)
    {
        $data = [
            "agent_id" => $this->eligibilityAgentId,
            "elgb_act" => $this->eligibilityAction,
            "elgb_comment" => $this->eligibilityCommnt,
            "elgb_act_date" => time(),
            "elgb_agent_id" => $request->login_user_id ?? request()->header('id'),
            "elgb_agent_name" => $request->login_user_name ?? request()->header('name'),
        ];
        AgentUpdate::create($data);
    }

    protected function setEligibilityAgentId(int $groupId): self
    {
        $this->eligibilityAgentId = $groupId;
        return $this;
    }

    protected function setEligibilityComment(string $comment): self
    {
        $this->eligibilityCommnt = $comment;
        return $this;
    }

    protected function setEligiblityAction(string $action): self
    {
        $this->eligibilityAction = $action;
        return $this;
    }
}
