<?php


namespace App\Traits;


use App\GroupUpdate;

trait GroupEligibilityLog
{
    private $eligibilityGroupId;
    private $eligibilityCommnt;
    private $eligibilityAction;

    protected function createGroupEligibilityLogs($request)
    {
        $data = [
            "group_id" => $this->eligibilityGroupId,
            "elgb_act" => $this->eligibilityAction,
            "elgb_comment" => $this->eligibilityCommnt,
            "elgb_act_date" => time(),
            "elgb_agent" => $request->login_user_id ?: null,
            "elgb_agentname" => $request->login_user_name ?: null,
        ];
        GroupUpdate::create($data);
    }

    protected function setEligibilityGroupId(int $groupId): self
    {
        $this->eligibilityGroupId = $groupId;
        return $this;
    }

    protected function setEligibilityComment(string $comment): self
    {
        $this->eligibilityCommnt = $comment;
        return $this;
    }

    protected function setEligiblityAction(string $action): self
    {
        $this->eligibilityAction = $action;
        return $this;
    }
}
