<?php


namespace App\Traits;


use Illuminate\Container\Container;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

trait Paginator
{
    public function links($data)
    {
        if ($data instanceof LengthAwarePaginator)
            return [
                'first' => $data->url(1),
                'last' => $data->url($data->lastPage()),
                'prev' => $data->previousPageUrl(),
                'next' => $data->nextPageUrl(),
            ];
    }

    public function meta($data)
    {
        if ($data instanceof LengthAwarePaginator)
            return [
                'current_page' => $data->currentPage(),
                'from' => $data->firstItem(),
                'last_page' => $data->lastPage(),
                'path' => $data->path(),
                'per_page' => $data->perPage(),
                'to' => $data->lastItem(),
                'total' => $data->total(),
            ];
    }

    public function linksWithQuery($data)
    {
        return $this->links(
            $data->appends(
                request()->query()
            )
        );
    }

    /**
     * Paginate a Collection instance
     *
     * @param Collection $results
     * @param $pageSize
     * @return LengthAwarePaginator
     */
    public function paginateCollection(Collection $results, $pageSize): LengthAwarePaginator
    {
        $page = \Illuminate\Pagination\Paginator::resolveCurrentPage('page');

        $total = $results->count();

        return $this->paginator($results->forPage($page, $pageSize), $total, $pageSize, $page, [
            'path' => \Illuminate\Pagination\Paginator::resolveCurrentPath(),
            'pageName' => 'page',
        ]);

    }

    /**
     * Create a new length-aware paginator instance.
     *
     * @param Collection $items
     * @param int $total
     * @param int $perPage
     * @param int $currentPage
     * @param array $options
     * @return LengthAwarePaginator
     */
    private function paginator(Collection $items, int $total, int $perPage, int $currentPage, array $options): LengthAwarePaginator
    {
        return Container::getInstance()->makeWith(LengthAwarePaginator::class, compact(
            'items', 'total', 'perPage', 'currentPage', 'options'
        ));
    }
}
