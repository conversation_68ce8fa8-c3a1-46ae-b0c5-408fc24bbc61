<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class MedicalTypes extends Model
{
    protected $table = 'medical_types';
    protected $primaryKey = 'id';
    protected $fillable = ['medical_code','description','created_by','created_at','updated_by','updated_at'];

    public function plans()
    {
        return $this->hasMany(Plan::class, 'medical_type_id');
    }

    public function agents()
    {
        return $this->hasMany(AgentMedicalTypes::class, 'medical_type_id');
    }
}
