<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PolicyClientBrowserInfo extends Model
{
    //
    protected $table = 'policy_client_browser_info';
    protected $primaryKey = 'policy_client_browser_info_id';
    public $timestamps = false;


    public function getDeviceTypeAttribute()
    {
        if ($this->isTablet) {
            return 'Tablet';
        } elseif ($this->isMobile) {
            return 'Mobile';
        } elseif ($this->isDesktop) {
            return 'Desktop';
        } elseif ($this->isChromeFrame) {
            return 'Chrome Frame';
        } elseif ($this->isRobot) {
            return 'Robot';
        } elseif ($this->isFacebook) {
            return 'Facebook';
        } else {
            return 'Desktop';
        }

    }
}
