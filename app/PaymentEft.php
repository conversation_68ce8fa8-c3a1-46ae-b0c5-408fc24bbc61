<?php

namespace App;

use App\UserInfo;
use App\Policy;
use Illuminate\Database\Eloquent\Model;

class PaymentEft extends Model
{
    protected $table = 'payment_eft';
    protected $primaryKey = 'bank_id';

    // get user from userinfo table for primary payment info
    public function getUser(){
        return $this->hasOne('App\UserInfo','userid','bank_userid');
    }

    // get policy information from policies table for primary payment info
    public function getPolicy(){
        return $this->hasOne('App\Policy','payment_id','bank_id');
    }

    public function getPoliciesFromUserId()
    {
        return $this->hasMany(Policy::class, 'policy_userid', 'bank_userid')->get();
    }

}
