<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AgentUplineHistory extends Model
{
    protected $table = 'agent_upline_history';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    protected $casts = [
        'effective_date_end' => 'datetime:Y-m-d',
        'effective_date_start' => 'datetime:Y-m-d'
    ];

    public function agent()
    {
        return $this->hasOne(AgentInfo::class, 'agent_id', 'agent_id');
    }

    public function upline()
    {
        return $this->hasOne(AgentInfo::class, 'agent_id', 'agent_ga');
    }
}
