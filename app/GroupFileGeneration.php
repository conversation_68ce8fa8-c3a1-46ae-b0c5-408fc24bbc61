<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GroupFileGeneration extends Model
{
    //
    protected $table = 'group_file_generations';
    protected $primaryKey = 'id';
    protected $fillable = ['file_path','file_name','file_generation_started','file_generation_ended','no_of_record','added_by','added_by_id','file_type'];


    public static function fetchgroupReport($id){
        return self::changeLink(GroupFileGeneration::query()->where('added_by_id',$id)->orderBy('id', 'DESC')->paginate(10));
    }
    public static function fetchAdmingroupReport($type,$id=null){
        if ($id) {
            return self::changeLink(GroupFileGeneration::query()->where('added_by_id',$id)->where('file_type',$type)->orderBy('id', 'DESC')->paginate(10));

        }else{
            return self::changeLink(GroupFileGeneration::query()->where('file_type',$type)->orderBy('id', 'DESC')->paginate(10));
        }
    }

    public static function changeLink($data)
    {
        $data->map(function ($data) {
            if($data['file_path'] != null  && $data['file_name'] != null){
                $data['file_download_link'] = config('filesystems.disks.s3-third.url').'/' . $data['file_path']. $data['file_name'];
            }
        });
        return $data;
    }
}