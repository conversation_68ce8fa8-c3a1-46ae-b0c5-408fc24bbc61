<?php

namespace App\Repositories;

use App\Address;
use App\UserActivityDetail;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use PHPUnit\Framework\Exception;

class UserInfoFeatures extends Model
{
    public function getUserAddress($userID)
    {
        try {
            $data = Address::where(['a_userid'=> $userID,'status'=>1])->get();

            if ($data->isEmpty()) {
                return ['error' => 'Invalid user.'];
            }
            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function fetchUserActivity($userID)
    {
        try {
            return UserActivityDetail::query()
                ->where('user_id', $userID)
                ->select(['user_id', 'user_type', 'act_time', 'action', 'device', 'device_type'])
                ->get()
                ->map(function ($activity) {
                    $activity->act_time = Carbon::parse($activity->act_time)->format('m/d/Y | H:i');
                    return $activity;
                });

        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }

    }
}
