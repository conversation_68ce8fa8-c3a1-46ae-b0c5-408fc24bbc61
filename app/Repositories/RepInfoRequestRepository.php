<?php


namespace App\Repositories;

use App\RepInfoRequest;
use App\RepInfoRequestsDetail;
use App\Repositories\Payment\PaymentFeatures;
use App\Service\RepInfoRequest\RepInfoRequestMessageService;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class RepInfoRequestRepository
{
    use ResponseMessage, Paginator;

    /**
     * @var RepInfoRequest
     */
    private $model;
    /**
     * @var RepInfoRequestMessageService
     */
    private $messageService;

    public function __construct(RepInfoRequest $model,RepInfoRequestMessageService $messageService)
    {
        $this->model = $model;
        $this->messageService = $messageService;
    }

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->with(['repInfoPaymentDetails']);
        if (request()->header('origin') === config('app.purenroll_system_ui.url')) {
            $query->whereNotIn('agent_id', config('app.extra_health_rep_ids'));
        }
        $query->orderBy('id', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }


    public function paginatedFormattedList($limit, $filters = [])
    {
        $query = $this->paginatedList($limit, $filters)
            ->appends($filters);
        $data = $this->formattedData($query);
        return $this->successResponse('Success', $data);

    }

    public function formattedData($data)
    {
        return [
            'data' => $this->formattedItems($data),
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    public function formattedItems($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return $result;
    }

    protected function singleFormattedItem($d)
    {
        $userInfo = null;
        if (isset($d->userInfo)) {
            $userInfo = [
                'userid' => $d->userInfo->userid,
                'name' => $d->userInfo->fullname,
                'email' => $d->userInfo->cemail
            ];
        }
        return [
            'id' => $d->id,
            'type' => $d->type,
            'type_name' => $d->type_name,
            'status' => $d->status,
            'status_name' => $d->status_name,
            'policy_id' => $d->policy_id,
            'user_info' => $userInfo,
            'is_enroll_prime' => in_array($d->agent_id,config('app.extra_health_rep_ids')),
            'repInfoPaymentDetails' => $d->repInfoPaymentDetails,
            'cancel_reason'=>$d->cancel_reason,
            'admin_reason' => $d->admin_reason,
            'information' => $d->information,
            'created_by' => isset($d->agentInfo) ? $d->agentInfo->rep_fullname : null,
            'requested_by' => isset($d->requested_by) && $d->requested_by === 'A' ? 'Agent' : ($d->requested_by === 'M' ? 'Member' : null),
            'cards_requested' => $d->cards_requested,
            'document'=>$d->document_url,
            'created_at' => Carbon::parse($d->created_at)->format('m/d/Y'),
            'updated_at' => Carbon::parse($d->updated_at)->format('m/d/Y')
        ];
    }

    protected function concatFullname($firstName, $middleName, $lastName)
    {
        $sqlQuery = "CONCAT_WS(' ',";
        $sqlQuery .= "CASE $firstName WHEN '' THEN NULL ELSE $firstName END,";
        $sqlQuery .= "CASE $middleName WHEN '' THEN NULL ELSE $middleName END,";
        $sqlQuery .= "CASE $lastName  WHEN '' THEN NULL ELSE $lastName END)";
        return $sqlQuery;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['member_name'])) {
            $data->whereHas('userInfo',function ($q) use ($filters){
                $concat = $this->concatFullname('cfname', 'cmname', 'clname');
                $q->where(DB::raw($concat), 'LIKE', '%' . $filters['member_name'] . '%');
            });
        }

        if (isset($filters['type'])) {
            $data->where('type', '=', $filters['type']);
        }

        if (isset($filters['agent_id'])) {
            $data->where('agent_id', '=', $filters['agent_id']);
        }

        if (isset($filters['status'])) {
            $data->where('status', '=', $filters['status']);
        }

        if (isset($filters['policy_id'])) {
            $data->where('policy_id', '=', $filters['policy_id']);
        }

        if(isset($filters['date'])){
            $formattedDate = \DateTime::createFromFormat('m/d/Y', $filters['date']);
            if ($formattedDate) {
                $data->where('created_at', 'LIKE', '%' . $formattedDate->format('Y-m-d') . '%');
            }
        }
        
        if(isset($filters['email'])){
            $data->whereHas('userInfo',function ($q) use ($filters){
                $q->where('cemail', 'LIKE', '%' . $filters['email'] . '%');
            });
        }
    }

    public function updateStatus($data,$status){
        /**
         * @var $model RepInfoRequest
         */
        $id = $data['id'];
        $cards_request = isset($data['cards_request']) && $data['cards_request'] ? $data['cards_request'] : false;

        $model = $this->model::find($id);
        $type ='';
        $completedStatus = $this->model::STATUS_COMPLETED;
        $progressStatus = $this->model::STATUS_IN_PROGRESS;
        if (!$model)return $this->failedResponse('Information Request not found.');
        if($status == $progressStatus && $model->status == $progressStatus )return $this->failedResponse('Information Request already approved.');
        if($status == $completedStatus && $model->status == $completedStatus )return $this->failedResponse('Information Request already completed.');
        DB::beginTransaction();
        try {
            $model->update([
                'status' => $status,
            ]);
            if ($status == $this->model::STATUS_IN_PROGRESS) {
                $type = "Approved";
            } elseif($status == $this->model::STATUS_COMPLETED) {
                $type = "Completed";
            }
            if($cards_request){
                $paymentFeature = new PaymentFeatures();
                $procssPayment = $paymentFeature->processPaymentForCards($model);
                if($procssPayment['success'] == false){
                    $model->update([
                        'status' => $this->model::STATUS_REJECTED,
                        'admin_reason'=>$procssPayment['message'] ?: null,
                    ]);

                    $this->messageService->sendRepInfoRequestActionEmail($model,'Rejected');
                    $successMessage= "Information Request Rejected. ".$procssPayment['message'];
                    DB::commit();
                    return $this->successResponse($successMessage, []);
                }
            }
            $this->messageService->sendRepInfoRequestActionEmail($model,$type);
            $successMessage= "Information Request {$type} successfully.";
            DB::commit();
            return $this->successResponse($successMessage, []);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse("Failed To {$type} Information Request.");
        }
    }

    public function rejectRequest($request){
        /**
         * @var $model RepInfoRequest
         */
        $model = $this->model::find($request['id']);
        if(!$model->status == $this->model::STATUS_REJECTED)return $this->failedResponse('Rep Information Request already rejected.');
        if (!$model)return $this->failedResponse('Information Request not found.');
        DB::beginTransaction();
        try {
            $model->update([
                'status' => $this->model::STATUS_REJECTED,
                'admin_reason'=>$request['admin_reason'] ?: null,
            ]);
            $this->messageService->sendRepInfoRequestActionEmail($model,'Rejected');
            $successMessage= "Information Request Rejected successfully.";
            DB::commit();
            return $this->successResponse($successMessage, []);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse("Failed To Reject Information Request.");
        }
    }

    public function getOptions()
    {
        $statuses = RepInfoRequest::$statuses;
        $types = RepInfoRequest::$types;
        $data =  [
            'statuses' => $this->keyValueOptions($statuses),
            'types' => $this->keyValueOptions($types)
        ];
        return $this->successResponse('Options', $data);
    }

    protected function keyValueOptions($options)
    {
        $data = [];
        foreach ($options as $k => $v) {
            $data[] = [
                'name' => $k,
                'value' => $v
            ];
        }
        return $data;
    }

    public function getPaymentDetails($id)
    {
        $details = RepInfoRequestsDetail::where('info_request_id', $id)->get();
        if (!$details) {
            return $this->failedResponse("No payment details found for this request.");
        }
        return $this->successResponse('Rep info payment detia fetched successfully.',$details);
    }

    public function getPolicyPaymentDetails($policy_id)
    {
        $requests = RepInfoRequest::with('repInfoPaymentDetails')
            ->where('policy_id', $policy_id)
            ->whereHas('repInfoPaymentDetails')
            ->get();
    
        if ($requests->isEmpty()) {
            return $this->failedResponse("No payment details found for this request.");
        }
    
        $details = $requests->flatMap(function ($request) {
            return $request->repInfoPaymentDetails->map(function ($detail) use ($request) {
                $detail->card_status = $request->status;
                $detail->cards_requested = $request->cards_requested;
                $detail->information = $request->information;
                $detail->cancel_reason = $request->cancel_reason;
                $detail->admin_reason = $request->admin_reason;
                $detail->type = $request->type;
                return $detail;
            });
        })->values();
    
        return $this->successResponse('Rep info payment details fetched successfully.', $details);
    }
    
}
