<?php

namespace App\Repositories\Ach;

use App\Ach;
use App\PlanOverview;
use App\UserInfoPolicyAddress;
use App\Policy;
use Illuminate\Database\Eloquent\Model;

class GenerateAch extends Model
{

    //get AT policies of payment eft or elist for ACH
    public function policyEftElist($effDate)
    {
        $data = UserInfoPolicyAddress::where('effective_date','LIKE',$effDate.'%')
                    ->whereIn('payment_type',['eft','elist'])
                    ->where('status','ACTIVE')
                    ->orWhere(function ($query) {
                        $query->where('status', 'TERMED')
                            ->whereRaw('timestampdiff(day,term_date,curdate()) < 0');
                        })
                    ->select('policy_id')
                    ->limit(10)
                    ->get();
       return $data;
    }

    //function to generate ACH
    public function generateAch($effDate)
    {
        $policyDetails = [];
        $policyList = self::policyEftElist($effDate);
        foreach($policyList as $key=>$policy){
            $policyDetails[] = app('App\Policy')->getPlanOverviewFromPolicy($policy->policy_id);
        }
        $achReport = app('App\Repositories\Ach\AchFunctions')->achHeader()
                     .app('App\Repositories\Ach\AchFunctions')->batchFive($effDate);
        return $achReport;
    }


}
