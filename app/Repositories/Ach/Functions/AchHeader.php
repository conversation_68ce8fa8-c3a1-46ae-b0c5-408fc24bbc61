<?php

namespace App\Repositories\Ach\Functions;

use App\Ach;
use Illuminate\Database\Eloquent\Model;

class AchHeader extends Model {

	public function achHeader()
    {
        $recordTypeCode            = '1';
        $priorityCode              = '01';
        $immediateDestination      = ' 091000019';
        $fileId                    = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) '7878782339', 10);
        $fileCreationDate          = date('ymd');
        $fileCreationTime          = date("Hi");
        $fileIdModifier            = 'A';
        $recordSize                = '094';
        $blockingFactor            = '10';
        $formatCode                = '1';
        $immediateDestinationName  = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) 'WELLS FARGO', 23);
        $companyName               = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) 'Elevate Wellness Assoc', 23);
        $referenceCode             = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) '', 8);

        $achHeader                 = $recordTypeCode.$priorityCode.$immediateDestination.$fileId.$fileCreationDate.$fileCreationTime.$fileIdModifier.$recordSize.$blockingFactor.$formatCode.$immediateDestinationName.$companyName.$referenceCode."<br>";

        return $achHeader;
    }

}
