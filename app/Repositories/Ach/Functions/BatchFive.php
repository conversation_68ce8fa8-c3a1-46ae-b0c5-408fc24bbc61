<?php

namespace App\Repositories\Ach\Functions;

use App\Ach;
use Illuminate\Database\Eloquent\Model;

class BatchFive extends Model 
{

	public function batchFive($effDate) 
	{
		$recordType           = '5';
		$companyData          = self::companyData($effDate);
	    $effectiveEntryDate   = date('ymd');
		$settlementDate       = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) '', 3);
		$originatorStatusCode = '1';
		$fargoRtNumber        = '09100001';
		// $batchNumber         = self::batchNum();
		$batchNumber          = app('App\Repositories\Ach\AchFunctions')->appendZeros((string) '01', 7);

		$batch5 = $recordType . $companyData . $effectiveEntryDate . $settlementDate . $originatorStatusCode . $fargoRtNumber . $batchNumber . "<br>";

		return $batch5;
	}

	//company data for batch5
	public function companyData($effDate) 
	{
		$serviceClassCode        = '200';
		$companyName             = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) 'EMPLOYERSNETWORK', 16);
		$companyDiscretionayData = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) '', 20);
		$companyId               = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) '1461785021', 10);
		$secCode                 = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) 'PPD', 3);
		$companyEntryDescription = app('App\Repositories\Ach\AchFunctions')->appendSpaces((string) 'INS PREM', 10);
		$effYear                 = substr(date('m/d/Y', strtotime($effDate)), -2);
		$monthYear               = substr(date('m/d/Y', strtotime($effDate)), -7);
		$effMonth                = substr($monthYear, -2);
		$companyDescriptiveDate  = $effYear . $effMonth . '01';

		$finalData = $serviceClassCode . $companyName . $companyDiscretionayData . $companyId . $secCode . $companyEntryDescription . $companyDescriptiveDate;

		return $finalData;
	}

}
