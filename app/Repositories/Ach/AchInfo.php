<?php

namespace App\Repositories\Ach;

use App\Ach;
use App\PlanOverview;
use App\UserInfoPolicyAddress;
use App\Policy;
use Illuminate\Database\Eloquent\Model;

class AchInfo extends Model
{

	//generated ach data based on year and month
    public function viewAch($achYear,$achMonth)
    {
        $data = Ach::where('ach_year',$achYear)
                    ->where($achMonth,'1')
                    ->get();
       return $data;
    }

    //get policies other than withdrawn(i.e. Active-Termed) and having payment type eft or elist
	public function policyAT()
    {
        $data = PlanOverview::where('status','ACTIVE')
                    ->orWhere(function ($query) {
	                    $query->where('status', 'TERMED')
	                        ->whereRaw('timestampdiff(day,term_date,curdate()) < 0');
	                    })
                    ->limit(100)
                    ->get();
       return $data;
    }


}
