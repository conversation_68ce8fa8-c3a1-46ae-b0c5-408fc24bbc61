<?php

namespace App\Repositories\Ach;

use Illuminate\Database\Eloquent\Model;

class AchFunctions extends Model
{
    //function 1: To generate ACH header
    public function achHeader()
    {
        $data = app('App\Repositories\Ach\Functions\AchHeader')->achHeader();
        return $data;
    }

    //function 2: To generate batch5
    public function batchFive($effDate)
    {
        $data = app('App\Repositories\Ach\Functions\BatchFive')->batchFive($effDate);
        return $data;
    }

    //function 3: To generate ACH Entry Detail
    public function entryDetail()
    {
        $data = app('App\Repositories\Ach\Functions\EntryDetail')->entryDetail();
        return $data;
    }

    //function 4: To generate ACH Addendar
    public function addenda()
    {
        $data = app('App\Repositories\Ach\Functions\Addenda')->addenda();
        return $data;
    }

    //function 5: To generate ACH Batch Eight
    public function batchEight()
    {
        $data = app('App\Repositories\Ach\Functions\BatchEight')->batchEight();
        return $data;
    }

    //function 6: To generate ACH Batch Nine
    public function batchNine()
    {
        $data = app('App\Repositories\Ach\Functions\BatchNine')->batchNine();
        return $data;
    }

    //function 7: To generate Processing Fee
    public function processingFee()
    {
        $data = app('App\Repositories\Ach\Functions\ProcessingFee')->processingFee();
        return $data;
    }

   
    //These are common function used for appending and prepending spaces and zeroes
    //function to appends spaces
    public function appendSpaces($variable, $limit) 
    {
        if (strlen($variable) == $limit) {
            return $variable;
        } elseif (strlen($variable) > $limit) {
            $variable = substr($variable, 0, $limit); //reduce size if exceed the limit..
        }
        for ($i = strlen($variable) + 1; $i <= $limit; $i ++) {
            $variable .= " "; //append spaches until it reaches the limit..
        }

        return $variable;
    }

    //function to append zeroes to prefix
    public function appendZeros($variable, $limit)
    {
        $amount = str_replace(".", "", $variable); //if decimal..simply remove it
        $result = "";
        for ($i = $limit; $i > strlen($amount) ; $i --) {
            $result .= "0"; //append 0 at begining to rich $limit
        }
        $result .= $amount;

        return $result;
    }


}
