<?php

namespace App\Repositories;

use App\AgentInfo;
use App\AgentMedicalTypes;
use App\GroupInfo;
use App\HomepageConfiguration;
use App\Http\Resources\SuccessResource;
use App\InsuranceDomain;
use App\Plan;
use App\PlanTextDescription;
use App\PlanZip;
use App\Repositories\Agents\ManageAgents;
use App\Repositories\Plans\PlanDetails;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use phpDocumentor\Reflection\Types\This;

class HomepageConfigurationRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var HomepageConfiguration
     */
    private $model;

    /**
     * HomepageConfigurationRepository constructor.
     * @param HomepageConfiguration $model
     */
    public function __construct(HomepageConfiguration $model)
    {
        $this->model = $model;
    }

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }

    public function paginatedFormattedList($limit, $filters = [])
    {
        $query = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($query);
    }

    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->orderBy('id', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['confFor'])) {
            $data->where('conf_for', '=', $filters['confFor']);
        }
        if (isset($filters['confType'])) {
            $data->where('conf_type', '=', $filters['confType']);
        }
        if (isset($filters['website'])) {
            $data->where('website', '=', $filters['website']);
        }
        if (isset($filters['planId'])) {
            $data->where('plan_id', '=', $filters['planId']);
        }
        if (isset($filters['agentId'])) {
            $data->where('agent_id', '=', $filters['agentId']);
        }
        if (isset($filters['groupId'])) {
            $data->where('group_id', '=', $filters['groupId']);
        }
    }

    protected function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    public function singleFormattedItem($d)
    {
        $planName = isset($d->plan) ? $d->plan->plan_name_system : null;
        $pId = isset($d->plan) ? $d->plan->pid : null;
        return [
            'id' => $d->id,
            'pId' => $pId,
            'planName' => $planName,
            'confFor' => $d->conf_for,
            'website' => $d->website,
            'isFeatured' => $d->is_featured,
            'createdDate' => $d->created_date,
            'formattedCreatedDate' => Carbon::parse($d->created_date)->format('m/d/Y'),
        ];
    }

    protected function getWebsites()
    {
        $data = [];
        $query = InsuranceDomain::query()
            ->select('domain')
            ->distinct()
            ->where('status', '=', 'A')
            ->where('domain', '!=', null)
            ->orderBy('domain', 'DESC')
            ->get()
            ->toArray();
        foreach ($query as $d) {
            $data[] = $d['domain'];
        }
        return [
            'data' => $data,
            'total' => count($data)
        ];
    }
    public function getHomepageConfigurationWebsite($reqeust){
        return response()->json(
            new SuccessResource([
                'statusCode' => 200,
                'data' => $this->getWebsites(),
                'message' => 'Website list has been fetched successfully.',
            ]),
            200
        );
    }

    public function getOptions()
    {
        $plan = new PlanDetails();
        $agentHomepagePlans = $plan->getHomepageConfigurationPlans();
        $groupHomepagePlans = $plan->getHomepageConfigurationPlans();
        $websites = $this->getWebsites();
        $data = [
            'agentHomepagePlans' => $agentHomepagePlans,
            'groupHomepagePlans' => $groupHomepagePlans,
            'websites' => $websites
        ];
        return $this->successResponse('Success', $data);
    }

    public function getAgentPlanQueries($agentId, $filters = [])
    {
        $query = $this->model::query()
            ->with('plan')
            ->where([
                'conf_for' => $this->model::CONFIGURATION_FOR_AGENT,
                'agent_id' => $agentId
            ])
            ->orderBy('id', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }

    public function paginatedAgentPlans($agentId, $limit = 25, $filters = [])
    {
        $query = $this->getAgentPlanQueries($agentId, $filters)
            ->paginate($limit)
            ->appends($filters);
        $data = $this->formattedData($query);
        return $this->successResponse('Success', $data);
    }

    public function getGroupPlanQueries($groupId, $filters = [])
    {
        $query = $this->model::query()
            ->with('plan')
            ->where([
                'conf_for' => $this->model::CONFIGURATION_FOR_GROUP,
                'group_id' => $groupId
            ])
            ->orderBy('id', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }

    public function paginatedGroupPlans($agentId, $limit = 25, $filters = [])
    {
        $query = $this->getGroupPlanQueries($agentId, $filters)
            ->paginate($limit)
            ->appends($filters);
        $data = $this->formattedData($query);
        return $this->successResponse('Success', $data);
    }

    public function toggleIsFeatured($id)
    {
        $model = $this->getById($id);
        if (!$model) return $this->failedResponse('No Data found.');
        $toggle = isset($model->is_featured) ? !$model->is_featured : true;
        try {
            $model->update([
                'is_featured' => $toggle
            ]);
            $data = $this->singleFormattedItem($model);
            return $this->successResponse('Homepage Configuration Updated.', $data, 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Update.');
        }
    }

    public function delete($id)
    {
        $model = $this->getById($id);
        if (!$model) return $this->failedResponse('No Data found.');
        try {        
            $zipbased = $this->checkZipBasedPlan($model->plan_id);
            if($zipbased){
                $getCode834 = $this->getCode834($model->plan_id);
                $code834AssociatePlansIds = $this->code834AssociatePlansIds($getCode834->code834);
                $this->zipBasedDelete($code834AssociatePlansIds , $model);
            }
            else{
                $model->delete();
            }
            return $this->successResponse('Homepage Configuration Deleted.', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Delete.');
        }
    }

    protected function checkZipBasedPlan($pid){
        return PlanZip::where('pid', $pid)->exists();
    }

    protected function getCode834($plansId){
        return Plan::where('pid', $plansId)->select('code834')->first();
    }

    protected function code834AssociatePlansIds($code834){
        return plan::where('code834', $code834)->where('forsale',1)->pluck('pid')->toArray();
    }

    protected function zipBasedDelete(array $pIds , object $data){
        $this->model->whereIn('plan_id',$pIds)
        ->where(function ($q) use($data){
            if($data->agent_id){
                $q->where('agent_id',$data->agent_id);
            }
            else{
                $q->where('group_id',$data->group_id);
            }
        })
        ->where('website',$data->website)
        ->delete();
    }

    protected function checkDuplicateRow($data, $planId, $confType)
    {
        $queryData = [
            'website' => $data['website'],
            'plan_id' => $planId,
            'conf_for' => $confType,
        ];
        if ($confType == $this->model::CONFIGURATION_FOR_AGENT) $queryData['agent_id'] = $data['agentId'];
        if ($confType == $this->model::CONFIGURATION_FOR_GROUP) $queryData['group_id'] = $data['groupId'];
        return $this->model::query()
            ->where($queryData)
            ->exists();
    }

    protected function formattedFormData($data, $planId, $conf)
    {
        return [
            'website' => $data['website'],
            'plan_id' => $planId,
            'conf_for' => $conf,
            'conf_type' => 'plan',
            'agent_id' => isset($data['agentId']) ? $data['agentId'] : null,
            'group_id' => isset($data['groupId']) ? $data['groupId'] : null,
        ];
    }

    public function createAgentPlan($data)
    {
        $agent = AgentInfo::find($data['agentId']);
        if (!$agent) return $this->failedResponse('Cannot add plan for this agent.');
        $plansId = $data['plans'];
        try {
            $confType = $this->model::CONFIGURATION_FOR_AGENT;
            $this->createAgentGroupHomepageConfiguration($data, $plansId, $confType);
            return $this->successResponse('Homepage Configuration Agent Plan Created.', [], 201);
        } catch (\Throwable $th) {
            return $this->failedResponse($th->getMessage());
        }
    }

    public function createGroupPlan($data)
    {
        $group = GroupInfo::find($data['groupId']);
        if (!$group) return $this->failedResponse('Cannot add plan for this group.');
        $plansId = $data['plans'];
        try {
            $confType = $this->model::CONFIGURATION_FOR_GROUP;
            $this->createAgentGroupHomepageConfiguration($data, $plansId, $confType);
            return $this->successResponse('Homepage Configuration Group Plan Created.', [], 201);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed to Create.');
        }
    }

    /**
     * @param $data
     * @param $plansId
     * @param $confType
     * @return int
     */
    protected function createAgentGroupHomepageConfiguration($data, $plansId, $confType): int
    {
        foreach ($plansId as $planId) {
            if($this->checkZipBasedPlan($planId)){
                $getCode834 = $this->getCode834($plansId);
                $code834AssociatePlansIds = $this->code834AssociatePlansIds($getCode834->code834);
                foreach ($code834AssociatePlansIds as $code834AssociatePlansId){
                    $checkDuplicateRow = $this->checkDuplicateRow($data, $code834AssociatePlansId, $confType);
                    $formData = $this->formattedFormData($data, $code834AssociatePlansId, $confType);
                    $formData['created_date'] = date('Y-m-d h:i:sa');
                    if ($checkDuplicateRow == false) {
                        $this->model::create($formData);
                    }
                }
            }else{
                $checkDuplicateRow = $this->checkDuplicateRow($data, $planId, $confType);
                $formData = $this->formattedFormData($data, $planId, $confType);
                $formData['created_date'] = date('Y-m-d h:i:sa');
                if ($checkDuplicateRow == false) {
                    $this->model::create($formData);
                }
            }

        }
        return true;
    }

    /**
     * @param $agentId
     * @return array
     * Fetch agent websites
     * only fetched website which are associated with agent
     */
    public function getAgentWebsites($agentId)
    {
        $manageAgent = new ManageAgents();
        $agentInfoElite = $manageAgent->countAgentGroup($agentId, 'elite');
        $agentInfoBrokerExchange = $manageAgent->countAgentGroup($agentId, 'brokerExchange');
        $links = HomepageConfiguration::$sites;
        $data = [];
        foreach ($links as $l) {
            $data[$l] = $l;
        }
        if ($agentInfoBrokerExchange < 1) unset($data[HomepageConfiguration::SITE_BROKER_EXCHANGE]);
        if ($agentInfoElite < 1) {
            unset($data[HomepageConfiguration::SITE_PREMIER_ENROLL]);
            unset($data[HomepageConfiguration::SITE_ELITE_ENROLL]);
            unset($data[HomepageConfiguration::SITE_GO_ENROLLL]);
        };
        return [
            'data' => array_values($data),
            'total' => count($data)
        ];
    }

    /**
     * @param $agentId
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * fetch agent plans and websites
     */
    public function getAgentOptions($agentId)
    {
        $plan = new PlanDetails();
        $agentHomepagePlans = $plan->getHomepageConfigurationPlans();
        $websites = $this->getAgentWebsites($agentId);
        $data = [
            'agentHomepagePlans' => $agentHomepagePlans,
            'websites' => $websites
        ];
        return $this->successResponse('Success', $data);
    }

    /**
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * fetch group plans and websites
     */
    public function getGroupOptions()
    {
        $plan = new PlanDetails();
        $groupHomepagePlans = $plan->getHomepageConfigurationPlans();
        $websites = $this->getWebsites();
        $data = [
            'groupHomepagePlans' => $groupHomepagePlans,
            'websites' => $websites
        ];
        return $this->successResponse('Success', $data);
    }

    public function deleteSelectedHomepageConfiguration($request)
    {
        $ids = $request['ids'];
        if ($request['selected_all']) {
            $ids = $this->get_all_homepage_configuration_by_type($request['selected_user_type'],$request['selected_user_id']);
        }
        try {
            $count = count($ids);
            $this->model::query()->whereIn('id', $ids)->delete();
            return $this->successResponse($count . ' Homepage Configuration Plan Deleted.', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed to Delete.');
        }
    }

    protected function get_all_homepage_configuration_by_type($type, $id)
    {
        $query = $this->model::query()
            ->where('conf_for', '=', $type);
        if ($type == $this->model::CONFIGURATION_FOR_GROUP) {
            $query->where('group_id', '=', $id);
        }
        if ($type == $this->model::CONFIGURATION_FOR_AGENT) {
            $query->where('agent_id', '=', $id);
        }
        return $query->pluck('id')->toArray();
    }

    public function getHomepageConfigurationPlanList($request){
        $plan = new PlanDetails();
        $medicalTypesInAgent = AgentMedicalTypes::where('agent_id',$request->agent_id)->pluck('medical_type_id')->toArray();
        $getUserHomepagePlans = $plan->getConfiguredPlanIds($request->agent_id ? $request->agent_id : null,
        $request->website,
        $request->agent_id ? 'A' : ($request->group_id ? 'G' : null),
        $request->group_id ? $request->group_id : null);
        $planQuery = Plan::whereIn('pid', $getUserHomepagePlans);
        $plans = Plan::whereIn('medical_type_id', $medicalTypesInAgent)->get(['pid', 'carrier']);
        $pid = $plans->pluck('pid')->toArray();
        $carriers = $plans->pluck('carrier')->toArray();
        $includeCarrier = Plan::whereNotIn('carrier', $carriers)->pluck('carrier')->toArray();
        $getUserHomepagePlansNames = $planQuery->pluck('web_display_name');
        $getUserAllHomePagePlans = $plan->getUserAllHomePagePlans($getUserHomepagePlans, $getUserHomepagePlansNames, $request);
        return response()->json(
            new SuccessResource([
                'statusCode' => 200,
                'data' => $this->formatHomepageConfigPlans($getUserAllHomePagePlans,$plan,$includeCarrier,$pid),
                'message' => 'Homepage Configuration Plans has been Fetched.',
            ]),
            200
        ); 
    }

    protected function formatHomepageConfigPlans($plans,$plan_details,$includeCarrier,$pid){
        
        $data = [];
        $prudentialPlan = Plan::where('carrier', 80)->pluck('pid')->toArray();
        foreach ($plans as $key => $plan) {
            $category = $plan_details->dbToDisplayCategoryMapping($plan->pl_type);
            if(!empty($pid)){
                if($plan->pl_type == 'MM' && (in_array($plan->pid,$pid) || in_array($plan->carrier,$includeCarrier)) ){
                    $data[$category][$key]['is_prudential_plan'] = in_array($plan->pid, $prudentialPlan);
                    $data[$category][$key]['plan_id'] = $plan->pid;
                    $data[$category][$key]['plan_name'] = $plan->web_display_name;
                    $data[$category][$key]['plan_name_system'] = $plan->plan_name_system;
                    $data[$category][$key]['plan_type'] = $plan->pl_type;
                    $data[$category][$key]['plan_category'] = $category;
                    $data[$category][$key]['plan_pricing_type'] = $plan->plan_pricing_type == 2 ? 'age' : 'tier';
                    $logoPath = 'https://corenroll.com/biz_image.php?file=';
                    $description  = PlanTextDescription::where('pid', '=', $plan->pid)->get(['planLogo','header_text','short_text'])->first();
                    $data[$category][$key]['plan_logo'] = isset($description->planLogo) ? $logoPath . $description->planLogo : $logoPath . 'default.png';
                    $data[$category][$key]['plan_header_text'] = isset($description->header_text) ? $description->header_text : 'default-header-text';
                    $data[$category][$key]['plan_short_text'] = isset($description->short_text)  ? $description->short_text : 'default-short-text';
                    $data[$category][$key]['plan_price'] = $plan->planPricings;
                    $data[$category][$key]['plan_configured'] = (int)isset($plan->homepagePlan[0]);
                    $data[$category][$key]['is_featured'] = isset($plan->homepagePlan[0]) ? (int) ($plan->homepagePlan[0]['is_featured'] ?: 0) : 0 ;
                    $data[$category][$key]['config_id'] = isset($plan->homepagePlan[0]) ? $plan->homepagePlan[0]['id'] : '';
                    $key_values = array_column($data[$category], 'plan_configured');
                    array_multisort($key_values, SORT_DESC, $data[$category]);
                }elseif($plan->pl_type != 'MM'){
                    $data[$category][$key]['is_prudential_plan'] = in_array($plan->pid, $prudentialPlan);
                    $data[$category][$key]['plan_id'] = $plan->pid;
                    $data[$category][$key]['plan_name'] = $plan->web_display_name;
                    $data[$category][$key]['plan_name_system'] = $plan->plan_name_system;
                    $data[$category][$key]['plan_type'] = $plan->pl_type;
                    $data[$category][$key]['plan_category'] = $category;
                    $data[$category][$key]['plan_pricing_type'] = $plan->plan_pricing_type == 2 ? 'age' : 'tier';
                    $logoPath = 'https://corenroll.com/biz_image.php?file=';
                    $description  = PlanTextDescription::where('pid', '=', $plan->pid)->get(['planLogo','header_text','short_text'])->first();
                    $data[$category][$key]['plan_logo'] = isset($description->planLogo) ? $logoPath . $description->planLogo : $logoPath . 'default.png';
                    $data[$category][$key]['plan_header_text'] = isset($description->header_text) ? $description->header_text : 'default-header-text';
                    $data[$category][$key]['plan_short_text'] = isset($description->short_text)  ? $description->short_text : 'default-short-text';
                    $data[$category][$key]['plan_price'] = $plan->planPricings;
                    $data[$category][$key]['plan_configured'] = (int)isset($plan->homepagePlan[0]);
                    $data[$category][$key]['is_featured'] = isset($plan->homepagePlan[0]) ? (int) ($plan->homepagePlan[0]['is_featured'] ?: 0) : 0 ;
                    $data[$category][$key]['config_id'] = isset($plan->homepagePlan[0]) ? $plan->homepagePlan[0]['id'] : '';
                    $key_values = array_column($data[$category], 'plan_configured');
                    array_multisort($key_values, SORT_DESC, $data[$category]);
                }
            }else{
                $data[$category][$key]['is_prudential_plan'] = in_array($plan->pid, $prudentialPlan);
                $data[$category][$key]['plan_id'] = $plan->pid;
                $data[$category][$key]['plan_name'] = $plan->web_display_name;
                $data[$category][$key]['plan_name_system'] = $plan->plan_name_system;
                $data[$category][$key]['plan_type'] = $plan->pl_type;
                $data[$category][$key]['plan_category'] = $category;
                $data[$category][$key]['plan_pricing_type'] = $plan->plan_pricing_type == 2 ? 'age' : 'tier';
                $logoPath = 'https://corenroll.com/biz_image.php?file=';
                $description  = PlanTextDescription::where('pid', '=', $plan->pid)->get(['planLogo','header_text','short_text'])->first();
                $data[$category][$key]['plan_logo'] = isset($description->planLogo) ? $logoPath . $description->planLogo : $logoPath . 'default.png';
                $data[$category][$key]['plan_header_text'] = isset($description->header_text) ? $description->header_text : 'default-header-text';
                $data[$category][$key]['plan_short_text'] = isset($description->short_text)  ? $description->short_text : 'default-short-text';
                $data[$category][$key]['plan_price'] = $plan->planPricings;
                $data[$category][$key]['plan_configured'] = (int)isset($plan->homepagePlan[0]);
                $data[$category][$key]['is_featured'] = isset($plan->homepagePlan[0]) ? (int) ($plan->homepagePlan[0]['is_featured'] ?: 0) : 0 ;
                $data[$category][$key]['config_id'] = isset($plan->homepagePlan[0]) ? $plan->homepagePlan[0]['id'] : '';
                $key_values = array_column($data[$category], 'plan_configured');
                array_multisort($key_values, SORT_DESC, $data[$category]);
            }
        }
      return $data;
    }

    public function removeBulkHomepagePlans($request){
        try{
        $plan = new PlanDetails();
        $plan->removeConfiguredPlanIds($request->agent_id ? $request->agent_id : null,
                                $request->website,
                                $request->agent_id ? 'A' : ($request->group_id ? 'G' : null),
                                $request->group_id ? $request->group_id : null);
        return response()->json(
            new SuccessResource([
                'statusCode' => 200,
                'message' => 'Homepage Configuration Plans has been Deleted Succesfully.',
            ]),
            200
        ); 
    } catch (\Throwable $th) {
        return $this->failedResponse('Failed to Delete Homepage Configuration Plans.');
    }
    }
}
