<?php

namespace App\Repositories\MemberCard;

use App\Policy;
use App\Plan;
use App\PlanOverview;
use App\PolicyUpdate;
use App\PlanPolicyMember;
use App\TierUpdate;
use App\UserInfo;
use App\UserInfoPolicyAddress;
use App\Helpers\PolicyUpdateHelper;
use App\Helpers\DecryptEcryptHelper;
use App\Repositories\MemberResource\MemberResourceFeatures;
use App\Helpers\GuzzleHelper;
use App\Helpers\UplineAgentsEmail;
use App\PlanPolicy;
use Illuminate\Support\Facades\Log;


class MemberCardRepository
{
    protected $member = '';

    public function sendMemberCardEmail($date, $type)
    {
        try {
            $yesterday = date('Y-m-d',strtotime('-1 days'));
            if(empty($type)){
                $policies = PlanOverview::where('effective_date',$date)->whereNotIn('pid', [1886, 1885])->pluck('policy_id')->unique();
                // fetch only data from plan policy member where created_at date is after feb 20 2024 and member_id not null and email_send is 0 and
                $planPolicyMember = PlanPolicyMember::where('email_sent',null)->whereDate('created_at','>', date('Y-m-d', strtotime('2024-02-25')))->where('is_updateMemberCard','0')->pluck('policy_id');
                $policies = $policies->merge($planPolicyMember)->unique();
                $policies_email_not_sent = PlanPolicy::where('is_email_sent','N')->pluck('policy_num')->unique();
                $policies = $policies->merge($policies_email_not_sent)->unique();
                foreach($policies as $policy) {
                    self::fetchPlanPolicies($policy, $date);
                }
            } else {
                $policies = PlanPolicyMember::where('is_updateMemberCard', 1)
                    ->where(function ($query) {
                        $query->where('email_sent', '!=', 'Y')
                            ->orWhereNull('email_sent');
                    })
                    ->whereDate('created_at', '>=', date('Y-m-d', strtotime('2024-02-27')))
                    ->select('policy_id', 'pid','p_ai')
                    ->get();

                    foreach ($policies as $policy) {
                        $policyId = $policy->policy_id;
                        if (isset($mergedRecords[$policyId])) {
                            $mergedRecords[$policyId]['pid'][] = $policy->pid;
                            $mergedRecords[$policyId]['p_ai'][] = $policy->p_ai;
                        } else {
                            $mergedRecords[$policyId] = [
                                'policy_id' => $policyId,
                                'pid' => [$policy->pid],
                                'p_ai' => [$policy->p_ai]
                            ];
                        }
                    }

                    $mergedPolicies = array_values($mergedRecords);
                foreach($mergedPolicies as $policy) {
                    self::fetchPlanPolicies($policy['policy_id'], $date, $type, $policy['p_ai']);
                }
            }
        } catch (Exception $e) {
            $msg = $e->getMessage(). ' ' . $e->getFile() . ' ' . $e->getLine();
            return ['error' => $msg];
        }
    }

    public function sendEmailWithMessageCenter($data)
    {
        $ccAddress = UplineAgentsEmail::addUplineCcAddress($data['agent_email'], $data['agent_id']);
        $messageCenterBaseUrl = config('app.messagecenter.key') ?? 'https://qa-api-msg.purenroll.com/';
        $url = $messageCenterBaseUrl . "api/v1/send-email-with-content-data";
        $requestData = [
            'email_message_configuration_name' => 'MEMBER_CARD_CONFIGURATION_V2',
            'toAddress' => [$data['email']],
            'ccAddress' => $ccAddress, //[$data['agent_email']]
            'bccAddress' => [],
            'subject' => $data['subject'],
            'attachedFiles' => [],
            'generalTemplateData' => [],
            'contentData' => [
                'member' => $data['member_name'],
                'cards' => $data['cards'],
            ]
        ];
        $responseJson = GuzzleHelper::postApi($url, [], $requestData);
        $response = json_decode($responseJson, true);
        sleep(1);
        if (isset($response['status_code']) && $response['status_code'] == '200' && $response['status'] == 'success') {
            return true;
        } else {
            return false;
        }
    }

    public function fetchPlanPolicies($policy, $date, $type = null, $PIA = null) {
        $extraHealthPlan = true;
        $policyDetails = UserInfoPolicyAddress::where('policy_id', $policy)->select('policy_id','userid','agent_email','cemail','cfname','cmname','clname','effective_date')->first();
        if(!$policyDetails instanceof UserInfoPolicyAddress) {
            Log::channel('memberCard')->info(PHP_EOL ."Policy Detail Not Found for Policy Id" . $policy . ".");
            return;
        }
        $member = UserInfo::where('userid',$policyDetails->userid)->first();
        $t_array = array();
        if ($member instanceof UserInfo) {
            $this->member = $member;
            foreach ($member->getPolicyList as $singlePolicy) {
                if ($policy != $singlePolicy->policy_id) {
                    continue;
                }
                $plans = $singlePolicy->getPolicyPlan->filter(function ($plan) use ($date, $type) {
                    if (empty($type)) {
                        return $plan->peffective_date == $date || $plan->peffective_date < $date;
                    } else {
                        return true;
                    }
                });
                $memberResource = new MemberResourceFeatures();
                $t_array = $memberResource->getPlanCards($plans, $singlePolicy, $t_array, $member);
            }
        }
        $tempArray = array();
        $uniqueData = array();
        foreach ($t_array as $val) {
            if (isset($val['filename'])) {
                if (in_array($val['filename'], $tempArray) || empty($val)) {
                    continue;
                }
                array_push($tempArray, $val['filename']);
                array_push($uniqueData, $val);
            }
        }
        $t_array = $uniqueData;
        if(!empty($t_array)){
            $p_ai_values = array_unique(array_column($t_array, 'p_ai'));
            $availablePlanCards = null;
                if($type) {
                    $plan_policy_member = PlanPolicyMember::whereIn('p_ai', $p_ai_values)->where('policy_id',$policy)->where('is_updateMemberCard','1')->where(function ($query) {
                        $query->where('email_sent', 'N')
                              ->orWhereNull('email_sent');
                    })->orderBy('id','DESC')->get();
                } else {
                    $plan_policy_member = PlanPolicyMember::whereIn('p_ai', $p_ai_values)->where('policy_id',$policy)->where('is_updateMemberCard','0')->where(function ($query) {
                        $query->where('email_sent', 'N')
                              ->orWhereNull('email_sent');
                    })->orderBy('id','DESC')->get();
                }
                $check_sent_email = PlanPolicyMember::whereIn('p_ai', $p_ai_values)->where('policy_id',$policy)->where('is_updateMemberCard','0')->where(function ($query) {
                    $query->where('email_sent', 'Y')
                        ->whereNotNull('email_sent_at');
                })->orderBy('id','DESC')->get();
                if(!($plan_policy_member->isEmpty())){
                    foreach($plan_policy_member as $ppm){
                        if($ppm) {
                            if((in_array($ppm->cid, ["11", "77", "59", "65", "67", "47"])) && empty($ppm->member_id)){
                                $plan_policy_member = [];
                                $availablePlanCards = null;
                            };
                            if(!in_array($ppm->cid, ["11", "77", "59", "65", "67", "47"]) || (in_array($ppm->cid, ["11", "77", "59", "65", "67", "47"])) && isset($ppm->member_id)) {
                                if(isset($type) && $policyDetails->effective_date <= date('2024-03-01')) {
                                    foreach ($t_array as $key => $item) {
                                        if(in_array($item['p_ai'],$p_ai_values)){
                                            $availablePlanCards[$key] = $item['p_ai'];
                                        }
                                    }
                                } elseif(isset($type) && self::checkFirstEmail($policy) || empty($type)) {
                                    foreach ($t_array as $key => $item) {
                                        if(in_array($item['p_ai'],$p_ai_values)){
                                            $availablePlanCards[$key] = $item['p_ai'];
                                        }
                                    }
                                } else {
                                    PlanPolicyMember::where('id',$ppm['id'])->update(['email_sent' => 'N']);
                                    Log::channel('memberCard')->info(PHP_EOL ."Member Card Absent in plan_policies_member for Policy Id " . $policyDetails->policy_id . ".");
                                }
                            }
                            else {
                                PlanPolicyMember::where('id',$ppm['id'])->update(['email_sent' => 'N']);
                                PlanPolicy::where('policy_num',$policy)->update(['is_email_sent' => 'N']);
                                Log::channel('memberCard')->info(PHP_EOL ."Member Card Absent in plan_policies_member for Policy Id " . $policyDetails->policy_id . ".");
                            }
                        }
                    }
                } elseif($plan_policy_member->isEmpty() && $check_sent_email->isEmpty()) {
                    if(isset($type) && self::checkFirstEmail($policy) || empty($type)) {
                        foreach ($t_array as $key => $item) {
                            if(in_array($item['p_ai'],$p_ai_values)){
                                $availablePlanCards[$key] = $item['p_ai'];
                            }
                        }
                    }
                }
                else {
                    $pai_values = implode(" ",$p_ai_values);
                    Log::channel('memberCard')->info(PHP_EOL ."Member Detail Not Found in plan_policies_member for Policy Id " . $policyDetails->policy_id . " and p_ai " . $pai_values . ".");
                }
                if($availablePlanCards){
                    $availablePlanCards = array_unique($availablePlanCards);
                    $filteredArray = array_filter($t_array, function($item) use ($availablePlanCards) {
                        return in_array($item['p_ai'], $availablePlanCards);
                    });
                    if($type) {
                        $filteredArray = array_filter($filteredArray, function($item) use ($p_ai_values) {
                            return in_array($item['p_ai'], $p_ai_values);
                        });
                    }
                    $cards = "";
                    foreach($filteredArray as $list) {
                        $cards .= "<li><a href=\"" . $list['url'] . "\" target=\"_blank\" rel=\"noopener noreferrer\">" . $list['filename'] . "</a></li>";
                    }
                    $userEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policyDetails->cemail;
                    $agentEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policyDetails->agent_email;
                    $member_name = $policyDetails->getFullNameAttribute();
                    if(self::checkFirstEmail($policy)) {
                        $subject = "New Digital Documents Available ";
                    } else {
                        $subject = "Digital Documents Available ";
                    }
                    $agentID = Policy::where('policy_id', '=', $policyDetails->policy_id)->value('p_agent_num');
                    $data = [
                        'email' => $userEmail,
                        'agent_email' => $agentEmail,
                        'member_name' => $member_name,
                        'cards' => $cards,
                        'subject' => $subject,
                        'agent_id' => $agentID,
                    ];
                    // $extraHealthPlan = $this->checkExtraHealthPlan($policyDetails->policy_id);
                    // if($extraHealthPlan) {
                        // }
                    $mailStatus = $this->sendEmailWithMessageCenter($data);
                    if($mailStatus) {
                        $today = date('Y-m-d');
                        $plan_policy_member = PlanPolicyMember::where('policy_id',$policy)->update(['email_sent' => 'Y','email_sent_at' => $today]);
                        PlanPolicy::where('policy_num',$policy)->update(['is_email_sent' => 'Y']);
                        if (empty($type)) {
                            $time = time();
                            $policyUpdateData = [
                                'elgb_act' => "MEMCARD",
                                'elgb_act_date' => $time,
                                'elgb_comment' => "Member Card Send Successfully.",
                                'elgb_file_date' => $today,
                                'elgb_term_date' => null,
                                'elgb_policyid' => $policy,
                                'elgb_agent' => 'auto',
                            ];
                            PolicyUpdateHelper::updateEligibility($policyUpdateData);
                        }
                        Log::channel('memberCard')->info(PHP_EOL ."Member Card Send Successfully for Policy Id " . $policyDetails->policy_id . ".");
                    } else {
                        PlanPolicyMember::where('policy_id',$policy)->update(['email_sent' => 'N']);
                        PlanPolicy::where('policy_num',$policy)->update(['is_email_sent' => 'N']);
                        Log::channel('memberCard')->info(PHP_EOL ."Mail Sending Failed for Policy Id " . $policyDetails->policy_id . ".");
                    }
                }

        }
        return true;
    }

    public static function checkFirstEmail($policy) {
        $fetchPolicy = PolicyUpdate::where('elgb_policyid',$policy)->where('elgb_act','MEMCARD')->first();
        if(isset($fetchPolicy)){
            return true;
        } else {
            return false;
        }
    }

    public static function checkExtraHealthPlan($policy) {
        $fetchPolicy = PlanOverview::where('policy_id', $policy)
            ->whereIn('pid', [1886, 1885])
            ->exists();
        return !$fetchPolicy;
    }
}
