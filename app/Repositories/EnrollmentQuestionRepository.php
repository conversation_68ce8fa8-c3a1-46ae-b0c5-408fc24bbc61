<?php


namespace App\Repositories;


use App\EnrollmentQuestion;
use App\Helpers\DecryptEcryptHelper;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EnrollmentQuestionRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var EnrollmentQuestion
     */
    private $model;

    /**
     * EnrollmentQuestionRepository constructor.
     * @param EnrollmentQuestion $model
     */
    public function __construct(EnrollmentQuestion $model)
    {
        $this->model = $model;
    }


    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }


    public function paginatedFormattedList($limit, $filters = [])
    {
        $query = $this->paginatedList($limit, $filters)
            ->appends($filters);
        $data = $this->formattedData($query);
        return $this->successResponse('Success', $data);

    }

    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->orderBy('qid', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }

    public function show($qId)
    {
        $query = $this->getById($qId);
        if (!$query) return $this->failedResponse('No Enrollment Question found.', 404);
        $data = $this->singleFormattedItem($query);
        return $this->successResponse('Success', $data);
    }


    protected function filterContent($data, $filters = [])
    {

        if (isset($filters['question'])) {
            $data->where('question', 'LIKE', '%' . $filters['question'] . '%');
        }

        if (isset($filters['true_condition'])) {
            $data->where('true_condition', '=', $filters['true_condition']);
        }

        if (isset($filters['status'])) {
            $data->where('status', '=', $filters['status']);
        }

        if (isset($filters['deleted'])) {
            $data->where('deleted', '=', $filters['deleted']);
        }

        if (isset($filters['withoutStatus'])) {
            $data->where('status', '!=', $filters['withoutStatus']);
        }
    }

    public function singleFormattedItem($d)
    {
        return [
            'qId' => $d->qid,
            'question' => $d->question,
            'errorMessage' => $d->error_message,
            'trueCondition' => $d->true_condition,
            'acceptanceCondition' => array_search($d->true_condition, EnrollmentQuestion::$trueConditions),
            'status' => $d->status,
            'formattedStatus' => array_search($d->status, EnrollmentQuestion::$statuses),
            'deleted' => $d->deleted,
        ];

    }

    public function formattedItems($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return $result;
    }

    public function formattedData($data)
    {
        return [
            'data' => $this->formattedItems($data),
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    protected function formattedFormData($data)
    {
        return [
            'question' => $data['question'],
            'error_message' => $data['errorMessage'],
            'status' => $data['status'] ? 1 : 0,
            'true_condition' => $data['trueCondition'] == '1' ? 1 : 0,
        ];
    }

    public function create($data)
    {
        $data = $this->formattedFormData($data);
        $data['addedon'] = time();
        try {
            $this->model::create($data);
            return $this->successResponse('Enrollment Question Created.', [], 201);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Create.');
        }
    }

    public function update($data, $id)
    {
        $model = $this->getById($id);
        $data = $this->formattedFormData($data);
        try {
            $model->update($data);
            return $this->successResponse('Enrollment Question Updated.', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Update.');
        }
    }

    public function delete($id)
    {
        $model = $this->getById($id);
        if (!$model) return $this->failedResponse('No Enrollment Question found.');
        try {
            $model->update([
                'deleted' => 1
            ]);
            return $this->successResponse('Enrollment Question Deleted.', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Delete.');
        }
    }

}
