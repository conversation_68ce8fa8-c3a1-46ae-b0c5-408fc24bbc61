<?php

namespace App\Repositories;

use App\PolicyNote;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

class PolicyEnrollmentNotes extends Model
{

    function viewPolicyEnrollmentNotes($request)
    {
        if($request->type == 'a'){
            $policyNote = PolicyNote::where('agent_id',$request->id)->get();
        } 
        if($request->type == 'g'){
            $policyNote = PolicyNote::where('gid',$request->id)->get();
        }
        if($request->type == 'c'){
            $policyNote = PolicyNote::where('policy_id',$request->id)->get();
        }

        return $policyNote;
    }
    function insertPolicyEnrollmentNotes($request)
    {
        try{
            $policyNote = new PolicyNote;
            $policyNote->ants = Carbon::now()->timestamp;
            $policyNote->anuser = $request->anuser;
            if($request->agent_id){
                $policyNote->agent_id = $request->agent_id;        
            }else{
                $policyNote->agent_id = '';
            }
            if($request->gid){
                $policyNote->gid = $request->gid;        
            }else{
                $policyNote->gid = '0';
            }    
            $policyNote->policy_id = $request->policy_id;
            $policyNote->annote = $request->annote;
            $policyNote->status = $request->status;
            $saved = $policyNote->save();
            if($policyNote->anID){
                return array('type' => 'success', 'message' =>'Enrollment Notes Added.');
            }
            else{
                return array('type' => 'error', 'message' =>'Enrollment Notes not Added.');
            }            
        }catch (Exception $e) {
            $message = 'Failed To add Enrollment Notes for Policy: ';
            return ['error' => $e->getMessage()];
        }
    }
    function editPolicyEnrollmentNotes($request)
    {
        try{
            $policyNote['ants'] = Carbon::now()->timestamp;
            $policyNote['annote'] = $request->annote;
            $policyNote['status'] = $request->status;
            $updated = PolicyNote::where('anID',$request->anID)->update($policyNote);
            if ($updated) {
                return array('type' => 'success', 'message' =>'Enrollment Notes Updated.');
            }
            else{
                return array('type' => 'error', 'message' =>'Enrollment Notes not Updated.');
            }        
        }catch (Exception $e) {
            $message = 'Failed To edit Enrollment Notes for Policy: ';
            return ['error' => $e->getMessage()];
        }
    }
    function deletePolicyEnrollmentNotes($request)
    {
        try{
            $deletedRows = PolicyNote::where('anID',$request->anID)->delete();
            if ($deletedRows > 0) {
                return array('type' => 'success', 'message' =>'Enrollment Note Deleted.');
            }
            else{
                return array('type' => 'error', 'message' =>'Enrollment Notes not Deleted.');
            }        
        }catch (Exception $e) {
            $message = 'Failed To edit Enrollment Notes for Policy: ';
            return ['error' => $e->getMessage()];
        }
    }
}