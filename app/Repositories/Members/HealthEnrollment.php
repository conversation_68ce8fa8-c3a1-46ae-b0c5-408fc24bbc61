<?php

namespace App\Repositories\Members;

use App\Plan;
use App\Policy;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use App\PlanOverview;
use App\PaymentCC;
use App\PaymentEft;
use App\UserInfoDepMedBex;
use App\DependentInPolicy;
use App\MedMedication;
use Illuminate\Database\Eloquent\Model;

class HealthEnrollment extends Model
{
    use ResponseMessage;

    public function getHealthEnrollment($request)
    {
        $perPage = !empty($request->perpage) ? $request->perpage : 15;
        $offset = !empty($request->next) ? trim($request->next) : 0;
        $policies = UserInfoPolicyAddress::with('healthAnswers')
            ->with('plans')
            ->where(function ($query) use ($request) {
                if ($request->effective_date) {
                    $query->where('effective_date', $request->effective_date);
                }
                if ($request->from_date && $request->to_date) {
                    $fromDate = strtotime($request->from_date);
                    $toDate = strtotime($request->to_date);
                    $query->whereBetween('edate', [$fromDate, $toDate]);
                }
            })->groupBy('policy_id')->orderBy('edate', 'DESC')->skip($offset)->take($perPage)->get();
        $totalRows = count($policies);
        $nextPage = 0;
        if (count($policies) > 0) {
            $nextPage = $offset + $perPage;
        }
        return array('type' => 'success', 'response' => $policies, 'totalrecs' => $totalRows, 'nextpage' => $nextPage);
    }

    public function getHealthDetail($request)
    {
        $policyData = PlanOverview::where('policy_id', $request->input('policy_id'))
            ->get();
        $userData = UserInfoPolicyAddress::where('policy_id', $request->input('policy_id'))
            ->first();
        $paymentType = $userData->payment_type;
        $paymentId = $userData->payment_id;
        $recurringId = $userData->recurring_payment_id;
        $recurringType = $userData->recurring_payment_type;
        $paymentDetail = self::getPaymentDetail($paymentType, $recurringType, $paymentId, $recurringId);
        $userId = $userData->userid;
        $personalQuestion = self::getQuestionDetail($userId);
        $dependentData = self::getDependentDetail($request->input('policy_id'));
        return array('type' => 'success', 'policyData' => $policyData, 'userData' => $userData, 'paymentDetail' => $paymentDetail,
            'personalQuestion' => $personalQuestion, 'dependentData' => $dependentData);
    }

    public function getPaymentDetail($paymentType, $recurringType, $paymentId, $recurringId)
    {
        if ($paymentType == 'cc') {
            $paymentData = PaymentCC::where('creditcard_id', $paymentId)
                ->get();
        } elseif ($paymentType == 'eft') {
            $paymentData = PaymentEft::where('bank_id', $paymentId)
                ->get();
        } else {
            $paymentData = '';
        }
        if ($recurringType == 'cc') {
            $recurringData = PaymentCC::where('creditcard_id', $recurringId)
                ->get();
        } elseif ($recurringType == 'eft') {
            $recurringData = PaymentEft::where('bank_id', $recurringId)
                ->get();
        } else {
            $recurringData = '';
        }
        return array('paymentData' => $paymentData, 'recurringData' => $recurringData);
    }

    public function getQuestionDetail($userId)
    {
        $questionData = UserInfoDepMedBex::with('getQuestion')
            ->with('getMedCondition')
            ->where('user_id', $userId)
            ->whereNull('dependent_id')
            ->get();
        $medMedication = MedMedication::where('user_id', $userId)->get();
        return array('questionData' => $questionData, 'usermedication' => $medMedication);
    }

    public function getDependentDetail($policyId)
    {
        $data = [];
        $dependents = DependentInPolicy::where('policy_id', $policyId)->get();
        if ($dependents) {
            foreach ($dependents as $key => $dep) {
                $data[$key] = $dep;
                $questions = UserInfoDepMedBex::with('getQuestion')
                    ->with('getMedCondition')
                    ->where('dependent_id', $dep->dependent_id)
                    ->whereNull('user_id')
                    ->get();
                $depMedication = MedMedication::where('dependent_id', $dep->dependent_id)->get();
                $data[$key]['depQuestions'] = $questions;
                $data[$key]['depMedication'] = $depMedication;
            }
        }
        return $data;
    }




}
