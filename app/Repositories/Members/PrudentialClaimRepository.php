<?php
namespace App\Repositories\Members;

use App\BundelPlan;
use App\ClaimFile;
use App\Plan;
use App\PlanOverview;
use App\Policy;
use App\PrudentialClaim;
use App\Traits\FileUpload\FileUpload;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;
use App\Traits\Paginator;
use App\UserInfoPolicyAddress;
use Illuminate\Support\Collection;
use DB;
use App\AdminUser;
use App\AgentInfo;



class PrudentialClaimRepository
{
    use FileUpload, Paginator;
    
    public function getAllMembersWithSpecialPlans($query = null, $limit = 10, $date = null)
    {
        $planTypes = ['ACCIDENT', 'CRITICAL', 'HOSPITAL', 'LIFE'];
        
        $relevantPlans = PlanOverview::select('policy_id', 'pid')
            ->where(function($query) use ($planTypes) {
                $query->where(function($q) use ($planTypes) {
                    $q->whereIn('pl_type', $planTypes)
                      ->where('cid', PrudentialClaim::PRUDENTIAL_CARRIER_ID);
                })->orWhere('7k_enable', 1);
            })
            ->get();

        $policyIds = $relevantPlans->pluck('policy_id')->unique();
        $planIds = $relevantPlans->pluck('pid')->unique();

        $bundlePlanIds = BundelPlan::whereIn('bundle_pid', $planIds)
            ->select('pid')
            ->get()
            ->pluck('pid')
            ->unique();

        $membersQuery = UserInfoPolicyAddress::with(['plans' => function($q) use ($planTypes, $bundlePlanIds) {
            $q->select('pid', 'policy_id', 'pl_type', '7k_enable', 'web_display_name')
              ->where(function($inner) use ($planTypes, $bundlePlanIds) {
                $inner->whereIn('pl_type', $planTypes)
                      ->orWhere('7k_enable', 1)
                      ->orWhereIn('pid', $bundlePlanIds);
            });
        }])
        ->select('userid', 'cfname', 'cmname', 'clname', 'cemail', 'policy_id')
        ->whereIn('policy_id', $policyIds)
        ->where(function($q) use ($query) {
            $searchTerm = '%' . $query . '%';
            $q->where('cfname', 'LIKE', $searchTerm)
              ->orWhere('cmname', 'LIKE', $searchTerm)
              ->orWhere('clname', 'LIKE', $searchTerm);
        });

        $members = $membersQuery->take($limit)->get();

        $bundlePlanIdsArray = $bundlePlanIds->toArray();
        return $members->map(function($member) use ($bundlePlanIdsArray) {
            $plans = $member->plans->map(function($plan) use ($bundlePlanIdsArray) {
                $planLabel = in_array($plan->pid, $bundlePlanIdsArray)
                    ? ($plan->{'7k_enable'} ? '7K Enabled (Bundled)' : 'Prudential (Bundled)')
                    : ($plan->{'7k_enable'} ? '7K Enabled' : 'Prudential');

                return [
                    'plan_id' => $plan->pid,
                    'plan_org_name' => $plan->web_display_name,
                    'plan_name' => $plan->web_display_name . '-' . $planLabel,
                    'plan_type' => $planLabel
                ];
            })->unique('plan_id')->values();

            return [
                'member_id' => $member->userid,
                'first_name' => $member->cfname,
                'middle_name' => $member->cmname,
                'last_name' => $member->clname,
                'email' => $member->cemail,
                'policy_id' => $member->policy_id,
                'plans' =>  $plans->unique('plan_id')->values()->all(),
            ];
        });
    }

    private function checkIsPrudentialOr7kEnabled($policy_id)
    {
            $planTypes = ['ACCIDENT', 'CRITICAL', 'HOSPITAL', 'LIFE'];

            $prudentialPlans = PlanOverview::where('policy_id', $policy_id)
            ->whereIn('pl_type', $planTypes)
            ->where('cid', PrudentialClaim::PRUDENTIAL_CARRIER_ID);

            $sevenKPlans = PlanOverview::where('policy_id', $policy_id)
            ->where('7k_enable', 1);

        $prudentialOr7k = $prudentialPlans->union($sevenKPlans)
            ->exists();
            if($prudentialOr7k){
                return $prudentialOr7k;
            }
            else{
                $planIds = PlanOverview::where('policy_id',$policy_id)->pluck('pid')->toArray();
                $bundlePlanIds =  BundelPlan::whereIn('bundle_pid',  $planIds)->pluck('pid')->toArray();

                $prudentialPlansBundled = Plan::whereIn('pid', $bundlePlanIds)
                ->whereIn('pl_type', $planTypes)
                ->where('carrier', PrudentialClaim::PRUDENTIAL_CARRIER_ID);

                $sevenKPlansBundled = Plan::whereIn('pid', $bundlePlanIds)
                ->where('7k_enable', 1);

                return $prudentialPlansBundled->union($sevenKPlansBundled)
                ->exists();
            }
    }

    public function getPrudentialList($policy_id){
        $prudentialClaim = PrudentialClaim::with([
            'files',
            'policy.getMember' => function ($query) {
                $query->select(['userid', 'cfname', 'cmname', 'clname', 'cemail']);
            },
            'policy' => function($query) {
                $query->select(['policy_id', 'policy_userid']);
            }
        ])
        ->where('policy_id', $policy_id)
        ->orderBy('created_at', 'desc')
        ->get();

        $prudentialPlanNames = $prudentialClaim->map(function ($claim) {
            $planData = PlanOverview::where('policy_id', $claim->policy_id)
                ->where('pid', $claim->pid)
                ->select(['web_display_name','cid','7k_enable'])
                ->first();
                if($planData){
                    $planNameLabel = $planData->{'7k_enable'} ? '7K Enabled' : 'Prudential';
                    $claim->plan_name = $planData->web_display_name . ' (' . $planNameLabel . ')';
                }
                if($claim->description === null){
                    $claim->description = '';
                }
            return $claim;
        });

        return $prudentialPlanNames;
    }
    public function getPrudentialAllList($data,$filters)
    {
        $perPage = $data['per_page'] ?? 20; 
        $page = $data['page'] ?? 1; // Use the 'page' parameter or default to 1
        
        $data = $this->paginatedList($perPage, $filters, $page);
        
        return $this->formattedData($data);
    }
    
    public function paginatedList($limit, $filters = [], $page = 1)
    {
        $query = $this->getQueries($filters);
        return $query->paginate($limit, ['*'], 'page', $page)->appends($filters);
    }
    
    public function getQueries($filters)
    {
        $query = PrudentialClaim::with([
            'files',
            'policy.getMember' => function ($query) {
                $query->select(['userid', 'cfname', 'cmname', 'clname', 'cemail']);
            },
            'policy' => function($query) {
                $query->select(['policy_id', 'policy_userid']);
            },
            'planOverview' => function ($query) {
                $query->select('pid', 'web_display_name', 'cid', '7k_enable');
            }
        ])
        ->orderBy('created_at', 'desc');
        
        $this->filterContent($query, $filters);
        
        return $query;
    }
    
    protected function filterContent($query, $filters = [])
    {
        if (isset($filters['member_name'])) {
            $query->whereHas('policy.getMember', function($q) use ($filters) {
                $q->where(function($subQ) use ($filters) {
                    $searchTerm = '%' . $filters['member_name'] . '%';
                    $subQ->where('cfname', 'LIKE', $searchTerm)
                         ->orWhere('clname', 'LIKE', $searchTerm)
                         ->orWhere('cmname', 'LIKE', $searchTerm)
                         ->orWhereRaw("CONCAT(cfname, ' ', clname) LIKE ?", [$searchTerm]);
                });
            });
        }
        if (isset($filters['member_email'])) {
            $query->whereHas('policy.getMember', function($q) use ($filters) {
                $q->where('cemail', 'LIKE', '%' . $filters['member_email'] . '%');
            });
        }

        if (isset($filters['created_date'])) {
            $query->where('created_at', 'LIKE', '%' . $filters['created_date'] . '%');
        }

        if (isset($filters['updated_date'])) {
            $query->where('updated_at', 'LIKE', '%' . $filters['updated_date'] . '%');
        }

        if (isset($filters['plan_ids']) && count($filters['plan_ids'])) {
            $query->whereIn('pid', $filters['plan_ids']);
        }

    }

    public function getPrudentialPlanList($request)
    {

        $search = $request['data'] ?? '';
        $limit = $request['limit'] ?? 100;
        
        $planids = PrudentialClaim::pluck('pid')->unique()->toArray();
        $plans = PlanOverview::select('pid', 'plan_name_system','web_display_name','7k_enable')
            ->whereIn('pid', $planids)
            ->where('web_display_name', 'like', "%{$search}%")
            ->limit($limit)
            ->distinct()
            ->get();

        $plans = $plans->map(function ($plan) {
            $plan->label = $plan->{'7k_enable'} ? '7K Enabled' : 'Prudential';
            return $plan;
        });
        
        return $plans;
            
    }
    
    protected function formattedData($data)
    {
        $data->getCollection()->map(function ($claim) {
            
            if($claim->planOverview){
                $planNameLabel = $claim->planOverview->{'7k_enable'} ? '7K Enabled' : 'Prudential';
                $claim->plan_name = $claim->planOverview->web_display_name . ' (' . $planNameLabel . ')';
            }
            
            $claim->description = $claim->description ?? '';
            $claim->submitted_by_name = $claim->submitted_id ? $this->formatUserName($claim->submitted_by, $claim->submitted_id, $claim->policy) : $claim->submitted_by;
            $claim->created_by_name = $claim->claim_by ? $this->formatUserName($claim->claim_by_type, $claim->claim_by, $claim->policy) : $claim->claim_by_type;
            return $claim;
        });
    
        return [
                'data' => $data->items(),  
                'links' => $this->links($data),  
                'meta' => $this->meta($data)    
        ];
    }
    
    private function formatUserName($type, $id, $policy = null)
    {
        switch ($type) {
            case 'ADMIN':
                $admin = AdminUser::find($id);
                return $admin ? 'ADMIN (' . $admin->name . ')' : 'ADMIN';
            case 'REP':
                $info = AgentInfo::find($id);
                return $info ? 'REP (' . $info->agent_fname . ' ' . $info->agent_lname.')' : 'REP';
            case 'MEMBER':
                return $policy->getMember ? 'MEMBER (' . $policy->getMember->cfname . ' ' . $policy->getMember->clname.')' : 'MEMBER';
            default:
                return '';
        }
    }

    public function plansByPolicy($policy_id)
    {
        $plansDetails = [];
        $planTypes = ['ACCIDENT', 'CRITICAL', 'HOSPITAL', 'LIFE'];

        $prudentialPlans = PlanOverview::where('policy_id', $policy_id)
            ->whereIn('pl_type', $planTypes)
            ->where('cid', PrudentialClaim::PRUDENTIAL_CARRIER_ID);

        $sevenKPlans = PlanOverview::where('policy_id', $policy_id)
            ->where('7k_enable', 1);

            $allPlanList = $prudentialPlans->union($sevenKPlans)
            ->orderBy('peffective_date')
            ->get();
            if ($allPlanList->isEmpty()) {
                $planIds = PlanOverview::where('policy_id', '=', $policy_id)->pluck('pid')->toArray();
                $bundledPlanIds = BundelPlan::whereIn('bundle_pid', $planIds)->pluck('pid')->toArray();
                if (!empty($bundledPlanIds)) {
                    $bundledPlanDetails = PlanOverview::whereIn('pid', $bundledPlanIds)->get();
                    $allPlanList = $allPlanList->merge($bundledPlanDetails);
                }
            }

        foreach ($allPlanList as $plans) {
            $bundledPlanPids = BundelPlan::where('bundle_pid', $plans->pid)->pluck('pid')->toArray();
            if (count($bundledPlanPids) > 0) {
                $prudentialPlansBundled = Plan::whereIn('pid', $bundledPlanPids)
                    ->whereIn('pl_type', $planTypes)
                    ->where('carrier', PrudentialClaim::PRUDENTIAL_CARRIER_ID)
                    ->get();
                $sevenKPlansBundled = Plan::whereIn('pid', $bundledPlanPids)
                    ->where('7k_enable', 1)
                    ->get();
                $allPlanListBundled = $prudentialPlansBundled->merge($sevenKPlansBundled)
                    ->sortBy('peffective_date');
                foreach ($allPlanListBundled as $actualPlan) {
                    $planNameLabel = $actualPlan->{'7k_enable'} ? '7K Enabled' : 'Prudential';
                    $plansDetails[] = [
                        'policy_id' => $policy_id,
                        'plan_id' => $actualPlan->pid,
                        'plan_name' => $actualPlan->web_display_name . ' (' . $planNameLabel . ')',
                    ];
                }
            } else {
                $planNameLabel = $plans->{'7k_enable'} ? '7K Enabled' : 'Prudential';
                $plansDetails[] = [
                    'policy_id' => $policy_id,
                    'plan_id' => $plans->pid,
                    'plan_name' => $plans->web_display_name . ' (' . $planNameLabel . ')',
                ];
            }
        }
    
        return $plansDetails;
    }

    public function getPrudentialForm($policy_id)
    {
        $planTypes = ['ACCIDENT', 'CRITICAL', 'HOSPITAL', 'LIFE'];
        $prudentialPlans = PlanOverview::where('policy_id', $policy_id)
        ->whereIn('pl_type', $planTypes)
        ->where('cid', PrudentialClaim::PRUDENTIAL_CARRIER_ID);

        $sevenKPlans = PlanOverview::where('policy_id', $policy_id)
            ->where('7k_enable', 1);

        $allPlanList = $prudentialPlans->union($sevenKPlans)
            ->orderBy('peffective_date')
            ->get();

        $data = [];

        foreach ($allPlanList as $plans) {
            $plan = Plan::find($plans->pid);
            if ($plan->welcomeletterfile != null && $plan->carrier = PrudentialClaim::PRUDENTIAL_CARRIER_ID) {
                $data[] = $this->formataData('Resources', $plan->welcomeletterfilename, $this->formatNueraURL($plan->welcomeletterfile, $plans->policy_id), $plans->p_ai);
            }
        }

        return $data;
    }

    public function formataData($header, $filename, $url, $p_ai = null)
    {
        $data = [];
        if ($filename != '') {
            $data['header'] = $header;
            $data['url'] = $url;
            $data['filename'] = $filename;
            $data['p_ai'] = $p_ai;
        }
        return $data;
    }

    public function formatNueraURL($cardname, $policyId)
    {
        $base_url = 'https://nuerabenefits.com/_pdfdocs/';
        return $base_url . $cardname . '?id=' . base64_encode($policyId);
    }

    public function create($request)
    {
        $user_id = PlanOverview::where('policy_id', $request['policy_id'])->select('userid')->first()->userid;
        $files = isset($request['files']) ? $request['files'] : null;
            $prudentialClaim = PrudentialClaim::create([
                "claim_for" => $user_id,
                "claim_by" => request()->header('id'),
                "claim_by_type" => $request['claim_type'],
                "policy_id" => $request['policy_id'],
                "pid" => $request['plan_id'],
                "description" => $request['description'],
            ]);

        if ($files) {
            return $this->syncPrudentialClaimFiles($files, $prudentialClaim);
        }
    }


    public function update($request)
    {
        $files = isset($request['files']) ? $request['files'] : null;
        $prudentialClaim = PrudentialClaim::find($request['claim_id']);
        if (!$prudentialClaim) {
            throw new \Exception('Claim not found');
        }
        $prudentialClaim->update([
            "policy_id" => $request['policy_id'],
            "pid" => $request['plan_id'],
            "claim_by_type" => $request['claim_type'],
            "description" => $request['description']
        ]);

        return $this->syncPrudentialClaimFiles($files, $prudentialClaim);
    }

    protected function syncPrudentialClaimFiles($files, PrudentialClaim $prudentialClaim)
    {
        $this->dropPrudentialClaimFile($prudentialClaim->id);
        if (isset($files)) {
            foreach ($files as $file) {
                if ($file instanceof UploadedFile) {
                    $apple = $this->uploadToS3($file, ClaimFile::UPLOAD_DIR, $file->getClientOriginalName());
                    ClaimFile::create([
                        'filename' => $file->getClientOriginalName(),
                        'filepath' => $this->getS3Url(ClaimFile::UPLOAD_DIR, $file->getClientOriginalName()),
                        'claim_id' => $prudentialClaim->id,
                    ]);
                }
            }
        }
    }

    protected function dropPrudentialClaimFile($prudentialId)
    {
        $request = \request()->capture();
        $data = ClaimFile::where('claim_id', $prudentialId)
            ->whereIn('id', $request->deleted_files ?? [])
            ->get();
        foreach ($data as $d) {
            if ($d->filepath) {
                Storage::disk('s3-third')->delete($d->filepath);
                $d->delete();
            }
        }
    }
}