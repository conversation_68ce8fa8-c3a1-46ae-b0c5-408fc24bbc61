<?php

namespace App\Repositories\Members;

use App\Traits\GetUserStat;
use App\UserInfoPolicyAddress;
use App\DependentInPolicy;
use Illuminate\Support\Facades\DB;

class MemberDashboardRepository
{
    use GetUserStat;
    public function totalAnalytics($filters = [],$fdate=null,$tdate=null)
    {
        $members = $this->getMembers($filters);
        $members = $this->filterByDate($members,$fdate,$tdate);

        $totalMembersCount = DB::table(DB::raw("({$members->toSql()}) as a"))
            ->mergebindings($members->getQuery())
            ->count();

        $activeMembers = $this->activeMembers($filters);
        $activeMembers = $this->filterByDate($activeMembers,$fdate,$tdate);
        $activeMembersCount = DB::table(DB::raw("({$activeMembers->toSql()}) as a"))
            ->mergebindings($activeMembers->getQuery())
            ->count();
        $activeMemberIds = $activeMembers->pluck('userid')->toArray();
        
        $termedMembers = $this->termedMembers($activeMemberIds, $filters);
        $termedMembers = $this->filterByDate($termedMembers,$fdate,$tdate);
        $termedMembersCount = DB::table(DB::raw("({$termedMembers->toSql()}) as a"))
            ->mergebindings($termedMembers->getQuery())
            ->count();
        $termedMemberIds = $termedMembers->pluck('userid')->toArray();

        $withdrawnMembers = $this->withdrawnMembers($activeMemberIds, $termedMemberIds, $filters);
        $withdrawnMembers = $this->filterByDate($withdrawnMembers,$fdate,$tdate);
        $withdrawnMembersCount = DB::table(DB::raw("({$withdrawnMembers->toSql()}) as a"))
            ->mergebindings($withdrawnMembers->getQuery())
            ->count();

        $totalMemberAnalytics = [
            'total_members' => $totalMembersCount,
            'active_members' => $activeMembersCount,
            'termed_members' => $termedMembersCount,
            'withdrawn_members' => $withdrawnMembersCount
        ];
        $data = ['totalMemberAnalytics' => $totalMemberAnalytics];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalAnalyticsOfDependents($filters = [])
    {
        $totalMemberUserIds = $this->getMembers($filters)->pluck('userid')->toArray();
        count($totalMemberUserIds) > 0? $total = implode(',',$totalMemberUserIds) : $total = "''";
        $activeMemberUserIds = $this->activeMembers($filters)->pluck('userid')->toArray();
        count($activeMemberUserIds) > 0? $active = implode(',',$activeMemberUserIds) : $active = "''";
        $termedMemberUserIds = $this->termedMembers($activeMemberUserIds, $filters)->pluck('userid')->toArray();
        count($termedMemberUserIds) > 0? $termed = implode(',',$termedMemberUserIds) : $termed = "''";
        $withdrawnMemberUserIds = $this->withdrawnMembers($activeMemberUserIds, $termedMemberUserIds, $filters)->pluck('userid')->toArray();
        count($withdrawnMemberUserIds) > 0? $withdrawn = implode(',',$withdrawnMemberUserIds) : $withdrawn = "''";
        $totalDependentAnalytics = DependentInPolicy::selectRaw("COUNT(DISTINCT CASE WHEN userid IN ($total) THEN dependent_id END) as total_dependent_members,
                COUNT(DISTINCT CASE WHEN userid IN ($active) THEN dependent_id END) as active_dependent_members,
                COUNT(DISTINCT CASE WHEN userid IN ($termed) THEN dependent_id END) as termed_dependent_members,
                COUNT(DISTINCT CASE WHEN userid IN ($withdrawn) THEN dependent_id END) as withdrawn_dependent_members")
                ->get();
        $data = ['totalDependentAnalytics' => $totalDependentAnalytics[0]];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalAnalyticsOfPolicies($filters = [])
    {
        $totalPolicyAnalytics = $this->getPolicies($filters)
            ->selectRaw("COUNT(DISTINCT policy_id) as total_policy_count,
                COUNT(DISTINCT CASE WHEN status = 'ACTIVE' AND Approval = 1 THEN policy_id END) AS active_policy_count,
                COUNT(DISTINCT CASE WHEN status = 'TERMED' THEN policy_id END) AS termed_policy_count,
                COUNT(DISTINCT CASE WHEN status = 'WITHDRAWN' THEN policy_id END) AS withdrawn_policy_count")
            ->get();
        $data = ['totalPolicyAnalytics' => $totalPolicyAnalytics[0]];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalYearProgress($fyear, $tyear, $filters = [])
    {
        $res = ['status' => 'error', 'message' => 'format error'];
        if ($fyear < $tyear) {
            $x = $tyear - $fyear;
            $totalyears = [];
            if ($x > 9) {
                return $res;
            }
            for ($i = 0; $i <= $x; $i++) {
                $fyear = strval($fyear);

                $policiesInfo = $this->getPolicies($filters)
                    ->where([
                        ['edate', '!=', null],
                        ['edate', '!=', '']
                    ])
                    ->selectRaw('DISTINCT policy_id , edate, status, Approval');

                $yearlyMemberData = DB::table(DB::raw("({$policiesInfo->toSql()}) as d"))
                    ->mergeBindings($policiesInfo->getQuery())
                    ->whereRaw("FROM_UNIXTIME(d.edate) LIKE '$fyear-%'")
                    ->selectRaw("COUNT(DISTINCT CASE WHEN d.status = 'ACTIVE' AND d.Approval = 1 THEN d.policy_id END) as active_count,
                        COUNT(DISTINCT CASE WHEN d.status = 'TERMED' THEN d.policy_id END) as termed_count,
                        COUNT(DISTINCT CASE WHEN d.status = 'WITHDRAWN' THEN d.policy_id END) as withdrawn_count")
                    ->get();

                //For ACTIVE member policies
                $totalActiveMember[$i] = $yearlyMemberData[0]->active_count;
                //For DISABLED member policies
                $totalTermedMember[$i] = $yearlyMemberData[0]->termed_count;
                //For Suspended member policies
                $totalWithdrawnMember[$i] = $yearlyMemberData[0]->withdrawn_count;
                array_push($totalyears, $fyear);
                $fyear++;
            }
        } else {
            return $res;
        }
        $activeMembers = [
            'name' => 'Active',
            'data' => $totalActiveMember
        ];
        $termedMembers = [
            'name' => 'Termed',
            'data' => $totalTermedMember
        ];
        $withdrawnMembers = [
            'name' => 'Withdrawn',
            'data' => $totalWithdrawnMember
        ];
        $yearProgress = [
            $activeMembers,
            $termedMembers,
            $withdrawnMembers
        ];
        $data = [
            'totalYears' => $totalyears,
            'totalProgressOfYear' => $yearProgress
        ];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalMonthsProgress($fdate, $tdate, $filters = [])
    {
        $res = ['status' => 'error', 'message' => 'format error'];
        $fyear = substr($fdate, 0, 4);
        $tyear = substr($tdate, 0, 4);
        $from_month = substr($fdate, 5, 2);
        $to_month = substr($tdate, 5, 2);
        $m = 0;
        $totalMonthProgess = [];
        $from_month > $to_month? $diff = $to_month + 12 - $from_month : $diff = $to_month - $from_month;
        $from_month = strval($from_month);
        strlen($from_month) < 2 ? $from_month = "0".$from_month : $from_month;
        if ($from_month > $to_month) {
            if (($tyear - $fyear > 1) || $fyear == $tyear) {
                return $res;
            }
            for ($i = 0; $i <= $diff; $i++) {
                $monthlyData = $this->getMonthlyData($fyear, $from_month, $filters);
                $totalMonthProgess[$i] = $monthlyData[0];
                if ($from_month == 12) {
                    $fyear++;
                    $fyear = strval($fyear);
                    $from_month = 1;
                } else {
                    $from_month++;
                }
                $from_month = strval($from_month);
                strlen($from_month) < 2 ? $from_month = "0".$from_month : $from_month;
            }
        } else {
            if (($fyear != $tyear)) {
                return $res;
            }
            for($i = 0; $i <= $diff; $i++) {
                $monthlyData = $this->getMonthlyData($fyear, $from_month, $filters);
                $totalMonthProgess[$i] = $monthlyData[0];
                $from_month++;
                $from_month = strval($from_month);
                strlen($from_month) < 2 ? $from_month = "0".$from_month : $from_month;
            }
        }
        $data = $totalMonthProgess;
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function groupingByOther($filters)
    {
        $activeMemberInfo = $this->activeMembers($filters)
            ->select('cgender', 'userid', 'weburl', 'payment_type', 'state', 'cdob', 'agent_id', 'agent_code', 'agent_fname', 'agent_lname', 'employ_start_date', 'eid', 'idilus-type', 'edate');
        $activeMemberUserIds = $this->activeMembers($filters)->pluck('userid')->toArray();
        $activeDependentInfo = DependentInPolicy::whereIn('userid', $activeMemberUserIds)
            ->select('dependent_id', 'd_gender', 'd_dob');

        //total active acount
        $totalActiveMemberCount = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as t"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->count();

        //by gender
        //primary
        $byMemberGender = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as g"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->selectRaw("COUNT(DISTINCT CASE WHEN g.cgender = 0 THEN g.userid END) AS total_primary_male,
                COUNT(DISTINCT CASE WHEN g.cgender = 1 THEN g.userid END) AS total_primary_female")
            ->get();
        //dependents
        $byDependentGender = DB::table(DB::raw("({$activeDependentInfo->toSql()}) as g"))
            ->mergeBindings($activeDependentInfo->getQuery())
            ->selectRaw("COUNT(DISTINCT CASE WHEN g.d_gender = 0 THEN g.dependent_id END) AS total_dependent_male,
                COUNT(DISTINCT CASE WHEN g.d_gender = 1 THEN g.dependent_id END) AS total_dependent_female")
            ->get();
        $totalMale = [
            $byMemberGender[0]->total_primary_male,
            $byDependentGender[0]->total_dependent_male
        ];
        $totalFemale = [
            $byMemberGender[0]->total_primary_female,
            $byDependentGender[0]->total_dependent_female
        ];
        $maleInfo = [
            'name' => 'Male',
            'type' => 'column',
            'data' => $totalMale
        ];
        $femaleInfo = [
            'name' => 'Female',
            'type' => 'column',
            'data' => $totalFemale
        ];
        $byGender = [
            $maleInfo,
            $femaleInfo
        ];

        //by app use
        /*$byAppUse = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as a"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->join('user_activity_details as u', 'a.userid', 'u.user_id')
            ->where([
                ['u.user_type', 'member'],
                ['u.action', 'login']
            ])
            ->selectRaw("COUNT(DISTINCT CASE WHEN u.device_type = 'ios' THEN u.user_id END) AS ios_count,
                COUNT(DISTINCT CASE WHEN u.device_type = 'android' THEN u.user_id END) AS android_count")
            ->get(); */

        $userDetails = $this->userTotalLogin()['data']['all_time_login'];
        $byAppUse['ios_count'] = $userDetails['ios']['member'];
        $byAppUse['android_count'] = $userDetails['android']['member'];
        //by website
        $memberWebsiteRefine = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as z"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->selectRaw("DISTINCT userid, TRIM(LEADING 'www.' FROM SUBSTRING_INDEX(weburl, '/', 1)) AS new_weburl")
            ->where([
                ['z.weburl', '!=', null],
                ['z.weburl', '!=', '']
            ]);

        $byWebsite = DB::table(DB::raw("({$memberWebsiteRefine->toSql()}) as r"))
            ->mergeBindings($memberWebsiteRefine)
            ->selectRaw('r.new_weburl, COUNT(*) as count')
            ->groupBy('r.new_weburl')
            ->orderBy('count', 'desc')
            ->get();

        //by dashboard access
        $dashboardAccess = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as d"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->join('user_activity_details as u', 'd.userid', 'u.user_id')
            ->where([
                ['u.user_type', 'member'],
                ['u.web', 1],
                ['u.action', 'login']
            ])
            ->selectRaw('u.web as access, count(distinct u.user_id) AS count')
            ->get();
        $totalNonAccess = $totalActiveMemberCount - $dashboardAccess[0]->count;
        $dashboardNonAccess = [
            'access' => "0",
            'count' => strval($totalNonAccess)
        ];
        $dashboardAndCount = [
            $dashboardAccess[0],
            $dashboardNonAccess
        ];
        $byDashboard = [
            'dashboardAndCount' => $dashboardAndCount
        ];

        //by payment method
        $byPaymentMethod = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as p"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->selectRaw("SUM(CASE WHEN p.payment_type = 'cc' AND `idilus-type` = 'ee' THEN 1 ELSE 0 END) as employee_cc_count,
                SUM(CASE WHEN p.payment_type = 'cc' AND `idilus-type` != 'ee' THEN 1 ELSE 0 END) as ind_cc_count,
                SUM(CASE WHEN p.payment_type = 'chck' AND `idilus-type` = 'ee' THEN 1 ELSE 0 END) as employee_chck_count,
                SUM(CASE WHEN p.payment_type = 'chck' AND `idilus-type` != 'ee' THEN 1 ELSE 0 END) as ind_chck_count,
                SUM(CASE WHEN p.payment_type = 'eft' AND `idilus-type` = 'ee' THEN 1 ELSE 0 END) as employee_eft_count,
                SUM(CASE WHEN p.payment_type = 'eft' AND `idilus-type` != 'ee' THEN 1 ELSE 0 END) as ind_eft_count,
                SUM(CASE WHEN p.payment_type = 'elist' AND `idilus-type` = 'ee' THEN 1 ELSE 0 END) as employee_elist_count,
                SUM(CASE WHEN p.payment_type = 'elist' AND `idilus-type` != 'ee' THEN 1 ELSE 0 END) as ind_elist_count,
                SUM(CASE WHEN p.payment_type = 'list' AND `idilus-type` = 'ee' THEN 1 ELSE 0 END) as employee_list_count,
                SUM(CASE WHEN p.payment_type = 'list' AND `idilus-type` != 'ee' THEN 1 ELSE 0 END) as ind_list_count,
                SUM(CASE WHEN p.payment_type = 'stmt' AND `idilus-type` = 'ee' THEN 1 ELSE 0 END) as employee_stmt_count,
                SUM(CASE WHEN p.payment_type = 'stmt' AND `idilus-type` != 'ee' THEN 1 ELSE 0 END) as ind_stmt_count")
            ->get();

        //by state
        $byState = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as s"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->selectRaw('s.state, COUNT(s.userid) as state_count')
            ->where([
                ['s.state', '!=', null],
                ['s.state', '!=', '']
            ])
            ->groupBy('s.state')
            ->havingRaw('COUNT(s.userid) >= 1')
            ->orderBy('state_count', 'desc')
            ->get();

        //by age
        $byPrimaryAge = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as a"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->selectRaw("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) >= 18 AND TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) <= 24 THEN 1 ELSE 0 END) AS eighteenToTwentyfour,
                SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) >= 25 AND TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) <= 29 THEN 1 ELSE 0 END) AS twentyfiveToTwentynine,
                SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) >= 30 AND TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) <= 34 THEN 1 ELSE 0 END) AS thirtyToThirtyfour,
                SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) >= 35 AND TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) <= 50 THEN 1 ELSE 0 END) AS thirtyfiveToFifty,
                SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.cdob, CURDATE()) > 50 THEN 1 ELSE 0 END) AS fiftyPlus")
            ->get();

        $byDependentAge = DB::table(DB::raw("({$activeDependentInfo->toSql()}) as a"))
            ->mergeBindings($activeDependentInfo->getQuery())
            ->selectRaw("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) >= 18 AND TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) <= 24 THEN 1 ELSE 0 END) AS eighteenToTwentyfour,
                SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) >= 25 AND TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) <= 29 THEN 1 ELSE 0 END) AS twentyfiveToTwentynine,
                SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) >= 30 AND TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) <= 34 THEN 1 ELSE 0 END) AS thirtyToThirtyfour,
                SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) >= 35 AND TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) <= 50 THEN 1 ELSE 0 END) AS thirtyfiveToFifty,
                SUM(CASE WHEN TIMESTAMPDIFF(YEAR, a.d_dob, CURDATE()) > 50 THEN 1 ELSE 0 END) AS fiftyPlus")
                ->get();

        $byAge = [
            'primaryAgeCount' => $byPrimaryAge[0],
            'dependentAgeCount' => $byDependentAge[0],
        ];

        //by rep (direct)
        $byRepDirect = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as r"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->selectRaw('r.agent_id, r.agent_code, r.agent_fname, r.agent_lname, COUNT(*) as rep_count')
            ->groupBy('r.agent_id')
            ->havingRaw('COUNT(*) >= 1')
            ->orderBy('rep_count', 'desc')
            ->get();

        //By LifeSpan
        $today = date('Y-m-d');
        $fiveMonths = date('Y-m-d', strtotime('-5 months', strtotime($today)));
        $sixMonths = date('Y-m-d', strtotime('-1 day', strtotime($fiveMonths)));
        $elevenMonths = date('Y-m-d', strtotime('-11 months', strtotime($today)));
        $oneYear = date('Y-m-d', strtotime('-1 day', strtotime($elevenMonths)));
        $twoYear = date('Y-m-d', strtotime('-2 years', strtotime($today)));
        $ftwoYear = date('Y-m-d', strtotime('-1 day' ,strtotime($twoYear)));
        $fourYear = date('Y-m-d', strtotime('-4 years' ,strtotime($today)));
        $fourPlus = date('Y-m-d', strtotime('-1 day' ,strtotime($fourYear)));

        $memberLifespan = DB::table(DB::raw("({$activeMemberInfo->toSql()}) as l"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->selectRaw("SUM(CASE WHEN FROM_UNIXTIME(l.edate) >= '$fiveMonths' AND FROM_UNIXTIME(l.edate) <= '$today' THEN 1 ELSE 0 END) AS uptoFiveMonthsCount,
                SUM(CASE WHEN FROM_UNIXTIME(l.edate) >= '$elevenMonths' AND FROM_UNIXTIME(l.edate) <= '$sixMonths' THEN 1 ELSE 0 END) AS sixToElevenMonthsCount,
                SUM(CASE WHEN FROM_UNIXTIME(l.edate) >= '$twoYear' AND FROM_UNIXTIME(l.edate) <= '$oneYear' THEN 1 ELSE 0 END) AS oneToTwoYearsCount,
                SUM(CASE WHEN FROM_UNIXTIME(l.edate) >= '$fourYear' AND FROM_UNIXTIME(l.edate) <= '$ftwoYear' THEN 1 ELSE 0 END) AS twoToFourYearsCount,
                SUM(CASE WHEN FROM_UNIXTIME(l.edate) <= '$fourPlus' THEN 1 ELSE 0 END) AS fourYearsPlusCount");
        $memberLifespan = $this->filterContent($memberLifespan, $filters)->get();

        $ls_date = [
            'today' => $today,
            'fiveMonths' => $fiveMonths,
            'sixMonths' => $sixMonths,
            'elevenMonths' => $elevenMonths,
            'oneYear' => $oneYear,
            'twoYear' => $twoYear,
            'ftwoYear' => $ftwoYear,
            'fourYear' => $fourYear,
            'fourPlus' => $fourPlus
        ];
        $byLifespan = [
            'filterDate' => $ls_date,
            'lifespanCount' => $memberLifespan[0]
        ];

        // by category
        $byCategory = Db::table(DB::raw("({$activeMemberInfo->toSql()}) as c"))
            ->mergeBindings($activeMemberInfo->getQuery())
            ->join('plan_overview as p', 'c.userid', 'p.userid')
            ->selectRaw("COUNT(DISTINCT CASE WHEN p.pl_type = 'VISION' THEN p.userid END) AS vision,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('MM') THEN p.userid END) AS medical,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('RX') THEN p.userid END) AS rx,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('HOSPITAL') THEN p.userid END) AS hospital,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('LM') THEN p.userid END) AS limitedMed,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('DENT', 'THEFT-DENT') THEN p.userid END) AS dental,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('ADI','CIADD','AME','LTD','GAP','AD','ASHIP', 'STDCANCER') THEN p.userid END) AS supplemental,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('DC','DISCOUNT','TELEMEDICINE','THEFT') THEN p.userid END) AS lifestyle,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('LIFE','TL') THEN p.userid END) AS term_life,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('ACCIDENT') THEN p.userid END) AS accident,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('CRITICAL', 'CI') THEN p.userid END) AS critical,
                COUNT(DISTINCT CASE WHEN p.pl_type IN ('DI') THEN p.userid END) AS di")
            ->where([
                ['p.pstatus', 1]
            ])
            ->get();

        $data = [
            'totalActiveMemberCount' => $totalActiveMemberCount,
            'byGender' => $byGender,
            'byAppUse' => $byAppUse,
            'byWebsite' => $byWebsite,
            'byDashboard' => $byDashboard,
            'byPayment' => $byPaymentMethod[0],
            'byState' => $byState,
            'byAge' => $byAge,
            'byRepDirect' => $byRepDirect,
            'byLifespan' => $byLifespan,
            'byCategory' => $byCategory[0]
        ];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    private function getMonthlyData($fyear, $from_month, $filters)
    {
        $ymdate = "$fyear-$from_month-01";
        $last_date = date("Y-m-t", strtotime($ymdate));
        $policiesInfo = $this->getPolicies($filters)
            ->where([
                ['edate', '!=', null],
                ['edate', '!=', '']
            ])
            ->selectRaw('DISTINCT policy_id, edate, status, Approval');

        $monthlyData = DB::table(DB::raw("({$policiesInfo->toSql()}) as d"))
            ->mergeBindings($policiesInfo->getQuery())
            ->whereRaw("FROM_UNIXTIME(d.edate) BETWEEN '$ymdate' AND '$last_date'")
            ->selectRaw("COUNT(DISTINCT CASE WHEN d.status = 'ACTIVE' AND d.Approval = 1 THEN d.policy_id END) as active_count,
                COUNT(DISTINCT CASE WHEN d.status = 'TERMED' THEN d.policy_id END) as termed_count,
                COUNT(DISTINCT CASE WHEN d.status = 'WITHDRAWN' THEN d.policy_id END) as withdrawn_count")
            ->get();
        $monthlyDataPoliciesUpdate = DB::table(DB::raw("({$policiesInfo->toSql()}) as d"))
            ->mergeBindings($policiesInfo->getQuery())
            ->join('policy_updates as p', 'p.elgb_policyid', 'd.policy_id')
            ->whereRaw("FROM_UNIXTIME(p.elgb_act_date) BETWEEN '$ymdate' AND '$last_date'")
            ->selectRaw("COUNT(DISTINCT CASE WHEN p.elgb_act IN ('rpc') AND d.Approval = 1 THEN  elgb_policyid END) AS reactivated_count_of_month,
                COUNT(DISTINCT CASE WHEN p.elgb_act IN ('tcp','tnp','tcr') THEN elgb_policyid END) AS termed_count_of_month,
                COUNT(DISTINCT CASE WHEN p.elgb_act IN ('wno','wnp','wdr') THEN elgb_policyid END) AS withdrawn_count_of_month")
            ->get();
        $monthlyData[0]->year = $fyear;
        $monthlyData[0]->month = $from_month;
        $monthlyData[0]->last_date = $last_date;
        $monthlyData[0]->reactivated_count_of_month = $monthlyDataPoliciesUpdate[0]->reactivated_count_of_month;
        $monthlyData[0]->termed_count_of_month = $monthlyDataPoliciesUpdate[0]->termed_count_of_month;
        $monthlyData[0]->withdrawn_count_of_month = $monthlyDataPoliciesUpdate[0]->withdrawn_count_of_month;
        return $monthlyData;
    }

    public function totalWeekProgress()
    {
        $weeklyData= UserInfoPolicyAddress::whereRaw("YEARWEEK(from_unixtime(edate,'%Y-%m-%d')) = YEARWEEK(now())")
        ->selectRaw("DAYNAME(from_unixtime(edate)) as day,COUNT(userid) as total_count")
        ->selectRaw("SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count")
        ->selectRaw("SUM(CASE WHEN status = 'WITHDRAWN' THEN 1 ELSE 0 END) as withdrawn_count")
        ->selectRaw("SUM(CASE WHEN status = 'TERMED' THEN 1 ELSE 0 END) as termed_count")
        ->groupBy('day')
        ->get();
        // $weeklyData = UserInfoPolicyAddress::take(5)->get();

        for($i=0;$i<count($weeklyData);$i++){

            $days[$i] = $weeklyData[$i]->day;
            $dates[$i] = $weeklyData[$i]->date;
            $totalCounts[$i] =$weeklyData[$i]->total_count;
            $activeCounts[$i] =$weeklyData[$i]->active_count;
            $withdrawnCounts[$i] =$weeklyData[$i]->withdrawn_count;
            $termedCounts[$i] =$weeklyData[$i]->termed_count;
        }

        $activeMembers = [];
        $withdrawnMembers = [];
        $termedMembers = [];
        $totalMembers = [];

        // Make data Zero if there are no records
        $daynames = ['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];
        $index = 0;

        foreach($daynames as $day)
        {
            if(isset($days) AND in_array($day,$days))
            {
                array_push($activeMembers,(int)$activeCounts[$index]);
                array_push($withdrawnMembers,(int)$withdrawnCounts[$index]);
                array_push($termedMembers,(int)$termedCounts[$index]);
                array_push($totalMembers,(int)$totalCounts[$index]);

                $index++;
            }
            else{
                array_push($activeMembers,0);
                array_push($withdrawnMembers,0);
                array_push($termedMembers,0);
                array_push($totalMembers,0);
            }
        }
        $days = [
            'name' => 'Days',
            'data' => $daynames
        ];
        $totalMembers =[
            'name' => 'Total Counts',
            'data' => $totalMembers
        ];
        $activeMembers =[
            'name' => 'Active',
            'data' => $activeMembers
        ];
        $withdrawnMembers =[
            'name' => 'Withdrawn',
            'data' => $withdrawnMembers
        ];
        $termedMembers =[
            'name' => 'Termed',
            'data' => $termedMembers
        ];
        $data = [$days,$totalMembers,$activeMembers,$withdrawnMembers,$termedMembers];
        $response = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];

        return $response;
    }

    private function filterContent($data, $filters)
    {
        if (isset($filters['user']) && isset($filters['userid'])) {
            if ($filters['user'] == 'group') {
                return $data->where('eid', $filters['userid']);
            } else if ($filters['user'] == 'agent') {
                return $data->where('agent_id', $filters['userid']);
            }
        }
        return $data;
    }

    private function filterByDate($data,$fdate,$tdate)
    {
        if(!is_null($fdate) and !is_null($tdate)){
            return $data->whereRaw("FROM_UNIXTIME(edate) >= '$fdate' AND FROM_UNIXTIME(edate) < DATE_ADD('$tdate', INTERVAL 1 day)");
        }
        else return $data;
    }

    private function getMembers($filters)
    {
        $members = UserInfoPolicyAddress::whereRaw("LOWER(cfname) != 'test'")
            ->groupBy('userid');
        $members = $this->filterContent($members, $filters);
        return $members;
    }

    private function activeMembers($filters)
    {
        $activeMembers = UserInfoPolicyAddress::whereRaw("status = 'ACTIVE' AND Approval = 1 AND LOWER(cfname) != 'test'")
            ->groupBy('userid');
        $activeMembers = $this->filterContent($activeMembers, $filters);
        return $activeMembers;
    }

    private function termedMembers($filters)
    {
        $termedMembers = UserInfoPolicyAddress::where('status', 'TERMED')
            ->whereRaw("LOWER(cfname) != 'test'")
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('userinfo_policy_address as active')
                    ->whereRaw('active.userid = userinfo_policy_address.userid')
                    ->where('active.status', 'ACTIVE')
                    ->where('active.Approval', 1);
            })
            ->groupBy('userid');
    
        return $this->filterContent($termedMembers, $filters);
    }
    
    private function withdrawnMembers($filters)
    {
        $withdrawnMembers = UserInfoPolicyAddress::where('status', 'WITHDRAWN')
            ->whereRaw("LOWER(cfname) != 'test'")
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('userinfo_policy_address as active')
                    ->whereRaw('active.userid = userinfo_policy_address.userid')
                    ->where('active.status', 'ACTIVE')
                    ->where('active.Approval', 1);
            })
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('userinfo_policy_address as termed')
                    ->whereRaw('termed.userid = userinfo_policy_address.userid')
                    ->where('termed.status', 'TERMED');
            })
            ->groupBy('userid');
    
        return $this->filterContent($withdrawnMembers, $filters);
    }

    private function getPolicies($filters)
    {
        $policiesInfo = UserInfoPolicyAddress::whereRaw("LOWER(cfname) != 'test'");
        $policiesInfo = $this->filterContent($policiesInfo, $filters);

        return $policiesInfo;
    }
}
