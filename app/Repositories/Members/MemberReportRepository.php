<?php


namespace App\Repositories\Members;

use App\ClientFileGeneration;
use App\Exports\MemberExport;
use App\NbInvoice;
use App\Repositories\PolicyRepository;
use App\Traits\ResponseMessage;
use App\Jobs\MemberExportExcelFile;
use App\Traits\Paginator;
use Maatwebsite\Excel\Excel;

class MemberReportRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var Excel
     */
    private $excel;

    /**
     * @var ManageClients
     */
    private $memberRepository;

    /**
     * MemberReportRepository constructor.
     * @param Excel $excel
     * @param ManageClients $memberRepository
     */
    public function __construct(Excel $excel, ManageClients $memberRepository)
    {
        $this->excel = $excel;
        $this->memberRepository = $memberRepository;
    }


    public function generate_export($filters = [])
    {
        $data['added_by'] = request()->header('name');
        $data['added_by_id'] = request()->header('id');
        MemberExportExcelFile::dispatch($filters , $data)->onQueue('memberExportProcessing');
    }

    public function getMemberReport($request){
        $clientFileDetail = ClientFileGeneration::fetchMemberReport($request['id']);
        $clientExpLst = $clientFileDetail->map(function ($clientExpList) {
            return collect($clientExpList->toArray())
                ->only(['id', 'file_path', 'file_name', 'file_generation_started', 'file_generation_ended', 'no_of_record', 'added_by', 'added_by_id', 'created_at', 'updated_at', 'file_download_link'])
                ->all();
        });
        $paginatedData = [
            'data'=>$clientExpLst,
            'links' => $this->links($clientFileDetail),
            'meta' => $this->meta($clientFileDetail)
        ];
        return $paginatedData;
    }
}
