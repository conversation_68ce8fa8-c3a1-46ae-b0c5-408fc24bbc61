<?php


namespace App\Repositories\Members;


use App\Helpers\GuzzleHelper;
use App\Policy;
use App\UserActivity;
use App\UserInfo;
use Carbon\Carbon;
use Carbon\Exceptions\Exception;
use Illuminate\Database\Eloquent\Model;

class MemberTextMessage extends Model
{
    public function sendTextMessageMemberStatistics()
    {
        $userDashboardLoginPercentage = 0;
        $userMobileLoginPercentage = 0;

        $activeUsers = Policy::where('status','ACTIVE')->pluck('policy_userid')->toArray();
        $userAll = UserInfo::whereIn('userid',$activeUsers)->count();
        $memberDashboardLogged = UserActivity::where([
            ['from_mobile','=','0'],
            ['action','=','login']
        ])
            ->distinct()->pluck('uid')->toArray();
        $userDashboardLogin = UserInfo::whereIn('userid',$memberDashboardLogged)
            ->whereIn('userid',$activeUsers)
            ->whereRaw('length(phone1) = 10')
            ->count();

        $memberMobileLogged = UserActivity::where([
            ['from_mobile','=','1']
        ])
            ->distinct()->pluck('uid')->toArray();
        $userMobileLogin = UserInfo::whereIn('userid',$memberMobileLogged)
            ->whereIn('userid',$activeUsers)
            ->whereRaw('length(phone1) = 10')
            ->count();

        if($userAll>0){
            $userDashboardLoginPercentage = ($userAll)?number_format(round(($userDashboardLogin*100)/$userAll,2),2):number_format(0,2);
            $userMobileLoginPercentage = ($userAll)?number_format(round(($userMobileLogin*100)/$userAll,2),2):number_format(0,2);
        }

        $message = "#3 - ".Carbon::now('America/New_York')->format('m/d/Y')."\n\n";
        $message .= "All Members - Dashboard Login\n";
        $message .= $userDashboardLogin." - ".$userDashboardLoginPercentage."%\n\n";
        $message .= "========================\n\n";
        $message .= "All Members - Mobile Login\n";
        $message .= $userMobileLogin." - ".$userMobileLoginPercentage."%\n\n";
        $message .= "========================\n";

        ECHO $message;  //lou 9149076059  Scott 4195141967  Pukar 4193771007
        $phoneNumbers = [9149076059,4195141967];
        echo $message;

        foreach ($phoneNumbers as $phone) {
            $request = [
                'phone' => $phone,
                'body' => $message
            ];

            $apiUrl = config('app.messagecenter.key') . 'api/v1/send-sms-with-content';
            $responseJson = GuzzleHelper::postApi($apiUrl, [], $request);
            $response = json_decode($responseJson, true);

            if (isset($response['status_code']) && $response['status_code'] == '200' && $response['status'] == 'success') {
                echo "Text message sent successfully to ".$phone.".\n";
            } else {
                echo "Failed to send text message to ".$phone.".\n";
            }
        }

    }
}
