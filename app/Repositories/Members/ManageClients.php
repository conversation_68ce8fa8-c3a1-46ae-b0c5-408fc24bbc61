<?php

namespace App\Repositories\Members;

use App\Helpers\DecryptEcryptHelper;
use App\Helpers\ManageClientsHelper;
use App\Http\Resources\SuccessResource;
use App\NbInvoice;
use App\Plan;
use App\PlanOverview;
use App\Policy;
use App\Service\CustomValidationService;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Repositories\TermRequestRepository;
use Symfony\Component\HttpFoundation\Response;
use App\SsoUsers;
use Illuminate\Support\Facades\Hash;
use App\UserLogin;
use App\Helpers\CommonHelper;

class ManageClients extends Model
{

    use Paginator, ResponseMessage;

    private $clientsHelper;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->clientsHelper = new ManageClientsHelper();
    }

    private function verifyEmailsAndPhones($data): array
    {
        $validationService = new CustomValidationService();

        $phones = [];
        $emails = [];

        foreach ($data->items() as $userinfoPolicyAddress) {
            $phones[] = $userinfoPolicyAddress->phone1;
            $emails[] = $userinfoPolicyAddress->cemail;
        }

        return [
            'validatedEmails' => $validationService->validateBulkEmailNeverBounce(array_filter($emails)),
            'validatedPhones' => $validationService->validateBulkPhoneNumVerify(array_filter($phones))
        ];
    }

    public function paginatedFormattedList($limit, $filters = []): array
    {

        if(isset($filters['cssn'])) {
            if(!is_numeric($filters['cssn']) || !(strlen($filters['cssn']) == 9 || strlen($filters['cssn']) == 4)) {
                throw new Exception('Invalid SSN format', Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }

        $prudentialStatus = [];
        $data = $this->paginatedList($limit, $filters, $prudentialStatus)->appends($filters);
        $result = $this->formattedData($data, $this->verifyEmailsAndPhones($data), $prudentialStatus);

        return [
            'links' => $this->links($data),
            'meta' => $this->meta($data),
            'data' => $result
        ];
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->manageClients($filters)
            ->orderBy('policy_id', 'desc')
            ->paginate($limit);
    }

    public function manageClients($filters): Builder
    {
        $selectColumns = [
            'policy_id',
            'userinfo_policy_address.userid',
            'userinfo_policy_address.status',
            'boffice',
            'address1',
            'address2',
            'city',
            'state',
            'zip',
            'cemail',
            'phone1',
            'edate',
            'effective_date',
            'userinfo_policy_address.payment_type',
            'userinfo_policy_address.payment_id',
            'recurring_payment_type',
            'recurring_payment_id',
            'agent_fname',
            'agent_lname',
            'idilus-type as idilus_type',
            'eid',
            'Approval',
            'agent_id',
            'agent_email',
            'home_address',
            'is_benefit_store',
            'userinfo_policy_address.weburl',
            'bill_date',
            'cfname',
            'clname',
            'cmname',
            'cdob',
            'userinfo_policy_address.cssn4',
            'userinfo_policy_address.state'
//            'upa_transferred'
        ];

        $relations = [
            'plans:policy_id,web_display_name,tier,user_dob,pstatus,cid,price_male_nons,pterm_date,pid,plan_id,plan_cat,esig,status,eprocess,plan_pricing_type,pl_type,peffective_date',
            'getGroup:gid,gname',
            'policyUpdate:elgb_policyid,elgb_act_date,elgb_act',
            'ccInfo:cc_id,cc_num4,cc_expmm,cc_expyyyy',
            'recurringCCInfo:cc_id,cc_num4,cc_expmm,cc_expyyyy',
            'eftInfo:bank_id,bank_name,bank_account4',
            'recurringEftInfo:bank_id,bank_name,bank_account4',
            'paidThrough:payment_id,payment_paid_through_date',
            'nbInvoice' => function ($q) use ($filters) {
                $q->select('invoice_policy_id', 'invoice_end_date', 'payment_party_status', 'processing_amount')
                    ->where(function ($q) {
                        $q->whereIn('invoice_payment_status', ['PAID', 'PARTIAL'])
                            ->orWhere('payment_party_status', 'PROCESSING');
                    });
            },
            'merchant' => function ($q) use ($filters) {
                $q->select('invoice_policy_id', 'invoice_end_date', 'payment_party_status', 'processing_amount','merchant')
                    ->where(function ($q) {
                        $q->whereIn('invoice_payment_status', ['PAID', 'PARTIAL','UNPAID'])
                            ->orWhere('payment_party_status', 'PROCESSING');
                    });
            },
//            'userinfo:userid,home_address',
//            'getHomeAddress:address_id,is_usps_valid',
            'signature:pid',
            'activeUserLogin:userid',
            'userActivities:uid',
            'policy:policy_id,is_auto_approved',
            'dependents',
        ];
        $query = UserInfoPolicyAddress::query()->with($relations)->select($selectColumns);
        $this->filterContent($query, $filters);
        return $query;
    }

    public function prudplanprice($request){
        return $this->clientsHelper->formatPlanData($request,null, null , 'external');
    }

    private function filterContent(Builder $query, $filters)
    {
        if(isset($filters['pay_type']) && $filters['pay_type'] != ''){
            $policyIds = $this->getFilteredPolicies('pay_type', $filters['pay_type']);
            $query->whereIn('policy_id', $policyIds);
        }

        if (isset($filters['iha_status']) && $filters['iha_status'] != '') {
            $policyIds = DB::table('vw_iha_member_status_info')
                ->where('iha_dashboard_status', '=', $filters['iha_status'])
                ->pluck('policy_id');
            $query->whereIn('policy_id', $policyIds);
        }

        if (isset($filters['system_id']) && $filters['system_id'] != '') {
            $query->where('policy_id', $filters['system_id']);
        }

        if (isset($filters['invoice_id']) && $filters['invoice_id'] != '') {
            $policyIds = $this->getNbInvoiceFilteredPolicyIds('invoice_id', $filters['invoice_id']);
            $query->whereIn('policy_id', $policyIds);
        }

        if (isset($filters['enrollment_date_from']) && $filters['enrollment_date_from'] != '') {
            $query->where('edate', '>=', Carbon::parse($filters['enrollment_date_from'])->timestamp);
        }

        if (isset($filters['enrollment_date_to']) && $filters['enrollment_date_to'] != '') {
            $query->where('edate', '<=', Carbon::parse($filters['enrollment_date_to'])->timestamp);
        }

        if (isset($filters['effective_date']) && $filters['effective_date'] != '') {
            $query->where('effective_date', Carbon::parse($filters['effective_date'])->format('Y-m-d'));
        }

        if (isset($filters['term_withdrawn_date']) && $filters['term_withdrawn_date'] != '') {
            $value = $filters['term_withdrawn_date'];
            $query->whereHas('plans', function ($q) use ($value) {
                return $q->where('pterm_date', Carbon::parse($value)->format('Y-m-d'));
            });
        }

        if (isset($filters['status']) && $filters['status'] != '') {
            if ($filters['status'] == 'PENDING') {
                $query->where('userinfo_policy_address.status', 'ACTIVE')
                    ->whereNull('userinfo_policy_address.Approval');
            }
            else if ($filters['status'] == 'ACTIVE') {
                $query->where('userinfo_policy_address.status', 'ACTIVE')
                    ->where('userinfo_policy_address.Approval', '1');
            }
            else {
                $query->where('userinfo_policy_address.status', $filters['status']);
            }
        }

        if (isset($filters['agent_code']) && $filters['agent_code'] != '') {
            $query->where('agent_code', $filters['agent_code']);
        }

        if (isset($filters['first_name']) && $filters['first_name'] != '') {
            $query->where('cfname', 'like', '%' . $filters['first_name'] . '%');
        }

        if (isset($filters['last_name']) && $filters['last_name'] != '') {
            $query->where('clname', 'like', '%' . $filters['last_name'] . '%');
        }

        if (isset($filters['email']) && $filters['email'] != '') {
            $query->where('cemail', 'like', '%' . $filters['email']. '%');
        }

        if (isset($filters['phone']) && $filters['phone'] != '') {
            $query->where('phone1', $filters['phone']);
        }

        if (isset($filters['payment_status']) && $filters['payment_status'] != '') {
            $policyIds = $this->getNbInvoiceFilteredPolicyIds('invoice_payment_status', $filters['payment_status']);
            $query->whereIn('policy_id', $policyIds);
        }

        if (isset($filters['pay_through_date']) && $filters['pay_through_date'] != '') {
            $policyIds = $this->getNbInvoiceFilteredPolicyIds('invoice_end_date', Carbon::parse($filters['pay_through_date'])->format('Y-m-d'));
            $query->whereIn('policy_id', $policyIds);
        }

        if (isset($filters['bill_date']) && $filters['bill_date'] != '') {
            if ($filters['bill_date'] == 'other') {
                $query->where('bill_date', '>', 31);
            } else {
                $query->where('bill_date', $filters['bill_date']);
            }
        }

        if (isset($filters['carrier']) && $filters['carrier'] != '') {
            $value = $filters['carrier'];
            $query->whereHas('plans', function ($q) use ($value) {
                return $q->where('cid', $value);
            });
        }

        if (isset($filters['payment_type']) && $filters['payment_type'] != '') {
            $query->where('userinfo_policy_address.payment_type', $filters['payment_type']);
        }

        if (isset($filters['platform']) && $filters['platform'] != '') {
            if ($filters['platform'] == 'enroll.purenroll.com') {
                $query = $query->where('is_benefit_store', '=', 1);
            } else {
                $query = $query->where('userinfo_policy_address.weburl', 'LIKE', $filters['platform'] . '%');
            }
        }

        if (isset($filters['registered_user']) && $filters['registered_user'] != '') {
            if ($filters['registered_user'] == 'false') {
                $query->leftJoin('user_login', 'userinfo_policy_address.userid', 'user_login.userid');
            } else {
                $query->join('user_login', 'userinfo_policy_address.userid', 'user_login.userid');
            }
        }


        //filtering from policies
        if (isset($filters['agent_id']) && $filters['agent_id'] != '') {
            $query->whereHas('policy', function ($q) use ($filters) {
                $q->where('p_agent_num', '=', $filters['agent_id']);
            });
        }
        if (isset($filters['group_id']) && $filters['group_id'] != '') {
            $query->whereHas('policy', function ($q) use ($filters) {
                $q->where('eid', '=', $filters['group_id']);
            });
        }
        if (isset($filters['pending_signature']) && $filters['pending_signature'] != '' && $filters['pending_signature'] == '1') {
            $query->whereHas('plans', function ($q) use ($filters) {
                $q->where([
                    ['effective_date', '>', '2018-09-01'],
                    ['esig', '=', 1],
                ])
                    ->whereNotNull('boffice')
                    ->whereDoesntHave('signature');
            });
        }

        if (isset($filters['plan_ids']) && count($filters['plan_ids'])) {
            $query->whereHas('plans', function ($q) use ($filters) {
                $q->whereIn('pid', $filters['plan_ids']);
            });
        }

        if (isset($filters['plan_cat']) && count($filters['plan_cat'])) {
            $query->whereHas('plans', function ($q) use ($filters) {
                $q->whereIn('plan_cat', $filters['plan_cat']);
            });
        }

        if (isset($filters['pl_type']) && count($filters['pl_type'])) {
            $query->whereHas('plans', function ($q) use ($filters) {
                $q->whereIn('pl_type', $filters['pl_type']);
            });
        }

        if (isset($filters['plan_tier']) && count($filters['plan_tier'])) {
            $query->whereHas('plans', function ($q) use ($filters) {
                $q->whereIn('tier', array_map(function ($each_tier) {
                    return strtoupper($each_tier);
                }, $filters['plan_tier']));
            });
        }

        if (isset($filters['group_ids']) && count($filters['group_ids'])) {
            $query->whereHas('getGroup', function ($q) use ($filters) {
                $q->whereIn('gid', $filters['group_ids']);
            });
        }

        if (isset($filters['cssn']) && $filters['cssn'] != '') {
            // if the SSN is 4 digits, match the last 4 digits
            if(strlen($filters['cssn']) == 4){
                $query->where('userinfo_policy_address.cssn4', $filters['cssn']);
            } elseif(strlen($filters['cssn']) == 9){
                // if the SSN is 9 digits, match the full SSN
                $query->where('userinfo_policy_address.cssn', DecryptEcryptHelper::encryptInfo($filters['cssn']));
            }
        }

        if (isset($filters['bank_account4']) && $filters['bank_account4'] != '') {
            $query->where(function ($query) use ($filters) {
                $query->whereHas('eftInfo', function ($q) use ($filters) {
                    $q->where('bank_account4', $filters['bank_account4']);
                })->orWhereHas('recurringEftInfo', function ($q) use ($filters) {
                    $q->where('bank_account4', $filters['bank_account4']);
                });
            });
        }

        if (isset($filters['dependent_name']) && $filters['dependent_name'] !== '') {
            $dependentName = $filters['dependent_name'];
            $query->whereHas('dependents', function ($subQuery) use ($dependentName) {
                CommonHelper::filterDependentsByName($subQuery, $dependentName);
            });
        }

        if (isset($filters['state']) && $filters['state'] != '') {
            $query->whereIn('userinfo_policy_address.state', $filters['state']);
        }
    }

    private function getFilteredPolicies($columnName, $value){
        $policyQuery = Policy::where($columnName, $value);
        if($value == 'monthly'){
            $policyQuery->orWhereNull('pay_type');
        }
        return $policyQuery
        ->groupBy('policy_id')
        ->pluck('policy_id')
        ->toArray();
    }

    private function getNbInvoiceFilteredPolicyIds($columnName, $value)
    {
        return NbInvoice::where($columnName, $value)
            ->whereDate('invoice_start_date', '>=', '2020-01-01')
            ->groupBy('invoice_policy_id')
            ->pluck('invoice_policy_id')
            ->toArray();
    }

    public function formattedData($data, $emailsAndPhones)
    {
        $mappedData = [];
        foreach ($data as $d) {
            $mappedData[] = $this->clientsHelper->mapData($d, $emailsAndPhones);
        }

        return $mappedData;
    }

    public function clientsPlansDetail($request){
        return PlanOverview::select('pid','plan_name_system')->groupBy('pid')->where('plan_name_system', 'like', "%{$request['data']}%")->limit($request['limit'])->get();
    }

    public function clientsPlansCat(){
        return Plan::select('plan_cat')
            ->where([
                ['plan_cat', '!=', null],
                ['plan_cat', '!=', ''],
                ['status', '=', '1']
            ])
            ->groupBy('plan_cat')->get()
            ->map(function ($each) {
                return [
                    'code' => $each->plan_cat,
                    'fullform' => $each->plan_cat_full_form
                ];
            });
    }

    public function clientsPlansType($carrierId = ''){
        $query = Plan::select('pl_type')
            ->where([
                ['pl_type', '!=', null],
                ['pl_type', '!=', ''],
                ['status', '=', '1'],
            ]);

            if(!empty($carrierId)){
                if (is_array($carrierId)) {
                    $query->whereIn('carrier', $carrierId);
                } else {
                    $query->where('carrier', $carrierId);
                }
            }

            return $query->groupBy('pl_type')->get()
            ->map(function ($each) {
                return [
                    'code' => $each->pl_type,
                    'fullform' => $each->plan_type_full_form
                ];
            });
    }

    public function getPolicyByFilter($filter)
    {
        $query = UserInfoPolicyAddress::where(function ($query) use ($filter) {
            if ($filter['searchValue']) {
                $this->getSearchValueQuery($query, $filter['searchValue']);
            }
        });
        $totalData = $query->get()->count();
        $data = $query->with('getGroup')->with('policyUpdates')
            ->with('plans')->with('policy')->with('creditCardInfo')
            ->with('recurringCreditCardInfo')->with('eftInfo')->with('recurringEftInfo')
            ->orderBy($filter['column'], $filter['dir'])
            ->paginate(10)
//            ->offset($filter['start'])
//            ->limit($filter['limit'])
            ->get();
        return ['totalData' => $totalData, 'data' => $data];
    }

    public function getSearchValueQuery($query, $searchValue)
    {
        return $query->where('policy_id', 'LIKE', "%{$searchValue}%")->orWhere('cfname', 'LIKE', "%{$searchValue}%")
            ->orWhere('clname', 'LIKE', "%{$searchValue}%")->orWhere('agent_fname', 'LIKE', "%{$searchValue}%")
            ->orWhere('agent_lname', 'LIKE', "%{$searchValue}%")
            ->orWhere('effective_date', 'LIKE', "%{$searchValue}%")->orWhere('status', 'LIKE', "%{$searchValue}%")
            ->orWhereRaw("concat(cfname, ' ', clname) like '%{$searchValue}%' ")
            ->orWhereRaw("concat(agent_fname, ' ', agent_lname) like '%{$searchValue}%' ");
    }

    public function getClientDetails($request)
    {
        $query = UserInfoPolicyAddress::where('policy_id', $request->policy_id)
            ->with('plans')
            ->with('getGroup')
            ->with('beneficiary')
            ->with('dependents')
            ->with('creditCardInfo')
            ->with('recurringCreditCardInfo')
            ->with('eftInfo')
            ->with('recurringEftInfo')
            ->first();
        return $query;
    }

    //function to reset app security
    public function resetAppSecurity($request)
    {
        try {
            $policy_id = $request->input('policy_id');
            $str = $this->gfRandomString('223');

            $updateTok = Policy::where('policy_id', $policy_id)->update(['fu_d_tok' => $str]);
            if (empty($updateTok))
                throw new Exception('No Update');
            $msg = "Security Reset you can now download, view or resend app.";
            return array('type' => 'success', 'response' => $msg);

        } catch (Exception $e) {
            $response = false;
            $message = $e->getMessage();
            return array('response' => $response, 'message' => $message);
        }
    }

    function gfRandomString($length)
    {
        $alphabet = "abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789";
        $pass = array(); //remember to declare $pass as an array
        $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
        for ($i = 0; $i < $length; $i++) {
            $n = rand(0, $alphaLength);
            $pass[] = $alphabet[$n];
        }
        return implode($pass); //turn the array into a string
    }

    public function deleteTestPol($request)
    {
        $policy_id = $request->input('policy_id');
        $userid = $request->input('userid');
        try {
            $this->deleteById('signatures', 'userid', $policy_id);

            $this->deleteById('policy_notes', 'policy_id', $policy_id);

            $this->deleteById('policy_benificiery_deps', 'bpolicy_id', $policy_id);

            $this->deleteById('policy_benificiery', 'bpolicy_id', $policy_id);

            $this->deleteById('policy_client_browser_info', 'policy_id', $policy_id);

            $this->deleteById('enrollment_question_answers', 'pid', $policy_id);

            $this->deleteById('plan_policies', 'policy_num', $policy_id);
            $this->deleteMedAns($policy_id, $userid);

            $this->deleteMedicine($userid);

            $dep_id = $this->get_dependents($policy_id);
            if ($dep_id->isNotEmpty()){
                foreach ($dep_id as $depID) {

                    $this->deleteDepsMedAns($depID->dependent_id);

                    $this->deleteDepsMedicine($userid);
                }
            }

            $this->deleteById('dependent_policies', 'policy_id', $policy_id);

            $this->deleteById('policies', 'policy_id', $policy_id);
            $msg = "Test Policy deleted successfully.";
            DB::commit();
            return $this->successResponse($msg);
        } catch (Exception $e) {
            DB::rollBack();
            $message = "Failed to delete.";
            return $this->failedResponse($message);
        }
    }

    public function deleteById($tableName, $field, $policyId)
    {
        $data =  DB::table($tableName)->where($field,$policyId)->get();
        if ($data->isNotEmpty()) {
            DB::table($tableName)->where($field,$policyId)->delete();
        }
    }


    function deleteMedAns($policy_id, $userid)
    {
        $data =  DB::table('userinfo_dep_meds_bex')->where('user_id', $userid)->get();
        if ($data->isNotEmpty()) {
            DB::table('userinfo_dep_meds_bex')->where('user_id', $userid)->delete();
        }
    }

    function deleteMedicine($userid)
    {
        $data =   DB::table('med_medications')->where('user_id', $userid)->get();
        if ($data->isNotEmpty()) {
            DB::table('med_medications')->where('user_id', $userid)->delete();
        }
    }

    function get_dependents($policy_id)
    {
        $depId = DB::table('dependents_in_policy')->where('policy_id', $policy_id)->select('dependent_id')->get();
        return $depId;
    }

    function deleteDepsMedAns($dep_id)
    {
        $medID = DB::table('userinfo_dep_meds_bex')->where('dependent_id', $dep_id)->select('med_id')->get();
        if (isset($medID)){
            foreach ($medID as $value) {
                $data =   DB::table('med_conditions')->where('med_id', $value->med_id)->get();;
                if ($data->isNotEmpty()) {
                    DB::table('med_conditions')->where('med_id', $value->med_id)->delete();
                }
            }
        }
    }

    function deleteDepsMedicine($userid)
    {
        $data =   DB::table('med_medications')->where('user_id', $userid)->get();;
        if ($data->isNotEmpty()) {
            DB::table('med_medications')->where('user_id', $userid)->delete();
        }
    }

    //        $columns = ['policy_id', 'cfname', 'effective_date', 'agent_fname'];
//        $filter['limit'] = $request->input('length') ?? 10;
//        $filter['start'] = $request->input('start') ?? 1;
//        $filter['column'] = $columns[$request->input('order.0.column')] ?? $columns[0];
//        $filter['dir'] = $request->input('order.0.dir') ?? 'DESC';
//        $filter['searchValue'] = $request->input('search.value');
    // Get agent group asscociate platforms
    public function getAgentPlatform($agent_id){
        $data = [];
        try{
            $result = DB::table('agents_ingroup AS aig')
                    ->join('group_info AS gi','aig.gid', '=', 'gi.gid')
                    ->where('gi.portal',1)
                    ->where('aig.agent_id',$agent_id)
                    ->select('agent_id','gweb_display_name','platform_website','portal')
                    ->get();
            if(count($result) > 0){
                $data = $result;
            }
        }catch(\Exception $e){
            return $this->failedResponse($e);
        }
        return $this->successResponse('Success', $data);
    }
    // Get the active association for policy
    public function getActivePolicyAssoc($policy_id){
        $data = null;
        try{
            $policy_assoc = PlanOverview::where('policy_id',$policy_id)
                            ->whereIn('pstatus', [1,2])
                            ->where('is_assoc',1)
                            ->whereNull('term_date')
                            ->orWhere('term_date','>',date('Y-m-j'))
                            ->orderby('p_ai','DESC')
                            ->first();
            if(isset($policy_assoc)){
                $data = $policy_assoc;
            }
        }catch(\Exception $e){
            return $this->failedResponse($e);
        }
        return $data;
    }
    // Get the active plan for policy
    public function getActivePolicyPlan($policy_id){
        $data = [];
        try{
            $policy_plan = PlanOverview::where('policy_id',$policy_id)
                            ->whereIn('pstatus', [1,2])
                            ->where('is_assoc','!=',1)
                            ->whereNull('term_date')
                            ->orWhere('term_date','>',date('Y-m-j'))
                            ->pluck('pid')->toArray();
            if(count($policy_plan)){
                $data = $policy_plan;
            }
        }catch(\Exception $e){
            return $this->failedResponse($e);
        }
        return $data;
    }
    // get the platform info
    public function getPlatformInfo($website,$policy_plans){
        $data = null;
        try{
            $result = DB::table('new_assoc_fee as af')
                        ->join('plan_pricing as pp','af.a_ppid','=','pp.plan_pricing_id')
                        ->where('website', 'LIKE', '%' . $website . '%')
                        ->whereIn('pid', $policy_plans)
                        ->select('pid','a_pid','a_ppid','website','price_male_nons')
                        ->orderBy('price_male_nons','ASC')
                        ->first();
            if(isset($result)){
                $data = $result;
            }
        }catch(\Exception $e){
            return $this->failedResponse($e);
        }
        return $data;
    }
    // Update the platform
    public function updateAgentPlatform($request){
        $message    = "";
        $termDate   = date('Y-m-j');
        $termedReqRepository = new TermRequestRepository();
        $loginUserId = $request->login_user;

        $policy_id  = $request->policy_id;
        $agent_id   = $request->agent_id;
        $data = [
            'weburl'  => $request->platform_website,
            'comment' => $request->comment,
        ];
        try{
            // Get an existing platform
            $get_existing_platform = DB::table('policies')->where('policy_id',$policy_id)->select('weburl')->first();
            if(isset($get_existing_platform) && $get_existing_platform->weburl == $request->platform_website){
                $message = "The selected platform remains the same.";
                return $this->failedResponse($message);
            }else{
                // Update the policies table using policy_id and agent_id [p_agent_num],
                $result = DB::table('policies')->where('policy_id',$policy_id)->where('p_agent_num',$agent_id)->update($data);
                if($result){
                    $message = "Successfully updated the platform.";
                    // Create a Log
                    $update_note = "The platform changed successfully to ".$request->platform_website;
                    $termedReqRepository->policyUpdateLog($policy_id, 'PTCHNG', $update_note, $termDate, $loginUserId);

                    $log_data = [
                        'policy_id' => $policy_id,
                        'weburl_old'=> $get_existing_platform->weburl,
                        'weburl_new'=> $request->platform_website,
                        'comment'   => $request->comment,
                        'update_by' => $loginUserId,
                        'created_at'=> date('Y-m-j')
                    ];
                    DB::table('policy_platform_change_log')->insert($log_data);
                }
            }

        }catch(\Exception $e){
            return $this->failedResponse($e);
        }
        return $this->successResponse('Success', $message);
    }

    function getClientDetail($id){
        return UserInfoPolicyAddress::where('policy_id',$id)->select('cemail as email','phone1 as phone','userid as user_id')->first();
    }

    public function clientPlanPricing($request){
        $plans = Planoverview::select(['policy_id','web_display_name','tier','user_dob','pstatus','cid','price_male_nons','pterm_date','pid','plan_id','plan_cat','esig','status','eprocess','plan_pricing_type','pl_type'])
        ->where('policy_id',$request->policy_id)
        ->where('pstatus',1)
        ->get()
        ->all();

        foreach ($this->clientsHelper->formatPlanData($plans , null, null , 'external') as $key=> $item) {
            $restructuredPlans[$item['pid']] = $item;
        }
        return $restructuredPlans;
    }

    public function changeMemberPassword($request)
    {
        try{
        $user = SsoUsers::where([['email',$request->member_email],['user_type','M']])->first();
        $member = UserLogin::where('username', $request->member_email)->first();
        if (!$user || !$member) return $this->failedResponse('This user is not registered as sso user');
            $user->password = Hash::make($request->confirm_password);
            if ($user->save())
            {
                $member->password = $user->password;
                if ($member->save())
                {
                    return $this->successResponse('Password updated successfully.');
                }
            }
            return $this->failedResponse('Password update failed.');
        }catch(Exception $e)
        {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getTermedDates($effectiveDate)
{
    $effective_date = Carbon::createFromFormat('Y-m-d', $effectiveDate)->format('Y-m');
    $currentDate = Carbon::now()->format('Y-m');
    $months = [];

    if ($effective_date >= $currentDate) {
        for ($i = 0; $i <= 12; $i++) {
            $date = Carbon::parse($effective_date)->addMonthWithOverflow((int) $i)->endOfMonth()->format('Y-m-d');
            $months[] = $date;
        }
    } else {
        $diff = $this->monthDiff($effective_date, $currentDate);
        if ($diff <= 6) {
            for ($i = 0; $i <= 12; $i++) {
                $date = Carbon::parse($effective_date)->addMonthWithOverflow((int) $i)->endOfMonth()->format('Y-m-d');
                $months[] = $date;
            }
        } else {
            for ($i = 5; $i >= 0; $i--) {
                $date = $this->getCurrentDateSub($currentDate, $i);
                $months[] = $date;
            }
            for ($i = 1; $i <= 7; $i++) {
                $date = $this->getMonthAddEndDate($currentDate, $i);
                $months[] = $date;
            }
        }
    }

    return array_merge([$effectiveDate], $months);
}

private function getCurrentDateSub($date, $sub)
{
    return Carbon::parse($date)->subMonthsWithOverflow((int) $sub)->endOfMonth()->format('Y-m-d');
}

private function getMonthAddEndDate($date, $add)
{
    return Carbon::parse($date)->addMonthWithOverflow((int) $add)->endOfMonth()->format('Y-m-d');
}

private function monthDiff($date1, $date2)
{
    $d1 = Carbon::parse($date1);
    $d2 = Carbon::parse($date2);
    return $d1->diffInMonths($d2);
}
}
