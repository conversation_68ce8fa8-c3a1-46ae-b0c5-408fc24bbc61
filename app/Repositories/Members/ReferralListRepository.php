<?php

namespace App\Repositories\Members;

use App\MemberReferral;
use App\Traits\Paginator;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

class ReferralListRepository
{
    use Paginator;

    /**
     * Retrieve a paginated list of member referrals.
     *
     * @param int $refId
     * @param int $limit
     * @param array $filters
     * @return array
     */
    public function paginatedList(int $refId, int $limit, array $filters = []): array
    {
        $query = $this->buildQuery($refId, $filters);
        $data = $query->paginate($limit);
        $result = $this-> formattedData($refId, $data);

        return [
            'links' => $this->links($data),
            'meta' => $this->meta($data),
            'data' => $result
        ];
    }

    /**
     * Retrieve a paginated list of member referrals.
     * @param object $data
     * @return object
     */


    public function formattedData(int $refId, object $data): object
    {
        return $data->map(function ($d) use($refId) {
            $userInfo = ($refId !== 0)
                ? $this->getUserInfoWithPlans($refId, $d->userid)
                : $this->getUserInfoWithPlans($d->ref_by, $d->userid);

            $planNames = $this->extractPlanNames($userInfo);

            return [
                'id' => $d->id,
                'name' => $d->name,
                'title' => $d->title,
                'message' => $d->message,
                'email' => $d->email,
                "redeemed" => $d->redeemed,
                "redeem_date" => $d->redeemed_date,
                "is_complete" => $d->is_complete,
                "enrolled_date" => $d->enrolled_date,
                "incentive" => $d->incentive,
                "referred_platform" => $d->referred_platform,
                "has_major_medical_plans" => $d->has_major_medical_plans,
                'ref_by' => $d->ref_by,
                'referred_by' => $this->getFullName($userInfo),
                'plan_names' => !empty($planNames) ? $planNames : null,
                'created_at' => $d->created_at->format('m/d/Y'),
                'updated_at' => $d->updated_at->format('m/d/Y'),
            ];
        });
    }

    /**
     * Build the base query for fetching member referrals.
     *
     * @param int $refId
     * @param array $filters
     * @return Builder
     */
    protected function buildQuery(int $refId, array $filters): Builder
    {
        $query = MemberReferral::query()
            ->select([
                'id',
                'name',
                'title',
                'message',
                'email',
                'incentive',
                'redeemed',
                'redeem_date',
                'is_complete',
                'enrolled_date',
                'created_at',
                'updated_at',
                'referred_platform',
                'has_major_medical_plan',
                'ref_by'
            ])
            ->when($refId !== 0, function ($q) use ($refId) {
                return $q->where('ref_by', $refId);
            });

        $this->applyFilters($query, $filters);

        return $query;
    }

    /**
     * Apply filters to the query.
     *
     * @param Builder $query
     * @param array $filters
     * @return void
     */
    protected function applyFilters(Builder $query, array $filters): void
    {
        $sortOrder = $filters['sort'] ?? 'desc';

        if (in_array($sortOrder, ['asc', 'desc'], true)) {
            $query->orderBy('id', $sortOrder);
        }

        if (!empty($filters['created_at'])) {
            $query->where(function ($subQuery) use ($filters) {
                $subQuery->whereDate('created_at', 'LIKE', '%' . $filters['created_at'] . '%')
                    ->orWhereDate('updated_at', 'LIKE', '%' . $filters['created_at'] . '%');
            });
        }

        if (!empty($filters['search'])) {
            $query->where('name', 'LIKE', '%' . $filters['search'] . '%');
        }
    }

    /**
     * Fetch user info along with associated plans.
     */
    private function getUserInfoWithPlans(int $refId, $userid)
    {
        return UserInfoPolicyAddress::query()
            ->with([
                'plans' => function ($query) use ($userid) {
                    $query->select('policy_id', 'web_display_name');

                    if (!is_null($userid)) {
                        $query->where('userid', $userid);
                    }
                }
            ])
            ->select('cfname', 'cmname', 'clname', 'policy_id')
            ->where('userid', $refId)
            ->first();
    }

    /**
     * Extract and return plan names.
     */
    private function extractPlanNames($userInfoAddress)
    {
        if (!is_object($userInfoAddress) || !$userInfoAddress->plans) {
            return [];
        }

        if (count($userInfoAddress->plans) > 0 || !$userInfoAddress->plans->isEmpty()) {
            return $userInfoAddress->plans->pluck('web_display_name')->toArray();
        }

        return [];
    }

    /**
     * Get the full name of the user.
     */
    private function getFullName($userInfoAddress): ?string
    {
        if (!is_object($userInfoAddress)) {
            return null;
        }

        if(is_null($userInfoAddress->cmname)) {
            return trim("{$userInfoAddress->cfname} {$userInfoAddress->clname}");
        }

        return trim("{$userInfoAddress->cfname} {$userInfoAddress->cmname} {$userInfoAddress->clname}");
    }
}
