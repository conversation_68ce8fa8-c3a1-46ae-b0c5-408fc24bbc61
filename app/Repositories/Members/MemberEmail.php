<?php


namespace App\Repositories\Members;


use App\Helpers\CheckPlanTypeHelper;
use App\Helpers\GuzzleHelper;
use App\Helpers\SendEmailMemberHelper;
use App\Policy;
use App\PolicyUpdate;
use App\UserActivity;
use App\UserInfo;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MemberEmail extends Model
{
    public function sendMemberScheduledEmail($schedule)
    {

        //Check for Approved Policies after January 20, 2021 (1611100800). Email will be send to users registered after Jan 20, 2021
        $totalEmailSend=0;
        $memberToEmailDay2=0;
        $memberToEmailDay6=0;
        $memberToEmailDay10=0;
        $memberToEmailDay15=0;
        $totalFailedCount=0;
        $sentDetails=[];
        $failedDetails=[];
        $usagAgentId = 101547;

        $policyUpdates = PolicyUpdate::where([
                ['elgb_act_date','>',1611100800],
                ['elgb_act','=','APPROVE']
            ])->get();

        foreach($policyUpdates as $policyUpdate){

            $shouldSendEmail = 0;
            $emailMessageConfiguration = NULL;
            $subject = NULL;
            $userInfo = NULL;
            $policy = Policy::where('policy_id', $policyUpdate->elgb_policyid)
                ->where('p_agent_num', '!=', $usagAgentId)
                ->first();

            if ( isset($policy) ) {

                $dateDiffDay=0;
                $dateDiffHour=0;
                $userInfo = UserInfo::where([
                    ['userid','=',$policy->policy_userid]
                ])->first();
                $policyEnrollDate = date('Y-m-d',$policy->edate);
                if(isset($userInfo)){
                    $userEnrollDate = date('Y-m-d',strtotime($userInfo->created_at));
                    if($userEnrollDate==$policyEnrollDate && ($policy->Approval=='1')){
                        $shouldSendEmail=1;
                    }
                }

                if ($schedule == 'HOURLY' && $shouldSendEmail==1 && isset($userInfo)) {
                    $dateDiffHour = floor(abs(time() - $policyUpdate->elgb_act_date)/(60*60));
                    if ($dateDiffHour==1 && $policy->Approval==1) {

                        $emailMessageConfiguration = 'ELEVATE_MEMBER_WELCOME_NOTIFICATION';
                        $subject = 'Welcome to Elevate Wellness Association - Next Steps';

                    }
                } elseif ($schedule=='DAILY') {
                    $dateDiffDay = ceil(abs(time() - $policyUpdate->elgb_act_date)/(60*60*24));

                    if ($dateDiffDay==3 && $policy->Approval==1 && isset($userInfo)) {

                        $emailMessageConfiguration = 'ELEVATE_MEMBER_BENEFIT_STORE_NOTIFICATION';
                        $subject = 'Elevate Wellness Association - Your Benefit Store';

                    } else {
                        if ( $shouldSendEmail==1 && $policy->Approval ) {
                            $appLogin = UserActivity::where([
                                ['uid','=',optional($userInfo)->userid],
                                ['from_mobile','=','1']
                            ])->first();
                            $dashboardLogin = UserActivity::where([
                                [ 'uid','=',optional($userInfo)->userid ],
                                [ 'from_mobile','=','0' ]
                            ])->first();
                            //Send email to members registered after Jan 20, 2021

                            if (($dateDiffDay==2 || $dateDiffDay==10 || $dateDiffDay==15) && !isset($appLogin)){

                                $emailMessageConfiguration = 'ELEVATE_MEMBER_REGISTER_MOBILE_APP_NOTIFICATION';
                                $subject = 'Elevate Wellness Association - Mobile App';

                                // if ( !isset($dashboardLogin) && $dateDiffDay==15){
                                //     $emailMessageConfiguration = 'ELEVATE_WELLNESS_MOBILE_WEB_DASHBOARD_NOTIFICATION';
                                //     $subject = 'Elevate Wellness Association - Mobile App / Dashboard';
                                // }
                            }
                        }
                    }
                }

                if ($emailMessageConfiguration != NULL) {
                    $result = SendEmailMemberHelper::sendEmailMember($policy->policy_id,$emailMessageConfiguration,$subject);
                    if ( isset($result) && $result['type'] == 'success') {
                        ECHO "Email sent successfully. Policy Id: ".$policyUpdate->elgb_policyid." \n";
                        if ($dateDiffDay == 2) {
                            $memberToEmailDay2 += 1;
                        } elseif ($dateDiffDay==6) {
                            $memberToEmailDay6 += 1;
                        } elseif ($dateDiffDay==10) {
                            $memberToEmailDay10 += 1;
                        } elseif ($dateDiffDay==15) {
                            $memberToEmailDay15 += 1;
                        }
                        $totalEmailSend += 1;
                        array_push($sentDetails,array('sn'=>$totalEmailSend,'member_name'=>$userInfo->cfname.' '.$userInfo->clname,'member_email'=>$userInfo->cemail));
                    } else {
                        ECHO "Failed to send email. Policy Id: ".$policyUpdate->elgb_policyid." \n";
                        $totalFailedCount += 1;
                        array_push($failedDetails,array('sn'=>$totalFailedCount,'member_name'=>$userInfo->cfname.' '.$userInfo->clname,'member_email'=>$userInfo->cemail));
                    }
                } else {
                    ECHO "Email notification not sent. Policy Id: ".$policyUpdate->elgb_policyid." \n";
                }
            }
        }

        $totalCount = $policyUpdates->count();
        if($totalCount>0){
            $memberToEmailDay2_percentage = number_format(round(($memberToEmailDay2*100)/$totalCount,2),2);
            $memberToEmailDay6_percentage = number_format(round(($memberToEmailDay6*100)/$totalCount,2),2);
            $memberToEmailDay10_percentage = number_format(round(($memberToEmailDay10*100)/$totalCount,2),2);
            $memberToEmailDay15_percentage = number_format(round(($memberToEmailDay15*100)/$totalCount,2),2);
        } else {
            $totalInactiveCount=0;
            $memberToEmailDay2_percentage = number_format(0,2);
            $memberToEmailDay6_percentage = number_format(0,2);
            $memberToEmailDay10_percentage = number_format(0,2);
            $memberToEmailDay15_percentage = number_format(0,2);
        }

        $contentData = [
            'member_email_day_2' => $memberToEmailDay2,
            'member_email_day_2_percentage' => $memberToEmailDay2_percentage,
            'member_email_day_6' => $memberToEmailDay6,
            'member_email_day_6_percentage' => $memberToEmailDay6_percentage,
            'member_email_day_10' => $memberToEmailDay10,
            'member_email_day_10_percentage' => $memberToEmailDay10_percentage,
            'member_email_day_15' => $memberToEmailDay15,
            'member_email_day_15_percentage' => $memberToEmailDay15_percentage,
            'total_inactive_agents' => $totalCount,
            'total_email_send' => $totalEmailSend,
            'total_failed_count' => $totalFailedCount,
            'sent_email_details' => $sentDetails,
            'failed_details' => $failedDetails
        ];

        if($schedule=='DAILY'){
            $toAddress = config("testemail.TEST_EMAIL") ? [config("testemail.TEST_EMAIL")] : ['<EMAIL>','<EMAIL>'];
            $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [];
            $bccAddress = config("testemail.TEST_EMAIL_BCC") ? [config("testemail.TEST_EMAIL_BCC")] : [];
            $body = [
                'email_message_configuration_name'=> 'MEMBER_EMAIL_SUMMARY_REPORT',
                'toAddress'=> $toAddress,
                'ccAddress'=> $ccAddress,
                'bccAddress'=> $bccAddress,
                'subject'=> 'Member Email Summary '.date('Y-m-d'),
                'attachedFiles'=> [],
                'generalTemplateData'=> [],
                'contentData'=> $contentData
            ];

            $apiUrl = config('app.messagecenter.key').'api/v1/send-email-with-content-data';
            $responseJson = GuzzleHelper::postApi($apiUrl,[],$body);
            $response = json_decode($responseJson,true);

            if( isset($response['status_code']) && $response['status_code']=='200' && $response['status']=='success'){
                ECHO "Email Notification sent successfully with email summary.\n";
            }else{
                ECHO "Email Notification sent successfully.\n";
            }
        }
    }

    public function sendMemberMedicalPlanEffectiveNotification()
    {
        $date = date('Y-m-d', strtotime(' -1 day'));
        $policies = Policy::where('effective_date','=',$date)->where('Approval','1')->where('status','ACTIVE')->get();
        foreach ($policies as $policy) {
            $result = null;
            $type = CheckPlanTypeHelper::checkPlanType($policy->policy_id);
            $isExtraHealth = CheckPlanTypeHelper::isPlanExtraHealth($policy->policy_id);
            ECHO "Type ".$type. "ExtraHealth".$isExtraHealth."";
            if($type == 1){
                $subject = 'Member Approval Email';
                $emailMessageConfiguration = 'MEMBER_MEDICAL_PLAN_WITHOUT_RIDER_EMAIL_NOTIFICATION';
                $result = SendEmailMemberHelper::sendEmailMember($policy->policy_id,$emailMessageConfiguration,$subject);
            } elseif($type == 0) {
                if($isExtraHealth) {
                    ECHO "ExtraHealth".$isExtraHealth."";
                }
                else {
                    $subject = 'Member Approval Email';
                    $emailMessageConfiguration = 'ELEVATE_MEMBER_WELCOME_NOTIFICATION';
                    $result = SendEmailMemberHelper::sendEmailMember($policy->policy_id, $emailMessageConfiguration, $subject);
                }
            } else {
                $subject = 'Member Approval Email';
                $emailMessageConfiguration = 'MEMBER_PLAN_WITH_RIDER_APPROVED_EMAIL_NOTIFICATION';
                $result = SendEmailMemberHelper::sendEmailMember($policy->policy_id,$emailMessageConfiguration,$subject);
            }
            if(isset($result['type']) && $result['type'] == 'success'){
                ECHO "Type ".$type. "ExtraHealth".$isExtraHealth." Email sent to user with Policy: ".$policy->policy_id."\n";
            } else {
                ECHO "No email sent for user with Policy: ".$policy->policy_id."\n";
            }
        }
    }

    public function sendMemberAlertEmailPolicyUpgrade()
    {
        set_time_limit(0);
        $query = "select u.cfname as cfname, agent_fname, agent_lname, agent_email, u.cemail as cemail from plan_overview as p join userinfo_policy_address as u on u.policy_id = p.policy_id where pl_type = 'MM' and p.status = 'ACTIVE' and pid in (642,643,581,582,644,638,639,636,637,640,942,800,775,776,777,386,795,796,794,767,769,768,756,755,706,705,707,399,535,473,472,474,540,400,539,538,696,792,793,791,384,502,503,630,629,627,556,467,466,626,628,722,631,468,557,470,469,471,697,695,385,634,635,699,901,902,900,632,580,505,506,633,504,700,698,388) and pstatus = '1'";

        $data = DB::select($query);

        $responseMessage = [];
        foreach ( $data as $row ) {
            $contentData = [
                'member_first_name' => $row->cfname,
                'rep_first_name'=> $row->agent_fname,
                'rep_last_name' => $row->agent_lname,
                'rep_email' => $row->agent_email
            ];

            $toAddress = config("testemail.TEST_EMAIL") ? [config("testemail.TEST_EMAIL")] : [$row->cemail];
            $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [];
            $bccAddress = config("testemail.TEST_EMAIL_BCC") ? [config("testemail.TEST_EMAIL_BCC")] : [];
            $body = [
                'email_message_configuration_name'=> 'MEMBER_ALERT_AFLAC_ACCIDENT_POLICY_ADDED',
                'toAddress'=> $toAddress,
                'ccAddress'=> $ccAddress,
                'bccAddress'=> $bccAddress,
                'subject'=> 'Alert – Upgrade to your coverage
',
                'attachedFiles'=> [],
                'generalTemplateData'=> [],
                'contentData'=> $contentData
            ];

            $apiUrl = config('app.messagecenter.key').'api/v1/send-email-with-content-data';
            $responseJson = GuzzleHelper::postApi($apiUrl,[],$body);
            $response = json_decode($responseJson,true);

            if( isset($response['status_code']) && $response['status_code']=='200' && $response['status']=='success'){
                $message = "Email Notification sent successfully for ".$row->cemail."\n";
            }else{
                $message = "Email Notification not sent for ".$row->cemail."\n";
            }
            Log::info($message);
            array_push($responseMessage,$message);
            sleep(1);
        }
        return $responseMessage;
    }

}
