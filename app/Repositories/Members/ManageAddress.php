<?php

namespace App\Repositories\Members;

use App\AddressDateLog;
use App\Service\CustomValidationService;
use App\State;
use App\Address;
use App\UserInfo;
use App\CreditCardInfo;
use App\GroupInfo;
use App\PlanOverview;
use App\HomePageConfiguration;
use App\Policy;
use App\Plan;
use App\PlanZip;
use App\AgentInfo;
use App\PlanPolicy;
use App\PlanPricing;
use App\PlanPricingDisplay;
use App\UserInfoPolicyAddress;
use App\Repositories\Plans\PlanChangeOnAddress;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ManageAddress extends Model
{
        /**
     * @var CustomValidationService
     */
    private $validationService;
    public function __construct(
    )
    {
        $this->validationService = new CustomValidationService();
        $this->PlanAddressChange = new PlanChangeOnAddress();
    }

    public function listStateDetail()
    {
        return State::where('status', '1')->orderBy('name', 'ASC')->get();
    }

    public function insertPolicyAddress($request): array
    {
        DB::beginTransaction();
        if ($request['is_usps_valid'] == 1) {
            $this->validationService->validateAddressUSPS(
                $request['address1'],
                $request['address2'],
                $request['city'],
                $request['state'],
                $request['zip']
            );
        }
        try {
            if (!UserInfo::where('userid', $request->a_userid)->count()) {
                return array('type' => 'error', 'message' => 'User not found');
            }
            $creditCardaddresses = CreditCardInfo::where('credit_userid', $request->a_userid)->get(['credit_addressid', 'credit_shipaddress']);
            /* all address list if user */
            $addressList = Address::where('a_userid', $request->a_userid)->where('type', $request->type)->pluck('address_id')->toArray();
            /* creditcard address id list of user */
            $crediCardAddressIds = [];
            if (!$creditCardaddresses->isEmpty()) {
                foreach ($creditCardaddresses->toArray() as $addressId) {
                    $crediCardAddressIds = array_merge($crediCardAddressIds, array_values($addressId));
                }
            }
            /* set status 0 to all existing address except credit card address */
            Address::where('a_userid', $request->a_userid)
                ->whereNotIn('address_id', $crediCardAddressIds)
                ->where('type', $request->type)
                ->update(['status' => 0]);
            AddressDateLog::whereIn('cust_address_id', $addressList)
                ->whereNotIn('cust_address_id', $crediCardAddressIds)
                ->update(['to_date' => date('Y-m-d')]);
            $addressNew['a_userid'] = $request->a_userid;
            $addressNew['address1'] = $request->address1;
            $addressNew['address2'] = $request->address2;
            $addressNew['city'] = $request->city;
            $addressNew['state'] = $request->state;
            $addressNew['zip'] = $request->zip;
            $addressNew['type'] = $request->type;
            if(isset($request->billing_name)) {
                $addressNew['billing_name'] = $request->billing_name;
            }
            $addressNew['status'] = '1';
            $addressNew['is_usps_valid'] = $request->is_usps_verified;

            $userInfo = UserInfo::find($request->a_userid);
            $userPolicyIDs = $userInfo->getPolicyList
                ->filter(function($eachPolicy) use ($request) {
                    return $eachPolicy->policy_id != $request->policy_id;
                })
                ->pluck('policy_id')
                ->prepend((int) $request->policy_id);

            foreach ($userPolicyIDs as $eachPolicyID) {
                $request->policy_id = $eachPolicyID;
                $changePlansToNewAddress = $this->PlanAddressChange->checkPlanAndChange($request, $addressNew);
                if(isset($changePlansToNewAddress['error'])){
                    break;
                }
            }

            if ($changePlansToNewAddress && !isset($changePlansToNewAddress['error'])) {
                $insertId = Address::insertGetId($addressNew);
                if ($insertId) {
                    AddressDateLog::insert(['cust_address_id' => $insertId, 'from_date' => date('Y-m-d')]);
                    $saved = $this->updateUserInfo($request->type, $request->a_userid, $insertId);

                    foreach ($userPolicyIDs as $eachPolicyID) {
                        $policy['policy_id'] = $eachPolicyID;
                        $this->PlanAddressChange->updatePolicyLog($policy, 'ADR',
                            'Address changed from backoffice. ' . $request->reason,
                            request()->header('id')
                        );
                    }
                } else {
                    $saved = ['error' => 'Failed adding address.'];
                }
            } else {
                $saved = ['error' => $changePlansToNewAddress['error'], 'error_type' => $changePlansToNewAddress['error_type']];
            }

            DB::commit();
            return $saved ?? ['error' => 'Failed adding address.'];
        } catch (Exception $e) {
            DB::rollBack();
            return ['error' => $e->getMessage()];
        }
    }

    public function changePolicyAddress($request)
    {
        try {
            $addresData = Address::where('address_id', $request->address_id)->where('status',0)->first();
            if (!$addresData instanceof Address) {
                return array('type' => 'error', 'message' => 'Address not found.');
            }
            $addressList = Address::where('a_userid', $addresData->a_userid)->where('type', $addresData->type)->pluck('address_id')->toArray();

            $creditCardaddresses = CreditCardInfo::where('credit_userid', $addresData->a_userid)->get(['credit_addressid', 'credit_shipaddress']);
            $crediCardAddressIds = [];
            if (!$creditCardaddresses->isEmpty()) {
                foreach ($creditCardaddresses->toArray() as $addressId) {
                    $crediCardAddressIds = array_merge($crediCardAddressIds, array_values($addressId));
                }
            }
            if(in_array($request->address_id,$crediCardAddressIds)) {
                return array('type' => 'error', 'message' => 'Cannot change addres: Address used for credit card.');
            }

            Address::where('a_userid', $addresData->a_userid)
                ->whereNotIn('address_id', $crediCardAddressIds)
                ->where('type', $addresData->type)
                ->update(['status' => 0]);
            Address::where('address_id',$addresData->address_id)->update(['status'=>1]);

            AddressDateLog::whereIn('cust_address_id', $addressList)
                ->whereNotIn('cust_address_id', $crediCardAddressIds)
                ->update(['to_date' => date('Y-m-d')]);
            AddressDateLog::insert(['cust_address_id' => $addresData->address_id, 'from_date' => date('Y-m-d')]);
            $saved = $this->updateUserInfo($addresData->type, $addresData->a_userid, $addresData->address_id);
            return $saved;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    private function updateUserInfo($type, $userid, $insertId)
    {
        try {
            $field = "home_address";
            if ($type == 'R') {
                $field = 'home_address';
            }
            if ($type == 'M') {
                $field = 'mailing_address';
            }
            if ($type == 'B') {
                $field = 'billing_address';
            }
            $saved = UserInfo::where('userid', $userid)->update([$field => $insertId]);
            if ($saved) {
                return array('type' => 'success', 'message' => 'Address Updated successfully.');
            } else {
                return array('type' => 'error', 'message' => 'Address not updated. User not found.');
            }
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
