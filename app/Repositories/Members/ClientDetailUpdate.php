<?php

namespace App\Repositories\Members;

use App\Beneficiary;
use App\Helpers\CommonHelper;
use App\Helpers\DecryptEcryptHelper;
use App\Helpers\PolicyUpdateHelper;
use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use App\Service\PlanAgeService;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use App\AgentInfo;
use App\Policy;
use App\PolicyUpdate;
use App\GroupInfo;
use App\PolicyBeneficiary;
use App\Dependent;
use App\Repositories\PolicyFeatures;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ClientDetailUpdate extends Model
{
    use ResponseMessage;

    private  $PolicyFeatures;

    public function __construct()
    {
        $this->PolicyFeatures = new PolicyFeatures();
    }

    public function UpdatePolicyAgent($request,$returnResponse = true)
    {
        $policy = Policy::where('policy_id', $request->policy_id)->first();
        $newAgent = AgentInfo::where('agent_code', $request->agent_code)->first();
        $oldAgent = AgentInfo::where('agent_id', $policy->p_agent_num)->first();

        try {
            $policy->update(['p_agent_num' => $newAgent->agent_id]);
            $this->PolicyFeatures->commonAssociationCheckDetailUpdate($request);

            $request->old_agent_id = $oldAgent->agent_id;
            $request->new_agent_id = $newAgent->agent_id;
            $request->reason = $request->reason .
                ". Agent Changed from $oldAgent->full_name($oldAgent->agent_id) to " .
                "$newAgent->full_name($newAgent->agent_id) with Effective Date $request->elgb_new_effdate";

            $this->addPolicyUpdates($request, PolicyUpdate::POLICY_AGENT_CHANGE);
            if($returnResponse){
                return response()->json(
                    new SuccessResource([
                        'statusCode' => Response::HTTP_OK,
                        'message' => 'Agent Updated in Policy',
                        'data' => null
                    ]), Response::HTTP_OK
                );
            }
        }
        catch (Exception $exception) {
            $errorCode = in_array($exception->getCode(), array_keys(Response::$statusTexts))
                ? $exception->getCode()
                : Response::HTTP_INTERNAL_SERVER_ERROR;

            return response()->json(
                new ErrorResource([
                    'statusCode' => $errorCode,
                    'message' => $exception->getMessage() ?? 'Agent Could not be updated.',
                    'data' => $exception->getFile() ?? null
                ]), $errorCode
            );
        }
    }

    public function addPolicyUpdates($request,$act)
    {
        try {
            $updates['elgb_policyid'] = $request->policy_id ?? $request['policy_id'];
            $updates['elgb_act'] = $act;
            $updates['elgb_comment'] = $request->reason ?? $request['reason'] ?? '';
            $updates['elgb_term_date'] = '';
            $updates['elgb_old_pid'] = $request->old_agent_id ?? $request['old_agent_id'] ?? '';
            $updates['elgb_new_pid'] = $request->new_agent_id ?? $request['new_agent_id'] ?? '';
            $updates['elgb_agent'] = $request->aid ?? request()->header('id') ?? '';
            $updates['elgb_act_date'] = time();
            $updates['elgb_file_date'] = date('Y-m-d');
            $updates['elgb_policyid_ol'] = '0';
            $updates['elgb_old_effdate'] = '';
            $updates['elgb_new_effdate'] = $request->elgb_new_effdate ?? '';
            $policies = PolicyUpdate::insertGetId($updates);
            $elgbid = $policies;
            $message = 'Policy Update Successfully Added';
        }catch (Exception $e) {
            $message = 'Failed To Add Policy Updates.';
            return ['error' => $e->getMessage()];
        }
        return ['success' => $message];
    }
    public function listGroupDetail()
    {
        return GroupInfo::where('gstatus', '1')->orderBy('gname','ASC')->get();
    }
    public function UpdatePolicyGroup($request)
    {
        try{
            $groupinfo = GroupInfo::where('gid',$request->gid)->first();
            if($groupinfo == null){
                return array('type' => 'error', 'message' =>'Group does not exist.');
            }else{
                $update=Policy::where('policy_id',$request->policy_id)->update(['eid'=>$groupinfo->gid]);
                $act = 'GRP';
                $policyUpdate = $this->addPolicyUpdates($request, $act);
                return array('type' => 'success', 'message' =>'Group Updated.');
            }
        }catch (Exception $e) {
            $message = 'Failed To change Group for Policy: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function GetPolicyBenRelation()
    {
        $relations = [];
        foreach (PolicyBeneficiary::$beneficiaryRelations as $relation) {
            $relations[] = [
                'name' => $relation,
                'value' => $relation
            ];
        }
        return $this->successResponse('Beneficiary Relations successfully fetched.', $relations);
    }
    public function CreatePolicyBen($request)
    {
        try {
            $total_percent = $request['ben_percent'];
            if (isset($request['existing_ben']))
                foreach ($request['existing_ben'] as $each_ben)
                    $total_percent += $each_ben['ben_percent'];
            if ($total_percent != 100)
                return $this->failedResponse('Total Beneficiary Percentage must be equal to 100.', 422);
            if (isset($request['existing_ben']))
                foreach ($request['existing_ben'] as $each_ben)
                    $this->adjustExistingBeneficiaryPercentage($each_ben['id'], $each_ben['ben_percent']);

            $new_ben = PolicyBeneficiary::create([
                'bpolicy_id' => $request['policy_id'],
                'bfname' => $request['first_name'],
                'blname' => $request['last_name'],
                'brelate' => $request['relation'],
                'bdob' => $request['dob'],
                'bssn' => DecryptEcryptHelper::encryptInfo( $request['ssn'] ),
                'ben_percentage' => $request['ben_percent']
            ]);

            Beneficiary::create([
                'fname' => $new_ben->bfname,
                'lname' => $new_ben->blname,
                'relation' => $new_ben->brelate,
                'dob' => $new_ben->bdob,
                'ssn' => $new_ben->getOriginal('bssn'),
                'ben_percentage' => $new_ben->ben_percentage,
                'ssn4' => substr($new_ben->bssn, -4)
            ]);

            $request['reason'] = "New Beneficiary '{$new_ben->full_name}' Added.";
            $policyUpdate = $this->addPolicyUpdates($request, 'BEN');
            return $this->successResponse('New Beneficiary Added Successfully.');
        } catch (Exception $e) {
            return $this->failedResponse('Failed To Add New Beneficiary.', 422);
        }
    }
    public function UpdatePolicyBen($request)
    {
        try {
            $total_percent = $request['ben_percent'];
            if (isset($request['existing_ben']))
                foreach ($request['existing_ben'] as $each_ben)
                    $total_percent += $each_ben['ben_percent'];
            if ($total_percent != 100)
                return $this->failedResponse('Total Beneficiary Percentage must be equal to 100.', 422);

            if (isset($request['existing_ben']))
                foreach ($request['existing_ben'] as $each_ben)
                    $this->adjustExistingBeneficiaryPercentage($each_ben['id'], $each_ben['ben_percent']);
            $policy_ben = PolicyBeneficiary::find($request['ben_id']);
            $ben = Beneficiary::where('dob', '=', $policy_ben['bdob'])
                ->where('ssn', '=', $policy_ben->getOriginal('bssn'))->first();

            // remove beneficiary from policy_benificiery (sic) table only
            if ($request['ben_percent'] == 0) {
                $success = $policy_ben->delete();
            } else {
                $success = $policy_ben->update([
                    'bfname' => $request['first_name'],
                    'blname' => $request['last_name'],
                    'brelate' => $request['relation'],
                    'bdob' => $request['dob'],
                    'bssn' => DecryptEcryptHelper::encryptInfo( $request['ssn'] ),
                    'ben_percentage' => $request['ben_percent']
                ]);

                $success = $ben->update([
                    'fname' => $policy_ben->bfname,
                    'lname' => $policy_ben->blname,
                    'relation' => $policy_ben->brelate,
                    'dob' => $policy_ben->bdob,
                    'ssn' => $policy_ben->getOriginal('bssn'),
                    'ben_percentage' => $policy_ben->ben_percentage,
                    'ssn4' => substr($policy_ben->bssn, -4)
                ]);
            }

            if ($success) {
                $policyUpdate = $this->addPolicyUpdates($request, 'BEN');
                $message = ($request['ben_percent'] == 0) ? 'Beneficiary Successfully Removed.' : 'Beneficiary Successfully Updated.';
                return $this->successResponse($message);
            }
        }catch (Exception $e) {
            return $this->failedResponse('Failed to Update Beneficiary Information.', 422);
        }
    }
    public function RemovePolicyGroup($request)
    {
        try{
            $update=Policy::where('policy_id',$request->policy_id)->update(['eid'=>'']);
            if($update){
                $act = 'GRP';
                $policyUpdate = $this->addPolicyUpdates($request, $act);
                return array('type' => 'success', 'message' =>'Group Removed for policy.');
            }
        }catch (Exception $e) {
            $message = 'Failed To remove Group for Policy: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function InsertPolicyDependent($request)
    {
        try{
            $dependents = new Dependent();
            $dependents->userid = $request->userid;
            $dependents->d_fname = $request->d_fname;
            $dependents->d_mname = $request->d_mname;
            $dependents->d_lname = $request->d_lname;
            $dependents->d_relate = $request->d_relate;
            $dependents->d_gender = $request->d_gender;
            $dependents->d_dob = $request->d_dob;
            $dependents->d_ssn = $request->d_ssn;
            $dependents->d_ssn4 = $request->d_ssn4;
            $saved = $dependents->save();
            if($saved){
                $act = 'DEP';
                $policyUpdate = $this->addPolicyUpdates($request, $act);
                return array('type' => 'success', 'message' =>'Dependent Added.');
            }
        }catch (Exception $e) {
            $message = 'Failed To add Dependent for Policy: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function ChangePolicyDependent($request)
    {
        $plan_tier = PolicyUpdateHelper::getPolicyPlanTier($request->policy_id);
        $dependents = PolicyUpdateHelper::getPolicyDependents($request->policy_id);

        switch ($plan_tier) {
            case 'IC':
                if ( strtoupper($request->d_relate) != 'C' ) {
                    return array(
                        'status'    => 'error',
                        'message'   => 'Only dependents of type Child is allowed in Tier IC.',
                        'statusCode'=> 422
                    );
                }
                break;
            case 'IF':
                if (strtoupper($request->d_relate) == 'S' && $dependents->where('dependent_id', '!=', $request->did)->contains('d_relate', 'S')) {
                    return array(
                        'status'    => 'error',
                        'message'   => 'Spouse already exists. Only single dependent of type Spouse is allowed in Tier IF.',
                        'statusCode'=> 422
                    );
                }
                break;
            case 'IS':
                if ( strtoupper($request->d_relate) != 'S' ) {
                    return array(
                        'status'    => 'error',
                        'message'   => 'Only single dependent of type Spouse is allowed in Tier IS.',
                        'statusCode'=> 422
                    );
                }
                break;
        }

        $planAgeService = new PlanAgeService();
        $ageCheck = $planAgeService->validateSingleDependent([
            "d_dob" => $request->d_dob,
            "policy_id" => $request->policy_id,
            "d_relate" => strtoupper($request->d_relate)
        ]);
        if (! $ageCheck['success']) return array(
            'status'    => 'error',
            'message'   => $ageCheck['message'],
            'statusCode'=> 422
        );

        try{
            $did = $request->did;
            $data['d_fname'] = $request->d_fname;
            $data['d_mname'] = $request->d_mname;
            $data['d_lname'] = $request->d_lname;
            $data['d_relate'] = $request->d_relate;
            $data['d_gender'] = $request->d_gender;
            $data['additional_notes'] = $request->d_notes;
            if($request->d_dob != ''){
                $data['d_dob'] = $request->d_dob;
            }
            if($request->d_ssn != ''){
                $data['d_ssn'] = DecryptEcryptHelper::encryptInfo($request->d_ssn);
                $data['d_ssn4'] = substr($request->d_ssn, -4, 4);
            }
            $saved = Dependent::where('did',$did)->update($data);
            if($saved){
                $act = 'DEP';
                $policyUpdate = $this->addPolicyUpdates($request, $act);
                return array('status' => 'success', 'message' =>'Dependent Updated.', 'statusCode' => 200);
            }
        }catch (Exception $e) {
            $message = 'Failed To update Dependent for Policy: ';
            return ['status' => 'error', 'data' => $e->getMessage(), 'statusCode' => $e->getCode()];
        }
    }

    private function adjustExistingBeneficiaryPercentage($ben_id, $ben_percent)
    {
        $policy_ben = PolicyBeneficiary::find($ben_id);
        $ben = Beneficiary::where('dob', '=', $policy_ben['bdob'])
            ->where('ssn', '=', $policy_ben->getOriginal('bssn'))->first();

        // if 0, remove existing beneficiary from policy_benificiery (sic) table only
        if ($ben_percent == '0') {
            $policy_ben->delete();
        } else {
            $policy_ben->update(['ben_percentage' => $ben_percent]);
            $ben->update(['ben_percentage' => $ben_percent]);
        }
    }
}
