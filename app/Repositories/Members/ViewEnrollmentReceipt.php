<?php

namespace App\Repositories\Members;

use App\PlanOverview;
use App\UserInfoPolicyAddress;
use App\GroupInfo;
use App\Dependent;
use App\Plan;
use App\UserInfo;
use App\DependentInPolicy;
use App\CcDetail;
use App\EftDetail;
use App\CompanyPaymentMethod;
use App\AgentInfo;
use App\OverAllPlanDiscount;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

class ViewEnrollmentReceipt extends Model
{
    public function detailEnrollmentReceipt($request)    
    {
        $planOverview = PlanOverview::where('policy_id',$request->policy_id)->get();
        $data['planOverview'] = $planOverview;
        $planOverviewFirst = $planOverview->first();
        $data['planOverviewFirst'] = $planOverviewFirst;        
        if(!$planOverview->first())
        {
            return array('type' => 'error', 'message' =>'Policy ID does not Exist.');
        }        
        $data['userInfoPolicyAddress'] = UserInfoPolicyAddress::where('policy_id',$request->policy_id)->get();
        if($data['userInfoPolicyAddress']->first()){
            $userInfoPolicyAddressFirst = $data['userInfoPolicyAddress']->first();
            $data['userInfoPolicyAddressFirst'] = $userInfoPolicyAddressFirst;            
            $data['userInfoFirst'] = UserInfo::where('userid',$userInfoPolicyAddressFirst->userid)->first();
            $data['dependentC'] = Dependent::where('userid',$userInfoPolicyAddressFirst->userid)->where('d_relate','C')->get();
            $data['dependentS'] = Dependent::where('userid',$userInfoPolicyAddressFirst->userid)->where('d_relate','S')->get();
            $data['agentInfo'] = AgentInfo::where('agent_id',$userInfoPolicyAddressFirst->agent_id)->get();
            $data['ccDetail'] = CcDetail::where('policy_id',$request->policy_id)->where('cc_id',$userInfoPolicyAddressFirst->payment_id)->get();
            $data['eftDetail'] = EftDetail::where('policy_id',$request->policy_id)->where('bank_id',$userInfoPolicyAddressFirst->payment_id)->get();
        }
        $data['groupInfoFirst'] = GroupInfo::where('gid',$planOverviewFirst->eid)->first();
        $planRiderAsc = PlanOverview::where('policy_id',$request->policy_id)->OrderBy('rider','ASC')->get();
        $data['planRiderAsc'] = $planRiderAsc;
        $planCollection = collect();
        $overallPlanDiscountsCollection = collect();
        foreach($planRiderAsc as $value)
        {
            $planCollection->add(Plan::where('pid',$value->pid)->get());
            $overallPlanDiscountsCollection->add(OverAllPlanDiscount::where('pid',$value->pid)->where('tier',$value->tier)->get());            
        }
        $data['planCollection'] = $planCollection;
        $data['overallPlanDiscountsCollection'] = $overallPlanDiscountsCollection;        
        $data['dependentInPolicy'] = DependentInPolicy::where('policy_id',$request->policy_id)->get();

        $data['companyPaymentMethodCC'] = CompanyPaymentMethod::where('status','1')->where('payname','CC')->get();
        $data['companyPaymentMethodEFT'] = CompanyPaymentMethod::where('status','1')->where('payname','EFT')->get();

        return $data;

    }
}