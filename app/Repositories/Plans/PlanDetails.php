<?php

namespace App\Repositories\Plans;

use App\HomepageConfiguration;
use App\Plan;
use App\PlanOverview;
use App\PlanTier;
use App\PlanPricing;
use App\PlanPricingDisplay;
use App\PlanPricingZip;
use Exception;
use Illuminate\Database\Eloquent\Model;

class PlanDetails extends Model
{
    public function allPlans()
    {
        return Plan::where('status', 1)->get();
    }

    public function getPlanDetail($id)
    {
        $data = Plan::with('planTier')
            ->with('planStates')
            ->with('planAgeRestriction')
            ->with('planAgeRestrictionDependent')
            ->where('pid', $id)
            ->first();
        return $data;
    }

    public function getPlanPricing($id)
    {
        $data = PlanPricingDisplay::where('pid', $id)->where('pricing_status', 1)->where('plan_status', 1)->get();
        return $data;
    }

    public function setPlanPricing($request)
    {
        $data = PlanTier::with('planPricing')->where('pid_tier', $request->pid)->get();
        try {
            foreach ($data as $d) {
                PlanPricing::where('plan_id', $d->idplan_tier)->update(['pricing_status' => 0]);
                $tierData = $d->tier;
                $pricingData = $this->pricingDataUpdate($d->idplan_tier, $request->$tierData);
                $pricingUpdate = PlanPricing::create($pricingData);
            }
            $message = "Pricing for plan updated successfully.";
        } catch (Exception $e) {
            return ["error" => "Pricing update for plan failed."];
        }
        return ["success" => $message];
    }

    public function setPlanPricingZip($request)
    {
        $data = PlanTier::with('planPricing')->where('pid_tier', $request->pid)->get();
        $zip = $request->zip;
        try {
            foreach ($data as $d) {
                PlanPricingZip::where('plan_id', $d->idplan_tier)
                    ->where('zip', json_encode($zip))
                    ->update(['pricing_status' => 0]);
                $tierData = $d->tier;
                $pricingData = $this->pricingZipDataUpdate($d->idplan_tier, $request->$tierData, $zip);
                $pricingUpdate = PlanPricingZip::create($pricingData);
            }
            $message = "Pricing for plan updated successfully.";
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
            return ["error" => "Pricing update for plan failed."];
        }
        return ["success" => $message];
    }

    public function setPlanPricingAgeBased($request)
    {
        $agedata = PlanTier::with('planPricing')->where('pid_tier', $request->pid)->get();
        try {
            foreach ($agedata as $data) {
                $tierData = $data->tier;
                PlanPricing::where('plan_id', $data->idplan_tier)->update(['pricing_status' => 0]);
                foreach ($request->$tierData as $tData) {
                    $pricingData = $this->ageBasedPricingDataUpdate($data->idplan_tier, $tData);
                    $pricingUpdate = PlanPricing::create($pricingData);
                }
            }
            $message = "Pricing for plan updated successfully.";
        } catch (Exception $e) {
            return ["error" => "Pricing update for plan failed."];
        }
        return ["success" => $message];
    }

    public function setPlanPricingAgeBasedZip($request)
    {
        $agedata = PlanTier::with('planPricing')->where('pid_tier', $request->pid)->get();
        $zip = $request->zip;
        try {
            foreach ($agedata as $data) {
                $tierData = $data->tier;
                PlanPricingZip::where('plan_id', $data->idplan_tier)
                    ->where('zip', json_encode($zip))
                    ->update(['pricing_status' => 0]);
                foreach ($request->$tierData as $tData) {
                    $pricingData = $this->ageBasedPricingZipDataUpdate($data->idplan_tier, $tData, $zip);
                    $pricingUpdate = PlanPricingZip::create($pricingData);
                }
            }
            $message = "Pricing for plan updated successfully.";
        } catch (Exception $e) {
            return ["error" => "Pricing update for plan failed."];
        }
        return ["success" => $message];
    }

    public function ageBasedPricingZipDataUpdate($id, $data, $zip)
    {
        $data1 = $this->ageBasedPricingDataUpdate($id, $data);
        $data2 = [
            "zip" => json_encode($zip)
        ];
        $data = array_merge($data1, $data2);
        return $data;
    }

    public function pricingZipDataUpdate($id, $data, $zip)
    {
        $data1 = $this->pricingDataUpdate($id, $data);
        $data2 = [
            "zip" => json_encode($zip)
        ];
        $data = array_merge($data1, $data2);
        return $data;
    }

    public function ageBasedPricingDataUpdate($id, $data)
    {
        $data1 = $this->pricingDataUpdate($id, $data['data']);
        $data2 = [
            "age1" => $data['age1'],
            "age2" => $data['age2']
        ];
        $data = array_merge($data1, $data2);
        return $data;
    }

    public function pricingDataUpdate($id, $data)
    {
        return [
            "plan_id" => $id,
            "price_male_nons" => $data[0],
            "price_male_s" => $data[1],
            "price_female_nons" => $data[2],
            "price_female_s" => $data[3],
            "wholesale_price_male_nons" => $data[4],
            "wholesale_price_male_s" => $data[5],
            "wholesale_price_female_nons" => $data[6],
            "wholesale_price_female_s" => $data[7],
            "commissionable_price_male_nons" => $data[8],
            "commissionable_price_male_s" => $data[9],
            "commissionable_price_female_nons" => $data[10],
            "commissionable_price_female_s" => $data[11],
            "net_price_male_nons" => $data[12],
            "net_price_male_s" => $data[13],
            "net_price_female_nons" => $data[14],
            "net_price_female_s" => $data[15],
            "pricing_date" => time(),
            "pricing_status" => 1
        ];
    }

    protected function planAssociationQuery($is_assoc = false)
    {
        return Plan::query()
            ->select(['pid', 'plan_name_system', 'forsale', 'is_assoc', 'web_display_name','rider'])
            ->where([
                'is_assoc' => $is_assoc,
                'forsale' => true
            ]);
    }

    public function getAssociationList($policy_id){
        $existing_plans = PlanOverview::where('policy_id',$policy_id)->where('pstatus',1)->select('pid','plan_id')->get();
        $plans = PlanChangeOnAddress::computeAssociationFees($existing_plans,$policy_id,false);
        $data = [];
        if(is_array($plans)){
            $asscociationPlans = array_column($plans,'plan_id');
            $data = Plan::query()
                ->select(['pid', 'plan_name_system', 'forsale', 'is_assoc', 'web_display_name','rider'])
                ->where([
                    'is_assoc' => true,
                    'forsale' => true
                ])->whereIn('pid',$asscociationPlans)->get();
        }
        return $this->formattedPlanData($data);
    }

    public function getAssociationFees()
    {
        $query = $this->planAssociationQuery(true)            
        ->with(['planPricings' => function ($query) {
            $query->where('price_male_nons', '>', 0)
                  ->select('pid','plan_pricing_id', 'price_male_nons');
            }]);

        $data = $query->get()->map(function ($plan) {
            return [
                'pId' => $plan->pid,
                'planName' => "{$plan->pid} - {$plan->plan_name_system}",
                'originalPlanName' => $plan->plan_name_system,
                'isAssoc' => $plan->is_assoc,
                'rider' => $plan->rider,
                'forsale' => $plan->forsale,
                'webDisplayName' => "{$plan->pid} - {$plan->web_display_name}",
                'originalWebDisplayName' => $plan->web_display_name,
                'plan_pricing_id' => optional($plan->planPricings->first())->plan_pricing_id,
                'amount' => optional($plan->planPricings->first())->price_male_nons 
                    ? '$' . number_format($plan->planPricings->first()->price_male_nons, 2)
                    : null,
            ];
        });

        return [
            'data' => $data,
            'meta' => ['total' => $data->count()]
        ];
    }

    public function getAssociationPlans($carrier_id = [], $planType = [], $reference = null)
    {
        $query = $this->planAssociationQuery(false)
            ->with(['planPricings' => function ($query) {
                $query->where('price_male_nons', '>', 0)
                      ->select('plan_pricing_id', 'price_male_nons');
            }]);
    
        if (count($planType) > 0) {
            $query->whereIn('pl_type', $planType);
        }

        if (count($carrier_id) > 0) {
            $query->whereIn('carrier', $carrier_id);
        }
        if($reference === 'create') {
            return $query->pluck('pid')->toArray();
        }
        $data = $query->get()->map(function ($plan) {
            return [
                'pId' => $plan->pid,
                'planName' => "{$plan->pid} - {$plan->plan_name_system}",
                'originalPlanName' => $plan->plan_name_system,
                'isAssoc' => $plan->is_assoc,
                'rider' => $plan->rider,
                'forsale' => $plan->forsale,
                'webDisplayName' => "{$plan->pid} - {$plan->web_display_name}",
                'originalWebDisplayName' => $plan->web_display_name,
                'plan_pricing_id' => optional($plan->planPricings->first())->plan_pricing_id,
                'amount' => optional($plan->planPricings->first())->price_male_nons 
                    ? '$' . number_format($plan->planPricings->first()->price_male_nons, 2)
                    : null,
            ];
        });
    
        return [
            'data' => $data,
            'meta' => ['total' => $data->count()]
        ];
    }
    


    protected function formattedPlanData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedPlanItem($d);
        }
        return [
            'data' => $result,
            'meta' => [
                'total' => count($result)
            ]
        ];
    }

    protected function singleFormattedPlanItem($d)
    {
        $planName = $d->pid . ' - ' . $d->plan_name_system;
        $webDisplayName = $d->pid . ' - ' . $d->web_display_name;
        $collection = [
            'pId' => $d->pid,
            'planName' => $planName,
            'originalPlanName' => $d->plan_name_system,
            'isAssoc' => $d->is_assoc,
            'rider' => $d->rider,
            'forsale' => $d->forsale,
            'webDisplayName' => $webDisplayName,
            'originalWebDisplayName' => $d->web_display_name
        ];
        if(isset($d->planPricings) && count($d->planPricings)){
            $collection['amount'] = '$' . number_format((float)$d->planPricings->all()[0]->price_male_nons, 2, '.', '') ;
            $collection['plan_pricing_id'] = $d->planPricings->all()[0]->plan_pricing_id;
        }
        return $collection;
    }

    public function getHomepageConfigurationPlans()
    {
        $data = $this->homepageConfigurationPlanQuery()->get();
        return $this->formattedPlanData($data);
    }

    protected function homepageConfigurationPlanQuery()
    {
        $IHACarrierID = PlanPricingDisplay::MAJOR_MEDICAL_IHA_CARRIER_ID;
        return PlanPricingDisplay::query()
            ->select(['pid', 'plan_name_system', 'forsale', 'is_assoc', 'web_display_name'])
            ->where([
                ['forsale', '=', 1],
                ['pricing_status', '=', 1],
                ['is_assoc', '!=', 1],
            ])
            ->whereNotNull('web_display_name')
            // ->whereRaw("
            //     CASE
            //     WHEN cid = {$IHACarrierID}
            //     THEN plan_name_system LIKE '%L4%'
            //     ELSE 1=1 END
            //     ")
            ->groupBy('pid')
            ->orderBy('pid');
    }

    public function getUserAllHomePagePlans($userConfPlanIds, $UserConfPlanIdsName, $request)
    {
        $allHomePagePlans = $this->homepageConfigurationPlanQuery()->pluck('pid');
        if(!$request->selectedPlan || $request->selectedPlan == 'Configured Plan'){
            $ConfiguredPlansUser = $this->queryPlanFetch(Plan::whereIn('pid', $userConfPlanIds), $allHomePagePlans, $request);
            if($request->selectedPlan == 'Configured Plan'){
                return $ConfiguredPlansUser;
            }
        }
        if(!$request->selectedPlan || $request->selectedPlan == 'Unconfigured Plan'){
            $unconfiguredPlansUser = $this->queryPlanFetch(Plan::whereNotIn('pid', $userConfPlanIds)->whereNotIn('web_display_name', $UserConfPlanIdsName), $allHomePagePlans, $request) ;
            if($request->selectedPlan == 'Unconfigured Plan'){
                return $unconfiguredPlansUser;
            }
        }
        
        return collect()->merge($ConfiguredPlansUser)->merge($unconfiguredPlansUser);
    }

    protected function queryPlanFetch($query, $allHomePagePlans, $request){
        $agent_id = $request->agent_id;
        $website = $request->website;
        $group_id = $request->group_id;
       return $query->with(['planPricings' => function($q) {
            $q->where('pricing_status', 1)
            ->select('pid', 'tier', 'price_male_nons', 'age1', 'age2');
        }])
        ->with(['homepagePlan' => function ($q) use ($agent_id, $group_id, $website){
            $q->when($agent_id, function ($q) use ($agent_id, $website){
                $q->where('agent_id', $agent_id)
                ->where('website',$website)
                ->where('conf_for', 'A');
            })
            ->when($group_id, function ($q) use ($group_id, $website){
                $q->where('group_id', $group_id)
                ->where('website',$website)
                ->where('conf_for', 'G');
            })
            ->get(['id', 'is_featured']);
        }])
        ->whereIn('pid', $allHomePagePlans)
        ->groupBy('web_display_name')
        ->orderBy('pl_type')
        ->get();
    }

    public function getConfiguredPlanIds($agentId, $website, $confFor, $groupId)
    {
        $query = HomePageConfiguration::where('conf_for', $confFor);
        if ($confFor == 'A') {
            $query->where('agent_id', $agentId);
        }
        if ($confFor == 'G') {
            $query->where('group_id', $groupId);
        }
        if ($website) {
            $query->where('website', $website);
        }
        return$query->select('plan_id')
            ->get()
            ->pluck('plan_id');
    }

    public function removeConfiguredPlanIds($agentId, $website, $confFor, $groupId)
    {
        $query = HomePageConfiguration::where('conf_for', $confFor);
        if ($confFor == 'A') {
            $query->where('agent_id', $agentId);
        }
        if ($confFor == 'G') {
            $query->where('group_id', $groupId);
        }
        if ($website) {
                    $query->where('website', $website);
                }
        return $query
            ->delete();
    }

    public static function planCategoryMappings () {

    	$categoryMappings = array(
    		'limitedmed' => array('LM'),
    		'medical' => array('MM'),
    		'dental' => array('DENT', 'THEFT-DENT'),
    		'vision' => array('VISION'),
    		'supplemental' => array('ADI','CIADD','AME','LTD','GAP','AD','ASHIP', 'STDCANCER'),
    		'rx' => array('RX'),
    		'lifestyle' => array('DC','DISCOUNT','TELEMEDICINE','THEFT'),
    		'term_life' => array('LIFE','TL'),
            'accident' => array('ACCIDENT'),
            'hospital' => array('HOSPITAL'),
            'critical' => array('CRITICAL', 'CI'),
            'di' => array('DI'),
            'pet' => array('PET')
    	);

    	return $categoryMappings;
    }

    public function dbToDisplayCategoryMapping ($dbCategory) {
    	$result = '';
    	foreach (self::planCategoryMappings() as $key => $category) {
    		foreach ($category as $subCategory) {
    			if ($subCategory == strtoupper($dbCategory)) {

    				$result = $key;
    			}
    		}
    	}
    	return $result;
    }

    public function getAssociationFeesSearch()
    {
        $data = $this->planAssociationQuery(true);
        $dataWithPricing = $this->withPlanPricing($data);
        return $this->formattedPlanData($dataWithPricing->get());
    }

    protected function withPlanPricing($data)
    {
        return $data->with(['planPricings' => function ($query) {
            $query->select(['pid','plan_pricing_id','price_male_nons'])
            ->where('tier','IO');
        }]);
    }
}
