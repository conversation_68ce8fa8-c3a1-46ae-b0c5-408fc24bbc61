<?php

namespace App\Repositories\Plans;

use App\AgentInfo;
use App\GroupAssociationFee;
use App\GroupInfo;
use App\HomepageConfiguration;
use App\NewAssocFee;
use App\Plan;
use App\PlanOverview;
use App\PlanPolicy;
use App\PlanPricingDisplay;
use App\PlanZip;
use App\Policy;
use App\PolicyUpdate;
use App\RepAssociationFee;
use App\RepWaiveAssociationFee;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;

class PlanChangeOnAddress extends Model
{

    protected static $array_downlines;
    protected static $benId = 1901;
    protected static $krivId = 100727;
    protected static $bexId = 1399;
    protected static $elevateId = 235;
    protected static $vipIds = [10055,101434];
    protected static $nortmanID = 1785;
    private static $agentVip = array();
    public static $elevateMembershipIds = [886,887,888,890];
    protected static $elevateSite = 'elevatetowellness.org';
    private static $loginUserId; // user id of the login user


    public function checkPlanAndChange($req , $addData){
        try{
            $req['policy_id'] = $req->policy_id;

            $return = 1;
            $data['policy_id'] = $req->policy_id;
            $data['termed_agree'] = isset($req->termed_agree)?$req->termed_agree:false;
            $data['user_id'] =$req->a_userid;
            $data['state'] =$addData['state'];
            $data['zip'] =$addData['zip'];
            $data['tier'] = PlanOverview::where('policy_id',$req['policy_id'])->get('tier')[0]->tier;
            $data['dob'] = PlanOverview::where('policy_id',$req['policy_id'])->get('user_dob')[0]->user_dob;
            $policyInfo = Policy::where('policy_id',$req['policy_id'])->select('p_agent_num as agent_id', 'eid as group_id', 'weburl as website','eprocess')->first();
            $data['agent_id'] = $policyInfo->agent_id;
            $data['group_id'] = $policyInfo->group_id;
            $data['website'] = $policyInfo->website;
            self::$loginUserId =  request()->header('id'); // getting the login user id from the request header
            $userPlans = self::getUserPlan($req['policy_id']);
            $planNeedToChange = [];
            foreach ($userPlans as $key => $value) {
                $zipCheck = PlanZip::where('pid',$value->pid)->count();
                if($zipCheck){
                    array_push($planNeedToChange, $value->pid);
                }else{
                    unset($userPlans[$key]);
                }
            }
            if(count($planNeedToChange) == 0){
                return 1;
            }

            foreach ($userPlans as $key => $value) {
                $planPolicyP_ai = PlanPolicy::where('plan_id',$value['plan_pricing_id'])->where('policy_num',$req['policy_id'])->select('p_ai','peffective_date')->where('pstatus',1)->first();
                if(in_array($value->pid,$planNeedToChange) && ($value->code834 !== null || $value->code834 !== '')){
                        $notMatchedPlan['pid'] = $value->pid;
                        $notMatchedPlan['code834'] = $value->code834;
                        $findAlternativePlans = self::alternaivePlansOnAdressChange($notMatchedPlan , $data); //returns the pid from the plans table
                        if(isset($findAlternativePlans['error']) && $data['termed_agree'] == false){
                            return $findAlternativePlans;
                        }
                        if(isset($findAlternativePlans['error']) && $data['termed_agree'] == true){
                            self::onlyTermePlan( $planPolicyP_ai , false, '','','');
                            continue;
                        }

                        if(isset($findAlternativePlans)){
                            if(in_array($value->pid,$findAlternativePlans)){
                                continue;
                            }
                            $this->replaceWithAlternatePlan( $value , $findAlternativePlans[0] , $req['policy_id'], $data);
                        }
                        else if($data['termed_agree'] == false){
                            return['error' => 'No Alternative Plan founds for some of your plan', 'error_type' => 'show_warning'];
                        }
                        else{
                            self::onlyTermePlan( $planPolicyP_ai , false, '','','');
                        }
                    }
                    else{
                        if($data['termed_agree'] == true){
                            self::onlyTermePlan( $planPolicyP_ai , false, '','','');
                        }
                        else{
                            return['error' => 'No Alternative Plan founds for some of your plan', 'error_type' => 'show_warning'];
                        }
                    }
            }
            return $return;
            }
        catch(Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public static function onlyTermePlan($planPolicyterminating , $alternateFound = false, $newEffDate,$newaddingPlanId,$newplanPricingId){
        try{
            Log::channel('planReplacedAlternative')->info("New Effective Date : ".$newEffDate);
            if(!$alternateFound){
                if(strtotime(date("Y-m-d")) < strtotime($planPolicyterminating['peffective_date'])){
                    $termdate = $planPolicyterminating['peffective_date'];
                    $reason = 'WDR';
                    $note = 'Plan withdrawn due to address change with no alternative plan';
                }
                else{
                    $termdate = date("Y-m-t");
                    $reason = 'TMO';
                    $note = 'Plan termed due to address change with no alternative plan';
                }
                Log::channel('planReplacedAlternative')->info("No Alternative Plan Found");
            }
            else{
                Log::channel('planReplacedAlternative')->info("Plan replacement started as the alternative is found");
                Log::channel('planReplacedAlternative')->info("New plan_id: " . $newaddingPlanId, ['plan_id' => $newaddingPlanId]);
                if(strtotime(date("Y-m-d")) < strtotime($planPolicyterminating['peffective_date'])){
                    $termdate = $planPolicyterminating['peffective_date'];
                    $reason = 'WDR';
                    $note = 'Plan withdrawn due to address change with alternative plan';
                }
                else{
                    $termdate = date("Y-m-t", strtotime("-1 month",strtotime($newEffDate)));
                    $reason = 'TMO';
                    $note = 'Plan termed due to address change with alternative plan';
                }
            }
        $request = Request::create('api/v1/active-plan-termed', 'POST',[
            'planPolicyId' => $planPolicyterminating['p_ai'],
            'termDate' => $termdate,
            'reason' => $reason,
            'notes' => $note,
            'loginUserId' => self::$loginUserId,
            'newPlanId' => $newaddingPlanId, // new alternative plan id,
            'newEffDate' => $newEffDate, // new effective date of the alternative plan
            'newplanPricingId' => $newplanPricingId // new alternative plan pricing id
        ]);
        $res = app()->handle($request);
        $responseBody = $res->getContent();
        return $responseBody;
        Log::channel('planReplacedAlternative')->info("Request made to active plan termed API: " . $responseBody);
    }
    catch(Exception $e) {
        return ['error' => $e->getMessage()];
    }
    }


    public function replaceWithAlternatePlan($terminatingPlan, $replacingPid, $policy_id, $plan_info){
        try{
            $addingPlan = Plan::where('pid', $replacingPid)->first();
            $replacingPlan = PlanOverview::where('pid',$terminatingPlan->pid)->where('policy_id',$policy_id)->get();
            $oldPlanPricingId = $terminatingPlan->plan_pricing_id;
            $newPlanPricingId = self::getPlanPricingId($addingPlan, $plan_info, $replacingPid);
            $replacingPlan[0]->peffective_date = $this->computePlanEffectiveDates($replacingPid , $addingPlan)[0];
            $replacingPlan[0]->pid = $newPlanPricingId->plan_pricing_id;
            Log::channel('planReplacedAlternative')->info("Replacing Plan Price id:", ['plan_pricing_id' => $newPlanPricingId->plan_pricing_id]);
            $data = self::updatePlanPolicies($replacingPlan[0]->attributes);
            Log::channel('planReplacedAlternative')->info("Replacing Plan:", ['plan' => $replacingPlan[0]->toArray()]);
            $policy['policy_id'] = $policy_id;
            $policy_log = $this->updatePolicyLog($policy, 'SPC', 'Policy plan has been change due to address change of User', self::$loginUserId);
            $planPolicyP_ai = PlanPolicy::where('plan_id',$oldPlanPricingId)->where('policy_num',$policy_id)->select('p_ai','peffective_date')->first();
            $plantermed = self::onlyTermePlan( $planPolicyP_ai , true ,$replacingPlan[0]->peffective_date,$replacingPid,$replacingPlan[0]->pid);
            $existing_plans = PlanOverview::where('policy_id',$policy_id)->where('pstatus',1)->select('pid','plan_id')->get();
            if(!PlanOverview::where('policy_id',$policy_id)->where('is_assoc',1)->where('pstatus',1)->exists()){
                $assoc_id = $this->computeAssociationFees ($existing_plans, $policy_id);
                if(count($assoc_id)){
                    $planIds = collect($assoc_id)->filter(function ($value) {
                        return is_object($value) && PlanPolicy::where('plan_id', $value->plan_pricing_id)->exists();
                    })->pluck('plan_pricing_id')->toArray();

                    if (!empty($planIds)) {
                        PlanPolicy::where('policy_num', $policy_id)
                            ->whereIn('plan_id', $planIds)
                            ->update([
                                'peffective_date' => $replacingPlan[0]->peffective_date,
                                'pterm_date' => '',
                                'pstatus' => 1,
                            ]);
                    }
                }
            }
    }
        catch(Exception $e) {
            return ['error' => $e->getMessage()];
        }

    }


    public static function computeAssociationFees ($existingPlans, $policy_id,$continue = true) {
        //expectes proper configuration of associations
        //skip association fee for existing user having existing membership
        if (self::checkElevateMembershipExisting($policy_id)) {
            return [];
        }
        $associationIds = [];
        $finalPlanIds = [];
        $website = '';
        $policyInfo = Policy::where('policy_id',$policy_id)->select('p_agent_num as agent_id', 'eid as group_id', 'weburl as website','eprocess')->first();
        $website = strtolower(strtok($policyInfo->website, '/'));
        $groupId = $data['group_id'] = $policyInfo->group_id;
        $isAssocationFeeWaived = GroupInfo::where('gid',$groupId)->pluck('is_association_fee_waived')->first();
        if($isAssocationFeeWaived && self::checkIfRepWiseAssocWaived($website,$policyInfo->agent_id)){
            return [];
        }

        if(in_array($policyInfo->agent_id,self::$vipIds) || in_array(10055, self::getAgentListForVIP(10005)) ||  in_array(101434, self::getAgentListForVIP(101434)) ) {
            return [];
        }

        foreach ($existingPlans as $key => $plan) {
            $finalPlanIdscollect['plan_id'] = $plan->pid;
            $finalPlanIdscollect['plan_pricing_id'] = $plan->plan_id;
            array_push($finalPlanIds , $finalPlanIdscollect);
        }
        foreach ($existingPlans as $key => $plan) {
            /* checking if assocation fee is waived on rep and plan id level */
            if(self::checkIfRepPlanWiseAssocWaived($plan->pid, $website, $policyInfo->agent_id)) { continue;}

            /* checking if assocation fee is waived on rep and carrier level */
            if(self::checkIfCarrierWiseAssocWaived($plan->pid, $website, $policyInfo->agent_id)) { continue;}

            if(self::nortManAssocFee($policyInfo->agent_id, $finalPlanIds)){continue; }

        	// if (self::checkAssociation($plan->pid))
        	// 	continue;
            

            $groupAssociation = GroupAssociationFee::where('pid', $plan->pid)
                ->where('gid', $groupId)
                ->select('a_pid', 'a_ppid')
                ->first();
            if ($groupAssociation) {
                $associationId = new \stdClass();

                $associationId->plan_id = (int) $groupAssociation->a_pid;
                $associationId->plan_pricing_id = (int) $groupAssociation->a_ppid;
                //check if already in cart
                if (in_array($groupAssociation->a_pid, array_column($finalPlanIds, 'plan_id'))) {
                    continue;
                }

                $finalPlanIds[] = $associationId;
            }
            if(!$continue){
                $planAssociation = NewAssocFee::where('pid', $plan->pid)
                ->where('website','like', "$website%")
                ->select(['a_pid', 'a_ppid'])
                ->get();
                foreach($planAssociation as $p){
                    $planAssociationId = new \stdClass();
                    $planAssociationId->plan_id = (int) $p->a_pid;
                    $planAssociationId->plan_pricing_id = (int) $p->a_ppid;
                    if (in_array($p->a_pid, array_column($finalPlanIds, 'plan_id'))) {
                        continue;
                    }
                    $finalPlanIds[] = $planAssociationId;
                }
            }else{
                $planAssociation = NewAssocFee::where('pid', $plan->pid)
                        ->where('website','like', "$website%")
                        ->select(['a_pid', 'a_ppid'])
                        ->first();
                if ($planAssociation) {
                    $planAssociationId = new \stdClass();
                    $planAssociationId->plan_id = (int) $planAssociation->a_pid;
                    $planAssociationId->plan_pricing_id = (int) $planAssociation->a_ppid;
                    if (in_array($planAssociation->a_pid, array_column($finalPlanIds, 'plan_id'))) {
                        continue;
                    }
                    $finalPlanIds[] = $planAssociationId;
                }
            }
            $planAssociation =  self::additionalFilterAssocationFee($plan->pid, $website, $policyInfo->agent_id );
            if ($planAssociation) {
                $planAssociationId = new \stdClass();
                $planAssociationId->plan_id = (int) $planAssociation->a_pid;
                $planAssociationId->plan_pricing_id = (int) $planAssociation->a_ppid;

                if (in_array($planAssociation->a_pid, array_column($finalPlanIds, 'plan_id'))) {
                    continue;
                }
                $finalPlanIds[] = $planAssociationId;
            }

        }
        $afterBexSpecific = self::bexSpecificCase($finalPlanIds, $groupId, $website);
        $afterGroupSpecific = self::groupSpecificCase($afterBexSpecific, $groupId);
        $addFee = true;
        $currentAssociationFees = [];
        $associationPlans = [];
        if(count($afterGroupSpecific) > 0){
            foreach ($afterGroupSpecific as $key => $associationPlan) {
                if(is_array($associationPlan)){
                    $pricing = self::getPLanPricingDisplay($associationPlan['plan_pricing_id']);
                }
                else{
                    $pricing = self::getPLanPricingDisplay($associationPlan->plan_pricing_id);
                }
                if ($pricing->is_assoc == '1') {
                    $currentAssociationFees[] = (float) $pricing->price_male_nons;
                    $associationPlans[$key]['plan_id'] = isset($associationPlan->plan_id) ? $associationPlan->plan_id : $associationPlan['plan_id'];
                    $associationPlans[$key]['plan_pricing_id'] = isset($associationPlan->plan_pricing_id) ? $associationPlan->plan_pricing_id : $associationPlan['plan_pricing_id'];
                    $associationPlans[$key]['price'] = (float) $pricing->price_male_nons;
                }
            }
        }

        if(!$continue){
            return $associationPlans;
        }

        $existingAssociationFees = self::getUserAssociationFees(request()->get('a_userid'));
        if (count($existingAssociationFees) > 0 && count($currentAssociationFees) > 0) {
            if (max(array_column($existingAssociationFees, 'price')) < max($currentAssociationFees)) {
                $addFee = true;
            } else {
                $addFee = false;
            }
        }

        if (count($associationPlans) > 0) {
            $afterGroupSpecific = self::maximumAssociationFee ($associationPlans, $afterGroupSpecific);
            return $afterGroupSpecific;
        }

        return [];
    }

    public static function additionalFilterAssocationFee($planID, $website, $agentID)
    {
        $repAssocFee = self::getRepWiseAssocFee($planID, $agentID, $website);
        if ($repAssocFee instanceof RepAssociationFee) {
            return $repAssocFee;
        }

        return NewAssocFee::where('pid', $planID)
            ->where('website', $website)
            ->select(['a_pid', 'a_ppid'])
            ->first();
    }

    private static function getRepWiseAssocFee($planID, $agentID, $website)
    {
        $hasRepWiseAssoc = RepAssociationFee::where([
            'rep_id' => $agentID,
            'website' => $website,
            'pid' => $planID
        ])->first()
            ??
            RepAssociationFee::where([
                'website' => $website,
                'pid' => $planID,
                'apply_for_downline' => true
            ])->whereIn('rep_id', self::getAllUplines($agentID))->first();


        if($hasRepWiseAssoc) return $hasRepWiseAssoc;

        $planCategory = self::getPlanCategory($planID);

        /* check same for plan category based implementation */
        $hasRepWiseAssoc = RepAssociationFee::where([
            'rep_id' => $agentID,
            'website' => $website
        ])->whereJsonContains('apply_category_wise', $planCategory)
            ->first()
            ??
            RepAssociationFee::where([
                'website' => $website,
                'apply_for_downline' => true,
            ])
                ->whereJsonContains('apply_category_wise', $planCategory)
                ->whereIn('rep_id',  self::getAllUplines($agentID))->first();


        return $hasRepWiseAssoc ?? false;
    }

    public static function getPlanCategory ($planId) {

    	return Plan::where('pid', '=', $planId)->value('pl_type');
    }

    public static function checkIfRepWiseAssocWaived($website, $agentID )
    {
        $repLevelAssocFeeWaived = RepWaiveAssociationFee::where([
            'website' => $website,
            'apply_for_all_plan' => true,
            'carrier' => null
        ])->whereIn('rep_id', array_merge(self::getAllUplines($agentID), [$agentID]))
            ->orderByRaw(" FIELD(rep_id, '$agentID'), FIELD(apply_for_downline, 1) ")
            ->first();
        if ($repLevelAssocFeeWaived instanceof RepWaiveAssociationFee && ($repLevelAssocFeeWaived->rep_id == $agentID || $repLevelAssocFeeWaived->apply_for_downline)) {
            return true;
        }
        return false;
    }

    public static function checkIfRepPlanWiseAssocWaived(int $planID, $website, $agentID): bool
    {
        $currentPlanWaiveCheck = RepWaiveAssociationFee::where([
            'website' => $website,
            'pid' => $planID,
        ])->whereIn('rep_id', array_merge(self::getAllUplines($agentID), [$agentID]))
            ->orderByRaw(" FIELD(rep_id, '$agentID'), FIELD(apply_for_downline, 1) ")
            ->first();

        if ($currentPlanWaiveCheck instanceof RepWaiveAssociationFee && ($currentPlanWaiveCheck->rep_id == $agentID || $currentPlanWaiveCheck->apply_for_downline)) {
            return true;
        }

        return false;
    }

    public static function checkIfCarrierWiseAssocWaived(int $planID, $website, $agentID): bool
    {
        $plansDetails = Plan::find($planID);
        $currentPlanWaiveCheck = RepWaiveAssociationFee::where([
            'website' => $website,
            'carrier' => $plansDetails->carrier,
        ])->whereIn('rep_id', array_merge(self::getAllUplines($agentID), [$agentID]))
            ->orderByRaw(" FIELD(rep_id, '$agentID'), FIELD(apply_for_downline, 1) ")
            ->first();
        if ($currentPlanWaiveCheck instanceof RepWaiveAssociationFee && ($currentPlanWaiveCheck->rep_id == $agentID || $currentPlanWaiveCheck->apply_for_downline)) {
            return true;
        }

        return false;
    }

    private static function nortManAssocFee($agentID, &$finalPlanIds) {
        $nortManSpecificFee = false;
        if($agentID == self::$nortmanID) {
            $nortManSpecificFee = true;
            if(!in_array(801, array_column($finalPlanIds, 'plan_id'))) {
                $IHAAsscoFee = PlanPricingDisplay::where(['tier' => 'IO', 'pid' => '801', 'pricing_status' => 1])->first();
                $planAssociationId = new \stdClass();
                $planAssociationId->plan_id = (int)$IHAAsscoFee->pid;
                $planAssociationId->plan_pricing_id = (int)$IHAAsscoFee->plan_pricing_id;
                $finalPlanIds[] = $planAssociationId;
            }
        }
        return $nortManSpecificFee;
    }

    public static function getAgentListForVIP($agentID)
    {
        if (!is_array($agentID)) { $agentID = [$agentID]; self::$agentVip = array();  }
        $agentList = AgentInfo::whereIn('agent_ga', $agentID)->pluck('agent_id')->toArray();
        if (!empty($agentList)) {
            self::$agentVip = array_merge(self::$agentVip, $agentList);
            self::getAgentListForVIP($agentList);
        }
        return self::$agentVip;
    }

    public static function getAllUplines($agentId)
    {
        $uplines = [];
        $data_agent = AgentInfo::select('agent_ga')->find($agentId);
        if (!empty($data_agent) && !is_null($data_agent->agent_ga)) {
            $upline_agent = AgentInfo::select('agent_id', 'agent_ga', 'agent_email', 'is_business_email')->find($data_agent->agent_ga);

            if (!empty($upline_agent)) {

                $uplines[] = $upline_agent->agent_id;

               self::getAllUplines($upline_agent->agent_id);
            }
        }
        return $uplines;
    }

    public static function maximumAssociationFee ($associationPlans, $afterGroupSpecific) {
        $associationPlans = array_values($associationPlans);
        $maxIndex = array_search(max(array_column($associationPlans, 'price')), array_column($associationPlans, 'price'));
        $validAssociation = $associationPlans[$maxIndex];
        foreach ($associationPlans as $key => $assoc) {

            if ($key != $maxIndex) {
                $notValid = array_search($assoc['plan_id'], array_column($afterGroupSpecific, 'plan_id'));
                unset($afterGroupSpecific[$notValid]);
            }
        }
        return $afterGroupSpecific;
    }

    public static function getPLanPricingDisplay ($id) {

    	return PlanPricingDisplay::where('plan_pricing_id', $id)->first();
    }

    public static function getUserAssociationFees ($userId) {
        $policies = Policy::where('policy_userid', $userId)
                        ->where('status', 'ACTIVE')
                        ->with('getPolicyPlans')
                        ->get();
        $associationsFees = [];
        foreach ($policies as $key => $policy) {
            if ($policy->associationPlans()->count() > 0) {
                $associationsFees[$key]['policy_id'] = $policy->policy_id;
                $associationsFees[$key]['plan_id'] = $policy->associationPlans()->value('pid');
                $associationsFees[$key]['plan_pricing_id'] = $policy->associationPlans()->value('plan_id');
                $associationsFees[$key]['price'] = (float) $policy->associationPlans()->value('price_male_nons');
            }
        }
        return $associationsFees;
    }

    public static function bexSpecificCase ($plans, $groupId, $website) {
        if ($website == 'brokerexchanges.com') {
            if (in_array(626, array_column($plans, 'plan_id')) || in_array(556, array_column($plans, 'plan_id')) || in_array(627, array_column($plans, 'plan_id'))) {
                if (!in_array(690, array_column($plans, 'plan_id')) && !in_array(8599, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 690;
                    $add->plan_pricing_id = 8599;;
                    array_push($plans, $add);
                }

                if (in_array(721, array_column($plans, 'plan_id'))) {
                    $index = array_search(721, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            } else {
                if (!in_array(721, array_column($plans, 'plan_id')) && !in_array(8941, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 721;
                    $add->plan_pricing_id = 8941;;
                    array_push($plans, $add);
                }

                if (in_array(690, array_column($plans, 'plan_id'))) {
                    $index = array_search(690, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            }
            if ($groupId == 2361) {
                if (in_array(734, array_column($plans, 'plan_id')) || in_array(617, array_column($plans, 'plan_id')) || in_array(729, array_column($plans, 'plan_id')) || in_array(809, array_column($plans, 'plan_id'))) {

                    if (!in_array(832, array_column($plans, 'plan_id')) && !in_array(10856, array_column($plans, 'plan_pricing_id'))) {
                        $add = new \stdClass();
                        $add->plan_id = 832;
                        $add->plan_pricing_id = 10856;;
                        array_push($plans, $add);
                    }
                    $index = array_search(721, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            }
        }
        return $plans;
    }

    public static function groupSpecificCase($plans , $groupId) {
        if ($groupId == 2361) {
            $total = 0;
            foreach ($plans as $plan) {
                if (self::checkAssociation(isset($plan->plan_id) ? $plan->plan_id : $plan['plan_id'])) {
                    $assocKey = isset($plan->plan_id) ? $plan->plan_id : $plan['plan_id'];
                } else {
                    $price = PlanPricingDisplay::where('plan_pricing_id', isset($plan->plan_pricing_id) ? $plan->plan_pricing_id : $plan['plan_pricing_id'])->value('price_male_nons');
                    $total += $price;
                }
            }
            if ($total < 10 && $total > 0) {
                $index = array_search($assocKey, array_column($plans, 'plan_id'));
                unset($plans[$index]);

                if (!in_array(832, array_column($plans, 'plan_id')) && !in_array(10856, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 832;
                    $add->plan_pricing_id = 10856;;
                    array_push($plans, $add);
                }
            }
        }
        return $plans;
    }

    public static function checkElevateMembershipExisting ($policy_id) {
        $status = false;
            $userPlans = PlanOverview::where('policy_id', '=', $policy_id)
            ->where('peffective_date', '>' ,date("Y-m-d") )
            ->whereIn('pid',self::$elevateMembershipIds)
            ->count();
            $status = $userPlans > 0 ? true : false;

        return $status;
    }


    public static function getPlanPricingId($planDetail, $planInfo, $pid){
        try{
            if ($planDetail->plan_pricing_type == '2') {
                $fetchPlan = PlanPricingDisplay::where('pid', $pid)
                    ->where('tier', $planInfo['tier'])
                    ->where('pricing_status', '1')
                    ->where('age1', '<=', self::calculateAge($planInfo['dob']))
                    ->where('age2', '>=', self::calculateAge($planInfo['dob']))
                    ->select('plan_pricing_id')
                    ->first();
                $tierPricingType = "age-based";
            } else {
                $fetchPlan = PlanPricingDisplay::where('pid', $pid)
                    ->where('tier', $planInfo['tier'])
                    ->where('pricing_status', '1')
                    ->select('plan_pricing_id')
                    ->first();
                $tierPricingType = "tier-based";
            }
            return $fetchPlan;
        }
        catch(Exception $e) {
            return ['error' => $e->getMessage()];
        }

    }

    public static function updatePlanPolicies($data)
    {
        try{
            $insertPlan = PlanPolicy::firstOrCreate(
                [
                    'policy_num' => $data['policy_num'],
                    'plan_id' => $data['pid']
                ],
                [
                    'peffective_date' => $data['peffective_date'],
                    'pterm_date' => null,
                    'pstatus' => 1,
                    "date" => time(),
                    "ppptype" => $data['ppptype'],
                    "pefees" => $data['pefees'],
                    "is_approved" => 1,
                ]
            );

            if ($insertPlan->wasRecentlyCreated) {
                // record was newly created
            } else {
                if ($insertPlan->pstatus != 1) {
                    $insertPlan->pstatus = 1;
                    $insertPlan->pterm_date = null;
                    $insertPlan->save();
                }
            }
            return $insertPlan;
        }

        catch(Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public static function alternaivePlansOnAdressChange($plan_info , $da){
        try{
            Log::channel('planReplacedAlternative')->info("Checking data for alternative plans");
            $check = [];
            $alternatePlans = Plan::where('code834',$plan_info['code834'])->where('forsale', 1)->pluck('pid')->toArray();
            $zip = substr($da['zip'], 0, 3);
            $zipPlan = PlanZip::where('zip', 'like', '%'.$zip.'%')->pluck('pid')->toArray();
            foreach ($zipPlan as $key => $value) {
                if(in_array($value,$alternatePlans))
                array_push($check , $value);
            }

            if(count($check) == 0){
                return['error' => 'No Alternative Plan founds for '.Plan::where('pid' , $plan_info['pid'])->select('plan_name_system')->get()[0]->plan_name_system , 'error_type' => 'show_warning'];
            }
            return $check;
        }
        catch(Exception $e){
            return ['error' => $e->getMessage()];
        }
    }

    public static function getUserPlan($policy_id)
    {
        try{
            return PlanOverview::where('policy_id', '=', $policy_id)
            ->where('is_assoc',0)
            ->whereNotIn('pid', self::$elevateMembershipIds)
            ->where(function ($query) {
                $query->where('pstatus', '=', 1);
                    // ->orWhere(function ($subQuery) {
                    //     $subQuery->where('pstatus', '=', 2)
                    //         ->where('pterm_date', '>', date('Y-m-d'));
                    // });
            })
            ->select('pid','code834','plan_pricing_id')->get();
        }
        catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }

    }



        public static function calculateAge($dob)
        {
            try{
                $birthDate = explode("-", $dob);
                $age = (date("md", date("U", mktime(0, 0, 0, $birthDate[1], $birthDate[2], $birthDate[0]))) > date("md")
                    ? ((date("Y") - $birthDate[0]) - 1)
                    : (date("Y") - $birthDate[0]));
                return $age;
            }
            catch (Exception $e) {
                return ['error' => $e->getMessage()];
            }
        }


        public function computePlanEffectiveDates ($planId , $replacingpId) {

                $configuration = PlanPricingDisplay::where('pid', $planId)->select('effdate1', 'effdate15', 'cutoff1', 'cutoff15')->first();

                $currentMonth = date('m');
                $currentDay = date('d');
                $currentDate = time();

                $date = date('Y-m-d');
                $effectiveDates = [];
                if ($configuration) {

                    if ($configuration->effdate15 == '1') {

                        if ($currentDay <= $configuration->cutoff15) {

                            $effectiveDates[] = date('m/15/Y', $currentDate);
                        } elseif ($currentDay > $configuration->cutoff15) {

                            $effectiveDates[] = date('m/15/Y', strtotime('+1 month'));
                        }
                    }

                    if ($configuration->effdate1 == '1') {

                        if ($currentDay <= $configuration->cutoff1) {

                            if (self::checkTelementalPlans($replacingpId)) {
                                 // $teleDates = self::dateRange(date('Y-m-d', strtotime('+3 day')), date('Y-m-01', strtotime("$date + 1 month")));
                                 // foreach ($teleDates as $teleDate) {
                                 //    $effectiveDates[] = date('m/d/Y', strtotime($teleDate));
                                 // }
                                if (date('d') >= 1 && date('d', strtotime('+2 day')) <= 15) {
                                    $effectiveDates[] = date('m/15/Y');
                                    $effectiveDates[] = date('m/01/Y', strtotime('+1 month'));
                                } else {
                                    $effectiveDates[] = date('m/01/Y', strtotime('+1 month'));
                                    $effectiveDates[] = date('m/15/Y', strtotime('+1 month'));
                                }
                            } else {
                                $effectiveDates[] = date('m/01/Y', strtotime('+1month'));
                            }

                        } elseif ($currentDay > $configuration->cutoff1) {
                            //days 31, 32 - bug fix logic
                            if ($currentDay > 28) {
                                $date = date('Y/m/d', strtotime('-4 day'));
                            }

                            if (self::checkTelementalPlans($replacingpId)) {
                                //  $teleDates = self::dateRange(date('Y-m-d', strtotime('+3 day')), date('Y-m-01', strtotime("$date + 2 month")));

                                // foreach ($teleDates as $teleDate) {
                                //     $effectiveDates[] = date('m/d/Y', strtotime($teleDate));
                                // }
                                if (date('d') >= 1 && date('d', strtotime('+2 day')) <= 15) {
                                    $effectiveDates[] = date('m/15/Y');
                                    $effectiveDates[] = date('m/01/Y', strtotime('+1 month'));
                                } else {
                                    $effectiveDates[] = date('m/01/Y', strtotime('+1 month'));
                                    $effectiveDates[] = date('m/15/Y', strtotime('+1 month'));
                                }
                            } else {
                                $effectiveDates[] = date('m/01/Y', strtotime("$date +2 month"));
                            }

                        }
                    }
                }
                $effectiveDates = array_map(function($date) {
                    return date('Y-m-d', strtotime($date));
                }, $effectiveDates);
                return $effectiveDates;
        }

        public static function checkTelementalPlans ($plan_id) {

            $telementalOnly = true;

                    if (!self::checkAssociation($plan_id)) {
                         if ($plan_id == '927' || $plan_id == '926' || $plan_id == '873' || $plan_id == '869' || $plan_id == '837') {
                            $telementalOnly = true;
                        }
                        $telementalOnly = false;
                    }
            return $telementalOnly;
        }

        public static function checkAssociation ($planId) {

            if (Plan::where('pid', $planId)->where('is_assoc', 1)->count() > 0 && !in_array($planId, self::$elevateMembershipIds))
                return true;

            return false;

        }

        public function updatePolicyLog($policy, $action, $comment, $loginUserId)
        {
            $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
            $update = [
                'elgb_policyid' => $policy['policy_id'],
                'elgb_act' => $action,
                'elgb_act_date' => time(),
                'elgb_comment' => $comment,
                'elgb_agent' => $loginUserId,
                'elgb_file_date' => date('Y-m-d'),
                'origin' => $origin
            ];
            $policyUpdateLog = new PolicyUpdate();
            $policyUpdateLog->create($update);
            return $policyUpdateLog;
        }

}
