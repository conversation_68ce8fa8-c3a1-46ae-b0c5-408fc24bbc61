<?php

namespace App\Repositories\Plans;

use App\Plan;
use App\PlanAgeRestriction;
use App\PlanAgeRestrictionDependent;
use App\PlanState;
use App\PlanTier;
use Exception;
use Illuminate\Database\Eloquent\Model;

class ManagePlans extends Model
{
    public function newPlan($request)
    {
        $data1 = $this->setPlanValue($request);
        $data2 = $this->setPlanBooleanValue($request);
        $relatedData = $this->setPlanRelatedData($request);
        $data = array_merge($data1, $data2);
        if ($request->pid){
            $result = $this->updatePlan($request->pid, $data, $relatedData);
        } else{
            $result = $this->addPlan($data, $relatedData);
        }
        return $result;
    }
    public function addPlan($planData, $planRelatedData)
    {
        $check_plan = Plan::where('code834', $planData['code834'])->count();
        if ($check_plan > 0) {
            $message = 'The plan is already added. Please check on plan list.';
            return ['error' => $message];
        } else {
            try {
                $plan = $this->addPlanToDb($planData);
                $planId = $plan->pid;
                $planTiers = $planRelatedData['plan_tiers'];
                $planStates = $planRelatedData['carrier_states'];
                $planTier = $this->addToPlanTier($planId, $planTiers);
                $carrierState = $this->addToPlanState($planId, $planStates);
                $planAgeRestriction = $this->planAgeRestriction($planId, $planRelatedData);
                $planAgeRestrictionDependent = $this->planAgeRestrictionDependent($planId, $planRelatedData);
                $message = 'Plan Successfully Added.';
            } catch (Exception $e) {
                $message = 'Failed to add new Plan.';
                return ['error' => $e->getMessage()];
            }
            return ['success' => $message];
        }
    }
    public function updatePlan($id, $planData, $planRelatedData)
    {
        try {
            $plan = $this->updatePlanToDb($id, $planData);
            $planTiers = $planRelatedData['plan_tiers'];
            $planStates = $planRelatedData['carrier_states'];
            $planTier = $this->updateToPlanTier($id, $planTiers);
            $carrierState = $this->updateToPlanState($id, $planStates);
            $planAgeRestriction = $this->planAgeRestriction($id, $planRelatedData);
            $planAgeRestrictionDependent = $this->planAgeRestrictionDependent($id, $planRelatedData);
            $message = 'Plan Successfully Updated.';
        } catch (Exception $e) {
            $message = 'Failed to update plan.';
            return ['error' => $e->getMessage()];
        }
        return ['success' => $message];

    }
    public function setPlanValue($request)
    {
        $data['plan_name_system'] = $request->pname;
        $data['plan_code_system'] = $request->pcode;
        $data['plan_name_carrier'] = $request->cpname;
        $data['plan_code_carrier'] = $request->cpcode;
        $data['plan_code_carrier15'] = $request->cpcode15;
        $data['carrier'] = $request->carrier_val;
        $data['web_display_name'] = $request->webname;
        $data['code834'] = $request->code834;
        $data['plan_pricing_type'] = $request->price_type;
        $data['comm_structure'] = $request->com_struc;
        $data['plan_desc'] = $request->pdescription;
        $data['rider'] = $request->pltype;
        $data['cutoff1'] = $request->pcut1;
        $data['cutoff15'] = $request->pcut2;
        $data['go_live'] = $request->golive1;
        $data['go_live1'] = $request->golive15;
        $data['is_assoc'] = $request->is_assoc;
        $data['added'] = time();
        return $data;
    }
    public function setPlanBooleanValue($request)
    {
        $data['forsale'] = ($request->fsale == true) ? 1 : 0;
        $data['ssn'] = ($request->rssn == true) ? 1 : 0;
        $data['ben'] = ($request->rbi == true) ? 1 : 0;
        $data['ben_deps'] = ($request->rdi == true) ? 1 : 0;
        $data['dssn'] = ($request->rdssn == true) ? 1 : 0;
        $data['heightrq'] = ($request->rh == true) ? 1 : 0;
        $data['weightrq'] = ($request->rw == true) ? 1 : 0;
        $data['rq_employer'] = ($request->re == true) ? 1 : 0;
        $data['rq_employ_start_date'] = ($request->resd == true) ? 1 : 0;
        $data['rq_occupation'] = ($request->ro == true) ? 1 : 0;
        $data['rq_hourly_rate'] = ($request->rhr == true) ? 1 : 0;
        $data['martials'] = ($request->rms == true) ? 1 : 0;
        $data['rq_age'] = ($request->ra == true) ? 1 : 0;
        $data['reqagtsig'] = ($request->rs == true) ? 1 : 0;
        $data['effdate1'] = ($request->peff1 == true) ? 1 : 0;
        $data['effdate15'] = ($request->peff15 == true) ? 1 : 0;
        $data['pm_cc'] = ($request->cc == true) ? 1 : 0;
        $data['pm_eft'] = ($request->eft == true) ? 1 : 0;
        $data['pm_lst'] = ($request->list == true) ? 1 : 0;
        $data['pm_stmt'] = ($request->stmt == true) ? 1 : 0;
        return $data;
    }
    public function setPlanRelatedData($request)
    {
        $data['pmin_age'] = $request->pminage;
        $data['pmax_age'] = $request->pmaxage;
        $data['dmin_age'] = $request->dminage;
        $data['dmax_age'] = $request->dmaxage;
        $data['plan_tiers'] = $request->plan_tiers;
        $data['carrier_states'] = $request->plan_states;
        return $data;
    }
    public function addPlanToDb($request)
    {
        $plan = Plan::create($request);
        return $plan;
    }
    public function addToPlanTier($id, $tiers)
    {
        for ($i = 0; $i < count($tiers); $i++) {
            $planTier = new PlanTier();
            $planTier->pid_tier = $id;
            $planTier->tier = $tiers[$i]['id'];
            $planTier->save();
        }
    }
    public function addToPlanState($id, $states)
    {
        for ($j = 0; $j < count($states); $j++) {
            $planState = new PlanState();
            $planState->pid = $id;
            $planState->state = $states[$j]['id'];
            $planState->save();
        }
    }
    public function planAgeRestriction($id, $planData)
    {
        $planAge = PlanAgeRestriction::firstOrNew(['plan_id' => $id]);
        $planAge->primary_min = $planData['pmin_age'];
        $planAge->primary_max = $planData['pmax_age'];
        $planAge->status = 1;
        $planAge->save();
    }
    public function planAgeRestrictionDependent($id, $planData)
    {
        $planAge = PlanAgeRestrictionDependent::firstOrNew(['plan_id' => $id]);
        $planAge->dmin = $planData['dmin_age'];
        $planAge->dmax = $planData['dmax_age'];
        $planAge->dstate = 1;
        $planAge->save();
    }
    public function updatePlanToDb($id, $data)
    {
        $plan = Plan::where('pid', $id)->update($data);
        return $plan;
    }
    public function updateToPlanTier($id, $data)
    {
        PlanTier::where('pid_tier', $id)->delete();
        return $this->addToPlanTier($id, $data);
    }
    public function updateToPlanState($id, $data)
    {
        PlanState::where('pid', $id)->delete();
        return $this->addToPlanState($id, $data);
    }
}
