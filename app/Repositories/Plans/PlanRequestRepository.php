<?php

namespace App\Repositories\Plans;

use App\Plan;
use App\PlanOverview;
use App\PlanPolicy;
use App\Policy;
use App\ReactivationRequest;
use App\Service\MessageService;
use App\UserInfoPolicyAddress;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use stdClass;

class PlanRequestRepository extends Model
{
    private  $PlanChangeOnAddress;

    public function __construct()
    {
        $this->PlanChangeOnAddress = new PlanChangeOnAddress();
    }

    public function getPlanRequest(){
       $PlanActivationRequest =  ReactivationRequest::query()->where('reactivation_status',null)
       ->orWhere('reactivation_status','')
       ->select(\DB::raw('(CASE 
       WHEN reactive_request.pid = "ALL" THEN  reactive_request.all_requested_pid
       WHEN reactive_request.pid != "ALL" THEN reactive_request.pid
       END) AS pid'),\DB::raw("CONCAT('https://corenroll.com/bank_sign/',signature) AS signature"),'id','policy_id','req_date','reason','reactivation_reason','policy_id')
       ->orderBy('id','DESC')
       ->get()->toArray();
       foreach ($PlanActivationRequest as $key => $plans) {
            $PlanActivationRequest[$key]['plans'] = $this->fetchRequestedPlans($plans);
            $PlanActivationRequest[$key]['member'] = $this->fetchRequestedmember($plans);
       }
       return $PlanActivationRequest;
    }

    function fetchRequestedPlans($plan){
        return PlanOverview::where('pid',$plan['pid'])
        ->where('policy_id',$plan['policy_id'])
        ->select('date','web_display_name','tier','price_male_nons','peffective_date','pterm_date','pid','ppptype')
        ->first();
    }

    function fetchRequestedmember($plan){
        return UserInfoPolicyAddress::
        where('policy_id',$plan['policy_id'])
        ->select('cfname','clname','cmname','cemail','cdob','phone1','baddress1','baddress2','bstate','bzip','bcity','agent_code','agent_fname','agent_fname','agent_lname','agent_id','agent_email')
        ->first();
    }

    function planRequestAction($request){
        DB::beginTransaction();
        try{
        $plan_fetch = Plan::where('pid',$request->plan_id)->select('plan_pricing_type','web_display_name')->first();
        
        $policy['policy_id'] = $request->policy_id;
        $user_detail = UserInfoPolicyAddress::
        where('policy_id',$request->policy_id)
        ->select('cfname','clname','cmname','cemail','cdob','phone1','baddress1','baddress2','bstate','bzip','bcity')
        ->first();
        $data['dataName'] = $user_detail['cfname'].' '.$user_detail['cmname'].' '.$user_detail['clname'];
        $data['email'] = $user_detail['cemail'];
        if($request->type == 'approve'){
            $action = 'RPA';
            $planDetail = new stdClass();
            $eff_date = $this->PlanChangeOnAddress->computePlanEffectiveDates($request->plan_id , $request->plan_id);
            $planDetail->plan_pricing_type = $plan_fetch->plan_pricing_type;
            $planInfo['dob'] = $user_detail->cdob;
            $planInfo['tier'] = $request->tier;
            $plan_pricing = $this->PlanChangeOnAddress->getPlanPricingId($planDetail, $planInfo, $request->plan_id);
            if(PlanPolicy::where('policy_num', $request->policy_id)->where('plan_id',$plan_pricing->plan_pricing_id)->where('pstatus',1)->count()){
                return array('type' => 'success', 'response' => 'This Plan is already active.');
            }

            PlanPolicy::where('policy_num', $request->policy_id)->where('plan_id',$plan_pricing->plan_pricing_id)->update([
                'peffective_date' => $eff_date[0],
                'pterm_date' => '',
                'pstatus' => 1,
            ]);
            Policy::where('policy_id', $request->policy_id)->update(['term_date' => '', 'status' => 'ACTIVE','Approval'=>1]);
            $existing_plans = PlanOverview::where('policy_id',$request->policy_id)->where('pstatus',1)->select('pid','plan_id')->get();
            if(!PlanOverview::where('policy_id',$request->policy_id)->where('is_assoc',1)->where('pstatus',1)->count()){
                $assoc_id = $this->PlanChangeOnAddress->computeAssociationFees ($existing_plans, $request->policy_id);
                if(count($assoc_id)){
                    foreach ($assoc_id as $key => $value) {
                        if(!is_array($value)){
                            if(PlanPolicy::where('plan_id',$value->plan_pricing_id)->count()){
                                    PlanPolicy::where('policy_num', $request->policy_id)->where('plan_id',$value->plan_pricing_id)->update([
                                        'peffective_date' => $eff_date[0],
                                        'pterm_date' => '',
                                        'pstatus' => 1,
                                    ]);
                            }
                        }
                    }
                }
            }

            ReactivationRequest::query()->where('id',$request->id)->update([
                'reactivation_status' => 'Approved',
            ]);
            DB::commit();
            $message = 'Requested plan has been approved'; 
            $email_message = 'Your plan "'.$plan_fetch['web_display_name'].'" requested for approval has now been approved.';
        }
        else{
            $action = 'RPR';
            ReactivationRequest::query()->where('id',$request->id)->update([
                'reactivation_status' => 'Rejected',
            ]);
            $message = 'Requested plan has been Rejected';
            $email_message = 'Your plan "'.$plan_fetch['web_display_name'].'" requested for approval has been rejected.';
            DB::commit();
        }
        $this->PlanChangeOnAddress->updatePolicyLog($policy, $action, $message, request()->header('id'));
        $data['subject'] = $message;
        $data['message'] = $email_message;
        self::sendUserEmployerEmail($data);
        return array('type' => 'success', 'response' => $message);
    } catch (Exception $e) {
        $response = false;
        DB::rollBack();
        $message = $e->getMessage();
        return array('response' => $response, 'message' => $message);
    }
    }

    public static function sendUserEmployerEmail($data)
    {
            $emailConfigurationName = "EXPOER_EXCEL_TEMPLETE";
            $emailData = [
                'email_message_configuration_name' => $emailConfigurationName,
                'toAddress' => $data['email'],
                'ccAddress' => [],
                'subject' => $data['subject'],
                'message' => $data['message'],
                'data' => [],
                'dataName' => $data['dataName']
            ];
            $messageService = new MessageService();
            $messageService->sendEmailWithContentData($emailData);
    }

    

}
