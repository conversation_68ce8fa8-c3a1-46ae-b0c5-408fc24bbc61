<?php


namespace App\Repositories\Plans;


use App\Plan;
use App\PlanOverview;
use Carbon\Carbon;
use Symfony\Component\Intl\NumberFormatter\NumberFormatter;

class PrudentialPlanRepository
{

    const PLAN_CARRIER_ID = 80;
    /**
     * @var PlanOverview
     */
    private $model;

    public function __construct(PlanOverview $model)
    {
        $this->model = $model;
    }

    protected function prudentialsPlans()
    {
        return Plan::query()
            ->select('pid', 'carrier', 'plan_name_system')
            ->where([
                ['carrier', '=', self::PLAN_CARRIER_ID]
            ])
            ->orderBy('pid', 'DESC')
            ->get();

    }

    protected function getPlanOverviewQuery($pId, $startDate, $endDate)
    {
        /**
         * @var $startDate Carbon
         * @var $endDate Carbon
         */
        $newStartDate = Carbon::parse($startDate)->timestamp;
        $newEndDate = Carbon::parse($endDate)->timestamp;
        return PlanOverview::query()
            ->where([
                ['cid', '=', $this->model::PLAN_CARRIER_ID],
                ['pid', '=', $pId],
            ])
            ->whereBetween('edate', [$newStartDate, $newEndDate]);
    }

    protected function countPlan($pId, $startDate, $endDate)
    {
        return $this->getPlanOverviewQuery($pId, $startDate, $endDate)
            ->whereIn('pstatus', [1, 2])
            ->count();
    }

    protected function getPriceMaleNonsByPId($pId, $startDate, $endDate)
    {
        return $this->getPlanOverviewQuery($pId, $startDate, $endDate)
            ->whereIn('pstatus', [1, 2])
            ->sum('price_male_nons');
    }

    /**
     * @return array
     */
    protected function weeklyDates(): array
    {
        $dateFormat = 'm/d/Y';
        /**
         * @var $startDate Carbon
         * @var $endDate Carbon
         */
        $startDate = Carbon::now()->subDay()->isStartOfDay() ? Carbon::now()->startOfWeek() : Carbon::now()->startOfWeek()->subDays(7);
        $endDate = Carbon::now()->subDay()->isEndOfDay() ? Carbon::now()->endOfWeek() : Carbon::now()->endOfWeek()->subDays(7);
        return [
            'currentStartDate' => $startDate->format($dateFormat),
            'thirdStartDate' => $startDate->subDays(7)->format($dateFormat),
            'secondStartDate' => $startDate->subDays(14)->format($dateFormat),
            'firstStartDate' => $startDate->subDays(21)->format($dateFormat),
            'currentEndDate' => $endDate->format($dateFormat),
            'thirdEndDate' => $endDate->subDays(7)->format($dateFormat),
            'secondEndDate' => $endDate->subDays(14)->format($dateFormat),
            'firstEndDate' => $endDate->subDays(21)->format($dateFormat),
        ];
    }

    public function weeklyReport()
    {
        $date = $this->weeklyDates();
        return $this->generatePlans($date);
    }

    public function startMonthDate(int $value)
    {
        $dateFormat = 'm/d/Y';
        return Carbon::now()->subMonths($value)->startOfMonth()->format($dateFormat);
    }

    public function endMonthDate(int $value)
    {
        $dateFormat = 'm/d/Y';
        return Carbon::now()->subMonths($value)->endOfMonth()->format($dateFormat);
    }

    /**
     * @return array
     */
    protected function monthlyDates(): array
    {

        $dateFormat = 'm/d/Y';
        /**
         * @var $startDate Carbon
         * @var $endDate Carbon
         */
        $startDate = Carbon::now()->isLastOfMonth() ? Carbon::now()->startOfMonth()->format($dateFormat) : $this->startMonthDate(1);
        $endDate = Carbon::now()->isLastOfMonth() ? Carbon::now()->endOfMonth()->format($dateFormat) : $this->endMonthDate(1);
        return [
            'currentStartDate' => $startDate,
            'thirdStartDate' => Carbon::now()->isLastOfMonth() ? $this->startMonthDate(1) : $this->startMonthDate(2),
            'secondStartDate' => Carbon::now()->isLastOfMonth() ? $this->startMonthDate(2) : $this->startMonthDate(3),
            'firstStartDate' => Carbon::now()->isLastOfMonth() ? $this->startMonthDate(3) : $this->startMonthDate(4),
            'currentEndDate' => $endDate,
            'thirdEndDate' => Carbon::now()->isLastOfMonth() ? $this->endMonthDate(1) : $this->endMonthDate(2),
            'secondEndDate' => Carbon::now()->isLastOfMonth() ? $this->endMonthDate(2) : $this->endMonthDate(3),
            'firstEndDate' => Carbon::now()->isLastOfMonth() ? $this->endMonthDate(3) : $this->endMonthDate(4),
        ];
    }

    public function monthlyReport()
    {
        $date = $this->monthlyDates();
        return $this->generatePlans($date);
    }

    /**
     * @param array $date
     * @return array
     */
    protected function generatePlans(array $date): array
    {
        $prudentialsPlans = $this->prudentialsPlans();
        $data = [];
        $data['header'] = [
            'plan' => 'Plan',
            'col_first' => "Total <br/>{$date['firstStartDate']} - {$date['firstEndDate']}",
            'col_second' => "Total<br/> {$date['secondStartDate']} - {$date['secondEndDate']}",
            'col_third' => "Total <br/>{$date['thirdStartDate']} - {$date['thirdEndDate']}",
            'col_current' => "Total <br/> {$date['currentStartDate']} - {$date['currentEndDate']}",
        ];
        foreach ($prudentialsPlans as $p) {
            $data['body'][] = [
                'plan_name_system' => "{$p['plan_name_system']}",
                'row_first' => $this->countPlan($p->pid, $date['firstStartDate'], $date['firstEndDate']),
                'row_first_price' => $this->getPriceMaleNonsByPId($p->pid, $date['firstStartDate'], $date['firstEndDate']),
                'row_second' => $this->countPlan($p->pid, $date['secondStartDate'], $date['secondEndDate']),
                'row_second_price' => $this->getPriceMaleNonsByPId($p->pid, $date['secondStartDate'], $date['secondEndDate']),
                'row_third' => $this->countPlan($p->pid, $date['thirdStartDate'], $date['thirdEndDate']),
                'row_third_price' => $this->getPriceMaleNonsByPId($p->pid, $date['thirdStartDate'], $date['thirdEndDate']),
                'row_current' => $this->countPlan($p->pid, $date['currentStartDate'], $date['currentEndDate']),
                'row_current_price' => $this->getPriceMaleNonsByPId($p->pid, $date['currentStartDate'], $date['currentEndDate']),
            ];
        }
        if ($data){
            $data['total'] = [
                'plan' => 'Total',
                'total_row_first' => $this->getSumOfRow($data['body'],'row_first_price'),
                'total_row_second' => $this->getSumOfRow($data['body'],'row_second_price'),
                'total_row_third' =>$this->getSumOfRow($data['body'],'row_third_price'),
                'total_row_current' => $this->getSumOfRow($data['body'],'row_current_price'),
            ];
        }
        return $data;
    }

    public function getSumOfRow($data,$row){
        $price = array_sum(array_column($data, $row));
        return $this->priceFormat($price);
    }


    protected function generatePlanPrice($pId, $firstDate, $endDate)
    {
        $count = $this->countPlan($pId, $firstDate, $endDate);
        $price = $this->getPriceMaleNonsByPId($pId, $firstDate, $endDate);
        $formattedPrice = $price > 0 ? "<br/> Total Premium -" . $this->priceFormat($price) : "";
        return $count . $formattedPrice;
    }

    /**
     * currently using only in this service class
     * @param $price
     * @return string
     * @todo make global
     */
    public function priceFormat($price)
    {
        return "$" . sprintf('%01.2f', $price);
    }
}
