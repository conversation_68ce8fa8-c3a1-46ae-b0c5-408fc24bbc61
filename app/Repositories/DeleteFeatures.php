<?php

namespace App\Repositories;

use App\Policy;
use App\DependentInPolicy;
use App\DependentPolicy;
use App\UserInfoDepMedBex;
use App\PolicyBeneficiaryDependent;
use App\EnrollmentQuestionAnswer;
use App\PolicyClientBrowserInfo;
use App\Signature;
use App\PolicyNote;
use App\PolicyBeneficiary;
use App\PlanPolicy;
use App\MedCondition;
use App\MedMedication;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

class DeleteFeatures extends Model
{
    public function deletePolicy($request)
    {
        /*
        Back Office has two functions for deletion
        1. For array of policies (bulk) that is called from same page. Only for test policies.
        2. For policy action delete event in Manageclients.php i.e. for individual policies.
        Here policy delete implemented is for both. Requires array of policy for group.For individual policy single policy Id can be send.        
        */ 

        try
        {
            $data=[];
            foreach($request->policy_id as $groupPolicy)                
            {
                $policy_id = $groupPolicy;
                $isValidPolicy = Policy::where('policy_id',$policy_id)->get()->count(); // is a valid policy if count is 1
                if($isValidPolicy == 1)
                {
                    $deleteUserData = Policy::where('policy_id',$policy_id)->first();

                    //backoffice logic is incorrect for deleting signature so using correct logic here by comparing userid with userid unlike back office where user id is compared with policy id.
                    Signature::where('userid',$deleteUserData->policy_userid)->delete(); 
            
                    PolicyNote::where('policy_id',$policy_id)->delete();
                    PolicyBeneficiaryDependent::where('bpolicy_id',$policy_id)->delete();        
                    PolicyBeneficiary::where('bpolicy_id',$policy_id)->delete();
                    PolicyClientBrowserInfo::where('policy_id',$policy_id)->delete();
                    EnrollmentQuestionAnswer::where('pid',$policy_id)->delete();
                    PlanPolicy::where('policy_num',$policy_id)->delete();
            
                    $userInfoDepMedBex1 = UserInfoDepMedBex::where('user_id',$deleteUserData->policy_userid)->get();
                    foreach($userInfoDepMedBex1 as $data)
                    {
                        MedCondition::where('med_id',$data->med_id)->delete();
                    }
                    UserInfoDepMedBex::where('user_id',$deleteUserData->policy_userid)->delete();            

                    //Only for group invoices  
                    if(count($request->policy_id)>1){
                        $deleteDepData = Policy::where('policy_id',$deleteUserData->policy_id)->first();
                        MedMedication::where('user_id',$deleteDepData->policy_userid)->delete();
                    }

                    $dependentData = DependentInPolicy::where('policy_id',$policy_id)->get();
                    foreach($dependentData as $depData) 
                    {
                        $userInfoDepMedBex2 = UserInfoDepMedBex::where('dependent_id',$depData->dependent_id)->get();
                        foreach($userInfoDepMedBex2 as $data)
                        {
                            MedCondition::where('med_id',$data->med_id)->delete();
                        }
                        UserInfoDepMedBex::where('dependent_id',$depData->dependent_id)->delete();                
                /* 
                In back office the below mentioned deletion is implemented using logic below however policies table donot have dependent_id column.
                $deleteDepData = Policy::where('dependent_id',$depData->dependent_id)->first();
                MedMedication::where('user_id',$deleteDepData->policy_userid)->delete();
                */
                    }        
                    DependentPolicy::where('policy_id',$policy_id)->delete();
                    Policy::where('policy_id',$policy_id)->delete();
                    array_push($data,array('policy_id'=>$policy_id,'type' => 'success', 'message' =>'Policy Deleted.'));
                }
                else //Either policy doesnot exist or invalid policy
                {
                    array_push($data,array('policy_id'=>$policy_id,'type' => 'error', 'message' =>'Either Policy does not exist or is not valid.'));
                }
            }
            return $data;
        }catch(Exception $e){
            $message = 'Failed To delete Policy: ';
            $message = $e->getMessage();
        }
    } 
}