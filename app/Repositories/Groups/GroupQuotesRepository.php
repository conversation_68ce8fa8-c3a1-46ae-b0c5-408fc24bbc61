<?php

namespace App\Repositories\Groups;

use App\GroupCensus;
use App\Traits\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class GroupQuotesRepository extends Model
{
    use Paginator;
    public function paginatedFormattedList($limit, $filters = []): array
    {
        $data = $this->paginatedList($limit, $filters)->appends($filters);

        return [
            'links' => $this->links($data),
            'meta' => $this->meta($data),
            'data' => $data->items(),
        ];
    }

    public function paginatedList($limit, $filters = [])
    {
        $query = $this->filterContent($filters)
            ->orderBy('id', 'desc');

        $paginatedData = $query->paginate($limit);

        $paginatedData->getCollection()->transform(function ($item) {
            $item['group_plans'] = json_decode($item['group_plans'], true);
            $item['primary_count'] = $item->groupCensusReports->count();
            $item['dependent_count'] = $item->groupCensusReports->sum(function ($report) {
                return $report->groupCensusDependent->count();
            });
            return $item;
        });

        return $paginatedData;
    }

    public function filterContent($filters): Builder
    {
        $query = GroupCensus::with(
            'groupCensusReports.plan:pid,plan_name_system as web_display_name',
            'groupCensusReports.groupCensusDependent'
        );

        if (isset($filters['groupName']) && $filters['groupName'] != '') {
            $query->where('group_name', 'like', '%' . $filters['groupName'] . '%');
        }

        if (isset($filters['contactName']) && $filters['contactName'] != '') {
            $query->where('group_contact_name', 'like', '%' . $filters['contactName'] . '%');
        }

        if (isset($filters['status']) && $filters['status'] != '') {
            $query->where('approval_status', 'like', '%' . $filters['status'] . '%');
        }

        if (isset($filters['contactPhone']) && $filters['contactPhone'] != '') {
            $query->where('group_contact_phone', 'like', '%' . $filters['contactPhone'] . '%');
        }

        if (isset($filters['contactEmail']) && $filters['contactEmail'] != '') {
            $query->where('group_contact_email', 'like', '%' . $filters['contactEmail'] . '%');
        }

        if (isset($filters['groupPlan']) && $filters['groupPlan'] != '') {
            $query->where('group_plans', 'like', '%' . $filters['groupPlan'] . '%');
        }

        return $query;
    }
    public function getPlanList()
    {
        $planlists = DB::table('plans')->select('pid', 'plan_name_system','web_display_name')->where('plan_name_system','like','SM%')->orWhere('plan_name_system','like','PW%')->get();
        return $planlists;
    }
}
