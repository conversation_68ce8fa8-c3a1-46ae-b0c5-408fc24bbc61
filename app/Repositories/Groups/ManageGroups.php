<?php

namespace App\Repositories\Groups;

use App\AgentInfo;
use App\AgentInGroup;
use App\AgentLicense;
use App\Docs;
use App\GroupBillingAddress;
use App\GroupEft;
use App\GroupIndustry;
use App\GroupInfo;
use App\GroupPhLevel;
use App\GroupPlanFee;
use App\GroupTotals;
use App\GroupUpdate;
use App\GroupUser;
use App\Helpers\CommonHelper;
use App\Helpers\DecryptEcryptHelper;
use App\Helpers\GuzzleHelper;
use App\Http\Requests\ManageGroup\ChangeGroupPasswordRequest;
use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use App\PlanOverview;
use App\Policy;
use App\PolicyNote;
use App\Repositories\Agents\ManageAgents;
use App\Repositories\PolicyUpdateLog;
use App\Repositories\TermRequestRepository;
use App\Service\CustomValidationService;
use App\Service\GroupService;
use App\Service\MessageService;
use App\SsoUsers;
use App\SubGroup;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use App\UserActivityDetail;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use MongoDB\Driver\Query;
use Symfony\Component\HttpFoundation\Response;
use App\Traits\SubGroupEmailTrait;

class ManageGroups extends Model
{
    use SubGroupEmailTrait;
    private const ASSOCIATION_LOG_CHANNEL_NAME = 'groupAssociationFeeWaiverLog';

    protected function mimes(){
        return array('pdf','doc','docx','xls','xlsx','csv');
    }
    public function __construct()
    {
        $this->groupFilePath = 'uploads/group_File_Path';
    }
    use Paginator, ResponseMessage;

    static $withRelations = [
        'memberCount',
        'mainAgent',
        'groupAgents',
        'infoQuick',
        'userActivity'
    ];

    public function manageGroups()
    {
        return GroupInfo::with('memberCount')
            ->with('mainAgent')
            ->orderBy('gid', 'DESC')
            ->get();
    }

    public function viewGroupDetail($request)
    {
        return GroupInfo::where('gid', $request->gid)
            ->with('memberCount')
            ->with('mainAgent')
            ->with('groupAgents')
            ->get();
    }

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getGroupList($request){
        return AgentInGroup::select('gid','gname')->where('gname', 'like', "%{$request['data']}%")->distinct('gid')->orderBy('gname','ASC')->where('ag_status','!=','D')->limit($request['limit'])->get();
    }


    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->orderBy('gid', 'desc')
            ->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = GroupInfo::query()
//            ->join('agent_info', 'group_info.gagent_code', '=', 'agent_info.agent_id')
            ->with(self::$withRelations);
//            ->whereHas('mainAgent');
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {

        if (isset($filters['group_name'])) {
            $data->where('gname', 'LIKE', '%' . $filters['group_name'] . '%');
        }

        if (isset($filters['group_code'])) {
            $data->where('gcode', '=', $filters['group_code']);
        }

        if (isset($filters['status'])) {
            $data->where('gstatus', '=', $filters['status']);
        }

        if (isset($filters['group_type'])) {
            $data->where('gtype', '=', $filters['group_type']);
        }

        if (isset($filters['taxid'])) {
            $data->where('gein', '=', $filters['taxid']);
        }

        if (isset($filters['agent_code'])) {
            $data->whereHas('mainAgent', function ($q) use ($filters) {
                $q->where('agent_code', '=', $filters['agent_code']);
            });
        }

        if (isset($filters['gemail'])) {
            $data->where('gemail','like', '%' . $filters['gemail']. '%');
        }

        if (isset($filters['signup_date_from']) || isset($filters['signup_date_to'])) {
            $fromDate = isset($filters['signup_date_from']) ? strtotime($filters['signup_date_from']) : $this->getFirstSignupDate();
            $toDate = isset($filters['signup_date_to']) ? strtotime($filters['signup_date_to']) : Carbon::now()->unix();
            $nextDate = Carbon::parse($toDate)->addDay()->unix();
            $data->whereBetween('gts', [$fromDate, $nextDate]);
        }

        //filter by member count
        if (isset($filters['member_count_from']) || isset($filters['member_count_to'])) {
            $data->whereHas('memberCount', function ($query) use ($filters) {
                //check for both
                if (isset($filters['member_count_from']) and isset($filters['member_count_to'])) {
                    $query->havingRaw("SUM(totals) BETWEEN {$filters['member_count_from']} AND {$filters['member_count_to']}");
                } //check for only member having count from
                elseif (isset($filters['member_count_from'])) {
                    $query->havingRaw("SUM(totals) > {$filters['member_count_from']}");
                } //check for only member having count to
                elseif (isset($filters['member_count_to'])) {
                    $query->havingRaw("SUM(totals) < {$filters['member_count_to']}");
                }
            });
        }

        //filter group vip
        if (isset($filters['is_vip'])) {
            $data->where('vip', '=', $filters['is_vip']);
        }

        //filter payment type
        if (isset($filters['payment_type']) and in_array($filters['payment_type'], GroupInfo::$paymentTypes)) {
            $data->where($filters['payment_type'], '=', 1);
        }

        //filter state
        if (isset($filters['state'])) {
            $data->where('gstate', '=', $filters['state']);
        }

        //filter website
        if (isset($filters['website'])) {
            $data->whereHas('infoQuick', function ($query) use ($filters) {
                $query->where('weburl', '=', $filters['website']);
            });
        }


        //filter app
        if (isset($filters['mobile_app_device']) and in_array($filters['mobile_app_device'], GroupInfo::$mobileAppDevices)) {
            $data->whereHas('userActivity', function ($query) use ($filters) {
                $query->where([
                    'app' => 1,
                    'device' => $filters['mobile_app_device']
                ]);
            });
        }

        //filter dashboard_access
        if (isset($filters['dashboard_access'])) {
            $dashboardAccess = filter_var($filters['dashboard_access'], FILTER_VALIDATE_BOOLEAN);
            if ($dashboardAccess == true) {
                $data->where('gstatus', '=', GroupInfo::STATUS_ACTIVE)
                    ->whereHas('userActivity', function ($query) {
                        $query->where('web', '=', 1);
                    });
            } else {
                $data
                    ->where('gstatus', '=', GroupInfo::STATUS_ACTIVE)
                    ->whereDoesntHave('userActivity')
                    ->orWhereHas('userActivity', function ($query) {
                        $query->where('web', '=', 0);
                    });
            }
        }
    }

    public function paginatedFormattedList($limit, $filters = [])
    {
        $data = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($data);
    }

    public function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    protected function getTotalData($data)
    {
        [$values, $total] = [[], 0];
        foreach ($data as $d) {
            $values[strtolower($d->status)] = $d->totals ?: 0;
            $total += $d->totals;
        }
        return [
            'data' => $data,
            'values' => $values,
            'total' => $total,
        ];
    }

    protected function getAgentInfo($agent)
    {
        return [
            'agent_id' => $agent->agent_id,
            'code' => $agent->agent_code,
            'first_name' => $agent->agent_fname,
            'middle_name' => $agent->agent_mname,
            'last_name' => $agent->agent_lname,
            'full_name' => $agent->fullname,
            'email' => $agent->agent_email,
            'phone1' => $agent->agent_phone1,
            'is_email_valid' => $agent->is_email_valid,
            'is_phone1_valid' => $agent->is_phone1_valid
        ];
    }

    protected function singleFormattedItem($d)
    {
        $totals = isset($d->memberCount) ? $this->getTotalData($d->memberCount) : null;
        $group = isset($d->mainAgent) ? $this->getAgentInfo($d->mainAgent) : null;
        $benefitStoreUrl = config('app.benefit_store_url') . "group-home?group_id=" . base64_encode($d->gid);
        $mobileAppDevice = "";
        $dashboardAccess = false;
        if (isset($d->userActivity)) {
            $activity = $d->userActivity;
            $mobileAppDevice = $activity->app == 1 ? $activity->device : "N/A";
            $dashboardAccess = $activity->web == 1 ? true : false;
        }
        return [
            'gid' => $d->gid,
            'gcode' => $d->gcode,
            'gname' => $d->gname,
            'gtype' => $d->gtype,
            'gstatus' => $d->gstatus,
            'gein' => $d->gein ?: 'N/A',
            'gcontact_fname' => $d->gcontact_fname,
            'gcontact_lname' => $d->gcontact_lname,
            'gcc' => $d->gcc,
            'geft' => $d->geft,
            'glist' => $d->glist,
            'gstmt' => $d->gstmt,
            'gstate' => $d->gstate,
            'gcity' => $d->gcity,
            'gzip' => $d->gzip,
            'gphone' => $d->gphone,
            'gmobile' => $d->gmobile,
            'is_email_valid' => $d->is_email_valid,
            'is_mobile_valid' => $d->is_phone2_valid,
            'is_phone_valid' => $d->is_phone1_valid,
            'gemail' => $d->gemail ?: 'N/A',
            'gwebaccess' => $d->gwebaccess ?: 'N/A',
            'gwebaccess_dental' => $d->gwebaccess_dental ?: 'N/A',
            'termed_date' => date('m/d/Y',strtotime($d->termed_date)) ?: 'N/A',
            'waive_status' => $d->waive_status,
            'formatted_agent_status' => isset($d->gstatus) ? array_search($d->gstatus, GroupInfo::$statuses) : null,
            'gcontact_fullname' => $d->contact_fullname,
            'benefit_store_url' => $benefitStoreUrl,
            'formatted_timestamp' => Carbon::createFromTimestamp($d->gts)->format('m/d/Y'),
            'agent' => $group,
            'totals' => $totals,
            'website' => isset($d->infoQuick) ? $d->infoQuick->weburl : 'N/A',
            'mobile_app_device' => $mobileAppDevice,
            'dashboard_access' => $dashboardAccess,
            'note_icon_color' => $this->getLatestNoteColor($d->gid),
            'group_level' => $d->group_level,
            'group_guaranteed'=>$d->group_guaranteed,
            'group_lump_sum_pay_fl'=>$d->lump_sum_pay_fl,
            'bill_date'=>$d->bill_date,
        ];
    }

    protected function getFirstSignupDate()
    {
        return (int)GroupInfo::query()
                ->whereNotNull('gts')
                ->orderBy('gts', 'ASC')->first()->agent_signup_date
            ?? strtotime(Carbon::create('1990')->timestamp);
    }


    public function groupDetail($groupId)
    {
        $group = GroupInfo::find($groupId);
        if (!$group) return $this->failedResponse('Group not found');
        $data = $this->groupDetailData($group);
        return $this->successResponse('Success', $data);
    }

    protected function groupDetailData($group)
    {
        $basicInfo = $this->groupBasicInfo($group);
        $addressInfo = $this->groupAddressInfo($group);
        $billingInfo = $this->groupBillingInfo($group);
        $totals = isset($group->memberCount) ? $this->getTotalData($group->memberCount) : null;
        $mainAgent = isset($group->mainAgent) ? $this->getAgentInfo($group->mainAgent) : null;
        $detailInfo = $this->groupDetailInfo($group);
        $salesInfo = $group->type != 'employer' ? $this->groupSalesInfo($group) : null;
        $countNewMembers = $this->newGroupMember($group->gid) ?: 0;
        $banking = $this->groupBankInfo($group);
        return [
            'basicInfo' => $basicInfo,
            'detail' => $detailInfo,
            'address' => $addressInfo,
            'banking' => $banking,
            'billing' => $billingInfo,
            'mainAgent' => $mainAgent,
            'salesInfo' => $salesInfo,
            'count_new_members' => $countNewMembers,
            'total' => $totals
        ];
    }

    protected function groupBasicInfo($d)
    {
        $sso_exists = SsoUsers::where([['email',$d->gemail],['user_type','G']])->exists();
        $formattedGroupIndustry = null;
        if (isset($d->emp_industry)) {
            $groupIndustry = GroupIndustry::query()
                ->where(function ($subQuery) use ($d) {
                    $subQuery->orWhere('value', '=', $d->emp_industry);
                    $subQuery->orWhere('title', 'LIKE', '%' . $d->emp_industry . '%');
                })
                ->first();
            $formattedGroupIndustry = isset($groupIndustry) ? $groupIndustry->title : null;
        }
        return [
            'gid' => $d->gid,
            'type' => $d->gtype,
            'vip' => $d->vip,
            'code' => $d->gcode,
            'name' => $d->gname,
            'contact_first_name' => $d->gcontact_fname,
            'contact_last_name' => $d->gcontact_lname,
            'contact_full_name' => $d->contact_fullname,
            'bio' => $d->gweb_aboutus,
            'tagline' => GroupUser::where('gid',$d->gid)->pluck('tagline')->first(),
            'email' => $d->gemail,
            'phone' => $d->gphone,
            'mobile' => $d->gmobile,
            'fax' => $d->gfax,
            'email2' => $d->gemail2,
            'agent_id' => $d->gagent_code,
            'group_logo' => $d->logo_url,
            'cover_img' => $d->grp_cover_img != null ? self::addImageUrl($d->grp_cover_img):null,
            'signup' => Carbon::createFromTimestamp($d->gts)->format('m/d/Y'),
            'status' => $d->gstatus,
            'formatted_status' => isset($d->gstatus) ? array_search($d->gstatus, GroupInfo::$statuses) : null,
            'tax_id' => $d->gein ?: null,
            'group_industry' => $d->emp_industry ?: null,
            'formatted_group_industry' => $formattedGroupIndustry,
            'is_phone_valid' => $d->is_phone1_valid,
            'is_mobile_valid' => $d->is_phone2_valid,
            'is_email_valid' => $d->is_email_valid,
            'sso_exists' => $sso_exists,
            'group_level' => $d->group_level,
            'group_guaranteed' => $d->group_guaranteed,
            'group_contribution'=>$d->group_contribution
        ];
    }

    public function addImageUrl($imageName){
        $s3PreviousFileNamePath = "/group/logo/".$imageName;
        if (Storage::disk('s3-third')->exists($s3PreviousFileNamePath)) {
            return config('filesystems.disks.s3-third.url').$s3PreviousFileNamePath;
        }
    }

    protected function groupAddressInfo($d)
    {
        return [
            'address1' => $d->gaddress1,
            'address2' => $d->gaddress2,
            'city' => $d->gcity,
            'state' => $d->gstate,
            'zip' => $d->gzip,
        ];
    }

    protected function groupBillingInfo($d)
    {
        $primaryBillingAddress = $this->primaryBillingAddress($d->gid);
        $uspsValid = isset($primaryBillingAddress) ? ($primaryBillingAddress->is_usps_valid == 1 ? true : false) : false;
        return [
            'first_name' => $d->gbillcontact_fname ?: 'N/A',
            'last_name' => $d->gbillcontact_lname ?: 'N/A',
            'address1' => $d->gbilladdress1,
            'address2' => $d->gbilladdress2,
            'city' => $d->gbillcity,
            'state' => $d->gbillstate,
            'zip' => $d->gbillzip,
            'phone' => $d->gbillphone ?: 'N/A',
            'fax' => $d->gbillfax ?: 'N/A',
            'email' => $d->gbillemail ?: 'N/A',
            'usps_valid' => $uspsValid
        ];
    }

    protected function groupDetailInfo($d)
    {
        return [
            'full_name' => $d->contact_fullname,
            'web_display_name' => $d->gweb_display_name ?: 'N/A',
            'web_display_number' => $d->gweb_display_number ?: 'N/A',
            'web_display_email' => $d->gweb_display_email ?: 'N/A',
            'web_display_sa_fees' => $d->gweb_display_safees ?: 'N/A',
            'web_access' => $d->gwebaccess ?: 'N/A',
            'web_access_dental' => $d->gwebaccess_dental ?: 'N/A',
            'web_access_dental_name' => $d->gweb_display_dental_name ?: 'N/A',
            'web_access_dental_number' => $d->gweb_display_dental_number ?: 'N/A',
            'web_access_dental_email' => $d->gweb_display_dental_email ?: 'N/A',
            'web_access_dental_sa_fees' => $d->gweb_display_dental_safees ?: 'N/A',
            'waive_status' => $d->waive_status,
            'waive_association_status' => $d->is_association_fee_waived,
            'elite_blue' => $d->eliteblue,
            'elite_health_plan' => $d->elite_flag,
        ];
    }

    public function groupSalesInfo($d)
    {
        return [
            'email' => $d->sales_email,
            'phone1' => $d->sales_phone1 ?: 'N/A',
            'phone2' => $d->sales_phone2 ?: 'N/A',
            'fax' => $d->sales_fax ?: 'N/A',
            'toll_free' => $d->sales_800 ?: 'N/A',
        ];
    }

    protected function newGroupMember($gId)
    {
        $refts = strtotime("first day of this month midnight");
        return UserInfoPolicyAddress::query()
            ->where('eid', '=', $gId)
            ->where('edate', '>', $refts)
            ->count();
    }

    public function groupAgents($groupId, $limit = 10, $filters)
    {
        $group = GroupInfo::find($groupId);
        if (!$group) return $this->failedResponse('Group not found');
        $aids = $group->agents->pluck('agent_id')->toArray();
        $agents = AgentInfo::query()
            ->whereIn('agent_id', $aids)
            ->whereNotIn('agent_status', ['D'])
            ->paginate($limit)
            ->appends($filters);
        $agentRepo = new ManageAgents();
        try {
            $data = $agentRepo->formattedData($agents);
            return $this->successResponse('Success', $data);
        } catch (\Throwable $th) {
            return $this->failedResponse("Failed to fetch data.");
        }
    }

    public function groupEligibilityLogs($groupId)
    {
        $group = GroupInfo::find($groupId);
        if (!$group) return $this->failedResponse('Group not found');
        $data = GroupUpdate::query()
            ->where('group_id', '=', $groupId)
            ->orderBy('elgb_id', 'DESC')
            ->paginate(10);
        $result = [];
        foreach ($data as $d) {
            $result [] = [
                "elgb_id" => $d->elgb_id,
                "group_id" => $d->group_id,
                "elgb_act" => $d->elgb_act ?: "N/A",
                "elgb_act_date" => Carbon::createFromTimestamp($d->elgb_act_date)->format('m/d/Y'),
                "elgb_act_date_original" => $d->elgb_act_date,
                "elgb_comment" => $d->elgb_comment ?: "N/A",
                "elgb_agent" => $d->elgb_agent ?: "N/A",
                "elgb_agentname" => $d->elgb_agentname ?: "N/A",
            ];
        }
        $paginatedData = [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
        try {
            return $this->successResponse('Success', $paginatedData);
        } catch (\Throwable $th) {
            return $this->failedResponse("Failed to fetch data.");
        }
    }

    protected function getGroupPaymentMethod($d)
    {
        if ($d->geft == 1) {
            $paymentType = [
                'value' => 'eft',
                'title' => 'EFT List Bill'
            ];
        } elseif ($d->gstmt == 1) {
            $paymentType = [
                'value' => 'stmt',
                'title' => 'STMT'
            ];
        } elseif ($d->glist == 1) {
            $paymentType = [
                'value' => 'list',
                'title' => 'List Bill'
            ];
        } elseif ($d->gcc == 1) {
            $paymentType = [
                'value' => 'cc',
                'title' => 'Credit Card'
            ];
        } else {
            $paymentType = null;
        }
        return $paymentType;
    }

    protected function groupBankInfo($d)
    {
        $paymentType = $this->getGroupPaymentMethod($d);
        $groupEft = GroupEft::select('account_type','account_holder_type')->where('bank_gid', $d->gid)->first();
        $maskedAccount = "XXXX" . substr($d->enaachacct, -4);
        $bankingInfo = null;
        if (isset($paymentType) && array_key_exists('value', $paymentType) && $paymentType['value'] == 'eft') {
            $bankingInfo = [
                'ach_bank_account_name' => $d->enaachbank ?: 'N/A',
                'ach_bank_name' => $d->enaachname ?: 'N/A',
                'ach_bank_account' => $d->enaachacct ?: 'N/A',
                'masked_ach_bank_account' => $d->enaachacct ? $maskedAccount : 'N/A',
                'ach_bank_routing' => $d->enaachrout ?: 'N/A',
            ];
        }
        if($groupEft) {
            $bankingInfo['ach_account_type'] = ucfirst($groupEft->account_type) ?: 'N/A';
            $bankingInfo['ach_account_holder_type'] = ucfirst($groupEft->account_holder_type) ?: 'N/A';
        } else {
            $bankingInfo['ach_account_type'] = 'N/A';
            $bankingInfo['ach_account_holder_type'] = 'N/A';
        }
        return [
            'paymentType' => $paymentType,
            'info' => $bankingInfo
        ];
    }

    public function editGroupLogo($request)
    {

        $groupInfo = GroupInfo::find($request['group_id']);
        $logo = $request['group_logo'];
        DB::beginTransaction();
        try {
            $logoName = $this->uploadGroupLogo($logo, $groupInfo->group_logo);
            if (isset($logoName)) {
                $groupInfo->update([
                    'group_logo' => $logoName
                ]);
            }
            $successMessage = "Group logo uploaded successfully.";
            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_IMAGE, $successMessage);
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    protected function uploadGroupLogo($image, $previousFileName = "")
    {
        $imageName = '';
        if ($image instanceof UploadedFile) {
            $fileName = pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME);
            $ext = $image->getClientOriginalExtension();
            $time = time();
            $imageName = "{$fileName}-{$time}.{$ext}";
            $imageName = preg_replace('/\s+/', '_', $imageName);
            //checking and deleting previous file.
            if ($previousFileName) {
                $this->removeGroupLogo($previousFileName);
            }
            //uploading new file
            $s3FilePath = "/group/logo/{$imageName}";
            Storage::disk('corenroll')->put('/img/abiz_logo/' . $imageName, file_get_contents($image));
            Storage::disk('s3-third')->put($s3FilePath, file_get_contents($image));
        }
        return $imageName;
    }

    protected function removeGroupLogo($imageName)
    {
        $previousFileNamePath = "/img/abiz_logo/{$imageName}";
        if (Storage::disk('corenroll')->exists($previousFileNamePath)) {
            Storage::disk('corenroll')->delete($previousFileNamePath);
        }
        $s3PreviousFileNamePath = "/group/logo/{$imageName}";
        if (Storage::disk('s3-third')->exists($s3PreviousFileNamePath)) {
            Storage::disk('s3-third')->delete($s3PreviousFileNamePath);
        }
    }

    protected function createGroupEligibilityLogs($request, $act = "", $comment = "")
    {
        $data = [
            "group_id" => $request['group_id'],
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent" => $request['loginUserId'] ?? request()->header('id'),
            "elgb_agentname" => $request['loginUserName'] ?? request()->header('name'),
        ];
        GroupUpdate::create($data);
    }

    public function getGroupAgents($agentId, $filters = [])
    {
        $groupInfo = GroupInfo::find($agentId);
        if (!$groupInfo) return $this->failedResponse("No Group Found");
        $query = AgentInfo::query()
            ->where('agent_id', '!=', $groupInfo->gagent_code)
            ->where('agent_status', '=', AgentInfo::STATUS_APPROVED);
        if (isset($filters['query'])) {
            $query->where(function ($subQuery) use ($filters) {
                $subQuery->orWhere('agent_code', 'LIKE', '%' . $filters['query'] . '%');
                $subQuery->orWhere('agent_fname', 'LIKE', '%' . $filters['query'] . '%');
                $subQuery->orWhere('agent_lname', 'LIKE', '%' . $filters['query'] . '%');
            });
        }
        $agents = $query->get()->take(50);
        $data = [];
        foreach ($agents as $d) {
            $data[] = [
                'agent_id' => $d->agent_id,
                'agent_code' => $d->agent_code,
                'agent_fullname' => $d->fullname,
                'formatted_name' => "{$d->agent_code} - {$d->fullname}"
            ];
        }
        return $this->successResponse('Success', $data);
    }

    public function updateGroupAgent($request)
    {
        $groupId = $request['group_id'];
        $groupInfo = GroupInfo::find($groupId);
        $mainAgent = $groupInfo->mainAgent;
        if ($mainAgent->agent_id == $request['gagent_code']) {
            return $this->failedResponse('This main agent already exist.', 409);
        }
        DB::beginTransaction();
        try {
            $this->updateMainAgent($request, $groupInfo);
            $successMessage = "Main Agent updated successfully.";
            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_GROUP_AGENT, $successMessage);

//            //send email
            $this->sendUpdatedGroupAgentEmail($groupInfo);

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to update main agent.');
        }
    }

    protected function updateMainAgent($request, GroupInfo $groupInfo)
    {
        $groupInfo->update([
            'gagent_code' => $request['gagent_code']
        ]);
    }


    protected function sendUpdatedGroupAgentEmail($groupInfo)
    {
        $newGroupAgent = $groupInfo->mainAgent;
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $contentData = "<h2>Hello {$groupInfo->contact_fullname}</h2>";
        $contentData .= "<p>As requested , your main agent change is complete.</p>";
        $contentData .= "<p>";
        $contentData .= "Please contact {$newGroupAgent->fullname} ({$newGroupAgent->agent_email})";
        $contentData .= " for any questions and have a training session to understand our products and services better.";
        $contentData .= "</p>";
        $contentData .= "<p>We look forward to helping you build a strong business.</p>";
        $contentData .= "Best Wishes,<br/>";
        $contentData .= "Elevate Wellness Association";
        $emailConfigurationName = "GROUP_INFO_CHANGE";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Group Main Agent Change",
            'contentTemplate' => $contentData,
        ];
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentTemplate($emailData);
    }

    public function updateWebDisplay($request)
    {
        $groupId = $request['group_id'];
        $groupInfo = GroupInfo::find($groupId);
        DB::beginTransaction();
        try {
            $webData = $this->updatedGroupWebDisplay($request);
            $groupInfo->update($webData);

            $groupUser = GroupUser::where('gid',$request['group_id'])->get();
            $groupUserUpdated = false;
            foreach ($groupUser as $eachUser) {
                $tagline = isset($request['tagline']) ?  $request['tagline'] : '';
                $eachUser->update(['tagline' => $tagline]);
                $groupUserUpdated = $eachUser->wasChanged();
            }

            if ($groupInfo->wasChanged() || $groupUserUpdated) {
                $successMessage = "Group web display details updated successfully.";
                //update to group_updates
                $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_WEB_DISPLAY, $successMessage);

                //send email
                $this->sendUpdateWebDisplayEmail($groupInfo);
            } else {
                $successMessage = "Provided Web Details same as existing. No changes made.";
            }

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to update group web display details.');
        }
    }

    public function updatedGroupWebDisplay($requestData)
    {
        $data = [
            "gweb_display_name" => $requestData['web_display_name'],
            "gweb_display_number" => $requestData['web_display_number'],
            "gweb_display_email" => $requestData['web_display_email'],
            "gweb_display_safees" => isset($requestData['web_display_sa_fees']) ? $this->convertSaFee($requestData['web_display_sa_fees']) : null,
            "gwebaccess" => $requestData['web_access'],
            "gweb_aboutus" => isset($requestData['bio']) ? $requestData['bio'] : '',
        ];
        return $data;

    }

    protected function sendUpdateWebDisplayEmail(GroupInfo $groupInfo)
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your web display details data has been updated  on " . $date . ".";
        $phone = $groupInfo->gweb_display_number ? CommonHelper::format_phone($groupInfo->gweb_display_number) : '--';
        $displayInfo = "";
        $displayInfo .= "Name : {$groupInfo->gweb_display_name}<br/>";
        $displayInfo .= "Email : {$groupInfo->gweb_display_email}<br/>";
        $displayInfo .= "Phone : {$phone}<br/>";
        $contentData = [
            'Group Id' => $groupInfo->gid,
            'Web Display Info' => $displayInfo,
            'Web Display Sa Fees' => $groupInfo->gweb_display_safees,
            'Web Access' => $groupInfo->gwebaccess,
            'Web Dental' => $groupInfo->gwebaccess_dental,
        ];
        $emailConfigurationName = "GROUP_INFO_CHANGE";

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Web Display",
            'message' => $message,
            'data' => $contentData,
            'dataName' => $groupInfo->contact_fullname
        ];
        \Log::channel('root_group')->info("Changing email if sub group while updating web display");
        $emailData = $this->processEmailAddresses($emailData, $groupInfo->gid);
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    protected function convertSaFee($fee)
    {
        return number_format((float)$fee, 2, '.', '');
    }

    public function getGroupIndustries()
    {
        $data = GroupIndustry::where('status', true)->orderBy('title')->get(['value', 'title'])->toArray();
        return $this->successResponse('Group Industries', $data);
    }

    protected function failedValidation($errors)
    {
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }

    public function updateGroupBasicDetail($request)
    {
        if(!$request['phone'] && !isset($request['mobile'])){
            return $this->failedResponse("Enter at least one contact number (Mobile or Phone).");
        }
        if(!isset($request['mobile'])){
            $request['mobile'] = null;
        }
        $groupId = $request['group_id'];
        $groupInfo = GroupInfo::find($groupId);
        $oldEmail = $groupInfo->gemail;
        $requestEmail = $request['email'];
        $requestMobile = $request['mobile'];

        $checkGroupEmail = $this->checkGroupUserEmail($requestEmail, $groupInfo->gid);

        //check email in group user
        if ($checkGroupEmail) {
            return $this->failedValidation(['email' => 'Email already exists.']);
        }

        //check phone in group user
        if($requestMobile){
            $checkGroupPhone = $this->checkGroupUserPhone($requestMobile, $groupInfo->gid);
            if ($checkGroupPhone) {
                return $this->failedValidation(['phone' => 'Phone already exists.']);
            }
        }

        try {
            DB::beginTransaction();
            //update sso_users
            $updatedSso = true;
            if ($groupInfo->gtype == 'employer') {
                $updatedSso = $this->updateInSsoUser($request, $oldEmail);
            }
            if ($updatedSso == true) {
                $updatedGroupInfo = $this->updateGroupDetail($request, $groupInfo);
                $successMessage = "Group details updated successfully.";

                //cover and logo image
                if(isset($request['logo'])){
                    $groupInfo = GroupInfo::find($request['group_id']);
                    $logoName = $this->uploadGroupLogo($request['logo'], $groupInfo->group_logo);
                    if (isset($logoName)) {
                        $groupInfo->where('gid',$request['group_id'])->update([
                            'group_logo' => $logoName
                        ]);
                    }
                    $successMessage = "Group logo uploaded successfully.";
                    $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_IMAGE, $successMessage);
                }

                if(isset($request['cover_img'])){
                    $groupInfo = GroupInfo::find($request['group_id']);
                    $coverImg = $this->uploadGroupLogo($request['cover_img'], $groupInfo->grp_cover_img);
                    if (isset($coverImg)) {
                        $groupInfo->where('gid',$request['group_id'])->update([
                            'grp_cover_img' => $coverImg
                        ]);
                    }
                    $successMessage = "Group cover image uploaded successfully.";
                    $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_IMAGE, $successMessage);
                }

                //update group users

                $this->updateInGroupUser($updatedGroupInfo);

                //update to group_updates
                $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_INFO, $successMessage);

//            //send email
                $this->sendUpdateBasicDetailEmail($updatedGroupInfo);
                DB::commit();
                return $this->successResponse($successMessage);
            } else {
                DB::rollBack();
                if(isset($updatedSso['message'])){
                    return $this->failedResponse($updatedSso['message']);
                }
                return $this->failedResponse('Failed to update group details.');
            }

        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    protected function updateGroupDetail($requestData, GroupInfo $groupInfo)
    {
        $customValidationService = new CustomValidationService();
        $data = [
            "gcontact_fname" => $requestData['contact_first_name'],
            "gcontact_lname" => $requestData['contact_last_name'],
            "gphone" => $requestData['phone'],
            "gemail" => $requestData['email'],
            "gein" => $requestData['tax_id'],
            "emp_industry" => $requestData['industry'] ?: null,
            "is_email_phone" => 'VALID',
        ];

        $data["gmobile"] = $requestData['mobile'];
        $data["is_phone1_valid"] = $requestData['phone'] ? 'VALID' : false;
        $data["phone1_line_type"] = $requestData['phone'] ? $customValidationService->validatePhoneNumVerify( $requestData['phone'], true)['data']['line_type'] : null;
        $data["gphone"] = $requestData['phone'];
        $data["is_phone2_valid"] = $requestData['mobile'] ? 'VALID' : false;
        $data["phone2_line_type"] = $requestData['mobile'] ? 'mobile' : null;
        $groupInfo->update($data);
        return $groupInfo;
    }

    protected function sendUpdateBasicDetailEmail(GroupInfo $groupInfo)
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your data has been updated  on " . $date . ".";
        $formattedGroupIndustry = "";
        if (isset($groupInfo->emp_industry)) {
            $formattedGroupIndustry = GroupIndustry::query()->where('value', '=', $groupInfo->emp_industry)->first()->title;
        }
        $contentData = [
            'Group Id' => $groupInfo->gid,
            'Contact Name' => $groupInfo->contact_fullname,
            'Email' => $groupInfo->gemail,
            'Phone' => CommonHelper::format_phone($groupInfo->gphone),
            'Industry' => $formattedGroupIndustry,
            'Tax Id' => 'xxxx' . substr($groupInfo->gein, -4),
        ];
        $emailConfigurationName = "GROUP_INFO_CHANGE";

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Basic Detail",
            'message' => $message,
            'data' => $contentData,
            'dataName' => $groupInfo->contact_fullname
        ];
        \Log::channel('root_group')->info("Updating email when updating basic detail");
        $emailData = $this->processEmailAddresses($emailData, $groupInfo->gid);
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    public function updateBillingDetail($request)
    {
        $groupId = $request['group_id'];
        $groupInfo = GroupInfo::find($groupId);
        DB::beginTransaction();
        try {
            $updatedGroupInfo = $this->updateGroupBillingDetail($request, $groupInfo);
            $successMessage = "Group billing details updated successfully.";

            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::BILLING_ACT_INFO, $successMessage);
//            //send email
            $this->sendUpdateBillingDetailEmail($updatedGroupInfo);

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to update group billing details.');
        }
    }

    protected function updateGroupBillingDetail($requestData, GroupInfo $groupInfo)
    {
        $data = [
            "gbillcontact_fname" => $requestData['contact_first_name'],
            "gbillcontact_lname" => $requestData['contact_last_name'],
            "gbillphone" => $requestData['phone'],
            "gbillemail" => $requestData['email'],
            "gbillfax" => $requestData['fax'],
        ];
        $groupInfo->update($data);
        return $groupInfo;
    }

    protected function primaryBillingAddress($gid)
    {
        return GroupBillingAddress::query()
            ->where([
                'gid' => $gid,
                'is_primary' => 1
            ])
            ->first();
    }

    protected function sendUpdateBillingDetailEmail($groupInfo)
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your billing details  has been updated  on " . $date . ".";
        $contentData = [
            'Group Id' => $groupInfo->gid,
            'Contact Info' => $groupInfo->gbillcontact_fname . " " . $groupInfo->gbillcontact_lname,
            'Email' => $groupInfo->gbillemail,
            'Phone' => CommonHelper::format_phone($groupInfo->gbillphone),
            'Fax' => $groupInfo->gbillfax,
        ];
        $emailConfigurationName = "GROUP_INFO_CHANGE";

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Billing Detail Display",
            'message' => $message,
            'data' => $contentData,
            'dataName' => $groupInfo->contact_fullname
        ];
        \Log::channel('root_group')->info("Changing email if sub group while updating billing detail");
        $emailData = $this->processEmailAddresses($emailData, $groupInfo->gid);
         $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    protected function setGroupEftData($request)
    {
        return [
            "bank_gid" => $request["group_id"],
            "bank_name" => $request['bank_name'],
            "bank_routing" => $request['bank_routing'],
            "bank_account" => DecryptEcryptHelper::encryptInfo($request['bank_account']),
            "bank_account4" => substr($request['bank_account'], -4),
            "bank_accountname" => $request['bank_accountname'],
            "is_primary" => 1,
            "account_type" => $request['account_type'],
            "account_holder_type" => $request['account_holder_type'],
            "bank_date" => date('Y-m-d')
        ];
    }

    public function checkEftBanks($groupId)
    {
        return GroupEft::query()
            ->where('bank_gid', '=', $groupId)
            ->exists();

    }

    public function updateEftWithBankDetail($request)
    {
        $groupId = $request['group_id'];
        $groupInfo = GroupInfo::find($groupId);
        $checkEftBanks = $this->checkEftBanks($groupId);
        $currentPaymentMethod = $this->getGroupPaymentMethod($groupInfo)['value'];
        if ($checkEftBanks) return $this->failedResponse('Eft banks already exists.', 409);
        DB::beginTransaction();
        try {
            $groupInfo->update([
                'geft' => 1,
                'glist' => 0,
                'gstmt' => 0,
                'gcc' => 0,
                "enaachrout" => $request['bank_routing'],
                "enaachname" => $request['bank_name'],
                "enaachbank" => $request['bank_accountname'],
                "enaachacct" => $request['bank_account'],
            ]);

            //create group eft data
            $groupEft = new GroupEft();
            $groupEftData = $this->setGroupEftData($request);
            $groupEft->create($groupEftData);
            $successMessage = "Payment method updated and eft bank detail created successfully.";

            if ($groupInfo->gtype == 'employer') {
                $policyComment = $currentPaymentMethod == 'eft'
                    ? "Payment method updated to eft through group."
                    : "Payment method updated form {$currentPaymentMethod} to eft through group.";
                //updating in policies
                $this->updateMemberPaymentMethod($groupId, $request, $policyComment);
            }

            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::BILLING_ACT_INFO, $successMessage);

//            //send email
            $this->sendEftUpdateWithNewBankEmail($groupInfo);

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
//            return $this->failedResponse($th->getMessage());
            return $this->failedResponse("Failed to update payment method and create eft bank detail.");
        }
    }

    protected function sendEftUpdateWithNewBankEmail($groupInfo)
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your payment method and bank detail has been updated  on " . $date . ".";
        $bankDetail = "Account Name : {$groupInfo->enaachbank}<br/>";
        $bankDetail .= "Bank Name : {$groupInfo->enaachname}<br/>";
        $bankDetail .= "Routing Number : {$groupInfo->enaachrout}<br/>";
        $bankDetail .= "Account Number : {$groupInfo->enaachacct}";

        $contentData = [
            'Group Id' => $groupInfo->gid,
            'Payment Method' => 'eft',
            'Bank Detail' => $bankDetail
        ];


        $emailConfigurationName = "GROUP_INFO_CHANGE";
        $subject = 'Banking Detail';
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
            'dataName' => $groupInfo->contact_fullname
        ];
        \Log::channel('root_group')->info("Changing email if sub group while updating eft");
        $emailData = $this->processEmailAddresses($emailData, $groupInfo->gid);
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    public function updatePaymentMethod($request)
    {
        Log::channel('payment_change')->info('Payment group change started', ['group_id' => $request['group_id']]);
        $groupId = $request['group_id'];
        $groupInfo = GroupInfo::find($groupId);
        $paymentMethod = $request['payment_method'];
        $currentPaymentMethod = $this->getGroupPaymentMethod($groupInfo)['value'];
        if ($paymentMethod == $currentPaymentMethod) return $this->failedResponse(ucwords($paymentMethod) . " already exists.", 409);
        DB::beginTransaction();
        try {
            $groupInfo->update([
                'geft' => $paymentMethod == 'eft' ? 1 : 0,
                'glist' => $paymentMethod == 'list' ? 1 : 0,
                'gstmt' => $paymentMethod == 'stmt' ? 1 : 0,
                'gcc' => $paymentMethod == 'cc' ? 1 : 0,
            ]);
            $successMessage = "Payment method updated from {$currentPaymentMethod} to {$paymentMethod} successfully.";

            if ($groupInfo->gtype == 'employer') {
                $policyComment = "Payment method updated form {$currentPaymentMethod} to {$paymentMethod} through group.";
                $this->updateMemberPaymentMethod($groupId, $request, $policyComment);
            }
            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_INFO, $successMessage);

//            //send email
            $this->sendUpdatePaymentMethodEmail($groupInfo, $request['payment_method']);

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            Log::channel('payment_change')->info('Error while payment type change', ['message' => $th->getMessage()]);
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
//            return $this->failedResponse("Failed to update payment method.");
        }
    }

    protected function updateMemberPaymentMethod($groupId, $request, $comment)
    {
        $members = Policy::query()
            ->where('eid', '=', $groupId)
            ->whereIn('status', [Policy::STATUS_ACTIVE, Policy::STATUS_TERMED])
            ->get();
        foreach ($members as $m) {
            if (
                $m->status == Policy::STATUS_ACTIVE
                ||
                ($m->status == Policy::STATUS_TERMED and $m->term_date > date('Y-m-d'))
            ) {
                $m->update([
                    'payment_type' => $request['payment_method'] == 'eft' ? 'elist' : 'list',
                    'recurring_payment_type' => $request['payment_method'] == 'eft' ? 'elist' : 'list'
                ]);
                Log::channel('payment_change')->info('members policy id for payment change', ['poliy_id' => $m->policy_id,'changed payment type' => $m->payment_type]);
                $loginUser = isset($request['loginUserId']) ? $request['loginUserId'] : "auto";
                $this->writePolicyLog($m->policy_id, 'BIC', $comment, $loginUser);
            }
        }
    }

    protected function writePolicyLog($policyId, $action, $comment, $loginUserId = 'auto')
    {
        $update = [
            'elgb_policyid' => $policyId,
            'elgb_act' => $action,
            'elgb_act_date' => time(),
            'elgb_comment' => $comment,
            'elgb_agent' => $loginUserId,
            'elgb_file_date' => date('Y-m-d')
        ];
        $policyUpdateLog = new PolicyUpdateLog();
        $policyUpdateLog->create($update);
        return $policyUpdateLog;
    }

    protected function sendUpdatePaymentMethodEmail($groupInfo, $paymentType = 'eft')
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your payment method  has been updated  on " . $date . ".";
        $contentData = [
            'Group Id' => $groupInfo->gid,
            'Payment Method' => $paymentType,
        ];
        $emailConfigurationName = "GROUP_INFO_CHANGE";
        $subject = 'Payment Method';
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
            'dataName' => $groupInfo->contact_fullname

        ];
        \Log::channel('root_group')->info("Changing email if sub group while updating payment method");
        $emailData = $this->processEmailAddresses($emailData, $groupInfo->gid);
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }


    protected function updateInGroupUser($groupInfo)
    {
        /**
         * @var $groupUser GroupUser
         */
        $groupUser = $groupInfo->groupUser;

        if (!$groupUser) {
            $groupUser = GroupUser::query()
                ->where('gid', '=', $groupInfo->gid)
                ->orderBy('gid', 'DESC')
                ->first();
        }
        if (isset($groupUser)) {
            $groupUser->update([
                'groupuser_fname' => $groupInfo->gcontact_fname,
                'groupuser_lname' => $groupInfo->gcontact_lname,
                'groupuser_email' => $groupInfo->gemail,
                'groupuser_username' => $groupInfo->gemail,
                'phone' => $groupInfo->gmobile,
                'is_email_valid' => $groupInfo->is_email_valid
            ]);
        }
    }


    protected function updateInSsoUser($request, $oldEmail)
    {
        $url = config('corenroll.corenroll_api_url') . 'api/v1/update-group-sync';
        $requestData = [
            'name' => $request['contact_first_name'] . " " . $request['contact_last_name'],
            'email' => $request['email'],
            'group_id' => $request['group_id'],
            'old_email' => $oldEmail
        ];
        if($request['mobile']){
            $requestData['phone'] = $request['mobile'];
        }
        $responseJson = GuzzleHelper::postApi($url, [], $requestData);
        $response = json_decode($responseJson, true);
        if ($response['status'] == 'success') {
            return true;
        } else {
            return $response;
        }
    }

    protected function checkGroupUserEmail($email, $gId)
    {
        return GroupUser::query()
            ->where('groupuser_email', '=', $email)
            ->where('gid', '!=', $gId)
            ->exists();
    }


    protected function checkGroupUserPhone($phone, $gId)
    {
        return GroupUser::query()
            ->where('phone', '=', $phone)
            ->where('gid', '!=', $gId)
            ->exists();
    }

    public function suspendAccount($request)
    {
        $groupInfo = GroupInfo::find($request['group_id']);
        $status = 0;
        if ($groupInfo->gstatus == 0) return $this->failedResponse('Failed to suspend account.', 409);
        DB::beginTransaction();
        try {
            /**
             * @var $groupInfo GroupInfo
             */
            $groupInfo->update([
                'gstatus' => $status
            ]);

            //update in GroupUser
            $this->updateStatusInGroupUser($groupInfo, $status);

            if ($groupInfo->gstatus == GroupInfo::STATUS_INACTIVE and $groupInfo->gtype == 'employer') {
                //update in Sso
                $this->updateStatusInSsoUser($groupInfo->gid);
            }

            $successMessage = "Group account suspended.";
            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_INFO, $successMessage);
            //send email
            $this->sendSuspendAccountEmail($groupInfo);
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to suspend account.');
        }
    }

    protected function updateStatusInGroupUser($groupInfo, $status)
    {
        /**
         * @var $groupUser GroupUser
         */
        $groupUser = $groupInfo->groupUser;

        if (!$groupUser) {
            $groupUser = GroupUser::query()
                ->where('gid', '=', $groupInfo->gid)
                ->orderBy('gid', 'DESC')
                ->first();
        }
        if (isset($groupUser)) {
            $groupUser->update([
                'groupuser_status' => $status,
            ]);
        }
    }

    protected function updateStatusInSsoUser($groupId)
    {
        $url = config('corenroll.corenroll_api_url') . "api/v1/remove-group-sso/{$groupId}";
        $requestData = [];
        $responseJson = GuzzleHelper::postApi($url, [], $requestData);
        $response = json_decode($responseJson, true);
        if ($response['status'] == 'success') {
            return true;
        } else {
            return false;
        }
    }

    protected function sendSuspendAccountEmail($groupInfo)
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $contentData = "<h2>Hello {$groupInfo->contact_fullname},</h2>";
        $contentData .= "<strong>Your account has been suspended.</strong>";
        $contentData .= "<p>";
        $contentData .= "Please contact Support at 888-243-4011.";
        $contentData .= "</p>";
        $emailConfigurationName = "GROUP_INFO_CHANGE";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Group Account Suspended.",
            'contentTemplate' => $contentData,
        ];
        \Log::channel('root_group')->info("Changing email if sub group while suspend account");
        $emailData = $this->processEmailAddresses($emailData, $groupInfo->gid);
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentTemplate($emailData);
    }

    public function deleteGroup($request)
    {
        $groupInfo = GroupInfo::query()
            ->where('gid', '=', $request['group_id'])
            ->where(function ($subQuery) {
                $subQuery->orWhere('gname', 'LIKE', '%test%');
                $subQuery->orWhere('gcontact_fname', 'LIKE', '%test%');
                $subQuery->orWhere('gcontact_lname', 'LIKE', '%test%');
            })->first();

        //temp check test group
        if (!isset($groupInfo)) {
            return $this->failedResponse('Only test group account can be deleted.');
        }
        $email = $groupInfo->gemail;
        $fullName = $groupInfo->contact_fullname;
        $id = $request['group_id'];
        DB::beginTransaction();
        try {
            //remove assoc fee
             $this->deleteCollection($groupInfo->gidAssocFees);

            //remove plans
            $this->deleteCollection($groupInfo->groupPlans);

            $this->deleteCollection($groupInfo->eftBanks);

            //delete general addresses
            $this->deleteCollection($groupInfo->generalAddress);

            //delete billing addresses
            $this->deleteCollection($groupInfo->billingAddress);

            //delete ach banks
            $this->deleteCollection($groupInfo->eftBanks);

            //remove group image from corenroll
            if ($groupInfo->group_logo) {
                 $this->removeGroupLogo($groupInfo->group_logo);
            }

            //remove info quick
             if (isset($groupInfo->infoQuick)) {
                 $groupInfo->infoQuick->delete();
            }
            //delete agent

            $this->deleteGroupUser($groupInfo);

            $this->updateStatusInSsoUser($groupInfo->gid);

            $groupInfo->delete();

            $successMessage = "Group account deleted.";
            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_INFO, $successMessage);
            //send email
            $this->sendGroupDeleteEmail($email, $fullName,$id);

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to delete group.');
        }
    }

    protected function deleteCollection($collection)
    {
        if (isset($collection)) {
            foreach ($collection as $c) {
                $c->delete();
            }
        }
    }

    protected function sendGroupDeleteEmail($email, $fullName, $groupId)
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $email;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $email;
        $contentData = "<h2>Hello {$fullName},</h2>";
        $contentData .= "<strong>Your account has been deleted.</strong>";
        $contentData .= "<p>";
        $contentData .= "Please contact Support at 888-243-4011.";
        $contentData .= "</p>";
        $emailConfigurationName = "GROUP_INFO_CHANGE";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Group Account Deleted.",
            'contentTemplate' => $contentData,
        ];
        \Log::channel('root_group')->info("Changing email if sub group while group delete");
        $emailData = $this->processEmailAddresses($emailData, $groupId);
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentTemplate($emailData);
    }

    protected function getGroupActivityQuery($groupId)
    {
        return UserActivityDetail::query()
            ->where([
                'user_id' => $groupId,
                'user_type' => UserActivityDetail::USER_TYPE_GROUP
            ]);
    }

    public function getGroupActivityDetail($groupId)
    {
        $group = GroupInfo::find($groupId);
        if (!$group) return $this->failedResponse('No Group found.');
        $data = $this->getGroupActivityQuery($groupId)->first();
        if (!$data) return $this->failedResponse('No Group Activity found.');
        $data['formatted_act_time'] = Carbon::parse($data->act_time)->format('m/d/Y H:i:s');
        return $this->successResponse('Success', $data);
    }

    /**
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * fetch group updates from group service.
     * formatted to name-value in response.
     */
    public function getGroupTypes()
    {
        try {
            $groupService = new GroupService();
            $groupTypes = $groupService->getGroupTypes();
            $data = [];
            foreach ($groupTypes as $g) {
                $data[] = [
                    'name' => ucfirst($g),
                    'value' => $g
                ];
            }
            return $this->successResponse('Group types fetched.', $data);
        } catch (\Exception $exception) {
            return $this->failedResponse('Failed to fetch group types.');
        }
    }

    /**
     * @param $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * Update group type.
     * updating gtype in group_info, update in group_updates and sending email.
     */
    public function updateGroupType($request)
    {
        $type = $request['group_type'];
        $group_contribution=$request['group_contribution'];
        /**
         * @var $groupInfo GroupInfo
         */
        $groupInfo = GroupInfo::find($request['group_id']);
        if ($type == $groupInfo->gtype  && $groupInfo->gtype==!'employer') return $this->failedResponse(ucfirst($type) . " type already exist.", 409);
        DB::beginTransaction();
        try {
            //updating in group info
            $groupInfo->update([
                'gtype' => $type,
                'group_contribution' => $group_contribution,
                'geft' => $type === 'employer' && $group_contribution == '1' ? 1 : 0
            ]);

            $updatedType = ucfirst($groupInfo->gtype);
            $successMessage = "Group type updated to {$updatedType}.";
            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_INFO, $successMessage);

            //send email
            $groupService = new GroupService();
            $groupService->sendGroupTypeUpdateEmail($groupInfo);
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to update group type.');
        }
    }

    public function addGroupFile($request){
        try {
            $doc = new Docs();

            $doc->docGID = $request['group_id'];
            $doc->docdate = Carbon::now()->timestamp;
            $doc->docnotes = $request['note'] ?? null;
            $doc->doctype = $request['type'];

            if ($request->hasFile('attachment')) {
                if ($request->file('attachment')->getSize() > 4200000) {
                    return response()->json(
                        new ErrorResource([
                            'statusCode' => 400,
                            'message' => 'File too large, max allowed is 4 MB.',
                        ]),
                        400
                    );
                }
                $fileNameParts = explode('.', $request->file('attachment')->getClientOriginalName());
                $ext = strtolower(end($fileNameParts));
                if(!in_array($ext,self::mimes())){
                    return response()->json(
                        new ErrorResource([
                            'statusCode' => 400,
                            'message' => 'File extension is invalid, it must be pdf, doc, docx, xls, xlsx, csv.',
                        ]),
                        400
                    );
                }
                $file = $request->file('attachment');
                $fileName = date('Y_m_d_H_i_s_') . $file->getClientOriginalName();;
                $fileName = str_replace(" ","_",$fileName);
                $doc->docfilename = $fileName;
                if(!Storage::disk('s3-third')->exists('group/files')){
                    Storage::disk('s3-third')->makeDirectory('group/files');
                }
                Storage::disk('s3-third')->put('group/files/' . $fileName , file_get_contents($file));
                Storage::disk('corenroll')->put('/__files__/docs/' . $fileName, file_get_contents($file));
            }

            $doc->save();
            $message = "File added successfully.";
            $comm = 'Group File Added.';
            $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_GROUP_FILE, $comm);
            return response()->json(
                new SuccessResource([
                    'statusCode' => 200,
                    'message' => $message,
                ]),
                200
            );
        } catch (Exception $e) {
            return response()->json(
                new ErrorResource([
                    'statusCode' => 400,
                    'message' => 'Failed adding file.',
                    'reason' => $e->getMessage(),
                ]),
                400
            );
        }
    }

    public function deleteGroupFile($id)
    {
        try {
            $findGroupFile = Docs::find($id);
            if ($findGroupFile instanceof Docs) {
                if(Storage::disk('s3-third')->exists('group/files/' . $findGroupFile->docfilename)) {
                    Storage::disk('s3-third')->delete('group/files/' . $findGroupFile->docfilename);
                }
                if(Storage::disk('corenroll')->exists('/__files__/docs/' . $findGroupFile->docfilename)) {
                    Storage::disk('corenroll')->delete('/__files__/docs/' . $findGroupFile->docfilename);
                }

                $this->createGroupEligibilityLogs(
                    $request = ['group_id' => $findGroupFile->docGID],
                    GroupUpdate::ACT_GROUP_FILE_DELETE,
                    $comment = 'Group File Removed Successfully.');
                Docs::where('docID', $id)->delete();
                return response()->json(
                    new SuccessResource([
                        'statusCode' => 200,
                        'message' => $comment,
                    ]),
                    200
                );
            } else {
                return response()->json(
                    new SuccessResource([
                        'statusCode' => 200,
                        'message' => 'Group file not found.',
                    ]),
                    200
                );
            }
        } catch (Exception $e) {
            return response()->json(
                new ErrorResource([
                    'statusCode' => 400,
                    'message' => 'Failed removing agent notes.',
                    'reason' => $e->getMessage(),
                ]),
                400
            );
        }
    }

    public function getGroupFiles($request){
        $groupFile = [];
        $groupFiles = Docs::query()->where('docGID',$request->group_id)->orderBy('docdate', 'desc')->paginate(10);
        foreach($groupFiles as $key => $gpFiles){
            $groupFile[$key]['group_file_id'] = $gpFiles['docID'];
            $groupFile[$key]['group_id'] = $gpFiles['docGID'];
            $groupFile[$key]['name'] = $gpFiles['docfilename'];
            $groupFile[$key]['type'] = $gpFiles['doctype'];
            $groupFile[$key]['note'] = $gpFiles['docnotes'];
            $groupFile[$key]['upload_date'] = date('m/d/Y',$gpFiles['docdate']);
            $groupFile[$key]['attachment'] = $gpFiles['docfilename'] ? Docs::DOC_LINK  . $gpFiles['docfilename'] : '';
        }

        if(count($groupFile)>0){
            $paginatedData = [
                'data' => $groupFile,
                'links' => $this->links($groupFiles),
                'meta' => $this->meta($groupFiles)
            ];
            return response()->json(
                new SuccessResource([
                    'statusCode' => 200,
                    'data' => $paginatedData,
                    'message' => 'Group file fetched successfully.',
                ]),
                200
            );
        }
    else{
        return response()->json(
            new SuccessResource([
                'statusCode' => 200,
                'data' => [],
                'message' => 'Group file not found.',
            ]),
            200
        );
    }

    }

    private function getLatestNoteColor($groupId)
    {
        $latestNote = PolicyNote::where('gid', '=', $groupId)->latest()->first();
        try {
            return isset($latestNote)
                ? PolicyNote::$colors[ array_search($latestNote['status'], PolicyNote::$statuses, false) ]
                : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function groupSearchList($filter){
        return GroupInfo::select('gname as name','gid as id','gcode as code')
        ->where('gname','like','%'.$filter->search_des.'%')
        ->orWhere('gid','like','%'.$filter->search_des.'%')
        ->orWhere('gcode','like','%'.$filter->search_des.'%')
        ->take($filter->limit)
        ->orderBy('gid','asc')
        ->get()
        ->toArray();
    }

    public function addSubGroups($request){
        DB::beginTransaction();
        try {
            foreach($request->data as $key => $data){
                $group_info = GroupInfo::find($data['gid']);
                if($group_info){
                    if($data['gid'] == $data['sub_group_id']){
                        return response()->json(
                            new ErrorResource([
                                'statusCode' => 400,
                                'message' => 'Cannot assign youself as subgroup',
                            ]),
                            400
                        );
                    }
                    if(SubGroup::where('gid',$data['gid'])->where('sub_group_id',$data['sub_group_id'])->count()){
                        return response()->json(
                            new ErrorResource([
                                'statusCode' => 400,
                                'message' => 'Subgroup '.GroupInfo::find($data['sub_group_id'])['gname'].' already exist',
                            ]),
                            400
                        );
                    }
                     SubGroup::create([
                        'gid' => $data['gid'],
                        'sub_group_id' => $data['sub_group_id'],
                    ]);
                }
                else{
                    return response()->json(
                        new ErrorResource([
                            'statusCode' => 400,
                            'message' => 'Group id '.$data['gid'].' is invalid',
                        ]),
                        400
                    );
                    DB::rollBack();
                }
            }
            $this->createGroupEligibilityLogs($request, GroupUpdate::SUB_GROUP_ADD, 'Sub group(s) has been added');
            DB::commit();
            return response()->json(
                new SuccessResource([
                    'statusCode' => 200,
                    'message' => 'Sub group(s) has been added successfully',
                ]),
                200
            );

        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    public function editSubGroups($request){
        if(SubGroup::find($request->data['id'])){
            SubGroup::where('id',$request->data['id'])->update([
                'status' =>(string) $request->data['status'],
            ]);
            $this->createGroupEligibilityLogs($request, GroupUpdate::SUB_GROUP_EDIT, 'Sub group has been updated');
            return response()->json(
                new SuccessResource([
                    'statusCode' => 200,
                    'message' => 'Sub group has been updated successfully',
                ]),
                200
            );
        }
        else{
            return response()->json(
                new ErrorResource([
                    'statusCode' => 400,
                    'message' => 'Sub group id is invalid',
                ]),
                400
            );
        }
    }

    public function deleteSubGroups($request){
        if(SubGroup::find($request->id)){
            SubGroup::where('id',$request->id)->delete();
            $this->createGroupEligibilityLogs($request, GroupUpdate::SUB_GROUP_DELETE, 'Sub group has been deleted');
            return response()->json(
                new SuccessResource([
                    'statusCode' => 200,
                    'message' => 'Sub group has been deleted successfully',
                ]),
                200
            );
        }
        else{
            return response()->json(
                new ErrorResource([
                    'statusCode' => 400,
                    'message' => 'Sub group id is invalid',
                ]),
                400
            );
        }
    }

    public function getSubGroups($gid){
        if(SubGroup::where('gid',$gid)->count()){
            $subgroups = [];
            $subGroup = SubGroup::query()->orderBy('sub_groups.id', 'DESC')
            ->leftJoin('group_info', 'sub_groups.sub_group_id', '=', 'group_info.gid')
            ->select('group_info.gname','sub_groups.id','sub_groups.gid','sub_groups.status','sub_groups.created_at')
            ->where('sub_groups.gid',$gid)
            ->paginate(10);
            foreach($subGroup as $key => $sbgroup){
                $subgroups[$key]['id'] = $sbgroup['id'];
                $subgroups[$key]['gid'] = $sbgroup['gid'];
                $subgroups[$key]['name'] = $sbgroup['gname'];
                $subgroups[$key]['status'] = $sbgroup['status'];
                $subgroups[$key]['created_at'] = date('m/d/Y',strtotime($sbgroup['created_at']));
            }
            if(count($subgroups)>0){
                $paginatedData = [
                    'data' => $subgroups,
                    'links' => $this->links($subGroup),
                    'meta' => $this->meta($subGroup)
                ];
                return response()->json(
                    new SuccessResource([
                        'statusCode' => 200,
                        'data' => $paginatedData,
                        'message' => 'Sub group has been fetched successfully',
                    ]),
                    200
                );
            }
        else{
            return response()->json(
                new SuccessResource([
                    'statusCode' => 200,
                    'data' => [],
                    'message' => 'Sub group not found.',
                ]),
                200
            );
        }
        }
        else{
            return response()->json(
                new ErrorResource([
                    'statusCode' => 400,
                    'message' => 'Group id is invalid',
                ]),
                400
            );
        }
    }

    function getGroupDetail($id){
        return GroupInfo::where('gid', $id)
            ->selectRaw("gemail as email, gphone as phone,
                         SUBSTRING_INDEX(gname, ' ', 1) as fname,
                         SUBSTRING(gname, LOCATE(' ', gname) + 1) as lname,
                         gcode as code")
            ->first();
    }

    public function changeGroupPassword(ChangeGroupPasswordRequest $request)
    {
        try{
        $user =SsoUsers::where([['email',$request->group_email],['user_type','G']])->first();
        $group = GroupUser::where('groupuser_email', $user->email)->first();
            $user->password = Hash::make($request->confirm_password);
            if ($user->save()) {
                if ($group) {
                    $group->groupuser_password = $user->password;
                    if ($group->save()) {
                        // GroupInfo::where('gid', $group->gid)->update(['' => $user->password]);
                        return $this->successResponse('Password updated successfully.');
                    }
                }
            }
            return $this->failedResponse('Password update failed.');
        }catch(Exception $e){
            return $this->failedResponse($e->getMessage());
        }
    }

    public function waiveGroupAssociationFee(GroupInfo $group, string $scheduleMonth): JsonResponse
    {
        if ($group->members->count() == 0) {
            return response()->json(
                new ErrorResource([
                    'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                    'error' => "No Members found.",
                    'message' => "Group needs Active Member(s) to waive their association fee."
                ]), Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        $scheduleDate = date('Y-m-t', strtotime($scheduleMonth));

        $this->processGroupAssociationFeeWaiver($group, $scheduleDate);

        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => "Group Members' Association Fee successfully waived."
            ]), Response::HTTP_OK
        );
    }

    private function processGroupAssociationFeeWaiver(GroupInfo $group, string $termDate)
    {
        Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)
            ->info(PHP_EOL . "■ ↓ ■ ↓ ■ ↓ ■ ↓ ■ ↓ ■ ↓ ■ ↓ " . PHP_EOL .
                "GROUP ASSOCIATION WAIVER FOR " . $group->gname . "({$group->gid})" . " COMMENCE" . PHP_EOL);

        try {
            $groupPolicies = $group->members;

            $message = 'Group Name: ' . $group->gname . " ({$group->gcode}) | Members: " . count($groupPolicies);
            Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->info($message);

            $associationsTermedOrWithdrawnCount = 0;

            foreach ($groupPolicies as $eachPolicy) {
                $associationPlan = $this->filterAssociationPlan($eachPolicy, $termDate);

                if (empty($associationPlan)) {
                    $message = 'No Association Plan found.';
                    Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->info($message);
                    continue;
                }

                $response = $this->terminateOrWithdrawPlan($associationPlan, $termDate, $group);

                $planEndResponse = json_decode($response->getContent());
                if (!$planEndResponse->success) {
                    $message = 'Association Termination Failed.';
                    Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->notice($message);
                    continue;
                }

                $associationsTermedOrWithdrawnCount++;

                // regen single invoices
                $this->regenerateInvoices($eachPolicy);
            }

            // refresh changes in group invoice
            $this->refreshGroupInvoice($group);
            $this->addGroupLog($group, $associationsTermedOrWithdrawnCount);

            $group->update([
                'is_association_fee_waived' => true
            ]);

            Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)
                ->notice('Association Plans Waived: ' . $associationsTermedOrWithdrawnCount);
        }
        catch (Exception $exception) {
            Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)
                ->error('Exception encountered: ' . PHP_EOL . $exception->getMessage());
        }
        finally {
            Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)
                ->info(PHP_EOL . "END OF GROUP ASSOCIATION WAIVER FOR "
                    . $group->gname . "({$group->gid})" . PHP_EOL . "■ ↑ ■ ↑ ■ ↑ ■ ↑ ■ ↑ ■ ↑ ■ ↑ " . PHP_EOL);
        }
    }

    private function filterAssociationPlan(Policy $policy, string $termDate)
    {
        $message = 'Policy ID: ' . $policy->policy_id . ' | Plans: ' . count($policy->planOverview);
        Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->info($message);

        return $policy->planOverview
            ->filter(function($plan) use ($termDate) {
                if ($plan->status != 'ACTIVE'
                    || !$plan->is_association_fee
                    || ($plan->pstatus != 1 && $plan->pterm_date <= $termDate)
                ) return false;

                return true;
            })
            ->first();
    }

    private function terminateOrWithdrawPlan(PlanOverview $associationPlan, string $termDate, GroupInfo $group)
    {
        $WITHDRAWN_FOR_OTHER = 'wno';
        $TERMINATE_FOR_OTHER = 'tmo';

        if ($associationPlan->peffective_date > $termDate) {
            $planEndMethod = $WITHDRAWN_FOR_OTHER;
            $planEndTerm = 'withdrawn';
        } else {
            $planEndMethod = $TERMINATE_FOR_OTHER;
            $planEndTerm = 'termed';
        }

        $message = "Association Fee (ID: {$associationPlan->p_ai} | Name: {$associationPlan->plan_name_system}) ";
        Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->info($message);

        $policyElgbNote = $message . $planEndTerm .
            " as initiated by Group (Code: {$group->gcode} | Name: {$group->gname})";

        $termRequestRepository = new TermRequestRepository();

        return $termRequestRepository->activePlanTermed([
            "termDate"     => $termDate,
            "planPolicyId" => $associationPlan->p_ai,
            "reason"       => $planEndMethod,
            "notes"        => $policyElgbNote,
            "loginUserId"  => request()->header('id')
        ]);
    }

    private function regenerateInvoices(Policy $policy)
    {
        $unpaidInvoices = $policy->invoices
            ->where('invoice_payment_status', 'UNPAID')
            ->all();

        if (empty($unpaidInvoices)) return;

        $unpaidInvoiceIds = $policy->invoices
            ->where('invoice_payment_status', 'UNPAID')
            ->pluck('invoice_id');

        $message = 'Invoices Unpaid: ' . count($unpaidInvoiceIds);
        Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->info($message);

        foreach ($unpaidInvoiceIds as $eachInvoiceId) {
            $url = config('app.purenroll_system.url') . "regenerateInvoice/$eachInvoiceId";

            try {
                $response = GuzzleHelper::getApi($url, []);
                $response = json_decode($response, true);

                if ($response['status'] == 'success') {
                    $message = 'Policy ID: ' . $policy->policy_id . ' | Invoice ID: ' .
                        $eachInvoiceId . ' Invoice Regenerated.';
                    Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->info($message);
                } else {
                    $message = 'Policy ID: ' . $policy->policy_id . ' | Invoice ID: ' .
                        $eachInvoiceId . ' Invoice Not Regenerated.' .
                        PHP_EOL . 'Reason: ' . $response['message'] ?? 'N/A';
                    Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->error($message);
                }
            }
            catch (Exception $exception) {
                $message = 'Policy ID: ' . $policy->policy_id . ' | Invoice ID: ' .
                $eachInvoiceId . ' Invoice Not Regenerated.' .
                PHP_EOL . 'Reason: ' . $exception->getMessage() ?? 'N/A';
                Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->error($message);
            }
        }
    }

    private function addGroupLog(GroupInfo $group, $countGroupMembersWaived)
    {
        $elgbComment = 'Association Fee of ' . $countGroupMembersWaived .' Member(s) waived.';
        GroupUpdate::create([
            'group_id' => $group->gid,
            'elgb_act' => GroupUpdate::ACT_WAIVEFEE,
            'elgb_act_date' => time(),
            'elgb_comment' => $elgbComment,
            'elgb_agent' => request()->header('id'),
            'elgb_agentname' => request()->header('name')
        ]);
    }

    private function refreshGroupInvoice(GroupInfo $group)
    {
        $url = config('app.purenroll_system.url') . "fixCarryforwardsGroup";

        try {
            $response = GuzzleHelper::postApi($url, [], [
                'group_id'  => $group->gid
            ]);
            $response = json_decode($response, true);

            if ($response['status'] == 'success') {
                $message = 'Group ID: ' . $group->gid . ' | Group Invoices carried-forward.';
                Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->info($message);
            } else {
                $message = 'Group ID: ' . $group->gid . ' | Group Invoices carried-forward FAILED.' .
                PHP_EOL . 'Reason: ' . $response['message'] ?? 'N/A';
                Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->error($message);
            }
        }
        catch (Exception $exception) {
            $message = 'Group ID: ' . $group->gid . ' | Group Invoices carried-forward FAILED.' .
            PHP_EOL . 'Reason: ' . $exception->getMessage() ?? 'N/A';
            Log::channel(self::ASSOCIATION_LOG_CHANNEL_NAME)->error($message);
        }
    }

    protected function deleteGroupUser($groupInfo)
    {
        $groupUser = $groupInfo->groupUser;

        if (!$groupUser) {
            $groupUsers = GroupUser::query()
                ->where('gid',$groupInfo->gid)
                ->delete();
        }
    }

   private function groupAssocScheduleInfo(GroupInfo $group): array
   {
       return [
           'is_waived' => $group->is_assoc_fee_waived,
           'is_scheduled' => $group->assoc_fee_waive_schedule
       ];
   }

   public function updateGroupLevelStatus($request){
        try{
            $group_info= GroupInfo::where('gid', $request->group_id)->first();
            if($group_info){
                DB::beginTransaction();
                $group_info
                ->update([
                    'group_level' => $request->group_level,
                ]);
                $data = [
                    "group_id" => $request['group_id'],
                    "elgb_act" => "GLU",
                    "elgb_comment" => "Group Level Status Updated",
                    "elgb_act_date" => time(),
                    "elgb_agent" => request()->header('id'),
                    "elgb_agentname" => request()->header('name'),
                ];
                GroupUpdate::create($data);
                DB::commit();
                return $this->successResponse('Group Level updated successfully.');
            }else{
                return $this->failedResponse('Group not Found.');
            }
        }catch(Exception $e){
            DB::rollBack();
            return $this->failedResponse($e->getMessage());
        }
   }

   public function updateGroupGuaranteedStatus($request){
        try{
            $group_info= GroupInfo::where('gid', $request->group_id)->first();
            if($group_info){
                DB::beginTransaction();
                $group_info
                ->update([
                    'group_guaranteed' => $request->group_guaranteed,
                ]);
                $data = [
                    "group_id" => $request['group_id'],
                    "elgb_act" => "GGU",
                    "elgb_comment" => "Group Guaranteed Status Updated",
                    "elgb_act_date" => time(),
                    "elgb_agent" => request()->header('id'),
                    "elgb_agentname" => request()->header('name'),
                ];
                GroupUpdate::create($data);
                DB::commit();
                return $this->successResponse('Group Guaranteed Status updated successfully.');
            }else{
                return $this->failedResponse('Group not Found.');
            }
        }catch(Exception $e){
            DB::rollBack();
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getGroupPhLevels(){
        try {
            $groupPhLevel = GroupPhLevel::pluck('level');
            return $this->successResponse('Group types fetched.', $groupPhLevel);
        } catch (\Exception $e) {
            return $this->failedResponse('Failed to fetch group types.');
        }
    }

    public function updateGroupLumpSumPay($request){
        try{
            $group_info= GroupInfo::where('gid', $request->group_id)->first();
            if($group_info){
                DB::beginTransaction();
                $group_info
                ->update([
                    'lump_sum_pay_fl' => $request->lump_sum_pay_fl,
                    'bill_date' => $request->bill_date,
                ]);
                $data = [
                    "group_id" => $request['group_id'],
                    "elgb_act" => "GLSPU",
                    "elgb_comment" => "Group LumpSum Pay Status Updated",
                    "elgb_act_date" => time(),
                    "elgb_agent" => request()->header('id'),
                    "elgb_agentname" => request()->header('name'),
                ];
                GroupUpdate::create($data);
                if($request->bill_date_changed){
                    $data = [
                        "group_id" => $request['group_id'],
                        "elgb_act" => "BDC",
                        "elgb_comment" => "Billing Date Update during Lumpsum Pay Flag Update",
                        "elgb_act_date" => time(),
                        "elgb_agent" => request()->header('id'),
                        "elgb_agentname" => request()->header('name'),
                    ];
                    GroupUpdate::create($data);
                }
                DB::commit();
                return $this->successResponse('Group LumpSum Pay Status updated successfully.');
            }else{
                return $this->failedResponse('Group not Found.');
            }
        }catch(Exception $e){
            DB::rollBack();
            return $this->failedResponse($e->getMessage());
        }
    }
}
