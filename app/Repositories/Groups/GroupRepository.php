<?php

namespace App\Repositories\Groups;
use App\GroupUpdate;
use App\PlanOverview;
use App\Traits\GroupEligibilityLog;
use Illuminate\Database\Eloquent\Model;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use App\GroupInfo;
use Illuminate\Http\Request;
use DB;


class GroupRepository extends model
{
    use ResponseMessage;
    use GroupEligibilityLog;

    public function totalActiveGroups($filters)
    {
        $activeGroups = GroupInfo::where('gstatus', '1')->select('gid as id', 'gname as name');
        if (isset($filters['gid'])) {
            $activeGroups = $activeGroups->whereNotIn('gid', (array) $filters['gid']);
        }
        if (isset($filters['limit'])) {
            $activeGroups = $activeGroups->take($filters['limit']);
        }
        if (isset($filters['query'])) {
            $activeGroups = $activeGroups->where('gname', 'Like', '%'.$filters['query'].'%');
        }
        $activeGroups = $activeGroups->orderBy('gname', 'asc')->get();
        return $this->successResponse('success', $activeGroups);
    }

    public function getGroupMemberList($filters)
    {
        $relations = ['plans:policy_id,web_display_name,tier,user_dob,pstatus,cid,price_male_nons,pterm_date,pid,plan_id,plan_cat,esig,status,eprocess,plan_pricing_type,pl_type',];
        $allmembers = UserInfoPolicyAddress::where('eid', $filters['gid'])
            ->where('status','ACTIVE')
            ->select('userid as id', DB::raw("CONCAT(cfname, ' ', clname) as name"), 'policy_id', DB::raw("DATE_FORMAT(effective_date, '%m/%d/%Y') as effective_date"));
        if(!$allmembers->get()->isEmpty()) {
            if (isset($filters['query'])) {
                $allmembers->where('cfname', 'like', '%' . $filters['query'] . '%');
            }

            if (isset($filters['limit'])) {
                $allmembers->take($filters['limit']);
            }
        }
        $allmembers = $allmembers->with($relations) // Eager load the 'plans' relationship
             ->orderBy('cfname', 'asc')
            ->get();
        return $this->successResponse('success', $allmembers);
    }

    public function updateGroupName(Request $request)
    {
        if(GroupInfo::where('gid',$request->group_id)->update(['gname'=> $request->group_name])) {
            $this->setEligibilityGroupId($request->group_id)
                ->setEligibilityComment('Group Name updated.')
                ->setEligiblityAction(GroupUpdate::ACT_INFO)
                ->createGroupEligibilityLogs($request);

            return $this->successResponse('Group name updated successfully.');
        }
        return $this->failedResponse('Failed updating group name.');
    }

    public function formattedGroup($group): array
    {
        $result = [];
        $activeMemberCount = PlanOverview::query()
            ->where('gid', '=', $group['gid'])
            ->where(function ($query) {
                $query->where('status', '=', 'ACTIVE')
                    ->orWhere('status', '=', 'WITHDRAWN');
            })
            ->distinct('userid')
            ->count('userid');

        $result['id'] = $group['gid'];
        $result['name'] = $group['gname'];
        $result['group_active_member'] = $activeMemberCount;

        return $result;
    }
}
