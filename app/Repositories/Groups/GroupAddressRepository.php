<?php


namespace App\Repositories\Groups;


use App\GroupAddress;
use App\GroupBillingAddress;
use App\GroupInfo;
use App\GroupUpdate;
use App\Service\CustomValidationService;
use App\Service\MessageService;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GroupAddressRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var GroupAddress
     */
    private $model;
    /**
     * @var GroupBillingAddress
     */
    private $billingAddressModel;
    /**
     * @var CustomValidationService
     */
    private $validationService;
    /**
     * @var MessageService
     */
    private $messageService;

    /**
     * GroupAddressRepository constructor.
     * @param GroupAddress $model
     * @param GroupBillingAddress $billingAddressModel
     * @param CustomValidationService $validationService
     * @param MessageService $messageService
     */
    public function __construct(
        GroupAddress $model,
        GroupBillingAddress $billingAddressModel,
        CustomValidationService $validationService,
        MessageService $messageService
    )
    {
        $this->model = $model;
        $this->billingAddressModel = $billingAddressModel;
        $this->validationService = $validationService;
        $this->messageService = $messageService;
    }


    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->orderBy('qid', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['gid'])) {
            $data->where('gid', '=', $filters['gid']);
        }
    }


    public function getAddressByGroupId($id)
    {
        $group = GroupInfo::find($id);
        if (!$group) return $this->failedResponse('Group does not exist.');
        $generalAddress = $this->model::query()
            ->where('gid', '=', $id)
            ->get()
            ->toArray();
        $billingAddresses = $this->billingAddressModel::query()
            ->where('gid', '=', $id)
            ->get()
            ->toArray();
        $data = array_merge($generalAddress, $billingAddresses);
        $collection = collect($data)->sortByDesc('is_primary');
        $sorted = $collection->values()->all();
        return $this->successResponse('Success', $sorted);
    }

    protected function setGroupAddressData($request)
    {
        return [
            'gid' => $request['group_id'],
            'address1' => $request['address1'],
            'address2' => isset($request['address2']) ? $request['address2'] : '',
            'zip' => $request['zip'],
            'city' => $request['city'],
            'state' => $request['state'],
            'is_primary' => isset($request['is_primary']) ? (int)$request['is_primary'] : 0,
            'is_usps_valid' => isset($request['usps_verified']) ? (int)$request['usps_verified'] : 0,
        ];
    }


    public function createGroupAddress($request)
    {
        $type = strtolower($request['type']);
        $requestData = $this->setGroupAddressData($request);
        if ($requestData['is_usps_valid'] == 1) {
            $this->validationService->validateAddressUSPS(
                $requestData['address1'],
                $requestData['address2'],
                $requestData['city'],
                $requestData['state'],
                $requestData['zip']
            );
        }
        $primaryData = [
            'group_id' => $request['group_id'],
            'type' => $type
        ];
        $isPrimary = isset($request['is_primary']) && $request['is_primary'] == 1 ? true : false;
        DB::beginTransaction();
        try {
            if ($type == GroupAddress::GROUP_ADDRESS_TYPE_BUSINESS) {
                $data = $this->model::create($requestData);
                $primaryData['id'] = $data->id;
            } elseif ($type == GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING) {
                $data = $this->billingAddressModel::create($requestData);
                $primaryData['id'] = $data->id;
            }
            //setPrimary
            if ($isPrimary) {
                $this->updateGroupPrimaryAddress($primaryData);
            }

            $successMessage = 'Group address added successfully.';
            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_ADDRESS_INFO, $successMessage);

            //send email
            $this->sendAddressEmail($data, $request['type']);

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse("Failed to add group address.");
        }
    }

    protected function getGroupInfoAddressData($requestData)
    {
        return [
            'gaddress1' => $requestData->address1,
            'gaddress2' => $requestData->address2,
            'gcity' => $requestData->city,
            'gstate' => $requestData->state,
            'gzip' => $requestData->zip
        ];
    }

    protected function getGroupInfoBillingAddressData($requestData)
    {
        return [
            'gbilladdress1' => $requestData->address1,
            'gbilladdress2' => $requestData->address2,
            'gbillcity' => $requestData->city,
            'gbillstate' => $requestData->state,
            'gbillzip' => $requestData->zip
        ];
    }

    protected function updateGroupInfoAddress($requestData, $groupId)
    {
        $data = GroupInfo::query()
            ->where('gid', '=', $groupId)
            ->update($requestData);
        return ($data) ? true : false;
    }

    protected function updateGroupPrimaryAddress($request)
    {
        $type = $request['type'];
        $groupId = $request['group_id'];
        $id = $request['id'];
        if ($type == GroupAddress::GROUP_ADDRESS_TYPE_BUSINESS) {
            $generalAddress = $this->model::query()
                ->where('gid', '=', $groupId)
                ->where('id', '=', $id)
                ->first();

            $generalAddress->update(['is_primary' => 1]);
            $this->model::query()
                ->where('gid', $groupId)
                ->where('id', '!=', $generalAddress->id)
                ->update(['is_primary' => 0]);
            $groupAddressData = $this->getGroupInfoAddressData($generalAddress);
            $this->updateGroupInfoAddress($groupAddressData, $groupId);

        } elseif ($type == GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING) {
            $billingAddress = $this->billingAddressModel::query()
                ->where('gid', '=', $groupId)
                ->where('id', '=', $id)
                ->first();

            $billingAddress->update(['is_primary' => 1]);
            $this->billingAddressModel::query()
                ->where('gid', $groupId)
                ->where('id', '!=', $billingAddress->id)
                ->update(['is_primary' => 0]);
            $billingAddressData = $this->getGroupInfoBillingAddressData($billingAddress);
            $this->updateGroupInfoAddress($billingAddressData, $groupId);
        }
    }

    public function setPrimaryGroupAddress($request)
    {
        $checkPrimaryAddress = $this->checkIfAddressIsPrimary($request);
        if (!$checkPrimaryAddress['status']) {
            DB::beginTransaction();
            try {
                $this->updateGroupPrimaryAddress($request);

                $successMessage = ucwords($request['type']) . " address successfully set to primary.";

                //update to agent_updates
                $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_ADDRESS_INFO, $successMessage);

                //send email
                $checkPrimaryAddress['data']['is_primary'] = 1;
                $this->sendAddressEmail($checkPrimaryAddress['data'], $request['type'], 'set to primary', 'Set Primary Address');

                DB::commit();
                return $this->successResponse($successMessage);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse("Failed to set primary address.");
            }
        } else {
            return $this->failedResponse('This address is already set to primary.', 409);
        }

    }

    public function deleteGroupAddress($request)
    {
        $checkPrimaryAddress = $this->checkIfAddressIsPrimary($request);
        if (!$checkPrimaryAddress['status']) {
            DB::beginTransaction();
            try {
                $this->deleteAddress($request);
                $successMessage = ucwords($request['type']) . " address deleted successfully.";
                //update to agent_updates
                $this->createGroupEligibilityLogs($request, GroupUpdate::ACT_ADDRESS_INFO, $successMessage);

                //send email
                $this->sendAddressEmail($checkPrimaryAddress['data'], $request['type'], 'deleted', 'Delete Address');

                DB::commit();
                return $this->successResponse($successMessage);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse($th->getMessage());
            }
        } else {
            return $this->failedResponse('Failed to delete address.', 409);
        }
    }

    protected function checkIfAddressIsPrimary($data)
    {
        $type = strtolower($data['type']);
        $id = $data['id'];
        if ($type == GroupAddress::GROUP_ADDRESS_TYPE_BUSINESS) {
            $generalAddress = $this->model::find($id);
            if ($generalAddress instanceof GroupAddress && $generalAddress->is_primary) {
                return ['status' => true, 'data' => $generalAddress];
            } else {
                return ['status' => false, 'data' => $generalAddress];
            }
        } elseif ($type == GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING) {
            $billingAddress = $this->billingAddressModel::find($id);
            if ($billingAddress instanceof GroupBillingAddress && $billingAddress->is_primary) {
                return ['status' => true, 'data' => $billingAddress];
            } else {
                return ['status' => false, 'data' => $billingAddress];
            }
        }
    }

    protected function createGroupEligibilityLogs($request, $act = "", $comment = "")
    {
        $data = [
            "group_id" => $request['group_id'],
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent" => isset($request['loginUserId']) ? $request['loginUserId'] : null,
            "elgb_agentname" => isset($request['loginUserName']) ? $request['loginUserName'] : null,
        ];
        GroupUpdate::create($data);
    }

    protected function formattedDataWithComma($data)
    {
        return !($data == null || $data === '') ? $data . ',' : '';
    }

    protected function formattedAddress($address1, $address2, $city, $state, $zip)
    {
        $address1 = $this->formattedDataWithComma($address1);
        $address2 = $this->formattedDataWithComma($address2);
        $city = $this->formattedDataWithComma($city);
        $state = $this->formattedDataWithComma($state);
        $zip = $zip ? $zip : '';
        return "{$address1} {$address2} {$city} {$state} {$zip}";
    }


    protected function formattedAddress2($address2, $city, $state, $zip)
    {
        $address2 = $this->formattedDataWithComma($address2);
        $city = $this->formattedDataWithComma($city);
        $state = $this->formattedDataWithComma($state);
        $zip = $zip ? $zip : '';
        return "{$address2} {$city} {$state} {$zip}";
    }

    protected function sendAddressEmail($address, $addressType, $statusMessage = "created", $subject = "Group Address Created")
    {
        $groupInfo = GroupInfo::find($address->gid);
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $date = Carbon::parse($address->created_at)->format('m/d/Y');
        $message = "This is an automated notice to notify that your following ${addressType} address has been {$statusMessage} on " . $date . ".";
        $addressDetail = $this->formattedAddress2(
            $address->address2,
            $address->city,
            $address->state,
            $address->zip
        );
        $contentData = [
            'Group Id' => $groupInfo->gid,
            'Address Detail' => $address->address1 . "<br/>" . $addressDetail,
            'Type' => ucwords($addressType),
            'Primary' => $address->is_primary || $address->active ? 'Yes' : 'No'
        ];
        $emailConfigurationName = "GROUP_INFO_CHANGE";

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
            'dataName'=>$groupInfo->contact_fullname
        ];
        return $this->messageService->sendEmailWithContentData($emailData);
    }

    protected function deleteAddress($request)
    {
        $type = $request['type'];
        $id = $request['id'];
        switch ($type) {
            case GroupAddress::GROUP_ADDRESS_TYPE_BUSINESS:
                /**
                 * @var $generalAddress GroupAddress
                 */
                $generalAddress = $this->model::find($id);
                $generalAddress->delete();
                return true;
            case GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING:
                /**
                 * @var $billingAddress GroupBillingAddress
                 */
                $billingAddress = $this->billingAddressModel::find($id);
                $billingAddress->delete();
                return true;
            default:
                return false;
        }
    }
}
