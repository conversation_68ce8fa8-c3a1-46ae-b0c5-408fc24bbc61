<?php

namespace App\Repositories\Groups;

use App\GroupInfo;
use App\Traits\GetUserStat;
use App\Traits\ResponseMessage;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class GroupDashboard extends Model
{
    use ResponseMessage, GetUserStat;

    public function totalAnalytics($from_date = null, $to_date = null)
    {
        $totalAnalytics = GroupInfo::selectRaw('COUNT(gid) as total_group')
            ->selectRaw('SUM(CASE WHEN gstatus = 1 THEN 1 ELSE 0 END) AS total_active_group')
            ->selectRaw('SUM(CASE WHEN gstatus = 0 THEN 1 ELSE 0 END) AS total_inactive_group');

        if (!is_null($from_date) && !is_null($to_date)) {
            $totalAnalytics = $totalAnalytics->whereRaw("FROM_UNIXTIME(gts) >= '$from_date' AND FROM_UNIXTIME(gts) < DATE_ADD('$to_date', INTERVAL 1 day)");
        }
        $totalAnalytics = $totalAnalytics->get();

        $data = ['totalAnalytics' => $totalAnalytics[0]];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalYearProgress($fyear, $tyear)
    {
        if ($fyear < $tyear) {
            $x = $tyear - $fyear;
            $totalYears = $totalActiveGroup = $totalInactiveGroup =[];

            for ($i = 0; $i <= $x; $i++) {
                $fyear = strval($fyear);
                $yearlyGroupData = GroupInfo::whereRaw("FROM_UNIXTIME(gts) LIKE '$fyear-%'")
                    ->selectRaw("COUNT(gid) as total_counts")
                    ->selectRaw('SUM(CASE WHEN gstatus = 1 THEN 1 ELSE 0 END) AS active_group')
                    ->selectRaw('SUM(CASE WHEN gstatus = 0 THEN 1 ELSE 0 END) AS inactive_group')
                    ->get();
                //For ACTIVE member policies
                $totalActiveGroup[$i] = $yearlyGroupData[0]->active_group;
                // //For INACTIVE member policies
                $totalInactiveGroup[$i] = $yearlyGroupData[0]->inactive_group;
                // For Total Counts
                $totalCounts[$i] = $yearlyGroupData[0]->total_counts;
                array_push($totalYears, $fyear);
                $fyear++;
            }
        } else {
            $res = ['status' => 'error', 'message' => 'format error'];
            return $res;
        }

        $activeGroup = [
            'name' => 'Active',
            'data' => $totalActiveGroup
        ];

        $inactiveGroup = [
            'name' => 'Inactive',
            'data' => $totalInactiveGroup
        ];

        $totalCounts = [
            'name' => 'Total Counts',
            'data' => $totalCounts
        ];
        $activeAndInactiveGroups = [
            $totalCounts,
            $activeGroup,
            $inactiveGroup
        ];

        $data = [
            'totalYears' => $totalYears,
            'activeAndInactiveGroup' => $activeAndInactiveGroups
        ];

        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalMonthsProgress($from_date, $to_date)
    {
        $res = ['status' => 'error', 'message' => 'format error', 'date' => []];
        $fyear = substr($from_date, 0, 4);
        $tyear = substr($to_date, 0, 4);
        $from_month = substr($from_date, 5, 2);
        $to_month = substr($to_date, 5, 2);
        $m = 0;
        $from_month > $to_month? $diff = $to_month + 12 - $from_month : $diff = $to_month - $from_month;
        if ($from_month > $to_month) {
            if ($diff >= 6 || ($tyear - $fyear > 1)) {
                return $res;
            }

            if ($fyear == $tyear) {
                return $res;
            }

            for ($i = 0; $i <= $diff; $i++) {
                $monthlyGroupData = $this->getMonthlyGroupData($fyear, $from_month);
                $progressInMonths[$i] = $monthlyGroupData[0];

                if ($from_month == 12) {
                    $fyear++;
                    $fyear = strval($fyear);
                    $from_month = 1;
                } else {
                    $from_month++;
                }

                $from_month = strval($from_month);
                strlen($from_month) < 2 ? $from_month = "0".$from_month : $from_month;
            }
        } else {
            if (($diff >= 6) || ($fyear != $tyear)) {
                return $res;
            }

            for ($i = 0; $i <= $diff; $i++) {
                $monthlyGroupData = $this->getMonthlyGroupData($fyear, $from_month);
                $progressInMonths[$i] = $monthlyGroupData[0];
                $from_month++;
                $from_month = strval($from_month);
                strlen($from_month) < 2 ? $from_month = "0".$from_month : $from_month;
            }
        }
        $data = $progressInMonths;
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalWeekProgress(){
        $weeklyData= GroupInfo::whereRaw("YEARWEEK(from_unixtime(gts,'%Y-%m-%d')) = YEARWEEK(now())")
        ->selectRaw("DAYNAME(from_unixtime(gts)) as day,COUNT(gid) as total_count")
            ->selectRaw("SUM(CASE WHEN gstatus = 1 THEN 1 ELSE 0 END) as activeGroup")
            ->selectRaw("SUM(CASE WHEN gstatus = 0 THEN 1 ELSE 0 END) as inactiveGroup")
            ->groupBy('day')
            ->get();

            for($i=0;$i<count($weeklyData);$i++){
                $days[$i] = $weeklyData[$i]->day;
                $dates[$i] = $weeklyData[$i]->date;
                $totalCounts[$i] =$weeklyData[$i]->total_count;
                $activeCounts[$i] =$weeklyData[$i]->activeGroup;
                $inactiveCounts[$i] =$weeklyData[$i]->inactiveGroup;
            }

        $activeAgents = [];
        $inactiveAgents = [];
        $totalAgents = [];
        $daynames = ['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];
        $index = 0;

        foreach($daynames as $day)
        {
            if(isset($days) AND in_array($day,$days))
            {
                array_push($activeAgents,(int)$activeCounts[$index]);
                array_push($inactiveAgents,(int)$inactiveCounts[$index]);
                array_push($totalAgents,(int)$totalCounts[$index]);

                $index++;
            }
            else{
                array_push($activeAgents,0);
                array_push($inactiveAgents,0);
                array_push($totalAgents,0);

            }
        }

        $days = [
            'name' => 'Days',
            'data' => $daynames
        ];

        $totalAgents =[
            'name' => 'Total Counts',
            'data' => $totalAgents
        ];
        $activeAgents =[
            'name' => 'Active',
            'data' => $activeAgents
        ];
        $inactiveAgents =[
            'name' => 'Inactive',
            'data' => $inactiveAgents
        ];

        $data =[$days,$totalAgents,$activeAgents,$inactiveAgents];
        $response = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];

        return $response;
    }

    public function groupByOtherCondition()
    {
        $res = ['status' => 'error', 'message' => 'Failed to get content', 'data' => []];
        $filters['gstatus'] = 1;
        //total group
        $totalActiveGroupCount = GroupInfo::where('gstatus', 1)->count('gid');

        //by payment type
        $groupByPaymentQuery = GroupInfo::query();
        $groupByPaymentQuery = $groupByPaymentQuery->selectRaw("SUM(CASE WHEN geft = 1 THEN 1 ELSE 0 END) AS eft_count")
            ->selectRaw("SUM(CASE WHEN glist = 1 THEN 1 ELSE 0 END) AS list_count")
            ->selectRaw("SUM(CASE WHEN gstmt = 1 THEN 1 ELSE 0 END) AS stmt_count")
            ->selectRaw("SUM(CASE WHEN gcc = 1 THEN 1 ELSE 0 END) AS cc_count");
        $groupByPaymentType = $this->filterContent($groupByPaymentQuery, $filters)->get();

        //by state
        $groupByStateQuery = GroupInfo::query();
        $groupByStateQuery = $groupByStateQuery->where([
            ['gstate','!=', null],
            ['gstate','!=', '']
            ])
            ->groupBy('gstate')
            ->havingRaw('count(gid) >= 1')
            ->orderBy('count', 'desc');
        $groupByState = $this->filterContent($groupByStateQuery, $filters)->selectRaw('gstate, count(gid) as count')->get();

        //by rep
        $groupByRepQuery = GroupInfo::query();
        $groupByRepQuery = $groupByRepQuery->join('agent_info','agent_info.agent_id', 'group_info.gagent_code')
            ->groupBy('gagent_code')
            ->havingRaw('count(gid) >= 1')
            ->orderBy('count', 'desc');
        $groupByRep = $this->filterContent($groupByRepQuery, $filters)->selectRaw('agent_info.agent_id, agent_info.agent_fname, agent_info.agent_lname, agent_info.agent_code, count(group_info.gid) as count')->get();

        //by groupType
        $groupByGroupTypeQuery = GroupInfo::query();
        $groupByGroupTypeQuery = $groupByGroupTypeQuery->where('gtype', '!=', null)
            ->selectRaw("SUM(CASE WHEN gtype = 'agency' THEN 1 ELSE 0 END) AS agency_count")
            ->selectRaw("SUM(CASE WHEN gtype = 'employer' THEN 1 ELSE 0 END) AS employer_count")
            ->selectRaw("SUM(CASE WHEN gtype = 'callcenter' THEN 1 ELSE 0 END) AS callcenter_count")
            ->selectRaw("SUM(CASE WHEN gtype = 'group' THEN 1 ELSE 0 END) AS group_count")
            ->selectRaw("SUM(CASE WHEN gtype = 'rx' THEN 1 ELSE 0 END) AS rx_count")
            ->selectRaw("SUM(CASE WHEN gtype = 'thirdparty' THEN 1 ELSE 0 END) AS thirdparty_count");
        $groupByGroupType = $this->filterContent($groupByGroupTypeQuery, $filters)->get();

        //By LifeSpan
        $today = date('Y-m-d');
        $fiveMonths = date('Y-m-d', strtotime('-5 months', strtotime($today)));
        $sixMonths = date('Y-m-d', strtotime('-1 day', strtotime($fiveMonths)));
        $twelveMonths = date('Y-m-d', strtotime('-12 months', strtotime($today)));
        $oneYear = date('Y-m-d', strtotime('-1 day', strtotime($twelveMonths)));
        $twoYear = date('Y-m-d', strtotime('-2 years', strtotime($today)));
        $ftwoYear = date('Y-m-d', strtotime('-1 day' ,strtotime($twoYear)));
        $fourYear = date('Y-m-d', strtotime('-4 years' ,strtotime($today)));
        $fourPlus = date('Y-m-d', strtotime('-1 day' ,strtotime($fourYear)));

        $groupByLifespanQuery = GroupInfo::query();
        $groupByLifespanQuery = $groupByLifespanQuery->selectRaw("SUM(CASE WHEN FROM_UNIXTIME(gts) >= '$fiveMonths' AND FROM_UNIXTIME(gts) <= '$today' THEN 1 ELSE 0 END) AS uptoFiveMonthsCount")
            ->selectRaw("SUM(CASE WHEN FROM_UNIXTIME(gts) >= '$twelveMonths' AND FROM_UNIXTIME(gts) <= '$sixMonths' THEN 1 ELSE 0 END) AS sixToTwelveMonthsCount")
            ->selectRaw("SUM(CASE WHEN FROM_UNIXTIME(gts) >= '$twoYear' AND FROM_UNIXTIME(gts) <= '$oneYear' THEN 1 ELSE 0 END) AS oneToTwoYearsCount")
            ->selectRaw("SUM(CASE WHEN FROM_UNIXTIME(gts) >= '$fourYear' AND FROM_UNIXTIME(gts) <= '$ftwoYear' THEN 1 ELSE 0 END) AS twoToFourYearsCount")
            ->selectRaw("SUM(CASE WHEN FROM_UNIXTIME(gts) <= '$fourPlus' THEN 1 ELSE 0 END) AS fourYearsPlusCount");
        $groupByLifespan = $this->filterContent($groupByLifespanQuery, $filters)->get();
        $date = [
            'today' => $today,
            'fiveMonths' => $fiveMonths,
            'sixMonths' => $sixMonths,
            'twelveMonths' => $twelveMonths,
            'oneYear' => $oneYear,
            'twoYear' => $twoYear,
            'ftwoYear' => $ftwoYear,
            'fourYear' => $fourYear,
            'fourPlus' => $fourPlus
        ];
        $groupByLifespan = [
            'filterDate' => $date,
            'lifespanCount' => $groupByLifespan[0]
        ];

        //by website
        $groupByWebsiteQuery = GroupInfo::query();
        $groupByWebsiteQuery = $groupByWebsiteQuery->join('group_info_quick as giq', 'giq.gid', 'group_info.gid')
            ->where('giq.weburl', '!=', null)
            ->groupBy('giq.weburl')
            ->havingRaw('count(giq.gid) >= 1')
            ->orderBy('count', 'desc');
        $groupByWebsite = $this->filterContent($groupByWebsiteQuery, $filters)->selectRaw('giq.weburl, COUNT(giq.gid) AS count')->get();

        //by app
        /*$groupByAppQuery = GroupInfo::query();
        $groupByAppQuery = $groupByAppQuery->join('user_activity_details as u', 'group_info.gid', 'u.user_id')
            ->where([
                ['u.user_type', 'group'],
                ['u.action', 'login'],
                ['u.device_type', '!=', null]
            ])
            ->selectRaw("SUM(CASE WHEN u.device_type = 'ios' THEN 1 ELSE 0 END) AS ios_count")
            ->selectRaw("SUM(CASE WHEN u.device_type = 'android' THEN 1 ELSE 0 END) AS android_count");
        $groupByApp = $this->filterContent($groupByAppQuery, $filters)->get(); */

        $userDetails = $this->userTotalLogin()['data']['all_time_login'];
        $groupByApp['ios_count'] = $userDetails['ios']['group'];
        $groupByApp['android_count'] = $userDetails['android']['group'];

        //by dashboard
        $groupByDashboardAccessQuery = GroupInfo::query();
        $groupByDashboardAccessQuery = $groupByDashboardAccessQuery->join('user_activity_details as u', 'group_info.gid', 'u.user_id')
            ->where([
                ['u.web', 1],
                ['u.user_type', 'group'],
                ['u.action', 'login']
            ]);
        $groupByDashboardAccess = $this->filterContent($groupByDashboardAccessQuery, $filters)->selectRaw('u.web as access, count(distinct u.user_id) AS count')->get();
        $totalNonAccess = $totalActiveGroupCount - $groupByDashboardAccess[0]->count;
        $dashboardNonAccess = [
            'access' => "0",
            'count' => strval($totalNonAccess)
        ];
        $dashboardAndCount = [
            $groupByDashboardAccess[0],
            $dashboardNonAccess
        ];
        $groupByDashboard = [
            'dashboardAndCount' => $dashboardAndCount
        ];
        $data = [
            'totalActiveGroupCount' => $totalActiveGroupCount,
            'groupByPaymentType' => $groupByPaymentType[0],
            'groupByState' => $groupByState,
            'groupByRep' => $groupByRep,
            'groupByGroupType' => $groupByGroupType[0],
            'groupByLifespan' => $groupByLifespan,
            'groupByWebsite' => $groupByWebsite,
            'groupByApp' => $groupByApp,
            'groupByDashboard' => $groupByDashboard
        ];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function getGroupsByMemberRange($request)
    {
        $from = $request['from'];
        $to = $request['to'];
        $groupDetail = $this->manageGroupMembersQuery($from, $to);
        $groupModifiedDetail = $this->manageGroupMembersDetail($groupDetail['groupDetail']);
        $groupCount = $groupDetail['groupCount'];
        $groupMemberInfo = [
            'groupCount' => $groupCount,
            'groupDetail' => $groupModifiedDetail
        ];
        $data = [
            'groupMemberInfo' => $groupMemberInfo
        ];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    protected function getMonthlyGroupData($fyear, $from_month)
    {
        $monthlyGroupData = GroupInfo::whereRaw("FROM_UNIXTIME(gts) LIKE '$fyear-$from_month-%'")
            ->selectRaw("COUNT(gid) as total_counts")
            ->selectRaw("SUM(CASE WHEN gstatus = 1 THEN 1 ELSE 0 END) as activeGroup")
            ->selectRaw("SUM(CASE WHEN gstatus = 0 THEN 1 ELSE 0 END) as inactiveGroup")
            ->get();
        $monthlyGroupData[0]->month = $from_month;
        $monthlyGroupData[0]->year = $fyear;
        $ymdate = "$fyear-$from_month-01";
        $last_date = date("Y-m-t", strtotime($ymdate));
        $monthlyGroupData[0]->last_date = $last_date;
        return $monthlyGroupData;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['gstatus'])) {
            return $data->where('gstatus', 1);
        }
    }

    protected function manageGroupMembersQuery($from, $to = null)
    {
        if ($to === null) {
            $groupQuery = DB::table('group_totals as gt')
                ->join('group_info as g', 'gt.eid', 'g.gid')
                ->select(DB::raw("gt.eid"))
                ->groupBy('gt.eid')
                ->havingRaw("SUM(gt.totals) > $from");
        } else {
            $groupQuery = DB::table('group_totals as gt')
                ->join('group_info as g', 'gt.eid', 'g.gid')
                ->select(DB::raw("gt.eid"))
                ->groupBy('gt.eid')
                ->havingRaw("SUM(gt.totals) >= '$from'")
                ->havingRaw("SUM(gt.totals) <= '$to'");
        }
        $groupCount = $groupQuery->get()->count();
        $groupTotals = $groupQuery->orderBy('gt.status', 'ASC')
            ->orderBy('gt.totals', 'DESC')
            ->limit(5)
            ->pluck('gt.eid');
        $groupDetail = GroupInfo::join('group_totals as gt', 'gt.eid', 'group_info.gid')
            ->selectRaw('gt.eid, gt.totals, gt.status, group_info.gname, group_info.gcode')
            ->whereIn('gt.eid', $groupTotals)
            ->orderBy('gt.totals', 'DESC')
            ->get()
            ->toArray();
        return (['groupCount' => $groupCount, 'groupDetail' => $groupDetail]);
    }

    protected function manageGroupMembersDetail($groupDetail = [])
    {
        $cg = count($groupDetail);
        for ($n = 0; $n < $cg; $n++) {
            $status = $groupDetail[$n]['status'];
            $groupDetail[$n]["$status"] = $groupDetail[$n]['totals'];
            unset($groupDetail[$n]['status']);
            $j = $n + 1;
            while ($j < $cg) {
                if ($groupDetail[$n]['eid'] === $groupDetail[$j]['eid']) {
                    $statusNew = $groupDetail[$j]['status'];
                    $groupDetail[$n]["$statusNew"] = $groupDetail[$j]['totals'];
                    array_splice($groupDetail, $j, 1);
                    $cg--;
                } else {
                    $j++;
                }
            }
            unset($groupDetail[$n]['totals']);
        }
        return $groupDetail;
    }
}
