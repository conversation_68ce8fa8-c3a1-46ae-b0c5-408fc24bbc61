<?php

namespace App\Repositories\Groups;

use App\GroupInfo;
use App\GroupUpdate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use App\Traits\ResponseMessage;
use App\Policy;
use App\PlanPolicy;
use Illuminate\Support\Facades\Log;
use App\Helpers\EmailSendHelper;
use App\Helpers\SendNotificationHelper;
use App\Repositories\PolicyFeatures;
use App\Helpers\PolicyUpdateHelper;
use App\UserInfoPolicyAddress;
use App\EmailPreference;
use App\GroupUser;
use App\Helpers\GuzzleHelper;
use App\Repositories\PolicyTerminateWithdraw\PolicyTerminateWithdrawFeatures;
use App\Repositories\V3\Archive\ArchiveRepository;
use DB;


class GroupFeatures extends Model
{
    use ResponseMessage;

    private  $archiveRepository;
    private  $policyTerminateWithdrawFeatures;

    public function __construct()
    {
        $this->policyTerminateWithdrawFeatures = new PolicyTerminateWithdrawFeatures();
        $this->archiveRepository = new ArchiveRepository();
    }

    public function updateFee($request)
    {
        try {
            $groupID = $request->group_id;
            $agentID = $request->agent_id;
            $agentName = $request->agent_name;
            $reason = $request->reason;
            $billFee = $request->fee_flag == 1 ? "1" : "0";
            if ($billFee == 1) {
                $bill_reason = "Group FEE WAIVED";
            } else {
                $bill_reason = "Group FEE ADDED";
            }
            $sendEmail = $request->sendEmail;
            if (!GroupInfo::where('gid', $groupID)->count()) {
                return $this->failedResponse('Group not found');
            }
            $status = GroupInfo::where('gid', $groupID)->update(['waive_status' => $billFee]);
            if ($status) {
                $data = [
                    'elgb_act' => GroupUpdate::ACT_WAIVEFEE,
                    'elgb_act_date' => time(),
                    'elgb_agent' => $agentID,
                    'elgb_agentname' => $agentName,
                    'created_at' => date('Y-m-d'),
                    'group_id' => $groupID,
                    'elgb_comment' => $reason ? $reason : $bill_reason,
                ];
                GroupUpdate::insert($data);
            }
            $responseData = [
                'groupId' => $request->group_id,
                'status' => 'waived'
            ];
            return $this->successResponse('Group fee updated.', $responseData);
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }


    protected function associationGroupsQuery()
    {
        return GroupInfo::query()
            ->select(['gid', 'gname', 'gcode',])
            ->where('gstatus', '=', true)
            ->orderBy('gid', 'ASC');
    }

    public function getAssociationGroups($cid = [])
    {
        $query = $this->associationGroupsQuery();
        if(count($cid)> 0) {
            $query->whereHas('groupPlansView', function ($query) use ($cid) {
                $query->whereIn('carrier', $cid);
            });
        }
        $data = $query->get();
        return $this->formattedAssocGroupData($data);
    }

    protected function formattedAssocGroupData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedAssocGroupItem($d);
        }
        return [
            'data' => $result,
            'meta' => [
                'total' => count($result)
            ]
        ];
    }

    protected function singleFormattedAssocGroupItem($d)
    {
        return [
            'gId' => $d->gid,
            'name' => $d->gname,
            'code' => $d->gcode,
        ];
    }

    public function terminateGroup($request) {
        ini_set('max_execution_time', '0');
        ini_set('memory_limit','-1');
        set_time_limit(0);
        $termedDate = date('Y-m-t', strtotime($request->termed_date ?? 'last day of this month'));
        $groupID = $request->group_id;
        $switch_group_id = $request->switch_group_id;
        $radio_option = $request->radio_option;
        if (!GroupInfo::where('gid', $groupID)->exists()) {
            return $this->failedResponse('Group not found');
        }
        try {

            if($radio_option && $radio_option == "switch") {
                if (!GroupInfo::where('gid', $switch_group_id)->exists()) {
                    return $this->failedResponse('Switch Group not found');
                }
                if (GroupInfo::where('gid', $switch_group_id)->where('gstatus',2)->exists()) {
                    return $this->failedResponse('Switch Group is already termed.');
                }
                $data = [
                    'gid' => $switch_group_id,
                ];
                $allpolicies = Policy::where('eid', $groupID)->pluck('policy_id');
                foreach($allpolicies as $policyId) {
                    $data['policy_id'] = $policyId;
                    $data['sendEmail'] = 1;
                    self::updateGroupPolicy($data);
                }
                Log::info("All \t" . $allpolicies->count() . "\t policies Group(eid) has been updated from \t" . $groupID . "\t to \t" . $switch_group_id . "\t");
                $old_data = [
                    'elgb_act' => 'TMO',
                    'elgb_act_date' => time(),
                    "elgb_agent" => $request['loginUserId'] ?? request()->header('id'),
                    "elgb_agentname" => $request['loginUserName'] ?? request()->header('name'),
                    'created_at' => date('Y-m-d'),
                    'group_id' => $groupID,
                    'elgb_comment' => "Group `$groupID` has been termed.",
                ];
                GroupUpdate::create($old_data);
                $switch_group = GroupInfo::where('gid', $switch_group_id)->first();
                GroupInfo::where('gid', $groupID)->update(['gstatus' => 2, 'termed_date' => $termedDate]);
                $groupInfo = GroupInfo::find($groupID);
                // update status of Group User while terminting
                $this->updateStatusInGroupUser($groupInfo, 2);
                Log::info("Group \t" . $groupID . "\t has been terminated successfully.");
                return $this->successResponse('Group successfully termed and member switched to '.$switch_group->gname.'');

            } elseif ($radio_option && $radio_option == "term") {
                // WithDrawn Policy Condition
                $withDrawnData = [];
                $allPolicyWithDrawn = Policy::where('eid', $groupID)->where('effective_date','>',$termedDate)->where('status','!=','TERMED')->pluck('policy_id');
                foreach($allPolicyWithDrawn as $policyId) {
                    $withDrawnData['policyID'] = $policyId;
                    $withDrawnData['tdate'] = $termedDate;
                    $withDrawnData['sendEmail'] = 1;
                    self::setWithdrawPolicy($withDrawnData);
                }

                // Termed Policy Condition
                $allPolicyTermed = Policy::where('eid', $groupID)->where('effective_date','<',$termedDate)->where('status','!=','TERMED')->pluck('policy_id');
                foreach($allPolicyTermed as $policyId) {
                    $withDrawnData['policyID'] = $policyId;
                    $withDrawnData['tdate'] = $termedDate;
                    $withDrawnData['sendEmail'] = 1;
                    self::setTerminatePolicy($withDrawnData);
                }
                $data = [
                    'elgb_act' => 'TMO',
                    'elgb_act_date' => time(),
                    "elgb_agent" => $request['loginUserId'] ?? request()->header('id'),
                    "elgb_agentname" => $request['loginUserName'] ?? request()->header('name'),
                    'created_at' => date('Y-m-d'),
                    'group_id' => $groupID,
                    'elgb_comment' => "Group $groupID has been termed.",
                ];
                $groupInfo = GroupInfo::find($groupID);
                
                GroupUpdate::create($data);
                GroupInfo::where('gid', $groupID)->update(['gstatus' => 2, 'termed_date' => $termedDate]);
                $this->archiveRepository->deleteGroup($groupID);
                // update status of Group User while terminting
                $this->updateStatusInGroupUser($groupInfo, 2);
                Log::info("Group \t" . $groupID . "\t has been terminated successfully.");
                return $this->successResponse('Group successfully termed.');
            }
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    protected function updateStatusInGroupUser($groupInfo, $status)
    {
        /**
         * @var $groupUser GroupUser
         */
        $groupUser = $groupInfo->groupUser;

        if (!$groupUser) {
            $groupUser = GroupUser::query()
                ->where('gid', '=', $groupInfo->gid)
                ->orderBy('gid', 'DESC')
                ->first();
        }
        if (isset($groupUser)) {
            $groupUser->update([
                'groupuser_status' => $status,
            ]);
        }
    }

    protected function updateStatusInSsoUser($groupId)
    {
        $url = config('corenroll.corenroll_api_url') . "api/v1/remove-group-sso/{$groupId}";
        $requestData = [];
        $responseJson = GuzzleHelper::postApi($url, [], $requestData);
        $response = json_decode($responseJson, true);
        if ($response['status'] == 'success') {
            return true;
        } else {
            return false;
        }
    }

    public function updateGroupPolicy($request)
    {

        try {
            $policyID = $request['policy_id'];
            $groupID = $request['gid'];
            $reason = $request->reason ?? null;
            $sendEmail = $request['sendEmail'];
            $status = Policy::where('policy_id', $policyID)->update(['eid' => $groupID]);
            if ($status) {
                $data = [
                    'elgb_act' => 'UGP',
                    'elgb_act_date' => time(),
                    'elgb_agent' => $request['loginUserId'] ?? request()->header('id'),
                    'elgb_file_date' => date('Y-m-d'),
                    'elgb_term_date' => null,
                    'elgb_policyid' => $policyID,
                    'elgb_comment' => $reason ? $reason : "New policy group updated."
                ];
                PolicyUpdateHelper::updateEligibility($data);
                if ($sendEmail) {
                   EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($policyID, 'Policy Group Updated', "policy group has been updated", 'billingdate', 'UGP');
                    EmailSendHelper::sendPolicyActionEmail($policyID, 'Policy Group Updated', "policy group has been updated", 'billingdate', 'UGP');
                }
            }
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }


    public function setTerminatePolicy($request)
    {
        $policyID = $request['policyID'];
        $agentID = $request->aid ?? null;
        $reason = 'Group Termination';
        $sendEmail = $request['sendEmail'];
        $tdate = $request['tdate'];
        $action = 'TMO';
        try {
            $tdate = date("Y-m-t", strtotime($tdate));
            $policyDetails = UserInfoPolicyAddress::where('policy_id', $policyID)->get()->first();
            Policy::where('policy_id', $policyID)->update(['term_date' => $tdate, 'status' => 'TERMED']);

            /* if chosen termination date is smaller than the effective date of that plan each plan, then term date will be their effective date */
            self::setPlanTermWithdrawStatus(2, $tdate, $policyID);

            $reasonFullName = EmailPreference::where('pref-code', $action)->pluck('pref-name')->first();
            SendNotificationHelper::sendTerminationNotification($policyDetails, $tdate, $reasonFullName);
            $reasonForEmail = EmailPreference::where('pref-code', $reason)->pluck('pref-name')->first();
            $reason = ($reason != '') ? $reason : $reason;
            if ($sendEmail == 1) {
                EmailSendHelper::sendPolicyActionEmail($policyID, 'Termination Notice', 'following programs has been terminated', 'terminate', strtoupper($action), $reason);
            }
            $data = [
                    'elgb_act' => 'TMO',
                    'elgb_act_date' => time(),
                    'elgb_agent' => $request['loginUserId'] ?? request()->header('id'),
                    'elgb_file_date' => date('Y-m-d'),
                    'elgb_term_date' => null,
                    'elgb_policyid' => $policyID,
                    'elgb_comment' => $reason ? $reason : "Policy has been termed."
                ];
                // $checkOtherPolicies = $this->policyTerminateWithdrawFeatures->checkOtherActivePolicies($policyDetails->userid, $policyID);
                // if($checkOtherPolicies) {
                //     $this->archiveRepository->deleteMember($policyDetails->userid);
                // }
            PolicyUpdateHelper::updateEligibility($data);
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function setWithdrawPolicy($request)
    {
        $policyID = $request['policyID'];
        $agentID = $request->aid ?? null;
        $reason = 'Group Termination';
        $sendEmail = $request['sendEmail'];
        $tdate = $request['tdate'];
        $action = 'WNO';
        try {
            $tdate = date("Y-m-t", strtotime($tdate));
            $policyDetails = UserInfoPolicyAddress::where('policy_id', $policyID)->get()->first();

            Policy::where('policy_id', $policyID)->update(['term_date' => $policyDetails->effective_date, 'status' => 'WITHDRAWN']);

            /* if chosen termination date is smaller than the effective date of that plan each plan, then term date will be their effective date */

            $plansList = PlanPolicy::where('policy_num', $policyID)->get();
            foreach ($plansList as $plans) {
                $plans->pstatus = 3;
                $plans->pterm_date = $plans->peffective_date;
                $plans->save();
            }

            $reasonFullName = EmailPreference::where('pref-code', $action)->pluck('pref-name')->first();
            SendNotificationHelper::sendWithdrawnNotification($policyDetails, $tdate, $reasonFullName);
            $reasonForEmail = EmailPreference::where('pref-code', $reason)->pluck('pref-name')->first();
            $reason = ($reason != '') ? $reason : $reason;
            if ($sendEmail == 1) {
                EmailSendHelper::sendPolicyActionEmail($policyID, 'Withdraw Notice Confirmation', 'following programs have been withdrawn', 'withdraw', strtoupper($action), $reason);
            }
            $data = [
                    'elgb_act' => 'WNO',
                    'elgb_act_date' => time(),
                    'elgb_agent' => $request['loginUserId'] ?? request()->header('id'),
                    'elgb_file_date' => date('Y-m-d'),
                    'elgb_term_date' => null,
                    'elgb_policyid' => $policyID,
                    'elgb_comment' => $reason ? $reason : "Policy has been withdrawn."
                ];
                // $checkOtherPolicies = $this->policyTerminateWithdrawFeatures->checkOtherActivePolicies($policyDetails->userid, $policyID);
                // if($checkOtherPolicies) {
                //     $this->archiveRepository->deleteMember($policyDetails->userid);
                // }
            PolicyUpdateHelper::updateEligibility($data);
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function setPlanTermWithdrawStatus($status, $tdate, $policyID)
    {
        /* if chosen termination/withdrawn date is smaller than the effective date of that plan each plan, then term date will be their effective date */
        $plansList = PlanPolicy::where('policy_num', $policyID)->where('pstatus',1)->get();
        foreach ($plansList as $plans) {
            $plans->pstatus = $status;
            $plans->pterm_date = $tdate;
            if ($plans->peffective_date && $plans->peffective_date > $tdate) {
                $plans->pterm_date = $plans->peffective_date;
            }
            $plans->save();
        }
    }
}
