<?php


namespace App\Repositories\Groups;


use App\GroupEft;
use App\GroupInfo;
use App\GroupUpdate;
use App\Helpers\DecryptEcryptHelper;
use App\Service\CustomValidationService;
use App\Service\MessageService;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GroupEftRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var GroupEft
     */
    private $model;
    /**
     * @var CustomValidationService
     */
    private $validationService;
    /**
     * @var MessageService
     */
    private $messageService;

    /**
     * GroupEftRepository constructor.
     * @param GroupEft $model
     * @param CustomValidationService $validationService
     * @param MessageService $messageService
     */
    public function __construct(
        GroupEft $model,
        CustomValidationService $validationService,
        MessageService $messageService
    )
    {
        $this->model = $model;
        $this->validationService = $validationService;
        $this->messageService = $messageService;
    }


    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->orderBy('bank_id', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['bank_id'])) {
            $data->where('bank_id', '=', $filters['gid']);
        }
    }

    public function getEftBankByGroupId($groupId)
    {
        $groupInfo = GroupInfo::find($groupId);
        if (!$groupInfo) return $this->failedResponse('Group not found.');
        $data = $this->model::query()
            ->where('bank_gid', '=', $groupId)
            ->orderBy('is_primary', 'DESC')
            ->get();
        $formattedData = $this->formattedData($data);
        return $this->successResponse('Success', $formattedData);
    }

    protected function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result[] = [
                "bank_id" => $d->bank_id,
                "bank_name" => $d->bank_name,
                "bank_accountname" => $d->bank_accountname,
                "bank_routing" => (int)$d->bank_routing,
                "bank_account" => (int)DecryptEcryptHelper::decryptInfo($d->bank_account),
                "masked_account" =>isset($d->bank_account) ? "XXXX" . $d->bank_account4 : '',
                "bank_account4" => (int)$d->bank_account4,
                "bank_gid" => $d->bank_gid,
                "is_primary" => $d->is_primary,
            ];
        }
        return $result;
    }

    protected function setGroupEftData($request)
    {
        return [
            "bank_name" => $request['bank_name'],
            "bank_routing" => $request['bank_routing'],
            "bank_gid" => $request["group_id"],
            "bank_account" => DecryptEcryptHelper::encryptInfo($request['bank_account']),
            "bank_account4" => substr($request['bank_account'], -4),
            "bank_accountname" => $request['bank_accountname'],
            "is_primary" => isset($request['is_primary']) && $request['is_primary'] == 1 ? 1 : 0,
            "account_type" => $request['account_type'],
            "account_holder_type" => $request['account_holder_type'],
            "bank_date" => date('Y-m-d')
        ];
    }


    protected function setGroupInfoData($groupEft)
    {
        return [
            "enaachrout" => $groupEft->bank_routing,
            "enaachbank" => $groupEft->bank_accountname,
            "enaachname" => $groupEft->bank_name,
            "enaachacct" => DecryptEcryptHelper::decryptInfo($groupEft->bank_account),
        ];
    }

    public function createGroupEft($request)
    {
        DB::beginTransaction();
        try {
            $data = $this->setGroupEftData($request);
            $groupEft = $this->model::create($data);
            $successMessage = 'Group eft bank detail added successfully.';
            //update to group_updates
            $this->createGroupEligibilityLogs($request, GroupUpdate::BILLING_ACT_INFO, $successMessage);

            //update group info
            if ($groupEft->is_primary == 1) {
                $groupInfoData = $this->setGroupInfoData($groupEft);
                $groupEft->groupInfo->update($groupInfoData);
                //set is_primary = 0 for other related banks
                $this->updateRelatedBanksPrimaryFalse($groupEft);
            }

            //send email
            $this->sendEftBankEmail($groupEft);
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse("Failed to add eft bank detail.");
        }
    }

    protected function createGroupEligibilityLogs($request, $act = "", $comment = "")
    {
        $data = [
            "group_id" => $request['group_id'],
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent" => isset($request['loginUserId']) ? $request['loginUserId'] : null,
            "elgb_agentname" => isset($request['loginUserName']) ? $request['loginUserName'] : null,
        ];
        GroupUpdate::create($data);
    }

    protected function sendEftBankEmail($eftBank, $statusMessage = "created", $subject = "Group Eft Bank Detail Created")
    {
        $groupInfo = GroupInfo::find($eftBank->bank_gid);
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $date = Carbon::parse($eftBank->created_at)->format('m/d/Y');
        $message = "This is an automated notice to notify that your following bank detail has been {$statusMessage} on " . $date . ".";

        $accountNumber = DecryptEcryptHelper::decryptInfo($eftBank->bank_account);
        $bankDetail = "Account Name : {$eftBank->bank_name}<br/>";
        $bankDetail .= "Account Holder : {$eftBank->bank_accountname}<br/>";
        $bankDetail .= "Routing Number : {$eftBank->bank_routing}<br/>";
        $bankDetail .= "Account Number : {$accountNumber}";
        $contentData = [
            'Group Id' => $eftBank->bank_gid,
            'Bank Detail' => $bankDetail,
            'Primary' => $eftBank->is_primary ? 'Yes' : 'No'
        ];
        $emailConfigurationName = "GROUP_INFO_CHANGE";

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
            'dataName'=>$groupInfo->contact_fullname
        ];
        return $this->messageService->sendEmailWithContentData($emailData);
    }

    public function setPrimaryEftBank($request)
    {
        $data = $this->model::find($request['bank_id']);
        if (!$data) return $this->failedResponse("No Eft Bank found.");
        $checkPrimaryAchPayment = $this->checkIfPaymentIsPrimary($request['bank_id']);
        if (!$checkPrimaryAchPayment) {
            DB::beginTransaction();
            try {
                $data->update(['is_primary' => 1]);
                //update related primary in group info/
                $groupInfoData = $this->setGroupInfoData($data);
                $data->groupInfo->update($groupInfoData);

                //set is_primary = 0 for other related banks
                $this->updateRelatedBanksPrimaryFalse($data);
                $successMessage = "Eft bank successfully set to primary.";

                //update to group_updates
                $request['group_id'] = $data->bank_gid;
                $this->createGroupEligibilityLogs($request, GroupUpdate::BILLING_ACT_INFO, $successMessage);

                //send email
                $this->sendEftBankEmail($data, 'set to primary', 'Set Primary Bank');

                DB::commit();
                return $this->successResponse($successMessage);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse("Failed to set eft bank primary.");
            }
        } else {
            return $this->failedResponse('This eft bank is already set to primary.', 409);
        }
    }

    public function deleteEftBank($request)
    {
        $data = $this->model::find($request['bank_id']);
        if (!$data) return $this->failedResponse("No Eft Bank found.");
        $checkPrimaryAchPayment = $this->checkIfPaymentIsPrimary($request['bank_id']);
        if (!$checkPrimaryAchPayment) {
            try {
                $data->delete();
                $successMessage = "Eft bank deleted successfully.";
                //update to group_updates
                $request['group_id'] = $data->bank_gid;
                $this->createGroupEligibilityLogs($request, GroupUpdate::BILLING_ACT_INFO, $successMessage);
                //send email
                $this->sendEftBankEmail($data, 'deleted', 'Delete Bank');
                return $this->successResponse($successMessage);
            } catch (\Throwable $th) {
                return $this->failedResponse("Failed to delete this eft bank.");
            }
        } else {
            return $this->failedResponse('Cannot delete this primary eft bank.', 409);
        }
    }

    protected function checkIfPaymentIsPrimary($id)
    {
        return $this->model::query()
            ->where([
                'bank_id' => $id,
                'is_primary' => 1
            ])
            ->exists();
    }

    public function checkBank($groupId)
    {
        $data = $this->model::query()
            ->where('bank_gid', '=', $groupId)
            ->exists();
        $message = $data ? 'Success - Group Eft Exists' : 'No Group Eft Exists';
        return $this->successResponse($message, $data);
    }


    public function updateRelatedBanksPrimaryFalse($data)
    {
        return $this->model::query()
            ->where('bank_gid', '=', $data->bank_gid)
            ->where('bank_id', '!=', $data->bank_id)
            ->update(['is_primary' => 0]);
    }
}
