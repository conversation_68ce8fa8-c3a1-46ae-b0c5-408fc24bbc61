<?php


namespace App\Repositories\Groups;


use App\CcDetail;
use App\EftDetail;
use App\Exports\GroupExport;
use App\GroupFileGeneration;
use App\GroupInfo;
use App\GroupPlans;
use App\Helpers\DecryptEcryptHelper;
use App\Jobs\ExportExcelFileGroup;
use App\Jobs\JohnExportExcelFile;
use App\PlanOverview;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class GroupReportRepository
{

    use Paginator , ResponseMessage;
    /**
     * @var Excel
     */
    private $excel;
    /**
     * @var ManageGroups
     */
    private $groupRepository;


    /**
     * GroupReportRepository constructor.
     * @param Excel $excel
     * @param ManageGroups $groupRepository
     */
    public function __construct(Excel $excel, ManageGroups $groupRepository)
    {
        $this->excel = $excel;
        $this->groupRepository = $groupRepository;
    }

    public function generate_export($filters =[]){
        $data['added_by'] = request()->header('name');
        $data['added_by_id'] = request()->header('id');
        ExportExcelFileGroup::dispatch($filters , $data)->onQueue('GroupProcessing');
    }

    public function getGroupReport($request){
        $groupFileDetail = GroupFileGeneration::fetchgroupReport($request['id']);
        $groupExpLst = $groupFileDetail->map(function ($groupExpList) {
            return collect($groupExpList->toArray())
                ->only(['id', 'file_path', 'file_name', 'file_generation_started', 'file_generation_ended', 'no_of_record', 'added_by', 'added_by_id', 'created_at', 'updated_at', 'file_download_link'])
                ->all();
        });
        $paginatedData = [
            'data'=>$groupExpLst,
            'links' => $this->links($groupFileDetail),
            'meta' => $this->meta($groupFileDetail)
        ];
        return $paginatedData;
         
    }
    public function getAdminGroupreport($request){
        $groupFileDetail = GroupFileGeneration::fetchAdmingroupReport('john',request()->header('id')??null);
        $groupExpLst = $groupFileDetail->map(function ($groupExpList) {
            return collect($groupExpList->toArray())
                ->only(['id', 'file_path', 'file_name', 'file_generation_started', 'file_generation_ended', 'no_of_record', 'added_by', 'added_by_id', 'created_at', 'updated_at', 'file_download_link'])
                ->all();
        });
        $paginatedData = [
            'data'=>$groupExpLst,
            'links' => $this->links($groupFileDetail),
            'meta' => $this->meta($groupFileDetail)
        ];
        return $paginatedData;
         
    }

  public function generateAdminGroupreport($request)
  {
      ini_set('max_execution_time', 300);
      $queryParams = $request->query();
      $filters = $request->query();
    
      // Define validation rules for query parameters
      $rules = [
          'group' => 'required',
          'edaterange' => 'nullable|in:0,1',
          'effdate' => 'nullable|string|required_if:edaterange,0',
          'effdate_s' => 'nullable|date|required_if:edaterange,1',
          'effdate_e' => 'nullable|date|required_if:edaterange,1',
          'termdate' => 'nullable|string',
      ];
      $message = [
       
          'effdate.required_if' => 'Effective date is required',
          'effdate_s.required_if' => 'Effective date start is required',
          'effdate_e.required_if' => 'Effective date end is required',
         
      ];

      // Create a validator instance using the Validator facade
      $validator = Validator::make($queryParams, $rules,$message);

      // Check if the validation fails
      if ($validator->fails()) {
          return response()->json([
              'status' => 'error',
              'errors' => $validator->errors(),
          ], 422);
      }
      $data['added_by'] = request()->header('name');
      $data['added_by_id'] = request()->header('id');
      
      JohnExportExcelFile::dispatch($filters,$data)->onQueue('johnExportProcessing');
//     $job =  new JohnExportExcelFile($filters);
// return ($job->handle());
      return response()->json([
        'status' => 'success',
        'message'=>'Your report is being generated in background. thank you!'
        
    ], 201);
     
  }
 

}