<?php

namespace App\Repositories;

use App\Helpers\FamilyRelationFinderHelper;
use App\Helpers\EmailSendHelper;
use Exception;
use App\Dependent;
use App\DependentPolicy;
use App\UserInfoPolicyAddress;
use Illuminate\Database\Eloquent\Model;

class DependentApproval extends Model
{
    public function getApprovalList()
    {
        $data = [];
        $dependents = Dependent::where('is_approved', 'N')->whereNotNull('pending_policies')->get();
        foreach ($dependents as $key => $d) {
            $data[$key]['dependent'] = $d;
            $policy_id = preg_replace('/[^A-Za-z0-9\-]/', '', $d->pending_policies);
            $data[$key]['dependent']['policy_id'] = $policy_id;
            $data[$key]['user'] = $d->getMember()->first();
        }
        return $data;
    }
    public function approvalAction($request)
    {
        try {
            if ($request->action === 'accept') {
                $this->acceptDependent($request);
                $message = "Dependent Approved Successfully.";
            } else {
                $this->rejectDependent($request);
                $message = "Dependent Rejected Successfully.";
            }
            $dependentData = Dependent::where('did', $request->dependent_id)
                ->get(['userid', 'd_fname', 'd_lname', 'd_relate', 'd_dob', 'd_ssn4', 'd_gender'])
                ->first()->toArray();
            $data = [
                "Dependent Name" => $dependentData['d_fname']." ".$dependentData['d_lname'],
                "Relation" => FamilyRelationFinderHelper::getFullRelation($dependentData['d_relate']),
                "DOB" => ($dependentData['d_dob'] !='') ? 'XX/XX/'.date('Y',strtotime($dependentData['d_dob'])) : '',
                "SSN" => "XXX-XX-" . $dependentData['d_ssn4'],
                "Gender" => $dependentData['d_gender'] == 0 ? 'Male' : 'Female'
            ];
            $subject = $request->action == 'accept' ? 'Dependent Approved' : 'Dependent Rejection';
            EmailSendHelper::sendGenericPolicyEmailUser($dependentData['userid'], $subject, $subject, 'dependentapproval', $data,'');
        } catch (Exception $e) {
            return ['error' => "Dependent information not found."];
        }
        return ['success' => $message];
    }
    public function acceptDependent($request)
    {
        $depRow = Dependent::where('did', $request->dependent_id)->first();
        $dPolicies = json_decode($depRow->pending_policies);
        foreach ($dPolicies as $dP) {
            $savePol = new DependentPolicy();
            $savePol->dependent_id = $request->dependent_id;
            $savePol->policy_id = $dP;
            $savePol->save();
        }
        $depRow->reason_to_accept = $request->reason;
        $depRow->pending_policies = null;
        $depRow->is_approved = null;
        $depRow->reason_to_reject = null;
        $depRow->save();
    }
    public function rejectDependent($request)
    {
        $depRow = Dependent::where('did', $request->dependent_id)->first();
        $depRow->reason_to_reject = $request->reason;
        $depRow->pending_policies = null;
        $depRow->save();
    }
}
