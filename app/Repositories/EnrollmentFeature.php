<?php

namespace App\Repositories;

use Exception;
use App\EnrollmentQuestion;
use App\EnrollmentText;
use Illuminate\Database\Eloquent\Model;

class EnrollmentFeature extends Model
{
    public function getQuestionList()
    {
        $data = EnrollmentQuestion::where('deleted','=','0')
            ->get();
        return $data;
    }
    public function getQuestionDetail($request)
    {
        $data = EnrollmentQuestion::where('qid','=', $request->qid)
            ->get();
        return $data;
    }
    public function addQuestion($request)
    {
        try {
            $questions = [
                'question' => $request->question,
                'true_condition' => $request->true_condition,
                'error_message' => $request->error_message,
                'status' => $request->status ? $request->status : '0',
                'addedon' => time(),
                ];
            $insert = EnrollmentQuestion::firstOrNew(['qid' => $request->qid]);
            $insert->fill($questions);
            $insert->save();
            $message = ['type' => "Success.", 'message'=> $insert];
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        return $message;
    }
    public function deleteData($id, $type)
    {
        try {
            if ($type == 'questions') {
                $update =EnrollmentQuestion::where('qid', $id)
                ->update(['deleted'=>'1']);
            } else {
                $update =EnrollmentText::where('etid', $id)
                ->update(['deleted'=>'1']);
            }
            $message = ['type' => "Success.", 'message'=> $update];
        } catch (Exception $e) {
           $message = $e->getMessage();
        }
        return $message;
    }
    public function getTextList()
    {
        $data = EnrollmentText::where('deleted','=','0')
            ->get();
        return $data;
    }
    public function getEnrollmentTextDetail($request)
    {
        $data = EnrollmentText::where('etid','=', $request->etid)
            ->get();
        return $data;
    }
    public function addEnrollText($request)
    {
        try {
            $text = [
                'enrollment_text_head' => $request->enrollment_text_head,
                'make_required_chk' => $request->make_required_chk,
                'make_required_text' => $request->make_required_text,
                'enrollment_text_body' => $request->enrollment_text_body,
                'details' => $request->details,
                'added' => time(),
                'deleted' => '0',
                ];
            $insert = EnrollmentText::firstOrNew(['etid' => $request->etid]);
            $insert->fill($text);
            $insert->save();
            $message = ['type' => "Success.", 'message'=> $insert];
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        return $message;
    }
}
