<?php

namespace App\Repositories;

use App\PolicyUpdate;
//use App\Address;

use Illuminate\Database\Eloquent\Model;

class PolicyUpdateLog extends Model
{
    protected $table = 'policy_updates';

    protected $guarded = [];

    public function addPolicyUpdates($request, $act)
    {
        try {
            $updates['elgb_policyid'] = $request->policy_id;
            $updates['elgb_act'] = $act;
            $updates['elgb_comment'] = $request->reason ? $request->reason : '';
            $updates['elgb_term_date'] = '';
            $updates['elgb_old_pid'] = '';
            $updates['elgb_new_pid'] = '';
            $updates['elgb_agent'] = $request->aid;
            $updates['elgb_act_date'] = time();
            $updates['elgb_file_date'] = date('Y-m-d');
            $updates['elgb_policyid_ol'] = '0';
            $updates['elgb_old_effdate'] = '';
            $updates['elgb_new_effdate'] = '';
            $policies = PolicyUpdate::insertGetId($updates);
            $elgbid = $policies;
            $message = 'Policy Update Successfully Added';
        } catch (Exception $e) {
            $message = 'Failed To Add Policy Updates.';
            return ['error' => $e->getMessage()];
        }
        return ['success' => $message];
    }
}
