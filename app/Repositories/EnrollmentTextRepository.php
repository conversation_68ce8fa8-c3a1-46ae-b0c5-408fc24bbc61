<?php

namespace App\Repositories;

use App\EnrollmentText;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EnrollmentTextRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var EnrollmentText
     */
    private $model;

    /**
     * EnrollmentTextRepository constructor.
     * @param EnrollmentText $model
     */
    public function __construct(EnrollmentText $model)
    {
        $this->model = $model;
    }

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->where('deleted', '!=', true)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }

    public function paginatedFormattedList($limit, $filters = [])
    {
        $query = $this->paginatedList($limit, $filters)
            ->appends($filters);
        $data = $this->formattedData($query);
        return $this->successResponse('Success', $data);

    }

    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->orderBy('etid', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }

    public function show($qId)
    {
        $query = $this->getByField('etid', $qId);
        if (!$query) return $this->failedResponse('No Enrollment Text found.', 404);
        $data = $this->singleFormattedItem($query);
        return $this->successResponse('Success', $data);
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['textHead'])) {
            $data->where('enrollment_text_head', 'LIKE', '%' . $filters['textHead'] . '%');
        }
        if (isset($filters['textBody'])) {
            $data->where('enrollment_text_body', 'LIKE', '%' . $filters['textBody'] . '%');
        }
        if (isset($filters['details'])) {
            $data->where('details', 'LIKE', '%' . $filters['details'] . '%');
        }
        if (isset($filters['makeRequiredText'])) {
            $data->where('make_required_text', 'LIKE', '%' . $filters['makeRequiredText'] . '%');
        }
        if (isset($filters['makeRequiredCheck'])) {
            $data->where('make_required_chk', '=', $filters['makeRequiredCheck']);
        }
        if (isset($filters['deleted'])) {
            $data->where('deleted', '=', $filters['deleted']);
        }
        if (isset($filters['addedDate'])) {
            //need from and to date to filter
            $filteredDate = strtotime($filters['addedDate']);
            $nextDate = Carbon::parse($filters['addedDate'])->addDay()->unix();
            $data->whereBetween('added', [$filteredDate, $nextDate]);
        }
    }

    public function singleFormattedItem($d)
    {
        return [
            'etId' => $d->etid,
            'textHead' => $d->enrollment_text_head,
            'textBody' => $d->enrollment_text_body,
            'details' => $d->details,
            'makeRequiredCheck' => $d->make_required_chk,
            'makeRequiredText' => $d->make_required_text,
            'added' => $d->added,
            'addedDate' => date('Y-m-d', $d->added),
            'deleted' => $d->deleted,
        ];
    }

    public function formattedItems($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return $result;
    }

    public function formattedData($data)
    {
        return [
            'data' => $this->formattedItems($data),
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    protected function formattedFormData($data)
    {
        return [
            'enrollment_text_head' => $data['textHead'],
            'enrollment_text_body' => $data['textBody'],
            'details' => $data['details'],
            'make_required_text' => isset($data['makeRequiredText']) ? $data['makeRequiredText'] : null,
            'make_required_chk' => isset($data['makeRequiredCheck']) ? $data['makeRequiredCheck'] : null,
        ];
    }

    public function create($data)
    {
        $data = $this->formattedFormData($data);
        $data['added'] = time();
        $data['deleted'] = 0;
        try {
            $this->model::create($data);
            return $this->successResponse('Enrollment Text Created.', [], 201);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Create.');
        }
    }

    public function update($data, $id)
    {
        $model = $this->getById($id);
        $data = $this->formattedFormData($data);
        try {
            $model->update($data);
            return $this->successResponse('Enrollment Text Updated.', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Update.');
        }
    }

    public function delete($id)
    {
        $model = $this->getById($id);
        if (!$model) return $this->failedResponse('No Enrollment Text found.');
        try {
            $model->update([
                'deleted' => 1
            ]);
            return $this->successResponse('Enrollment Text Deleted.', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Delete.');
        }
    }
}
