<?php

namespace App\Repositories\PolicyRider;

use App\Helpers\EmailSendHelper;
use App\Helpers\PolicyUpdateHelper;
use App\PlanOverview;
use App\PlanPolicy;
use App\PlanPricingDisplay;
use App\Plan;
use App\Policy;
use App\UserInfoPolicyAddress;
use Illuminate\Database\Eloquent\Model;
use League\Flysystem\Exception;

class PolicyRiderFeatures extends Model
{
    public function getRiderList($policyID)
    {
        try {
            $planDetails = PlanOverview::where('policy_id', $policyID)->where('pstatus', '1')->get(['tier', 'effective_date'])->first();
            if ($planDetails) {
                $data['plan_list'] = PlanPricingDisplay::where('tier', $planDetails->tier)
                    ->where('rider', '1')
                    ->where('plan_status', '1')
                    ->where('pricing_status', '1')
                    ->whereNotIn('pid', PlanOverview::where('policy_id', $policyID)->pluck('pid')->toArray())
                    ->where(function ($query) {
                        $query->where('is_assoc', NULL)
                            ->orWhere('is_assoc', '=', '');
                    })
                    ->get(['plan_pricing_id', 'plan_name_system', 'tier', 'price_male_nons']);
            } else {
                $data = ['error' => 'Policy details not found.'];
            }
            return $data;
        } catch (Exception $e) {
            return ['error' => 'Failed getting rider plan list.'];
        }
    }

    public function addPolicyRiderPlan($request)
    {
        try {
            $reason = $request->reason;
            $agentID = $request->aid;
            $planPricingID = $request->plan_pricing_id;
            $policyID = $request->policy_id;
            $planDetails = PlanOverview::where('policy_id', $policyID)->where('pstatus', '1')->get(['tier', 'effective_date'])->first();
            if ($planDetails) {
                $tier = PlanPricingDisplay::where('plan_pricing_id', $planPricingID)->pluck('tier')->first();
                if ($planDetails->tier != $tier) {
                    return ['error' => 'Rider plan should have same tier.'];
                }
                if (PlanPolicy::where('policy_num', $policyID)->where('plan_id', $planPricingID)->count()) {
                    return ['error' => 'Rider plan already exists.'];
                }
                $policyRider = [
                    'policy_num' => $policyID,
                    'plan_id' => $planPricingID,
                    'date' => strtotime(date('Y-m-d')),
                    'peffective_date' => $planDetails->effective_date,
                    'ppptype' => 'price_male_nons',
                    'pstatus' => 1,
                ];
                $status = PlanPolicy::insert($policyRider);
                if ($status) {
                     $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
                    $data = [
                        'elgb_act' => 'adr',
                        'elgb_act_date' => time(),
                        'elgb_agent' => $agentID,
                        'elgb_file_date' => date('Y-m-d'),
                        'elgb_term_date' => null,
                        'elgb_policyid' => $policyID,
                        'elgb_comment' => $reason ? $reason : "New rider plan added to policy.",
                        'origin' => $origin
                    ];
                    PolicyUpdateHelper::updateEligibility($data);
                    $data = ['success' => 'Policy rider added successfully.'];
                } else {
                    $data = ['success' => 'Failed adding rider policy.'];
                }
                return $data;
            } else {
                return ['error' => 'Invalid policy.'];
            }
        } catch (Exception $e) {

            return ['error' => $e->getMessage()];
        }
    }

    public function terminateWithDrawRiderPolicy($request)
    {
        try {
            $reason = $request->reason;
            $agentID = $request->aid;
            $termDate = $request->term_date;
            $planPolicyID = $request->plan_policy_id;
            $policyID = $request->policy_id;
            $type = $request->type;
            if (!UserInfoPolicyAddress::where('policy_id', $policyID)->where('status', 'ACTIVE')->count()) {
                return ['error' => 'Policy not exist or not active.'];
            }
            $planPolicyCount = PlanOverview::where('p_ai', $planPolicyID)
                ->where('policy_id', $policyID)
                ->where('pstatus', '1')->get(['effective_date', 'rider', 'policy_id', 'web_display_name'])
                ->first();

            if (!$planPolicyCount instanceof PlanOverview) {
                return ['error' => 'Policy plan not found.'];
            }
            if ($type == 'terminate') {
                if (date('d', strtotime($termDate)) != date('t', strtotime($termDate)) && date('d', strtotime($termDate)) != '15' ) {
                    return ['error' => 'Termination date should be the last of month or 15th of month.'];
                }
                if (!PolicyUpdateHelper::validateTermDate($policyID, $termDate)) {
                    return ['error' => 'Termination date should be higher than effective date.'];
                }
                $policyStatus = 'TERMED';
                $pstatus = 2;
                $keyword = "terminated";
                $action = 'TCR';
            } else if ($type == 'withdraw') {
                $policyStatus = 'WITHDRAWN';
                $termDate = $planPolicyCount->effective_date;
                $pstatus = 3;
                $keyword = "withdrawn";
                $action = 'WDR';
            }
            $planPoliciesIds = [$planPolicyID];
            if ($planPolicyCount->rider == 1) {
                $status = PlanPolicy::where('p_ai', $planPolicyID)
                    ->update(['pterm_date' => $termDate, 'pstatus' => $pstatus]);
                $comment = "Rider Plan $planPolicyCount->web_display_name has beeen $keyword.";
            } else {
                $data = PlanOverview::where('policy_id', $policyID)
                    ->where('pstatus', '1')
                    ->groupBy('policy_id')
                    ->selectRaw('GROUP_CONCAT(web_display_name) as plan_names')
                    ->selectRaw('GROUP_CONCAT(rider) as rider_data')
                    ->selectRaw('GROUP_CONCAT(p_ai) as plan_policy_ids')
                    ->selectRaw("COUNT(CASE WHEN rider != '1' THEN 1 END) standAlonePlanCount")
                    ->get()->first();
                $standAlonePlanCount = $data->standAlonePlanCount;
                $planPoliciesIds = $standAlonePlanCount > 1 ? [$planPolicyID] : explode(',', $data->plan_policy_ids);
                $status = PlanPolicy::whereIn('p_ai', $planPoliciesIds)
                    ->update(['pterm_date' => $termDate, 'pstatus' => $pstatus]);

                $comment = "Rider Plan $planPolicyCount->web_display_name has beeen $keyword.";
                if ($standAlonePlanCount == 1) {
                    Policy::where('policy_id', $policyID)->update(['term_date' => $termDate, 'status' => $policyStatus]);
                    $comment = "Rider Plans $data->plan_names has beeen $keyword.";
                }
            }
            if ($status) {
                 $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
                $data = [
                    'elgb_act' => $action,
                    'elgb_act_date' => time(),
                    'elgb_comment' => $request->reason,
                    'elgb_agent' => $agentID,
                    'elgb_file_date' => date('Y-m-d'),
                    'elgb_term_date' => null,
                    'elgb_policyid' => $policyID,
                    'elgb_comment' => $comment,
                    'origin' => $origin
                ];
                PolicyUpdateHelper::updateEligibility($data);
                if ($request->sendEmail == 1 && $type == 'terminate') {
                    EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($policyID, 'Termination Notice', 'following plan has been terminated', 'terminate', 'TCR', $reason, $planPoliciesIds);
                } else if ($request->sendEmail == 1 && $type == 'withdraw') {
                    EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($policyID, 'Withdraw Notice Confirmation #'.$action, 'following plan has been withdrawn', 'withdraw', 'WDR', $reason, $planPoliciesIds);
                }
                return ['success' => "Plan rider ".strtolower($policyStatus)." successfully."];
            } else {
                return ['error' => "Rider policy ".strtolower($policyStatus)." failed."];
            }
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }

    }
}
