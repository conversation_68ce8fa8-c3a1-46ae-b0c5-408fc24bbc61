<?php

namespace App\Repositories\PolicyTerminateWithdraw;

use App\Dependent;
use App\DependentPolicy;
use App\GroupPlanFee;
use App\GroupPlans;
use App\Helpers\EmailSendHelper;
use App\Helpers\PolicyUpdateHelper;
use App\PlanPricing;
use App\PlanPricingDisplay;
use App\PlanTier;
use App\Policy;
use App\PlanPolicy;
use App\Plan;
use App\PolicyUpdate;
use App\TierUpdate;
use App\NbInvoice;
use App\NbInvoiceItem;
use App\NbPayment;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use Illuminate\Database\Eloquent\Model;
use PHPUnit\Framework\Exception;
use App\PlanOverview;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
use App\Helpers\GuzzleHelper;
use App\Helpers\CheckPlanTypeHelper;
use App\PlanPolicyMember;
use App\Repositories\MemberCard\MemberCardRepository;
use App\Repositories\MemberResource\MemberResourceFeatures;
use App\UserInfo;
use Illuminate\Support\Facades\Log;


class PolicyTierFeatures extends Model
{
    public function getBriefInfo($policyID)
    {
        try {
            $userPolicyData = UserInfoPolicyAddress::where('policy_id', $policyID)->get(['cfname', 'clname', 'policy_id', 'effective_date'])->first();
            $data['user_policy_info'] = ($userPolicyData) ? $userPolicyData : [];
            $data['policy_overview'] = [];
            if (isset($userPolicyData->policy_id)) {
                $data['policy_overview'] = PlanOverview::where('policy_id', $userPolicyData->policy_id)->where('pstatus', 1)->get();
            }
            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    protected function checkTierRestrictions($tier)
    {
        if (strtoupper($tier) == 'IC' || strtoupper($tier) == 'IS') {
            return ['IO'];
        } elseif (strtoupper($tier) == 'IF') {
            return ['IO', 'IC', 'IS'];
        } elseif (strtoupper($tier) == 'IO') {
            return 'IO Tier Cannot Be Changed';
        } else {
            return 'Policy Tier not found';
        }
    }

    protected function checkTier($tier)
    {
        $all_tier = ['IO', 'IC', 'IS', 'IF'];
        // $all_tier = array_diff($all_tier, [$tier]);
        return $all_tier;

    }

    protected function findDependent($newTier)
    {
        $dependent = [];
        if ($newTier == 'IC') {
            $dependent = ['S'];
        } elseif ($newTier == 'IS') {
            $dependent = ['C'];
        } elseif ($newTier == 'IO') {
            $dependent = ['C', 'S'];
        }
        return $dependent;
    }

    public function getPolicyTierInformation($request)
    {
        try {
            $policyID = $request->policy_id;
            $policyDetails = PlanOverview::where('policy_id', $policyID)->where('pstatus', 1)
                ->whereNotIn('pid', Plan::where('is_assoc', 1)->pluck('pid')->toArray())
                ->get(['tier', 'age1', 'age2', 'plan_pricing_type'])->first();
            if (empty($policyDetails)) {
                return ['error' => 'Policy Not Found'];
            }
            $policyDetails = $policyDetails->toArray();
            $tierList = $this->checkTier($policyDetails['tier']);
            if (!is_array($tierList)) {
                return ['error' => $tierList];
            }
            $validPlans = PlanPolicy::join('plan_pricing', 'plan_policies.plan_id', '=', 'plan_pricing.plan_pricing_id')
                ->join('plan_tier', 'plan_tier.idplan_tier', '=', 'plan_pricing.plan_id')
                ->where('policy_num', $policyID)
                ->where('pstatus', 1)
                ->where('plan_tier.status', 1)
                ->whereNotIn('plan_tier.pid_tier', Plan::where('is_assoc', 1)->pluck('pid')->toArray())
                ->pluck('plan_tier.pid_tier')->toArray();
            if(!empty($validPlans) && !PlanPricingDisplay::whereIn('pid', $validPlans)->whereIn('tier', $tierList)->count()){
                return ['error' => 'Plan has no other tier.'];
            }
            $prudentialItem = PlanOverview::where('policy_id', $policyID)->where('pstatus', '=', '1')->where('carrier_name','Prudential')->first(['carrier_name','age1','age2']);
            $pricingDetailsData = PlanPricingDisplay::whereIn('pid', $validPlans)->whereIn('tier', $tierList)->where('pricing_status', 1)->get();
            if($prudentialItem) {
                $ageWisePrudentials = $pricingDetailsData->where('cid','80')->where('age1','>=',$prudentialItem->age1)->where('age2','<=',$prudentialItem->age2);
                $pricingDetailsData = $pricingDetailsData->reject(function ($item) {
                    return $item->cid === 80;
                });
                $pricingDetailsData = $pricingDetailsData->concat($ageWisePrudentials);
                $pricingDetailsData = $pricingDetailsData->toArray();
            }
            $ggid = Policy::where('policy_id', $policyID)->pluck('eid')->first();
            $pricingDetails = [];
            foreach ($pricingDetailsData as $pricingData) {
                if ($policyDetails['plan_pricing_type'] == 2) {
                    if ($policyDetails['age1'] != $pricingData['age1'] && $policyDetails['age2'] != $pricingData['age2']) {
                        continue;
                    }
                }
                $pricingData['enrollmentFee'] = $this->getEnrollmentFees($ggid, $pricingData['plan_pricing_id']);
                $pricingData['ggid'] = $ggid;
                array_push($pricingDetails, $pricingData);
            }
            $data['pricingDetails'] = $pricingDetails;
            $data['policyBriefInfo'] = $this->getBriefInfo($policyID);;
            (empty($data['pricingDetails'])) ? $data['error'] = 'No eligible tier found.' : $data['success'] = 'List of elibigle plan.' ;
            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function setPolicyTier($request)
    {
        // DB::beginTransaction();
        try {
            $policyID = $request->policy_id;
            $isAssoc = Plan::where('is_assoc', 1)->pluck('pid')->toArray();
            // Check tier change effective date is triggered on paid invoice month or not
            $newTier = $request->new_tier;
            $today = date('Y-m-d');
            $policy_detail = Policy::find($request->policy_id);
            $time = time();
            if($request->pid) {
                $policyPlans = PlanOverview::where('policy_id', $policyID)
                    ->where('pid',$request->pid)
                    ->where('pstatus', 1)
                    ->whereNotIn('pid', $isAssoc)->first();
                if(empty($policyPlans)) {
                    return ['status' => 'error', 'message' => 'There is no requested plan associated with the given policy id {$policyID}', 'statusCode' => 422];
                }
                if ($newTier == '') {
                    return ['status' => 'error', 'message' => 'Policy Tier not found', 'statusCode' => 422];
                }

                $currentDate = date('Y-m-d');
                $nextMonthDay = date('Y-m-01', strtotime('+1 month', strtotime($currentDate)));

                $dependentsConditions = $this->findDependent($newTier);

                $oldPolicyID = $request->policy_id;

                $planID = $policyPlans->pid;
                 $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
                if(isset($policyPlans)) {
                    if($policyPlans->tier == $newTier) {
                        return ['status' => 'error', 'message' => 'The requested plan tier change is already in tier ' .$newTier, 'statusCode' => 422];
                    }

                    $newPolicy = $policy_detail->replicate();
                    $data = [
                        'elgb_act' => "ptun",
                        'elgb_act_date' => time(),
                        'elgb_agent' => $request->aid,
                        'elgb_file_date' => $today,
                        'origin' => $origin
                    ];
                    $nextPossibleEffectiveDate = self::getNextPossibleEffectiveDate($request->policy_id, $request->pid);
                    $efectiveDay = date('d', strtotime($policy_detail->effective_date)) - 1;
                    $newEffectiveDate = date('Y-m-d', (strtotime("$efectiveDay days", strtotime($nextPossibleEffectiveDate))));
                    $termDate = date('Y-m-d', (strtotime("-1 days", strtotime($newEffectiveDate))));
                    if ($newPolicy->save()) {
                        Log::channel('tierUpdate')->info(PHP_EOL ."Old policy $policy_detail->policy_id} has been cloned into new policy {$newPolicy->policy_id}.");
                        Log::channel('tierUpdate')->info(PHP_EOL ."Updated PTC flag to eligible invoice items for policy ID {$request->policy_id}.");
                        $oldPolicyActs = PolicyUpdate::where('elgb_policyid', $policyID)->chunk(200, function ($newPolicyActs) use ($newPolicy) {
                            $newPolicyActs->each(function ($newPolicyAct) use ($newPolicy) {
                                $newPolicyAct = $newPolicyAct->replicate();
                                $newPolicyAct->elgb_policyid = $newPolicy->policy_id;
                                $newPolicyAct->is_cloned = 1;
                                $newPolicyAct->save();
                            });
                        });
                        $comment = "The policy ID {$oldPolicyID},plan {$policyPlans->web_display_name}(ID {$policyPlans->plan_id}) has changed tier from {$policyPlans->tier} to {$newTier} with an effective date of {$newEffectiveDate}. The policy has been cloned into a new policy with ID {$newPolicy->policy_id} on {$today}.";
                        $data['elgb_policyid'] = $newPolicy->policy_id;
                        $data['elgb_new_effdate'] = $newEffectiveDate;
                        $data['elgb_old_effdate'] = $termDate;
                        $data['elgb_comment'] = $comment;
                        $data['elgb_policyid_ol'] = $policy_detail->policy_id;
                        PolicyUpdateHelper::updateEligibility($data);
                    }
                    if ($policy_detail->save()) {
                        $data['elgb_policyid'] = $policy_detail->policy_id;
                        $data['elgb_policyid_ol'] = 0;
                        $data['elgb_act'] = 'ptu';
                        $data['elgb_old_effdate'] = null;
                        $data['elgb_policyid_new'] = $newPolicy->policy_id;
                        $data['elgb_comment'] = $comment;
                        PolicyUpdateHelper::updateEligibility($data);
                    }
                    $newPolicyID = $newPolicy->policy_id;
                    $newPlanPricingID = PlanPricing::join('plan_tier', 'plan_tier.idplan_tier', '=', 'plan_pricing.plan_id')
                        ->where('plan_tier.tier', $newTier)
                        ->where('plan_pricing.pricing_status', 1)
                        ->where('plan_tier.pid_tier', $planID)
                        ->where('plan_tier.status', 1)
                        ->whereNotIn('plan_tier.pid_tier', Plan::where('is_assoc', 1)->pluck('pid')->toArray());
                    if ($policyPlans->plan_pricing_type == 2) {
                        $newPlanPricingID->where('plan_pricing.age1', $policyPlans->age1)->where('plan_pricing.age2', $policyPlans->age2);
                    }
                    $newPlanPricingID = $newPlanPricingID->pluck('plan_pricing_id')->first();
                    if ($newPlanPricingID != '') {
                        $olderPlan = PlanPolicy::where('policy_num', $oldPolicyID)->where('plan_id', $policyPlans->plan_id)->where('pstatus', '1')->first();
                        $olderPlan->policy_num = $newPolicyID;
                        $olderPlan->plan_id = $newPlanPricingID;
                        $olderPlan->save();
                        Log::channel('tierUpdate')->info(PHP_EOL ."Updated tier changed to {$newTier} in new policy.");
                         // Dependent Cloned
                        $olderPolicyDependent = DependentPolicy::where('policy_id', $oldPolicyID)->get();
                        foreach ($olderPolicyDependent as $dependent) {
                            $newDependent = $dependent->replicate();
                            $newDependent->policy_id = $newPolicyID;
                            $newDependent->save();
                        }
                        Log::channel('tierUpdate')->info(PHP_EOL ."Cloned dependent detail.");


                        $tier_update_data = [
                            'policy_id' => $newPolicyID,
                            'changed_from' => null,
                            'changed_to' => $newTier,
                            'comments' => $request->reason,
                            'elgb_old_effdate' => $termDate,
                            'elgb_new_effdate' => $newEffectiveDate,
                            'created_by' => $request->aid,
                            'old_ppid' => $policyPlans['plan_pricing_id'],
                            'new_ppid' => $newPlanPricingID,
                            'pid' => $planID,
                            'plan_name' => $policyPlans['web_display_name'],
                            'is_plan' => '1',
                        ];
                        $tier_update = TierUpdate::create($tier_update_data);
                        if($nextPossibleEffectiveDate >= $nextMonthDay ) {
                            $fetchAllInvoice = NbInvoice::where('invoice_policy_id', $request->policy_id)->where('invoice_start_date', '>=', $nextPossibleEffectiveDate)->get();
                            if(isset($fetchAllInvoice)) {
                                foreach($fetchAllInvoice as $invoice) {
                                    $url = config('app.purenroll_system.url') . "regenerate-invoice/{$invoice->invoice_id}";
                                    $responseJson = GuzzleHelper::getApi($url, []);
                                    $response = json_decode($responseJson, true);
                                    if ($response['statusCode'] == '200') {
                                        Log::channel('tierUpdate')->info(PHP_EOL ."Invoice regenerated successfully for Invoice ID " . $invoice->invoice_id . ".");
                                    } else {
                                        Log::channel('tierUpdate')->info(PHP_EOL ."There was error while regeneration invoice for Invoice ID " . $invoice->invoice_id . ".");
                                    }
                                }
                            }
                        }

                        $client = new Client();
                        Log::channel('tierUpdate')->info(PHP_EOL ."Commission Billing api called for generating single invoice.");
                        $url = config('app.purenroll_system.url') . "generate-single-invoice";
                        $payload = [
                            'policy_id' => $newPolicyID,
                            'effective_date' => [$newEffectiveDate],
                        ];
                        $responseJson = GuzzleHelper::postApi($url, [], $payload);
                        $pattern = '/\{.*?\}/';
                        if (preg_match($pattern, $responseJson, $matches)) {
                            $result_string = $matches[0];
                            if($result_string) {
                                $response = json_decode($result_string, true);
                            }
                        }

                        if($response && $response['status'] == "success") {
                            Log::channel('tierUpdate')->info(PHP_EOL ."Invoice genereated Successfully.");
                        } else {
                            Log::channel('tierUpdate')->info(PHP_EOL ."There was error while generating invoice.");
                        }
                    }
                }

                $message = ($newPolicyID)
                    ? "Successfully changed plan tier to {$newTier} effective from {$newEffectiveDate}. "
                    : "Successfully changed plan tier to {$newTier} effective from {$newEffectiveDate}. ";
                if ($request->sendEmail == 1) {
                    EmailSendHelper::sendPolicyActionEmail($policyID, 'Plan Tier Changed', "Policy Plan Tier Changed to $newTier", "policytier");
                }
                $policyID = $newPolicyID;

                $existingPlanPoliciesMember = PlanPolicyMember::where('policy_id', $oldPolicyID)->where('pid',$request->pid)->where('is_updateMemberCard','0')->where('email_sent_at',null)->first();
                if($existingPlanPoliciesMember) {
                    $planOverview = PlanOverview::where('policy_id', $policyID)->where('pid',$planID)->select('brand','cid','p_ai','pid','policy_id','policy_userid')->first();
                    $existingPlanPoliciesMember->policy_id = $policyID;
                    $existingPlanPoliciesMember->pid = $planID;
                    $existingPlanPoliciesMember->p_ai = $planOverview->p_ai;
                    $existingPlanPoliciesMember->brand = $planOverview->brand;
                    $existingPlanPoliciesMember->cid = $planOverview->cid;
                    $existingPlanPoliciesMember->save();
                }
                else{
                    $planOverview = PlanOverview::where('policy_id', $policyID)->where('pid',$planID)->select('brand','cid','p_ai','pid','policy_id','policy_userid')->first();
                    if(in_array($planOverview->cid, ["11", "77", "59", "65", "67", "47"])){
                        PolicyUpdateHelper::addPlanPoliciesMember($policyID, $planID);
                    }
                    else{
                        self::sendMemberCardEmail($policyID);
                    }
                }
            } else {
                $isIHAPlans = CheckPlanTypeHelper::checkIHAPlan($request->policy_id);
                if($isIHAPlans) {
                    $nextPossibleEffectiveDate = self::getNextPossibleEffectiveDate($request->policy_id);
                    $efectiveDay = date('d', strtotime($policy_detail->effective_date)) - 1;
                    $newEffectiveDate = date('Y-m-d', (strtotime("$efectiveDay days", strtotime($nextPossibleEffectiveDate))));
                    $termDate = date('Y-m-d', (strtotime("-1 days", strtotime($newEffectiveDate))));
                    $PolicyPlans = PlanOverview::where('policy_id', $policyID)->where('pstatus', 1)->whereNotIn('pid', $isAssoc)->get()->toArray();
                    foreach ($PolicyPlans as $key => $PolicyPlan) {
                        $tier_update = "";
                        if($PolicyPlan['tier'] != $newTier) {
                            $planID = $PolicyPlan['pid'];
                            $oldPricingID = $PolicyPlan['plan_pricing_id'];
                            $newPlanPricingID = PlanPricing::join('plan_tier', 'plan_tier.idplan_tier', '=', 'plan_pricing.plan_id')
                                ->where('plan_tier.tier', $newTier)
                                ->where('plan_pricing.pricing_status', 1)
                                ->where('plan_tier.pid_tier', $planID)
                                ->where('plan_tier.status', 1)
                                ->whereNotIn('plan_tier.pid_tier', Plan::where('is_assoc', 1)->pluck('pid')->toArray());
                            if ($PolicyPlan['plan_pricing_type'] == 2) {
                                $newPlanPricingID->where('plan_pricing.age1', $PolicyPlan['age1'])->where('plan_pricing.age2', $PolicyPlan['age2']);
                            }
                            $newPlanPricingID = $newPlanPricingID->pluck('plan_pricing_id')->first();
                            $tierChangedStatus = PlanPolicy::where('policy_num', $policyID)->where('plan_id', $oldPricingID)->where('pstatus', '1')->update(['plan_id' => $newPlanPricingID, 'date' => $time, 'ppptype' => 'price_male_nons', 'pstatus' => 1]);

                            if ($newPlanPricingID != '') {
                                $tier_update_data['policy_id'] = $policyID;
                                $tier_update_data['old_ppid'] = $oldPricingID;
                                $tier_update_data['new_ppid'] = $newPlanPricingID;
                                $tier_update_data['pid'] = $planID;
                                $tier_update_data['changed_to'] = $newTier;
                                $tier_update_data['elgb_old_effdate'] = $termDate;
                                $tier_update_data['elgb_new_effdate'] = $newEffectiveDate;
                                $tier_update_data['plan_name'] = $PolicyPlan['web_display_name'];
                                $tier_update_data['comments'] = $request->reason;
                                $tier_update_data['created_by'] = $request->aid;
                                $tier_update_data['status'] = "1";
                                $tier_update = TierUpdate::where('policy_id', $policyID)->where('pid', $planID)->where('elgb_new_effdate', $newEffectiveDate)->where('is_plan','0')->first();
                                if($tier_update) {
                                    $tier_update->update($tier_update_data);
                                    Log::channel('tierUpdate')->info(PHP_EOL ."Tier update data updated successfully.");

                                } else {
                                    $tier_update = TierUpdate::create($tier_update_data);
                                    Log::channel('tierUpdate')->info(PHP_EOL ."Tier update data created successfully.");
                                }
                                Log::channel('tierUpdate')->info(PHP_EOL ."Tier update data created successfully.");
                            }
                            $comment = "The policy tier for the {$PolicyPlan['web_display_name']} plan was changed from {$PolicyPlan['tier']} to {$newTier} on {$nextPossibleEffectiveDate}.";
                            $data = [
                                'elgb_act' => "bpc",
                                'elgb_act_date' => $time,
                                'elgb_comment' => $comment,
                                'elgb_agent' => $request->aid,
                                'elgb_file_date' => $today,
                                'elgb_term_date' => null,
                                'elgb_policyid' => $policyID
                            ];
                            Log::channel('tierUpdate')->info(PHP_EOL ."Policy updates created successfully.");
                            PolicyUpdateHelper::updateEligibility($data);
                        }
                    }
                } else {
                    $currentMonth = date('Y-m');
                    if (strtotime($request->new_effective_date) <= strtotime($currentMonth)) {
                        return ['status' => 'error', 'message' => 'New effective month should be greater than current month.', 'statusCode' => 422];
                    }
                    $nbInvoices = NbInvoice::where('invoice_policy_id', $request->policy_id)->get();
                    if(!$nbInvoices->isEmpty()) {
                        foreach($nbInvoices as $key => $nb_invoice) {
                            if($nb_invoice->invoice_payment_status != "UNPAID" || ($nb_invoice->invoice_payment_status == "UNPAID" && $nb_invoice->payment_party_status == "PROCESSING") || $nb_invoice->invoice_payment_status == "UNPAID" && self::checkIfPaymentExists($nb_invoice->invoice_id)) {
                                $newEffectiveDate = date("Y-m-d", strtotime($request->new_effective_date));
                                $invoiceStartDate = date("Y-m-01", strtotime($nb_invoice->invoice_end_date));
                                if ($newEffectiveDate <= $invoiceStartDate) {
                                    $monthName = date('F', strtotime($nb_invoice->invoice_end_date));
                                    $year = date('Y', strtotime($nb_invoice->invoice_end_date));
                                    $message = "The new effective month should be greater than upcoming {$monthName} {$year}.";
                                    return ['status' => 'error', 'message' => $message, 'statusCode' => 422];
                                }
                            }
                        }
                    }
                    $tier = PlanOverview::where('policy_id', $policyID)
                        ->where('pstatus', 1)
                        ->whereNotIn('pid', $isAssoc)->get()
                        ->pluck('tier')->unique()->toArray();
                    $oldTier = array_values($tier);
                    $oldTier_encoded = json_encode($oldTier);
                    $tierList = $this->checkTier($tier);
                    if (!is_array($tierList)) {
                        return ['status' => 'error', 'message' => $tierList, 'statusCode' => 422];
                    }

                    $newTier = $request->new_tier;
                    $dependentsConditions = $this->findDependent($newTier);
                    if ($newTier == '') {
                        return ['status' => 'error', 'message' => 'Policy Tier not found', 'statusCode' => 422];
                    }
                    if (!in_array($newTier, $tierList)) {
                        return ['status' => 'error', 'message' => "$tier tier cannot be changed to $newTier tier", 'statusCode' => 422];
                    }
                    $policyPlans = PlanOverview::where('policy_id', $policyID)
                        ->where('pstatus', 1)
                        ->whereNotIn('pid', $isAssoc)->get()
                        ->pluck('pid')->unique()->toArray();
                    $policyPlans_encoded = json_encode($policyPlans);
                    $time = time();
                    $policy_detail = Policy::find($request->policy_id);
                    $efectiveDay = date('d', strtotime($policy_detail->effective_date)) - 1;
                    $newEffectiveDate = date('Y-m-d', (strtotime("$efectiveDay days", strtotime($request->new_effective_date))));
                    $termDate = date('Y-m-d', (strtotime("-1 days", strtotime($newEffectiveDate))));
                    $date_efile = date('Y-m-d');
                    $comment = "Policy Tier Changed from {$oldTier_encoded} to {$newTier}, and changed on {$date_efile}";
                    $tier_update_data = [
                        'policy_id' => $policyID,
                        'changed_from' => null,
                        'changed_to' => $newTier,
                        'comments' => $request->reason,
                        'elgb_old_effdate' => $termDate,
                        'elgb_new_effdate' => $newEffectiveDate,
                        'created_by' => $request->aid,
                    ];
                    $PolicyPlans = PlanOverview::where('policy_id', $policyID)->where('pstatus', 1)->whereNotIn('pid', $isAssoc)->get()->toArray();
                    foreach ($PolicyPlans as $key => $PolicyPlan) {
                        $planID = $PolicyPlan['pid'];
                        $oldPricingID = $PolicyPlan['plan_pricing_id'];
                        $newPlanPricingID = PlanPricing::join('plan_tier', 'plan_tier.idplan_tier', '=', 'plan_pricing.plan_id')
                            ->where('plan_tier.tier', $newTier)
                            ->where('plan_pricing.pricing_status', 1)
                            ->where('plan_tier.pid_tier', $planID)
                            ->where('plan_tier.status', 1)
                            ->whereNotIn('plan_tier.pid_tier', Plan::where('is_assoc', 1)->pluck('pid')->toArray());
                        if ($PolicyPlan['plan_pricing_type'] == 2) {
                            $newPlanPricingID->where('plan_pricing.age1', $PolicyPlan['age1'])->where('plan_pricing.age2', $PolicyPlan['age2']);
                        }
                        $newPlanPricingID = $newPlanPricingID->pluck('plan_pricing_id')->first();
                        if ($newPlanPricingID != '') {
                            $tier_update_data['old_ppid'] = $oldPricingID;
                            $tier_update_data['new_ppid'] = $newPlanPricingID;
                            $tier_update_data['pid'] = $planID;
                            $tier_update_data['changed_from'] = null;
                            $tier_update_data['plan_name'] = $PolicyPlan['web_display_name'];
                            $tier_update = TierUpdate::where('policy_id', $policyID)->where('pid', $planID)->where('elgb_new_effdate', $newEffectiveDate)->where('is_plan','0')->first();
                            if($tier_update) {
                                $tier_update->update($tier_update_data);
                                Log::channel('tierUpdate')->info(PHP_EOL ."Tier update data updated successfully.");

                            } else {
                                $tier_update = TierUpdate::create($tier_update_data);
                                Log::channel('tierUpdate')->info(PHP_EOL ."Tier update data created successfully.");
                            }
                        }
                    }
                    if (!$tier_update) {
                        return ['status' => 'error', 'message' => 'Failed changing tier', 'statusCode' => 422];
                    }
                     // Remove []
                    $oldTier_encoded = str_replace(['[', ']'], '', $oldTier_encoded);

                    // Remove double quotes
                    $oldTier_encoded = str_replace('"', '', $oldTier_encoded);
                }

                $message = ($tier_update)
                    ? "Successfully changed policy tier to {$newTier}. "
                    : "Successfully changed policy tier to {$newTier}. ";
                if ($request->sendEmail == 1) {
    //                EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($policyID, 'Policy Tier Changed', "Policy Changed from $oldTier_encoded to $newTier ", "policytier");
                    EmailSendHelper::sendPolicyActionEmail($policyID, 'Policy Tier Changed', "Policy Tier Changed to $newTier", "policytier");                }
                }
            // DB::commit();
            return ['status' => 'success', 'message' => $message, 'data' => ['new_policy_id' => $policyID], 'statusCode' => 200];
        } catch (Exception $e) {
            // DB::rollBack();
            return ['status' => 'error', 'message' => $e->getMessage(), 'statusCode' => $e->getCode()];
        }
    }

    protected function getEnrollmentFees($grp, $planpricingID)
    {
        if ($grp == '') {
            $grp = 77;
        }
        $sum = GroupPlanFee::select("gefees", "aefees", "cefees", "cafees")->where('plan_pricing_id', $planpricingID)->where('group_id', $grp)->get()->toArray();
        if (count($sum) > 0) {
            return (float)$sum[0]['gefees'] + (float)$sum[0]['aefees'] + (float)$sum[0]['cefees'] + (float)$sum[0]['cafees'];
        } else {
            return 0;
        }
    }

    protected function clonePolicyAndData($request)
    {
        try {
            $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
            $oldPolicy = Policy::find($request->policy_id);
            /* getting efffective day from older policy and adding it to year & month getting from request */
            $efectiveDay = date('d', strtotime($oldPolicy->effective_date)) - 1;
            $newEffectiveDate = date('Y-m-d', (strtotime("$efectiveDay days", strtotime($request->new_effective_date))));
            $termDate = date('Y-m-d', (strtotime("-1 days", strtotime($newEffectiveDate))));
            if(strtotime($oldPolicy->effective_date) > strtotime($newEffectiveDate)) {
                return ['status' => 'error', 'message'=>"New effective date should be equal or higher than old one."];
            } else if(strtotime($oldPolicy->effective_date) == strtotime($newEffectiveDate)) {
                $termDate = $oldPolicy->effective_date;
            }

            $oldPolicyID = $request->policy_id;
            $oldPolicy = Policy::find($request->policy_id);
            $newPolicy = $oldPolicy->replicate();
            /* terming older policy and set term date */
            $oldPolicy->status = 'TERMED';
            $oldPolicy->term_date = $termDate;
            /* setting new effective date for cloned policy */
            $newPolicy->effective_date = $newEffectiveDate;
            $newPolicy->edate = time();
            $newPolicy->bill_date = 30;
            $data = [
                'elgb_act' => "new",
                'elgb_act_date' => time(),
                'elgb_agent' => $request->aid,
                'elgb_file_date' => date('Y-m-d'),
                'origin' => $origin
            ];
            if ($newPolicy->save()) {
                $data['elgb_policyid'] = $newPolicy->policy_id;
                $data['elgb_new_effdate'] = $newEffectiveDate;
                $data['elgb_old_effdate'] = $oldPolicy->effective_date;
                $data['elgb_comment'] = date('Y-m-d');
                PolicyUpdateHelper::updateEligibility($data);
            }
            if ($oldPolicy->save()) {
                $data['elgb_policyid'] = $oldPolicy->policy_id;
                $data['elgb_term_date'] = $termDate;
                $data['elgb_old_effdate'] = $oldPolicy->effective_date;
                $data['elgb_act'] = 'bpc';
                $data['elgb_comment'] = ($request->reason) ? $request->reason : 'Terminated old policy as tier gets changed.';
                PolicyUpdateHelper::updateEligibility($data);
            }
            /* clone plan of policy */
            $newPolicyID = $newPolicy->policy_id;
            if($newPolicyID) {
                $olderPlan = PlanPolicy::where('policy_num', $oldPolicyID)->where('pstatus', '1')->get();
                foreach ($olderPlan as $plan) {
                    $newPlan = $plan->replicate();
                    //$newPlan->p_ai = null;
                    $newPlan->policy_num = $newPolicyID;
                    $newPlan->peffective_date = $newEffectiveDate;
                    $newPlan->save();
                }
                PlanPolicy::where('policy_num', $oldPolicyID)->where('pstatus', '1')->update(['pstatus' => '2', 'pterm_date' => $termDate]);
                /*clone dependent */
                $olderPolicyDependent = DependentPolicy::where('policy_id', $oldPolicyID)->get();
                foreach ($olderPolicyDependent as $dependent) {
                    $newDependent = $dependent->replicate();
                    //$newDependent->iddependents_policies_ai = null;
                    $newDependent->policy_id = $newPolicyID;
                    $newDependent->save();
                }
            } else {
                return ['status' => 'error', 'message'=>'Failed creating new policy.'];
            }
            return $newPolicyID;
        } catch (Exception $e) {
            return ['status' => 'error', 'statusCode' => $e->getCode(), 'message' => $e->getMessage()];
        }
    }

    public static function checkIfPaymentExists($invoice_id)
    {
        return NbPayment::where('invoice_id', $invoice_id)
            ->count() > 0;
    }

    public static function getNextPossibleEffectiveDate($policyID, $pid = null)
    {
        $policy_detail = Policy::find($policyID);
        $nbInvoices = NbInvoice::where('invoice_policy_id', $policyID)->get();
        if(!$nbInvoices->isEmpty()) {
            foreach($nbInvoices as $key => $nb_invoice) {
                if($nb_invoice->invoice_payment_status != "UNPAID" || ($nb_invoice->invoice_payment_status == "UNPAID" && $nb_invoice->payment_party_status == "PROCESSING") || $nb_invoice->invoice_payment_status == "UNPAID" && self::checkIfPaymentExists($nb_invoice->invoice_id)) {
                    $invoiceStartDate = date("Y-m-01", strtotime($nb_invoice->invoice_end_date));
                    $nextPossibleEffectiveDate = date("Y-m-01", strtotime($invoiceStartDate));
                    $nextPossibleEffectiveDate = date("Y-m-01", strtotime($nextPossibleEffectiveDate . " +1 month"));
                    if ($policy_detail->pay_type == 'annual') {
                        $nextPossibleEffectiveDate = date("Y-m-d", strtotime($nextPossibleEffectiveDate . " +12 months"));
                    }

                }
            }
        }
        $currentDay = date("d");
        $currentDate = date('Y-m-d');
        $nextMonth = date("m", strtotime("+1 month"));
        $nextMonthDay = date('Y-m-01', strtotime('+1 month', strtotime($currentDate)));
        if(isset($nextPossibleEffectiveDate)) {
            if((date("m", strtotime($nextPossibleEffectiveDate)) == $nextMonth) && ($currentDay >= '18')) {
                $nextPossibleEffectiveDate = date("Y-m-01", strtotime($nextPossibleEffectiveDate . " +1 month"));
            } elseif((date("Y-m-d", strtotime($nextPossibleEffectiveDate)) > (date("Y-m-d", strtotime($nextMonthDay)))))  {
                $nextPossibleEffectiveDate = $nextPossibleEffectiveDate;
            } else {
                if($currentDay < '18') {
                    $nextPossibleEffectiveDate = date('Y-m-01', strtotime('+1 month', strtotime($currentDate)));
                } else {
                    $nextPossibleEffectiveDate = date('Y-m-01', strtotime('+2 month', strtotime($currentDate)));
                }
            }
        } else {
            if($currentDay < '18') {
                $nextPossibleEffectiveDate = date('Y-m-01', strtotime('+1 month', strtotime($currentDate)));
            } else {
                $nextPossibleEffectiveDate = date('Y-m-01', strtotime('+2 month', strtotime($currentDate)));
            }
        }

        $previous_nextPossibleEffectiveDate = date('Y-m-t', strtotime('-1 month', strtotime($nextPossibleEffectiveDate)));
        if ($policy_detail->pay_type == 'annual') {
            $annualInvoices = NbInvoice::where('invoice_policy_id', $policyID)
                                ->where('invoice_start_date', '<=', $previous_nextPossibleEffectiveDate)
                                ->where('invoice_end_date', '>=', $previous_nextPossibleEffectiveDate)
                                ->where('invoice_payment_status', '!=', 'PAID')
                                ->get();

            $monthlyInvoices = NbInvoice::where('invoice_policy_id', $policyID)
                ->where('invoice_end_date', '<=', $previous_nextPossibleEffectiveDate)
                ->where('invoice_payment_status', '!=', 'PAID')
                ->get();

            $previous_UnpaidInvoices = $annualInvoices->merge($monthlyInvoices)->unique('invoice_id')->sortBy('invoice_end_date');
        } else {
            $previous_UnpaidInvoices = NbInvoice::where('invoice_policy_id', $policyID)->where('invoice_end_date', '<=' ,$previous_nextPossibleEffectiveDate)->get();
        }
        if(isset($previous_UnpaidInvoices)) {
            foreach($previous_UnpaidInvoices as $previous_invoice) {
                if($previous_invoice->invoice_payment_status != "UNPAID" || ($previous_invoice->invoice_payment_status == "UNPAID" && $previous_invoice->payment_party_status == "PROCESSING") || $previous_invoice->invoice_payment_status == "UNPAID" && self::checkIfPaymentExists($previous_invoice->invoice_id)){
                } else {
                    if((date("Y-m-d", strtotime($previous_invoice->invoice_end_date)) > (date("Y-m-d", strtotime($nextMonthDay))))) {
                        if($pid == null) {
                            NbInvoiceItem::where('invoice_id', $previous_invoice->invoice_id)->update(['is_ptc' => "1"]);
                        } else {
                            $invoice_items = NbInvoiceItem::where('invoice_id', $previous_invoice->invoice_id)->where('plan_id', $pid)->first();
                            if($invoice_items) {
                                $invoice_items->is_ptc = "1";
                                $invoice_items->save();
                            }
                        }
                        if ($policy_detail->pay_type == 'annual') {
                            $nextPossibleEffectiveDate = date('Y-m-01',strtotime($previous_invoice->invoice_end_date));
                            $nextPossibleEffectiveDate = date('Y-m-01', strtotime('+1 month', strtotime($nextPossibleEffectiveDate)));
                        }
                    } elseif(!(date("Y-m-d", strtotime($previous_invoice->invoice_end_date)) > (date("Y-m-d", strtotime($nextMonthDay))))) {
                        if($pid == null) {
                            NbInvoiceItem::where('invoice_id', $previous_invoice->invoice_id)->update(['is_ptc' => "1"]);
                        } else {
                            $invoice_items = NbInvoiceItem::where('invoice_id', $previous_invoice->invoice_id)->where('plan_id', $pid)->first();
                            if($invoice_items) {
                                $invoice_items->is_ptc = "1";
                                $invoice_items->save();
                            }
                        }
                        if ($policy_detail->pay_type == 'annual') {
                            $nextPossibleEffectiveDate = date('Y-m-01',strtotime($previous_invoice->invoice_end_date));
                            $nextPossibleEffectiveDate = date('Y-m-01', strtotime('+1 month', strtotime($nextPossibleEffectiveDate)));
                        }
                    }

                }
            }
        }
        return $nextPossibleEffectiveDate;
    }

    public function sendMemberCardEmail($policy){
        $policyDetails = UserInfoPolicyAddress::where('policy_id', $policy)->select('policy_id','userid','agent_email','cemail','cfname','cmname','clname','effective_date','agent_id')->first();
        if(!$policyDetails instanceof UserInfoPolicyAddress) {
            Log::channel('memberCard')->info(PHP_EOL ."Policy Detail Not Found.");
        }
        $member = UserInfo::where('userid',$policyDetails->userid)->first();
        $t_array = array();

        if ($member instanceof UserInfo) {
            foreach ($member->getPolicyList as $singlePolicy) {
                if ($policy != $singlePolicy->policy_id) {
                    continue;
                }
                $plans = $singlePolicy->getPolicyPlan;
                $memberResource = new MemberResourceFeatures();
                $t_array = $memberResource->getPlanCards($plans, $singlePolicy, $t_array, $member);
            }
        }
        $tempArray = array();
        $uniqueData = array();
        foreach ($t_array as $val) {
            if (isset($val['filename'])) {
                if (in_array($val['filename'], $tempArray) || empty($val)) {
                    continue;
                }
                array_push($tempArray, $val['filename']);
                array_push($uniqueData, $val);
            }
        }
        $t_array = $uniqueData;

        if(!empty($t_array)){

            $p_ai_values = array_unique(array_column($t_array, 'p_ai'));
                    $cards = "";
                    foreach($t_array as $list) {
                        $cards .= "<li><a href=\"" . $list['url'] . "\" target=\"_blank\" rel=\"noopener noreferrer\">" . $list['filename'] . "</a></li>";
                    }
                    $userEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policyDetails->cemail;
                    $agentEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policyDetails->agent_email;
                    $member_name = $policyDetails->getFullNameAttribute();
                    if(self::checkFirstEmail($policy)) {
                        $subject = "New Digital Documents Available ";
                    } else {
                        $subject = "Digital Documents Available ";
                    }
                    $data = [
                        'email' => $userEmail,
                        'agent_email' => $agentEmail,
                        'member_name' => $member_name,
                        'cards' => $cards,
                        'subject' => $subject,
                        'agent_id' => $policyDetails->agent_id
                    ];
                    $memberCardRepository = new MemberCardRepository();
                    $mailStatus = $memberCardRepository->sendEmailWithMessageCenter($data);
                    if($mailStatus) {
                        $today = date('Y-m-d');
                         $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
                        $plan_policy_member = PlanPolicyMember::where('policy_id',$policy)->update(['email_sent' => 'Y','email_sent_at' => $today]);
                        PlanPolicy::where('policy_num',$policy)->update(['is_email_sent' => 'Y']);
                            $time = time();
                            $policyUpdateData = [
                                'elgb_act' => "MEMCARD",
                                'elgb_act_date' => $time,
                                'elgb_comment' => "Member Card Send Successfully.",
                                'elgb_file_date' => $today,
                                'elgb_term_date' => null,
                                'elgb_policyid' => $policy,
                                'elgb_agent' => 'auto',
                                'origin' => $origin
                            ];
                            PolicyUpdateHelper::updateEligibility($policyUpdateData);
                        Log::channel('memberCard')->info(PHP_EOL ."Member Card Send Successfully for Policy Id " . $policyDetails->policy_id . ".");
                    } else {
                        PlanPolicyMember::whereIn('p_ai', $p_ai_values)->whereNot('email_sent','Y')->update(['email_sent' => 'N']);
                        PlanPolicy::where('policy_num',$policy)->update(['is_email_sent' => 'N']);
                        Log::channel('memberCard')->info(PHP_EOL ."Mail Sending Failed for Policy Id " . $policyDetails->policy_id . ".");
                    }
                }
    }

    public static function checkFirstEmail($policy) {
        $fetchPolicy = PolicyUpdate::where('elgb_policyid',$policy)->where('elgb_act','MEMCARD')->first();
        if(isset($fetchPolicy)){
            return true;
        } else {
            return false;
        }
    }
}
