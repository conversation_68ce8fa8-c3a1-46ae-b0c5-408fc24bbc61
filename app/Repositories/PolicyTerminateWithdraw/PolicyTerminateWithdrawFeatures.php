<?php

namespace App\Repositories\PolicyTerminateWithdraw;

use App\Address;
use App\EmailPreference;
use App\Helpers\CommonHelper;
use App\Helpers\DecryptEcryptHelper;

use App\Helpers\EmailSendHelper;
use App\Helpers\PolicyUpdateHelper;
use App\Helpers\SendNotificationHelper;
use App\Mail\Termination;
use App\Mail\Withdrawn;
use App\Policy;
use App\PlanPolicy;
use App\PolicyUpdate;
use App\Service\MessageService;
use App\Traits\SyncMember;
use App\UserInfo;
use App\UserInfoPolicyAddress;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Exception;
use App\PlanOverview;
use App\UserLogin;
use App\CompanyInformation;
use App\EmailSetting;
use App\Helpers\InvoiceHelper;
use App\NewAssocFee;
use App\PlanZip;
use App\Repositories\Plans\PlanChangeOnAddress;
use App\Repositories\PolicyFeatures;
use App\Repositories\V3\Archive\ArchiveRepository;
use App\Traits\ResponseMessage;
use Illuminate\Support\Facades\DB;
use App\GroupInfo;

class PolicyTerminateWithdrawFeatures extends Model
{
    use SyncMember;
    use ResponseMessage;
    private  $PlanChangeOnAddress;
    private  $archiveRepository;
    private  $policyFeature;

    public function __construct()
    {
        $this->PlanChangeOnAddress = new PlanChangeOnAddress();
        $this->archiveRepository = new ArchiveRepository();
        $this->policyFeature = new PolicyFeatures();
    }

    protected function setRequest($request)
    {
        $this->tdate = $request->tdate;
        $this->reason = $request->reason;
        $this->policyID = $request->policy_id;
        $this->agentID = $request->aid;
        $this->time = $time = time();
        $this->dateEFile = date('Y-m-d');
        $this->sendEmail = $request->sendEmail;
        $this->action = $request->action;
    }

    protected function postEligibility()
    {
        $data['elgb_policyid'] = $this->policyID;
        $data['elgb_act'] = $this->action;
        $data['elgb_act_date'] = $this->time;
        $data['elgb_comment'] = $this->reason;
        $data['elgb_agent'] = $this->agentID;
        $data['elgb_file_date'] = $this->dateEFile;
        $data['elgb_term_date'] = $this->tdate;
        PolicyUpdateHelper::updateEligibility($data);
        return $this->successResponse("Successfully Added " . strtoupper($data['elgb_act']));
    }

    public function validateTermDate($policyID, $termDate)
    {
        $effectiveDate = Policy::where('policy_id', $policyID)->pluck('effective_date')->first();
        if ($effectiveDate > $termDate) {
            return false;
        }
        return true;
    }

    public function setTerminatePolicy($request)
    {
        $this->setRequest($request);
        try {
            if (!$this->validateTermDate($this->policyID, $this->tdate)) {
                return ['error' => 'Termination date should be higher than effective date.'];
            }
            $tdate = strtotime($this->tdate);
            $effectiveDate = Policy::where('policy_id', $this->policyID)->pluck('effective_date')->first();
            $eday = explode('-',$effectiveDate)[2];
            $effDates = ['1','31','32'];
            if(in_array($eday,$effDates) && date('d', $tdate) != date('t', $tdate)){
                return $this->failedResponse("Termination date should be the last of month!");
            }

            if(!in_array($eday,$effDates) &&  date('t', $tdate) != ($eday -1)){
                return $this->failedResponse("Termination date should be only end of service period.");
            }

            $policyDetails = UserInfoPolicyAddress::where('policy_id', $this->policyID)->get()->first();
            if (empty($policyDetails)) {
                return $this->failedResponse('Policy not found');
            }
            Policy::where('policy_id', $this->policyID)->update(['term_date' => $this->tdate, 'status' => 'TERMED','Approval'=> 0]);

            /* if chosen termination date is smaller than the effective date of that plan each plan, then term date will be their effective date */
            $this->setPlanTermWithdrawStatus(2);

            // $checkOtherPolicies = self::checkOtherActivePolicies($policyDetails->userid, $this->policyID);
            // if($checkOtherPolicies) {
            //     $this->archiveRepository->deleteMember($policyDetails->userid);
            // }

            $reasonFullName = EmailPreference::where('pref-code', $this->action)->pluck('pref-name')->first();
            $groupInfo = GroupInfo::where('gid',$policyDetails->eid)->get();
            if (is_object($groupInfo)) {
                $groupInfo = $groupInfo->toArray();
            }
            $groupEmail = isset($groupInfo[0]['gemail']) ? $groupInfo[0]['gemail'] : null;
            SendNotificationHelper::sendTerminationNotification($policyDetails, $this->tdate, $reasonFullName);
            $reasonForEmail = EmailPreference::where('pref-code', $this->reason)->pluck('pref-name')->first();
            $this->reason = ($reasonForEmail != '') ? $reasonForEmail : $this->reason;
            if ($request->sendEmail == 1) {
            EmailSendHelper::sendPolicyActionEmail($this->policyID, 'Termination Notice', 'following programs has been terminated', 'terminate', strtoupper($this->action), $this->reason,[],$groupEmail);
            }
            return $this->postEligibility();
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function setWithdrawPolicy($request)
    {
        $this->setRequest($request);
        try {
            $policyDetails = UserInfoPolicyAddress::where('policy_id', $this->policyID)->get()->first();
            if (empty($policyDetails)) {
                return $this->failedResponse('Policy not found');
            }

            Policy::where('policy_id', $this->policyID)->update(['term_date' => $policyDetails->effective_date, 'status' => 'WITHDRAWN','Approval'=> 0]);

            /* if chosen termination date is smaller than the effective date of that plan each plan, then term date will be their effective date */

            $plansList = PlanPolicy::where('policy_num', $this->policyID)->get();
            foreach ($plansList as $plans) {
                $plans->pstatus = 3;
                $plans->pterm_date = $plans->peffective_date;
                $plans->is_approved = 0;
                $plans->save();
            }
            // $checkOtherPolicies = self::checkOtherActivePolicies($policyDetails->userid, $this->policyID);
            // if($checkOtherPolicies) {
            //     // $deleteFromSSO = CommonHelper::deleteSSOUser($policyDetails->cemail, 'M');
            //     $this->archiveRepository->deleteMember($policyDetails->userid);
            // }

            $reasonFullName = EmailPreference::where('pref-code', $this->action)->pluck('pref-name')->first();
            SendNotificationHelper::sendWithdrawnNotification($policyDetails, $this->tdate, $reasonFullName);
            $reasonForEmail = EmailPreference::where('pref-code', $this->reason)->pluck('pref-name')->first();
            $this->reason = ($reasonForEmail != '') ? $reasonForEmail : $this->reason;
            $ifPolicyHasExtraHealth = $this->policyFeature->checkIfPolicyHasExtraHealth($this->policyID);
            if ($ifPolicyHasExtraHealth) {
                Log::channel('refund_withdrawn_extra_plan_payments_log')
                    ->info("An extra health plan was found for the withdrawn policy (policy_id: {$this->policyID}).");

                $ifPolicyWithdrawnWithinLimit = $this->policyFeature->checkIfPolicyWithdrawnWithinLimit($this->policyID);
                if ($ifPolicyWithdrawnWithinLimit) {
                    Log::channel('refund_withdrawn_extra_plan_payments_log')
                        ->info("The enrollment date is within five days. Proceeding with the refund for policy_id: {$this->policyID}.");

                    InvoiceHelper::refundToPaymentSystem($this->policyID);
                } else {
                    Log::channel('refund_withdrawn_extra_plan_payments_log')
                        ->info("The enrollment date exceeds five days. Refund will not be processed for policy_id: {$this->policyID}.");
                }
            }
            if ($request->sendEmail == 1) {
                EmailSendHelper::sendPolicyActionEmail($this->policyID, 'Withdraw Notice Confirmation', 'following programs have been withdrawn', 'withdraw', strtoupper($this->action), $this->reason);
            }
            return $this->postEligibility();
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getBriefInfo($policyID)
    {
        try {
            $userPolicyData = UserInfoPolicyAddress::where('policy_id', $policyID)->get(['cfname', 'clname', 'policy_id', 'effective_date'])->first();
            $data['user_policy_info'] = $userPolicyData;
            $data['policy_overview'] = [];
            if (isset($userPolicyData[0]['policy_id'])) {
                $data['policy_overview'] = PlanOverview::where('policy_id', $userPolicyData[0]['policy_id'])->where('pstatus', 1)->get();
            }
            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function getPolicyInformation($uid)
    {
        try {
            return UserInfo::where('userid', $uid)->get();
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function updatePolicyInformation($request)
    {
        try {
            $userDetails = UserInfoPolicyAddress::where('userid', $request->userid)->first();
            if (!$userDetails instanceof UserInfoPolicyAddress) {
                return ['error' => 'User not found.'];
            }
            $encryptedSSN = trim(DecryptEcryptHelper::encryptInfo($request->cssn));
            $ssnCheck = UserInfoPolicyAddress::where('cssn', "$encryptedSSN")
                ->where('userid', '!=', $request->userid)
                ->where('status', 'ACTIVE')
                ->where('cfname', 'NOT LIKE', "%test%")
                ->count();
            if ($ssnCheck) {
                return ['error' => 'SSN already exists.Same SSN is associated with another user.'];
            }

            if (UserInfoPolicyAddress::where('phone1', $request->phone1)
                ->where('status', 'ACTIVE')
                ->where('userid', '!=', $request->userid)->count()) {
                return ['error' => 'Phone number already exists.Same Phone Number is associated with another user.'];
            }

            $emailCheck = UserLogin::whereIn('userid', UserInfoPolicyAddress::where('status', 'ACTIVE')->pluck('userid')->toArray())
                ->where('userid', '!=', $request->userid)
                ->where('username', $request->cemail)
                ->count();
            if ($emailCheck) {
                return ['error' => 'Email already exists.Same Email is associated with another user.'];
            }

            /* if (UserInfo::where('cemail', $request->cemail)->where('userid', '!=', $request->userid)->count()) {
                 return ['error' => 'User email already exists.'];
             } */
            $updateData = [
                'cfname' => $request->cfname,
                'clname' => $request->clname,
                'cgender' => ($request->cgender) ? 1 : 0,
                'cemail' => $request->cemail,
                'phone1' => $request->phone1,
                'cssn' => $encryptedSSN,
                'cssn4' => substr($request->cssn, -4, 4),
                'cdob' => $request->cdob
            ];
            $userID = $request->userid;
            ($request->has('employed')) ? $updateData['employed'] = $request->employed : null;
            ($request->has('cmname')) ? $updateData['cmname'] = $request->cmname : null;
            ($request->has('cemail_alt')) ? $updateData['cemail_alt'] = $request->cemail_alt : null;
            ($request->has('phone2')) ? $updateData['phone2'] = $request->phone2 : null;
            UserInfo::where('userid', $userID)->update($updateData);
            $email = $updateData['cemail'];
            UserLogin::where('userid', $userID)->update(['username' => $email, 'phone' => $request->phone1]);
            $data['elgb_policyid'] = $request->policyID;
            $data['elgb_act'] = 'pic';
            $data['elgb_act_date'] = time();
            $data['elgb_comment'] = ($request->pic_reason != '') ? $request->pic_reason : "User details changed";
            $data['elgb_agent'] = $request->aid;
            $data['elgb_file_date'] = date('Y-m-d');
            $data['elgb_term_date'] = '';
            PolicyUpdateHelper::updateEligibility($data);
            $middleName = ($request->cmname != '') ? " " . $request->cmname : '';
            $dataEmail = [
                "Name" => $request->cfname . $middleName . " " . $request->clname,
                "DOB" => ($request->cdob != '') ? 'XX/XX/' . date('Y', strtotime($request->cdob)) : '',
                "SSN" => "XXX-XX-" . $updateData['cssn4'],
                "Gender" => $request->cgender == 0 ? 'Male' : 'Female'
            ];
            $this->syncMemberSSO($userDetails, $request->email);
            if ($request->sendEmail == 1) {
//                EmailSendHelper::sendGenericPolicyEmailUser($userID, "User Info Changed", "User Info has been changed", 'updatepolicy', $dataEmail, 'PIC');
                EmailSendHelper::sendUserPolicyActionEmail($userID, "User Info Changed", "Your user information has been updated or changed", 'updatepolicy', $dataEmail, 'PIC');

            }
            return ['success' => "Successfully Added " . strtoupper($data['elgb_act'])];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function getReinstatePolicyDetails($policyID)
    {
        try {
            $data['briefinfo'] = $this->getbriefinfo($policyID);
            $data['plan_overview_data'] = PlanOverview::where('policy_num', $policyID)->get();
            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function setReinstatePolicyDetails($request)
    {
        try {
            $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
            $data['elgb_policyid'] = $request->policyID;
            $data['elgb_act'] = 'rpc';
            $data['elgb_act_date'] = time();
            $data['elgb_comment'] = $request->reason;
            $data['elgb_agent'] = $request->aid;
            $data['elgb_file_date'] = date('Y-m-d');
            $data['elgb_term_date'] = '';
            $data['origin'] = $origin;
            $policyDetails = UserInfoPolicyAddress::where('policy_id', $request->policyID)->get()->first();
            if (empty($policyDetails)) {
                return ['error' => 'Policy not found'];
            }
            $term_date = $this->computeTermDate($request);
            $current_policy = Policy::where('policy_id', $request->policyID)->first();
            if($term_date){
                $planEligiblityCheck = $this->checkPlansEligibilityForReinstate($request, $term_date);
                if(!$planEligiblityCheck['status']){
                    return ['error' => $planEligiblityCheck['message']];
                }
                Policy::where('policy_id', $request->policyID)->update(['term_date' => '', 'status' => 'ACTIVE','Approval'=>1]);

                if(PlanOverview::whereIn('p_ai',$request->plan_policies)->where('policy_id',$request->policyID)->where('is_assoc',1)->exists()){
                $filtered_plans = PlanOverview::whereIn('p_ai', $request->plan_policies)
                ->where('is_assoc', '!=', 1)
                ->pluck('p_ai');
                PlanPolicy::whereIn('p_ai', $filtered_plans)
                ->update([
                    'pterm_date' => '',
                    'pstatus' => 1,
                    'is_approved' => 1
                ]);
                $existing_plans = PlanOverview::where('policy_id',$request->policyID)->where('pstatus',1)->select('pid','plan_id')->get();
                $assoc_id = $this->PlanChangeOnAddress->computeAssociationFees($existing_plans, $request->policyID);
                    if (count($assoc_id) > 0) {
                        PlanPolicy::whereIn('p_ai', $request->plan_policies)->update(['pterm_date' => '', 'pstatus' => 1,'is_approved'=>1]);
                    }
                }
                else{
                    PlanPolicy::whereIn('p_ai', $request->plan_policies)->update(['pterm_date' => '', 'pstatus' => 1,'is_approved'=>1]);
                }

                if(isset($planEligiblityCheck['term_date'])){
                    if(isset($planEligiblityCheck['termed_policies']) && count($planEligiblityCheck['termed_policies']) > 0){
                        foreach($planEligiblityCheck['termed_policies'] as $key => $value){
                            InvoiceHelper::updatePaidNBInvoice($term_date,$value);
                        }
                    }
                    InvoiceHelper::updatePaidNBInvoice($term_date,$request->policyID);
                    InvoiceHelper::regenerateAndFixInvoices($request->policyID,$current_policy);
                }
                PolicyUpdateHelper::updateEligibility($data);
                // CommonHelper::addMemberSSO($policyDetails->userid);
                // $checkOtherPolicies = self::checkOtherActivePolicies($policyDetails->userid, $request->policyID);
                // if($checkOtherPolicies) {
                //     $this->archiveRepository->restoreMember($policyDetails->userid);
                // }
                SendNotificationHelper::sendReinstatePolicyNotification($policyDetails);
                if ($request->sendEmail == 1) {
    //                EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($request->policyID, 'Reinstate Policy Coverage', 'following plan has been updated', 'reinstate', 'RPC');
                    EmailSendHelper::sendPolicyActionEmail($request->policyID, 'Reinstate Policy Coverage', 'following policy has been updated', 'reinstate', 'RPC','',$request->plan_policies);
                }
                return ['success' => "Successfully Added " . strtoupper($data['elgb_act'])];
            }
            else{
               return ['error' => 'No plans Found in this policy'];
            }

        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    private function setPlanTermWithdrawStatus($status)
    {
        /* if chosen termination/withdrawn date is smaller than the effective date of that plan each plan, then term date will be their effective date */
        $plansList = PlanPolicy::where('policy_num', $this->policyID)->where('pstatus',1)->get();

        foreach ($plansList as $plans) {
            $plans->pstatus = $status;
            $plans->pterm_date = $this->tdate;
            if ($plans->peffective_date && $plans->peffective_date > $this->tdate) {
                $plans->pterm_date = $plans->peffective_date;
            }
            $plans->save();
        }
    }

    public static function checkOtherActivePolicies($userid, $policy_id) {
        $today = date('Y-m-d');
        $policies =  PlanOverview::where('userid',$userid)->where('policy_id','!=',$policy_id)->whereRaw("(pstatus = '1' or (pstatus = '2' and pterm_date > $today))")->get();
        if($policies->count() > 0) {
            return false;
        } else {
            return true;
        }
    }

    public function checkPlansEligibilityForReinstate($data, $term_date){
        $response = [];
        if(isset($data->plan_policies)){
            $userPlans = PlanOverview::whereIn('p_ai',$data->plan_policies)->where('policy_id',$data->policyID)->get();
            if(!empty($userPlans)){

                if($userPlans->where('is_assoc',1)->count() > 1){
                    $response['status'] = false;
                    $response['message'] = 'Please pick only one Association fee.';
                }
                else if($userPlans->where('is_assoc',1)->count() === count($data->plan_policies)){
                    $response['status'] = false;
                    $response['message'] = 'Association fee only cannot be activated.';
                }
                else if($userPlans->where('is_assoc',1)->count() === 0){
                    $response['status'] = true;
                    $response['message'] = "This policy's selected plans can all be activated.";
                    $response['term_date'] = $term_date;
                }
                else{
                    if($userPlans->where('is_assoc',1)->count() === 1){
                        $website = Policy::where('policy_id',$data->policyID)->select('weburl')->first();
                        $assocId =  $userPlans->where('is_assoc',1)->pluck('plan_id')->toArray();
                        $planIds = $userPlans->where('is_assoc','!=',1)->pluck('pid')->toArray();
                        $correctAssoc = $this->checkCorrectAssociation($website->weburl ,$planIds ,$assocId);
                        if(!$correctAssoc){
                                $response['status'] = false;
                                $response['message'] = 'The association you selected is not part of the chosen plan.';
                                return $response;
                        }

                    }
                    $userId = PlanOverview::where('policy_id',$data->policyID)->first()->userid;
                    $today = date('Y-m-d');
                    $checkOtherActivePolicies = PlanOverview::where('userid', $userId)
                    ->where('policy_id', '!=', $data->policyID)
                    ->where(function($query) use ($today) {
                        $query->where('pstatus', '1')
                              ->orWhere(function($query) use ($today) {
                                  $query->where('pstatus', '2')
                                        ->where('pterm_date', '>', $today);
                              });
                    })
                    ->count();
                if ($checkOtherActivePolicies > 0) {
                    $userAssociationIds = PlanOverview::where('userid', $userId)
                        ->where('is_assoc', 1)
                        ->where('policy_id','!=',$data->policyID)
                        ->where(function($query) use ($today) {
                            $query->where('pstatus', '1')
                                  ->orWhere(function($query) use ($today) {
                                      $query->where('pstatus', '2')
                                            ->where('pterm_date', '>', $today);
                                  });
                        })
                        ->select('pid', 'policy_id', 'price_male_nons','p_ai')
                        ->get();
                    $currentPolicy = PlanOverview::whereIn('p_ai', $data->plan_policies)
                        ->select('pid', 'policy_id', 'price_male_nons')
                        ->where('is_assoc', 1)
                        ->get();
                    $allPolicies = $userAssociationIds->concat($currentPolicy);
                    $currentPrice = !empty($currentPolicy) ? $currentPolicy[0]->price_male_nons : null;

                    if ($currentPrice !== null) {
                        $result = $this->termPolicies($userId, $today, $data->policyID, $currentPrice, $allPolicies, $term_date);
                        if ($result['is_current_highest']) {
                            if (count($result['highest_policies']) > 1) {
                            $response['status'] = false;
                            $response['message'] = "Please uncheck the Association fee because same fee is already active in ".$result['highest_policies'][0]->policy_id;
                            } else {
                                $response['status'] = true;
                                $response['message'] = "This policy's selected plans can all be activated.";
                                $response['term_date'] = $result['term_date'];
                                $response['termed_policies'] = $result['termed_policies'];
                            }
                        } else {
                            $response['status'] = false;
                            $response['message'] = "Please uncheck the Association fee because the highest fee is already active in ".$result['highest_policies'][0]->policy_id;
                        }
                    }
                }
                    else{
                        $response['status'] = true;
                        $response['message'] = "This policy's selected plans can all be activated.";
                        $response['term_date'] = $term_date;
                    }
                }
            }
            else{
                $response['status'] = false;
                $response['message'] = 'There are no Plans in your policy.';
            }
        }
        else{
            $response['status'] = false;
            $response['message'] = 'Plan policy id is required.';
        }
        return $response;
    }

    public function computeTermDate($data){
        if(isset($data->plan_policies)){
            $userPlans = PlanOverview::whereIn('p_ai',$data->plan_policies)->where('policy_id',$data->policyID)->get();
            if(!empty($userPlans)){
                $user_max_termed_date = PlanOverview::where('policy_id', $data->policyID)
                ->where('status','!=',1)
                ->whereNotNull('pterm_date')
                ->where('is_assoc',1)
                ->groupBy('policy_id')
                ->select(DB::raw('DATE_FORMAT(MAX(pterm_date), "%Y-%m-%d") as term_date'),'effective_date')
                ->first();
                $peffective_date = date('Y-m-01', strtotime('+1 month'));

                if($user_max_termed_date) {
                $peffective_date = date('Y-m-01', strtotime($user_max_termed_date->term_date));
                if($user_max_termed_date->term_date != $user_max_termed_date->effective_date) {
                $peffective_date = date('Y-m-d', strtotime('+1 month', strtotime($peffective_date)));
                }
                }
                if($peffective_date == null) {
                $peffective_date = date('Y-m-t');
                }
                $term_date = date('Y-m-d', strtotime('-1 days',strtotime($peffective_date)));
                return $term_date;
            }
        }
        return null;
    }

    public function checkCorrectAssociation($website , $plan_id , $assocId){
        $website = (explode('/',$website))[0];
        return  NewAssocFee::whereIn('pid', $plan_id)
        ->where('website','like', "$website%")
        ->whereIn('a_ppid', $assocId)
        ->select(['a_pid', 'a_ppid'])
        ->exists();
    }

    public function termPolicies($userId, $today, $currentPolicyId, $currentPrice, $policies, $term_date)
{
    $termedPolicies = [];
    $highestPolicyPrice = $currentPrice;
    $highestPolicies = [];
    $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
    foreach ($policies as $policy) {
        if ($policy->price_male_nons < $currentPrice) {
                PlanPolicy::where('policy_num', $policy->policy_id)
                ->where('p_ai',$policy->p_ai)
                ->where('pstatus', 1)
                ->update([
                    'pterm_date' => $term_date,
                    'pstatus' => 2,
                ]);
                $data = [
                    'elgb_act' => 'TMO',
                    'elgb_act_date' => time(),
                    'elgb_file_date' =>  $today,
                    'elgb_term_date' => null,
                    'elgb_policyid' =>  $policy->policy_id,
                    'elgb_comment' => "Association Plan termed while reinstating policy.",
                    'origin' => $origin
                ];
                PolicyUpdateHelper::updateEligibility($data);
                array_push($termedPolicies,$policy->policy_id);
        } elseif ($policy->price_male_nons >= $currentPrice) {
            if ($policy->price_male_nons > $highestPolicyPrice) {
                $highestPolicyPrice = $policy->price_male_nons;
                $highestPolicies = [$policy];
            } elseif ($policy->price_male_nons == $highestPolicyPrice) {
                $highestPolicies[] = $policy;
            }
        }
    }

    foreach ($policies as $policy) {
        if ($policy->price_male_nons < $highestPolicyPrice && $policy->price_male_nons > $currentPrice) {
            PlanPolicy::where('policy_num', $policy->policy_id)
            ->where('p_ai',$policy->p_ai)
            ->where('pstatus', 1)
            ->update([
                'pterm_date' => $term_date,
                'pstatus' => 2,
            ]);
            $data = [
                'elgb_act' => 'TMO',
                'elgb_act_date' => time(),
                'elgb_file_date' =>  $today,
                'elgb_term_date' => null,
                'elgb_policyid' =>  $policy->policy_id,
                'elgb_comment' => "Association Plan termed while reinstating policy.",
                'origin' => $origin
            ];
            PolicyUpdateHelper::updateEligibility($data);
            array_push($termedPolicies,$policy->policy_id);
        }
    }

    return [
        'term_date' => $term_date,
        'termed_policies' => $termedPolicies,
        'is_current_highest' => count($highestPolicies) === 0 || ($currentPrice == $highestPolicyPrice && count($highestPolicies) >= 1),
        'highest_policies' => $highestPolicies,
    ];
}
}
