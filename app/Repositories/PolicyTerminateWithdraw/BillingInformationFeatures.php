<?php

namespace App\Repositories\PolicyTerminateWithdraw;

use App\Address;
use App\Helpers\EmailSendHelper;
use App\PaymentEft;
use App\PaymentCC;
use App\Helpers\DecryptEcryptHelper;
use App\PaymentEftDeleted;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use PHPUnit\Framework\Exception;
use Illuminate\Support\Facades\DB;

class BillingInformationFeatures extends Model
{
    public function getBillingInformation($uid)
    {
        try {
            return PaymentCC::where('credit_userid', $uid)->get();
        } catch (Exception $e) {
            return ['message' => $e->getMessage()];
        }
    }

    public function updateBillingInformationCC($request)
    {
        DB::beginTransaction();
        try {
            if (!UserInfoPolicyAddress::where('userid', $request->credit_userid)->count()) {
                return ['error' => 'User not found.'];
            }
            $custAddress = $this->paymentAddAddressData($request);
            $addressID = $request->maddress;
            if (!$request->maddress) {
                $addressID = Address::insertGetId($custAddress);
            }
            $data = $this->paymentCcData($request,$addressID);
            DB::table('payment_cc')->insert($data);
            if ($request->sendEmail == 1) {
//                EmailSendHelper::sendGenericPolicyEmailUser($request->credit_userid, "Billing Information CC", "Billing Information CC has been updated", 'billinginformation', [], 'BIC');
                EmailSendHelper::sendUserPolicyActionEmail($request->credit_userid, "Billing Information CC", "Billing Information CC has been updated", 'billinginformation', [], 'BIC');
            }
            DB::commit();
            return ['message' => 'Successfully Added Billing Information CC'];
        } catch (Exception $e) {
            DB::rollBack();
            return ['error' => $e->getMessage()];
        }
    }

    public function updateBillingInformationEFT($request)
    {
        try {
            if (!UserInfoPolicyAddress::where('userid', $request->userid)->count()) {
                return ['error' => 'User not found.'];
            }
            $data['bank_name'] = $request->bank_name;
            $data['bank_accountname'] = $request->bank_accountname;
            $data['bank_account'] = DecryptEcryptHelper::encryptInfo($request->bank_account);
            $data['bank_routing'] = $request->bank_routing;
            $data['bank_userid'] = $request->userid;
            $data['bank_date'] = time();
            $data['bank_account4'] = substr($request->bank_account, -4, 4);
            $data['account_type'] = $request->account_type;
            $data['account_holder_type'] = strtolower($request->account_holder_type) == 'business' ? 'company' : $request->account_holder_type;
            PaymentEft::insert($data);
            if ($request->sendEmail == 1) {
//                EmailSendHelper::sendGenericPolicyEmailUser($request->userid, "Billing Information EFT", "Billing Information EFT has been updated", "billinginformation", [], 'BIC');
                EmailSendHelper::sendUserPolicyActionEmail($request->userid, "Billing Information EFT", "Billing Information EFT has been updated", "billinginformation", [], 'BIC');
            }
            return ['message' => 'Successfully Added Billing Information EFT'];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function deleteBillingInformationEFT($request): array
    {
        DB::beginTransaction();
        try {
            $eft_info = PaymentEft::find($request['bank_id']);
            $isSetAsDefaultPayment = $eft_info->bank_status == 'A';
            $isSetAsRecurringPayment = $eft_info->getPoliciesFromUserId()
                ->where('recurring_payment_type', 'eft')
                ->where('recurring_payment_id', $eft_info->bank_id)
                ->isNotEmpty();

            if ($isSetAsDefaultPayment || $isSetAsRecurringPayment)
                return [
                    'type' => 'error',
                    'error' => 'Bank Information is either set as Default or Recurring Payment, cannot delete.',
                    'message' => 'Cannot delete Bank Information set as Default or Recurring Payment.'
                ];

            $eft_deleted = new PaymentEftDeleted([
                'bank_name' => $eft_info->bank_name,
                'bank_accountname' => $eft_info->bank_accountname,
                'bank_routing' => $eft_info->bank_routing,
                'bank_account' => $eft_info->bank_account,
                'bank_account4' => $eft_info->bank_account4,
                'bank_date' => $eft_info->bank_date,
                'bank_userid' => $eft_info->bank_userid,
                'bank_status' => $eft_info->bank_status,
                'bankbranchloc' => $eft_info->bankbranchloc,
                'is_approved' => $eft_info->is_approved,
                'is_action_taken' => $eft_info->is_action_taken,
                'reason_to_reject' => $eft_info->reason_to_reject,
                'request_type' => $eft_info->request_type,
                'reason_to_accept' => $eft_info->reason_to_accept,
                'accepted_date' => $eft_info->accepted_date,
                'rejected_date' => $eft_info->rejected_date,
                'created_date' => $eft_info->created_date,
                'delete_request_date' => date('Y-m-d H:i:s'),
                'account_type' => $eft_info->account_type,
                'account_holder_type' => $eft_info->account_holder_type,
                'created_at' => $eft_info->created_at,
                'updated_at' => $eft_info->updated_at,
                'deleted_at' => date('Y-m-d H:i:s'),
                'sign' => $eft_info->sign,
                'is_primary' => $eft_info->is_primary,
                'bank_sign' => $eft_info->bank_sign,
                'funding_source_id' => $eft_info->funding_source_id,
                'customer_id' => $eft_info->customer_id,
                'is_verified' => $eft_info->is_verified,
                'unique_identification_number' => $eft_info->unique_identification_number
            ]);
            $eft_deleted->save();

            $eft_info->delete();
            DB::commit();
            return [
                'type' => 'success',
                'message' => 'Billing Information EFT successfully deleted.'
            ];
        } catch (\Exception $exception) {
            DB::rollBack();
            return [
                'type' => 'error',
                'error' => $exception->getMessage(),
                'message' => 'Failed to delete Bank Information.'
            ];
        }
    }

    protected function paymentCcData($request,$addressId){
        return [
            'cc_type'=>$request->credit_type,
            'cc_num'=>DecryptEcryptHelper::encryptInfo($request->credit_num),
            'cc_num4'=>substr($request->credit_num, -4, 4),
            'cc_expmm'=>$request->credit_expmonth,
            'cc_expyyyy'=>$request->credit_expyear,
            'cc_date'=>strtotime("now"),
            'cc_status'=>$request->credit_status,
            'cc_userid'=>$request->credit_userid,
            'cc_contactfname'=>$request->firstname,
            'cc_contactlname'=>$request->lastname,
            'cc_cvc'=>$request->credit_cvc,
            'cc_addressid'=>$addressId,
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now(),
        ];
    }

    protected function paymentAddAddressData($request)
    {
        return[
            'a_userid' => $request->credit_userid,
            'address1'=>$request->address1,
            'address2'=>$request->address2,
            'city'=>$request->city,
            'state'=>$request->state,
            'zip'=>$request->zip,
            'status'=>1,
            'type'=>'B',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now(),
        ];
    }
}
