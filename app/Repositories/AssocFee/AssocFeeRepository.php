<?php


namespace App\Repositories\AssocFee;


use App\AssocFee;
use App\InsuranceDomain;
use App\Plan;
use App\PlanPricingDisplay;
use App\Repositories\Plans\PlanDetails;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AssocFeeRepository
{

    use Paginator, ResponseMessage;
    /**
     * @var AssocFee
     */
    private $model;


    /**
     * AssocFeeService constructor.
     * @param AssocFee $model
     */
    public function __construct(AssocFee $model)
    {
        $this->model = $model;
    }

    static $withRelations = [
        'plan',
        'assocPlan'
    ];

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }


    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->with(self::$withRelations)
            ->orderBy('a_id', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }


    protected function filterContent($data, $filters = [])
    {

        if (isset($filters['pid'])) {
            $data->where('pid', '=', $filters['pid']);
        }

        if (isset($filters['website'])) {
            $data->where('website', 'LIKE', '%' . $filters['website'] . '%');
        }


    }

    public function paginatedFormattedList($limit, $filters = [])
    {
        $data = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($data);
    }

    protected function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    public function singleFormattedItem($d)
    {
        return [
            'aId' => $d->a_id,
            'planId' => isset($d->plan) ? $d->plan->pid : null,
            'planName' => isset($d->plan) ? $d->plan->plan_name_system : null,
            'assocPlanId' => isset($d->assocPlan) ? $d->assocPlan->pid : null,
            'assocPlanName' => isset($d->assocPlan) ? $d->assocPlan->plan_name_system : null,
            'price' => isset($d->plan_price) ? $d->plan_price : null,
            'website' => $d->website
        ];
    }

    protected function getWebsites()
    {
        $data = [];
        $query = InsuranceDomain::query()
            ->select('domain')
            ->distinct()
            ->where('status', '=', 'A')
            ->where('domain', '!=', null)
            ->orderBy('domain', 'DESC')
            ->get()
            ->toArray();
        foreach ($query as $d) {
            $data[] = $d['domain'];
        }
        return [
            'data' => $data,
            'total' => count($data)
        ];
    }

    public function getOptions()
    {
        $plan = new PlanDetails();
        $associationFees = $plan->getAssociationFees();
        $associationPlans = $plan->getAssociationPlans();
        $websites = $this->getWebsites();
        return [
            'associationFees' => $associationFees,
            'plans' => $associationPlans,
            'websites' => $websites
        ];
    }

    protected function requestData($data)
    {
        return [
            'pid' => (int)$data['planId'],
            'a_pid' => (int)$data['assocPlanId'],
            'website' => $data['website'],
            'a_ppid' => (int)$data['a_ppid']
        ];
    }

    protected function getPlanPricingId($assocPlanId)
    {
        return PlanPricingDisplay::query()
            ->where('pid', '=', $assocPlanId)
            ->pluck('plan_pricing_id')
            ->first();
    }

    public function checkAssociation($data,$planId)
    {
        return $this->model::query()
            ->where([
                'website' => $data['website'],
                'pid' => $planId,
                'a_pid' => $data['assocPlanId']
            ])->exists();
    }

    public function create($data)
    {
        $data['a_ppid'] = $this->getPlanPricingId($data['assocPlanId']);
        $requestData = $this->requestData($data);
        return $this->model::create($requestData);
    }

    public function update($aId, $data)
    {
        $requestData = $this->requestData($data);
        $model = $this->getByField('a_id', $aId);
        return $model->update($requestData);
    }

    protected function getPidsFromPlan()
    {
        $plans = Plan::query()
            ->select('pid')
            ->distinct()
            ->where([
                'is_assoc' => false,
                'forsale' => true
            ])
            ->get()
            ->toArray();

        $data = [];
        foreach ($plans as $p) {
            $data[] = $p['pid'];
        }
        return $data;
    }

    protected function createFromAllPlan($data)
    {
        $pIdsData = $this->getPidsFromPlan();
        DB::beginTransaction();
        try {
            foreach ($pIdsData as $pId) {
                $newData = $data;
                $newData['planId'] = $pId;
                $checkAssoc = $this->checkAssociation($newData);
                if (!$checkAssoc) {
                    $this->create($newData);
                }
            }
            DB::commit();
            return $this->successResponse('Created', [], 201);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed To Create.');
        }
    }

    public function createIfPlan($data)
    {
        $checkAssoc = $this->checkAssociation($data);
        if ($checkAssoc) return $this->failedResponse('Association fee already assigned for this plan and group.', 409);
        DB::beginTransaction();
        try {
            $this->create($data);
            DB::commit();
            return $this->successResponse('Created', [], 201);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed To Create');
        }
    }

    public function delete($aId)
    {
        $data = $this->getByField('a_id', $aId);
        if (!$data) return $this->failedResponse('Data not found');
        try {
            $data->delete();
            return $this->successResponse('Deleted', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Delete');
        }
    }

    public function createWebsiteAssoc($data)
    {
        $plansId = $data['plans'];
        DB::beginTransaction();
        try {
            $count = $this->createAll($data, $plansId);
            DB::commit();
            if ($count > 0){
                return $this->successResponse($count . ' Website Association Fees Created.', [], 201);
            }else{
                return $this->failedResponse('This Website Association Fees are already created.', 409);
            }
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed To Create.');
        }
    }

    /**
     * @param $data
     * @param $plansId
     * @return int
     */
    protected function createAll($data, $plansId): int
    {
        $count = 0;
        foreach ($plansId as $planId) {
            $checkDuplicateRow = $this->checkAssociation($data, $planId);
            $data['planId'] = $planId;
            if ($checkDuplicateRow == false) {
                $this->create($data);
                $count++;
            }
        }
        return $count;
    }

    public function deleteSelectedWebsiteAssocFee($request){
        $ids = $request['ids'];
        if ($request['selected_all']) {
            $ids = $this->model::query()->pluck('a_id')->toArray();
        }
        try {
            $count = count($ids);
            $this->model::query()->whereIn('a_id', $ids)->delete();
            return $this->successResponse($count . ' Website Association Fees Deleted.', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed to Delete.');
        }
    }

    public function getAssociationFeesSearch($policy_id){
        $plan = new PlanDetails();
        return $plan->getAssociationList($policy_id);
    }
}
