<?php


namespace App\Repositories\AssocFee;


use App\GidAssocFee;
use App\PlanPricingDisplay;
use App\Repositories\Carrier;
use App\Repositories\Groups\GroupFeatures;
use App\Repositories\Members\ManageClients;
use App\Repositories\Plans\PlanDetails;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Illuminate\Support\Facades\DB;

class GidAssocFeeRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var GidAssocFee
     */
    private $model;
    private $carrierModel;
    private $manageClientsModel;


    /**
     * GidAssocFeeService constructor.
     * @param GidAssocFee $model
     */
    public function __construct(GidAssocFee $model)
    {
        $this->model = $model;
        $this->carrierModel = new Carrier();
        $this->manageClientsModel = new ManageClients();
    }

    static $withRelations = [
        'groupInfo',
        'plan',
        'assocPlan',
    ];

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }


    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->with(self::$withRelations)
            ->orderBy('a_id', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }


    protected function filterContent($data, $filters = [])
    {

        if (isset($filters['pid'])) {
            $data->where('pid', '=', $filters['pid']);
        }

        if (isset($filters['gid'])) {
            $data->where('gid', '=', $filters['gid']);
        }
    }

    public function paginatedFormattedList($limit, $filters = [])
    {
        $data = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($data);
    }

    protected function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    protected function singleFormattedItem($d)
    {
        $groupName = isset($d->groupInfo) ? $d->groupInfo->gname : null;

        return [
            'aId' => $d->a_id,
            'gId' => $d->gid,
            'groupName' => $groupName,
            'planId' => isset($d->plan) ? $d->plan->pid : null,
            'planName' => isset($d->plan) ? $d->plan->plan_name_system : null,
            'assocPlanId' => isset($d->assocPlan) ? $d->assocPlan->pid : null,
            'assocPlanName' => isset($d->assocPlan) ? $d->assocPlan->plan_name_system : null,
            'price' => isset($d->plan_price) ? $d->plan_price : null
        ];
    }

    public function getOptions($filters = [])
    {
        $plan = new PlanDetails();
        $groupInfo = new GroupFeatures();
        $carrier = $filters['cid'] ?? [];
        $planType = $filters['pl_type'] ?? [];
        $associationPlans = $plan->getAssociationPlans($carrier, $planType);
        $groups = $groupInfo->getAssociationGroups($carrier);
        $carriers = $this->carrierModel->carrierList();
        $pl_types = $this->manageClientsModel->clientsPlansType($carrier);
        $associationFees = $plan->getAssociationFees();

    
        return [
            'associationFees' => $associationFees,
            'plans' => $associationPlans,
            'groups' => $groups,
            'carriers' => $carriers,
            'pl_types' => $pl_types
        ];
    }

    protected function requestData($data)
    {
        return [
            'gid' => (int)$data['gId'],
            'pid' => (int)$data['planId'],
            'a_pid' => (int)$data['assocPlanId'],
            'a_ppid' => (int)$data['a_ppid']
        ];
    }


    public function checkGroupAssociation($data,$planId)
    {
        return $this->model::query()
            ->where([
                'gid' => $data['gId'],
                'pid' => $planId,
                'a_pid' => $data['assocPlanId']
            ])->exists();
    }

    protected function getPlanPricingId($assocPlanId)
    {
        return PlanPricingDisplay::query()
            ->where('pid', '=', $assocPlanId)
            ->pluck('plan_pricing_id')
            ->first();
    }

    public function create($data)
    {
        $data['a_ppid'] = $this->getPlanPricingId($data['assocPlanId']);
        $requestData = $this->requestData($data);
        return $this->model::create($requestData);
    }

    public function update($aId, $data)
    {
        $requestData = $this->requestData($data);
        $data = $this->getByField('a_id', $aId);
        return $data->update($requestData);
    }

    public function delete($aId)
    {
        $data = $this->getByField('a_id', $aId);
        if (!$data) return $this->failedResponse('Data not found');
        try {
            $data->delete();
            return $this->successResponse('Deleted', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed To Delete');
        }
    }

    public function createGroupAssoc($data)
    {
        $cid = isset($data['cid']) && is_array($data['cid']) ? array_values($data['cid']) : [];
        $pl_type = isset($data['pl_type']) && is_array($data['pl_type']) ? array_values($data['pl_type']) : [];
        if (empty($cid) && empty($pl_type)) {
            return $this->failedResponse('Either carrier or plan type must have at least one value.', 409);
        }
        $plansId = isset($data['plans']) && is_array($data['plans']) && !empty($data['plans']) 
            ? array_filter($data['plans']) 
            : [];
    
        if (empty($plansId)) {
            $cidFormat =  array_map('strval', $cid);
            $pl_typeFormat =  array_map('strval', $pl_type);
            $plan = new PlanDetails();
            $plansId = $plan->getAssociationPlans($cidFormat, $pl_typeFormat , 'create');
        }
        DB::beginTransaction();
        try {
            $count = $this->createAll($data, $plansId);
            DB::commit();
            if ($count > 0){
                return $this->successResponse($count . ' Group Association Fees Created.', [], 201);
            }else{
                return $this->failedResponse('This Group Association Fees are already created.', 409);
            }
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed To Create.');
        }
    }

    /**
     * @param $data
     * @param $plansId
     * @return int
     */
    protected function createAll($data, $plansId): int
    {
        $count = 0;
        foreach ($plansId as $planId) {
            $checkDuplicateRow = $this->checkGroupAssociation($data, $planId);
            $data['planId'] = $planId;
            if ($checkDuplicateRow == false) {
                $this->create($data);
                $count++;
            }
        }
        return $count;
    }

    public function deleteSelectedGroupAssocFee($request){
        $ids = $request['ids'];
        if ($request['selected_all']) {
            $ids = $this->model::query()->pluck('a_id')->toArray();
        }
        try {
            $count = count($ids);
            $this->model::query()->whereIn('a_id', $ids)->delete();
            return $this->successResponse($count . ' Group Association Fees Deleted.', [], 200);
        } catch (\Throwable $th) {
            return $this->failedResponse('Failed to Delete.');
        }
    }
}
