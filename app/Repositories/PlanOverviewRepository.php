<?php

namespace App\Repositories;

use App\PlanOverview;
use App\Traits\Paginator;

class PlanOverviewRepository
{
    use Paginator;
    /**
     * @var PlanOverview
     */
    private $model;

    /**
     * PlanOverviewService constructor.
     * @param PlanOverview $model
     */
    public function __construct(PlanOverview $model)
    {
        $this->model = $model;
    }

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->with(['userInfo']);
        $this->filterContent($query, $filters);
        return $query;
    }

    public function getHealthPlansQuery($filters)
    {
        $query = $this->model::query()
            ->with(['userInfo'])
            ->where('pl_type', '=', 'MM');
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['cemail'])) {
            $data->where('cemail', '=', $filters['cemail']);
        }

        if (isset($filters['userid'])) {
            $data->where('userid', '=', $filters['userid']);
        }

        if (isset($filters['policy_id'])) {
            $data->where('policy_id', '=', $filters['policy_id']);
        }

        if (isset($filters['ppptype'])) {
            $data->where('ppptype', '=', $filters['ppptype']);
        }

        if (isset($filters['peffective_date'])) {
            $data->where('peffective_date', '=', $filters['peffective_date']);
        }

        if (isset($filters['pterm_date'])) {
            $data->where('pterm_date', '=', $filters['pterm_date']);
        }

        if (isset($filters['pl_type'])) {
            $data->where('pl_type', '=', $filters['pl_type']);
        }

    }

    public function paginatedFormattedList($limit, $filters = [])
    {
        $data = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($data);
    }

    public function singleFormattedItem($d)
    {
        $userFullName = isset($d->userInfo) ? $d->userInfo->fullname : null;
        return [
            'policyId' => $d->policy_id,
            'planPolicyId'=>$d->p_ai, //primary plan policy id
            'pId'=>$d->pid, //primary key for table plans
            'planId'=>$d->plan_id,
            'planNameSystem'=>$d->plan_name_system,
            'userId'=>$d->userid,
            'userName' => $userFullName,
            'effectiveDate'=>$d->effective_date,
            'termDate'=>$d->term_date,
            'enrollmentDate'=>$d->enrollment_date,
            'tier'=>$d->tier,
            'pptypes'=>$d->ppptype,
            'plType'=>$d->pl_type,
            'idilusLtd'=>$d->idilus_ltd,
            'idilusSurchage'=>$d->idilus_surcharge,
            'brand'=>$d->brand,
            'status'=>$d->status,
            'planPolicyStatus'=>(isset($d->planPolicy)) ? $d->planPolicy->is_approved : null,
        ];

    }
    public function formattedItems($data){
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return $result;
    }

    public function formattedData($data)
    {

        return [
            'data' => $this->formattedItems($data),
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }



}
