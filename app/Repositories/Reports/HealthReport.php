<?php

namespace App\Repositories\Reports;

use App\DependentInPolicy;
use App\UserInfoPolicyAddress;
use Illuminate\Database\Eloquent\Model;

class HealthReport extends Model
{
    public function getHealthReport($request, $newDate)
    {
        $data = [];
        $policies = UserInfoPolicyAddress::with('healthAnswers')
            ->with('plans')
            ->where('edate', '>', $newDate)
            ->where(function ($query) use ($request) {
                if ($request->effective_date) {
                    $query->where('effective_date', $request->effective_date);
                }
                if ($request->from_date && $request->to_date) {
                    $fromDate = strtotime($request->from_date);
                    $toDate = strtotime($request->to_date);
                    $query->whereBetween('edate', [$fromDate, $toDate]);
                }
            })->groupBy('policy_id')->orderBy('edate', 'DESC')->get();
        foreach ($policies as $key => $p) {
            $data[$key]=$p;
            $dependents = DependentInPolicy::with('healthAnswers')->where('policy_id', $p->policy_id)->get();
            if ($dependents) {
                $data[$key]['dependent']=$dependents;
            }
        }
        return $data;
    }
}
