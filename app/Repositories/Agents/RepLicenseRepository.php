<?php


namespace App\Repositories\Agents;


use App\Agent;
use App\AgentInfo;
use App\AgentLicense;
use App\AgentLicenseDocument;
use App\Helper\AgentHelper;
use App\Http\Resources\ErrorResource;
use App\Http\Resources\ReferralResource;
use App\Http\Resources\SuccessResource;
use App\Traits\AgentEligibilityLog;
use App\Traits\FileUpload\FileUpload;
use App\Traits\Pagination;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class RepLicenseRepository
{
    use ResponseMessage;
    use Pagination;
    use FileUpload;
    use AgentEligibilityLog;
    use Paginator;

    public function getAgentLicense(Request $request): JsonResponse
    {
        $take = $request->page_size ?: 10;
        $query = AgentLicense::with(['state', 'agent'])
            ->has('state');
        $this->filterContent($query, $request->query->all());
        $data = $query->paginate($take)
            ->appends(array_merge(['page_size' => $take], array_diff_key($request->all(), array_flip(['user_type', 'is_from_mobile_app']))));

        $data->getCollection()
            ->transform(function ($data) {
                return $this->getLicenseData($data);
            });
        $paginatedData = [
            'data' => $data,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Agent Licenses successfully fetched.',
                'data' => $paginatedData
            ]), Response::HTTP_OK
        );
    }

    /**
     * @param $data
     * @param array $filters
     * filter / query
     */
    protected function filterContent($data, array $filters = [])
    {
        if (isset($filters['license_number'])) {
            $data->where('license_number', 'LIKE', '%' . $filters['license_number'] . '%');
        }
        if (isset($filters['agent_id'])) {
            $data->where('license_agent_id', $filters['agent_id']);
        }

        if (isset($filters['license_state'])) {
            $data->where('license_state', '=', $filters['license_state']);
        }

        if (isset($filters['rep_name'])) {
            $data->whereHas('agent', function ($query) use ($filters) {
                $concat = $this->concatFullName('agent_fname', 'agent_mname', 'agent_lname');
                $query->where(DB::raw($concat), 'LIKE', '%' . $filters['rep_name'] . '%');
            });
        }

        $data->where('license_deleted', false)->orderBy('license_id', 'DESC');
    }

    protected function concatFullName($firstName, $middleName, $lastName): string
    {
        $sqlQuery = "CONCAT_WS(' ',";
        $sqlQuery .= "CASE $firstName WHEN '' THEN NULL ELSE $firstName END,";
        $sqlQuery .= "CASE $middleName WHEN '' THEN NULL ELSE $middleName END,";
        $sqlQuery .= "CASE $lastName  WHEN '' THEN NULL ELSE $lastName END)";
        return $sqlQuery;
    }

    private function getLicenseData($data): array
    {
        $licenseDoc = optional($data->licenseDoc)->file_name;
        //$agent = isset($data->agent) ? $data->agent->rep_full_name : null;
        //$selfLicense = isset($data->agent) && $data->agent->agent_id == Auth::user()->agentInfo()->agent_id ? true : false;
        return [
            'license_id' => $data->license_id,
            'license_exp_date' => optional($data->license_exp_date)->format('m/d/Y'),
            'state' => $data->state->name . " (" . $data->state->abbrev . ")",
            'status' => $data->statusText,
            'license_number' => $data->license_number,
            'license_resident' => (int)$data->license_resident,
            'license_doc' => $licenseDoc ? $this->getS3Url(AgentInfo::LICENSE_DIR, $licenseDoc) : null,
            'rep_name' => null,
            'created_at' => Carbon::createFromTimestamp($data->license_added_date)->format('m/d/Y'),
            'updated_at' => optional($data->updated_at)->format('m/d/Y')
//            'selfLicense' => false,
//            'self_license' => false
        ];
    }

    public function removeRepLicense($request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $licenseID = $request['license_id'];
            $license = AgentLicense::where('license_id', $licenseID)->first();
            $license->update(['license_deleted' => 1]);
            $license->delete();

            DB::commit();
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'License successfully removed.'
                ]), Response::HTTP_OK
            );
        } catch (\Exception $e) {
            DB::rollBack();

            $error_code = in_array($e->getCode(), Response::$statusTexts, true)
                ? $e->getCode()
                : Response::HTTP_UNPROCESSABLE_ENTITY;
            return response()->json(
                new ErrorResource([
                    'statusCode' => $error_code,
                    'error' => $e->getMessage(),
                    'message' => 'Failed to remove Agent License.'
                ]), $error_code
            );
        }
    }


    public function addRepLicense($request): JsonResponse
    {
        DB::beginTransaction();
        try {
            if (AgentLicense::where([
                ['license_status', 'A'],
                ['license_number', $request['license_number']]
            ])->exists()) {
                return response()->json(
                    new ErrorResource([
                        'statusCode' => Response::HTTP_CONFLICT,
                        'message' => 'License Number already exists.'
                    ]), Response::HTTP_CONFLICT
                );
            }

            $data = $this->addLicenseData($request);
            $license = AgentLicense::create($data);
            $this->storeLicenseDoc($request, $data, $license->license_id);

            $this->setEligibilityAgentId($request['agent_id'])
                ->setEligibilityComment("New Agent License (#{$request['license_number']} | ID:{$license->license_id}) added.")
                ->setEligiblityAction(\App\AgentUpdate::AGENT_LICENSE)
                ->createAgentEligibilityLogs($request);

            DB::commit();
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'License successfully added.',
                    'data' => $this->getLicenseData($license)
                ]), Response::HTTP_OK
            );
        } catch (\Exception $e) {
            DB::rollBack();

            $error_code = in_array($e->getCode(), Response::$statusTexts, true)
                ? $e->getCode()
                : Response::HTTP_UNPROCESSABLE_ENTITY;
            return response()->json(
                new ErrorResource([
                    'statusCode' => $error_code,
                    'error' => $e->getMessage(),
                    'message' => 'Failed to add Agent License.'
                ]), $error_code
            );
        }
    }

    /**
     * @param $request
     * @return array
     */
    protected function addLicenseData($request): array
    {
        return [
            'license_agent_id' => $request['agent_id'],
            'license_number' => $request['license_number'],
            'license_state' => $request['state'],
            'license_exp_date' => $request['license_exp_date'],
            'license_resident' => $request['license_resident'],
            'license_added_date' => $request['license_added_date'] ?? Carbon::now()->timestamp,
            'license_status' => $request['license_status'] ?? 'A',
            'added_by' => request()->header('id')
        ];
    }

    /**
     * @param $request
     * @param array $data
     * @param int $licenseId
     * @return array
     */
    protected function storeLicenseDoc($request, array $data, int $licenseId): array
    {
        if (array_key_exists('license_doc', $request) && file_exists($request['license_doc'])) {
            $file = $request['license_doc'];
            $fileName = 'license_doc_' . time() . '_.' . $file->getClientOriginalExtension();
            if ($this->uploadToS3($file, AgentInfo::LICENSE_DIR, $fileName)) {
                //Storage::disk('corenroll')->put('/admn/__files__/licenses/' . $fileName, file_get_contents($file));
                $data['support_pic'] = $fileName;

                $existing_doc = AgentLicenseDocument::find($request['doc_id'] ?? null);
                if (! is_null($existing_doc) &&
                    $this->existsInS3(AgentInfo::LICENSE_DIR, $existing_doc->file_name)
                ) {
                    $this->deleteFromS3(AgentInfo::LICENSE_DIR, $existing_doc->file_name);
                }

                AgentLicenseDocument::updateOrCreate(
                    ['doc_id' => $request['doc_id'] ?? null],
                    [
                        'license_id' => $licenseId,
                        'file_name' => $fileName,
                        'date_added' => Carbon::now()->timestamp,
                        'file_status' => true,
                        'type' => $file->getMimeType(),
                        'userid' => request()->header('id')
                    ]
                );
            }
        }
        return $data;
    }

    public function getLicenseDetail(Request $request, $licenseID): JsonResponse
    {
        try {
            $license = AgentLicense::where('license_id', $licenseID)->first();
            if (!$license) {
                return response()->json(
                    new ErrorResource([
                        'statusCode' => Response::HTTP_NOT_FOUND,
                        'message' => 'Invalid License ID. The License ID does not exist.'
                    ]), Response::HTTP_NOT_FOUND
                );
            }
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'License successfully fetched.',
                    'data' => $this->getLicenseData($license)
                ]), Response::HTTP_OK
            );
        } catch (\Exception $e) {
            $error_code = in_array($e->getCode(), Response::$statusTexts, true)
                ? $e->getCode()
                : Response::HTTP_UNPROCESSABLE_ENTITY;
            return response()->json(
                new ErrorResource([
                    'statusCode' => $error_code,
                    'error' => $e->getMessage(),
                    'message' => 'Failed to fetch Agent License.'
                ]), $error_code
            );
        }
    }

    public function editLicenseDetail($request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $license = AgentLicense::find($request['license_id']);
            $license_doc = $license->licenseDoc;
            $request['doc_id'] = $license_doc->doc_id ?? null;
            $request['agent_id'] = $license->license_agent_id;
            $request['license_added_date'] = $license->license_added_date;

            $data = $this->addLicenseData($request);
            $this->storeLicenseDoc($request, $data, $request['license_id']);

            $license->update($data);

            $this->setEligibilityAgentId($license->license_agent_id)
                ->setEligibilityComment("Agent License (#{$request['license_number']} | ID:{$request['license_id']}) updated.")
                ->setEligiblityAction(\App\AgentUpdate::AGENT_LICENSE)
                ->createAgentEligibilityLogs($request);

            DB::commit();
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'License successfully updated.',
                    'data' => $this->getLicenseData($license)
                ]), Response::HTTP_OK
            );
        } catch (\Exception $e) {
            DB::rollBack();

            $error_code = in_array($e->getCode(), Response::$statusTexts, true)
                ? $e->getCode()
                : Response::HTTP_UNPROCESSABLE_ENTITY;
            return response()->json(
                new ErrorResource([
                    'statusCode' => $error_code,
                    'error' => $e->getMessage(),
                    'message' => 'Failed to update Agent License.'
                ]), $error_code
            );
        }
    }
}
