<?php

namespace App\Repositories\Agents;

use App\AgentBusiness;
use App\AgentBusinessAddress;
use App\AgentCheckpayment;
use App\AgentChequeAddress;
use App\AgentInfo;
use App\AgentPersonalAddress;
use App\Helpers\AgentUpdatesHelper;
use App\Service\CustomValidationService;
use App\Service\MessageService;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AgentAddressRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var AgentPersonalAddress
     */
    private $agentPersonalAddressModel;
    /**
     * @var AgentBusinessAddress
     */
    private $agentBusinessAddressModel;

    /**
     * @var CustomValidationService
     */
    private $validationService;
    /**
     * @var AgentChequeAddress
     */
    private $agentChequeAddressModel;
    /**
     * @var MessageService
     */
    private $messageService;

    /**
     * AgentAddressRepository constructor.
     * @param AgentPersonalAddress $agentPersonalAddress
     * @param AgentBusinessAddress $agentBusinessAddress
     * @param AgentChequeAddress $agentChequeAddress
     * @param CustomValidationService $validationService
     * @param MessageService $messageService
     */
    public function __construct(
        AgentPersonalAddress $agentPersonalAddress,
        AgentBusinessAddress $agentBusinessAddress,
        AgentChequeAddress $agentChequeAddress,
        CustomValidationService $validationService,
        MessageService $messageService
    )
    {
        $this->agentPersonalAddressModel = $agentPersonalAddress;
        $this->agentBusinessAddressModel = $agentBusinessAddress;
        $this->agentChequeAddressModel = $agentChequeAddress;
        $this->validationService = $validationService;
        $this->messageService = $messageService;
    }


    public function getAddressByAgentId($id)
    {
        $agentInfo = AgentInfo::find($id);
        if (!$agentInfo) return $this->failedResponse('Agent does not exist.');
        $businessAddresses = $this->agentBusinessAddressModel::query()
            ->where('agent_id', '=', $id)
            ->get()
            ->toArray();
        $personalAddress = $this->agentPersonalAddressModel::query()
            ->where('agent_id', '=', $id)
            ->addSelect(['*', 'active as is_primary', 'usps_verified as is_usps_valid'])
            ->get()
            ->toArray();
        $chequeAddress = $this->agentChequeAddressModel::query()
            ->where('agent_id', '=', $id)
            ->get()
            ->toArray();
        $data = array_merge($personalAddress, $businessAddresses, $chequeAddress);
        $collection = collect($data)->sortByDesc('is_primary');
        $sorted = $collection->values()->all();
        return $this->successResponse('Success', $sorted);
    }

    protected function setAgentAddressData($request)
    {
        return [
            'agent_id' => $request['agent_id'],
            'address1' => $request['address1'],
            'address2' => isset($request['address2']) ? $request['address2'] : '',
            'zip' => $request['zip'],
            'city' => $request['city'],
            'state' => $request['state'],
            'is_primary' => isset($request['is_primary']) ? (int)$request['is_primary'] : 0,
            'is_usps_valid' => isset($request['usps_verified']) ? (int)$request['usps_verified'] : 0,
        ];
    }

    public function createAgentAddress($request)
    {
        $type = $request['type'];
        $requestData = $this->setAgentAddressData($request);
        if ($requestData['is_usps_valid'] == 1) {
            $this->validationService->validateAddressUSPS(
                $requestData['address1'],
                $requestData['address2'],
                $requestData['city'],
                $requestData['state'],
                $requestData['zip']
            );
        }
        $primaryData = [
            'agent_id' => $request['agent_id'],
            'type' => $type
        ];
        $isPrimary = isset($request['is_primary']) && $request['is_primary'] == 1 ? true : false;
        DB::beginTransaction();
        try {
            switch ($type) {
                case AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL:
                    $requestData['active'] = isset($request['is_primary']) ? (int)$request['is_primary'] : 0;
                    unset($requestData['is_primary']);
                    $requestData['usps_verified'] = $requestData['is_usps_valid'];
                    unset($requestData['is_usps_valid']);
                    $data = $this->agentPersonalAddressModel::create($requestData);
                    $primaryData['id'] = $data->id;
                    break;
                case AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS:
                    $agentBusiness = AgentBusiness::query()->where('business_agent_id', '=', $request['agent_id'])->exists();
                    if (!$agentBusiness) return $this->failedResponse('Failed to add agent business address, agent business data not found.');
                    $data = $this->agentBusinessAddressModel::create($requestData);
                    $primaryData['id'] = $data->id;
                    break;
                case AgentChequeAddress::AGENT_ADDRESS_TYPE_PAYMENT:
                    $this->createAgentCheckPayment($requestData);
                    $data = $this->agentChequeAddressModel::create($requestData);
                    $primaryData['id'] = $data->id;
                    break;
                default:
                    return $this->failedResponse('Something went wrong.');
            }
            //setPrimary
            if ($isPrimary) {
                $this->updateAgentPrimaryAddress($primaryData);
            }
            $successMessage = 'Agent address added successfully.';
            //update to agent_updates
            $this->createAgentUpdate($request, \App\AgentUpdate::ACT_ADDRESS_INFO, $successMessage);

            //send email
            $this->sendAddressEmail($data, $request['type']);


            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse("Failed to add group address.");
        }
    }

    protected function createAgentCheckPayment($request)
    {
        $agentChequePayment = AgentCheckpayment::query()->where('checkpaymentagent_id', $request['agent_id'])->exists();
        if (!$agentChequePayment) {
            $agentInfo = AgentInfo::find($request['agent_id']);
            $newCheckData = [
                "checkpayment_name" => $agentInfo->agent_email,
                "checkpayment_address1" => $request['address1'],
                "checkpayment_address2" => $request['address2'],
                "checkpayment_city" => $request['city'],
                "checkpayment_state" => $request['state'],
                "checkpayment_zip" => $request['zip'],
                "checkpayment_added" => strtotime(date('Y-m-d H:i:s')),
                "checkpaymentagent_id" => $request['agent_id'],
                "checkpayment_default" => 1
            ];
            AgentCheckpayment::create($newCheckData);
        }
    }

    public function setPrimaryAgentAddress($request)
    {
        $checkPrimaryAddress = $this->checkIfAddressIsPrimary($request);
        if (!$checkPrimaryAddress['status']) {
            DB::beginTransaction();
            try {
                $this->updateAgentPrimaryAddress($request);

                $successMessage = ucwords($request['type']) . " address successfully set to primary.";

                //update to agent_updates
                $this->createAgentUpdate($request, \App\AgentUpdate::ACT_ADDRESS_INFO, $successMessage);

                //send email
                $checkPrimaryAddress['data']['is_primary'] = 1;
                $this->sendAddressEmail($checkPrimaryAddress['data'], $request['type'], 'set to primary', 'Set Primary Address');
                DB::commit();
                return $this->successResponse($successMessage);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse($th->getMessage());
            }
        } else {
            return $this->failedResponse('This address is already set to primary.', 409);
        }

    }

    public function deleteAgentAddress($request)
    {
        $checkPrimaryAddress = $this->checkIfAddressIsPrimary($request);
        if (!$checkPrimaryAddress['status']) {
            DB::beginTransaction();
            try {
                $this->deleteAddress($request);
                $successMessage = ucwords($request['type']) . " address deleted successfully.";
                //update to agent_updates
                $this->createAgentUpdate($request, \App\AgentUpdate::ACT_ADDRESS_INFO, $successMessage);

                //send email
                $this->sendAddressEmail($checkPrimaryAddress['data'], $request['type'], 'deleted', 'Delete Address');

                DB::commit();
                return $this->successResponse($successMessage);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse("Failed to delete address.");
            }
        } else {
            return $this->failedResponse('Failed to delete address.', 409);
        }
    }

    protected function getAgentInfoData($requestData)
    {
        return [
            'agent_address1' => $requestData->address1,
            'agent_address2' => $requestData->address2,
            'agent_city' => $requestData->city,
            'agent_state' => $requestData->state,
            'agent_zip' => $requestData->zip
        ];
    }

    protected function getAgentBusinessData($requestData)
    {
        return [
            "business_address1" => $requestData->address1,
            "business_address2" => $requestData->address2,
            "business_city" => $requestData->city,
            "business_zip" => $requestData->zip,
            "business_state" => $requestData->state,
        ];
    }

    protected function getAgentChequePaymentData($requestData)
    {
        return [
            "checkpayment_address1" => $requestData->address1,
            "checkpayment_address2" => $requestData->address2,
            "checkpayment_city" => $requestData->city,
            "checkpayment_zip" => $requestData->zip,
            "checkpayment_state" => $requestData->state,
        ];
    }

    protected function updateAgentInfoAddress($requestData, $agentId)
    {
        $data = AgentInfo::query()
            ->where('agent_id', '=', $agentId)
            ->update($requestData);
        return ($data) ? true : false;
    }

    protected function updateAgentBusiness($requestData, $agentId)
    {
        $data = AgentBusiness::query()
            ->where('business_agent_id', '=', $agentId)
            ->update($requestData);
        return ($data) ? true : false;
    }

    protected function updateAgentChequePayment($requestData, $agentId)
    {
        $data = AgentCheckpayment::query()
            ->where('checkpaymentagent_id', '=', $agentId)
            ->update($requestData);
        return ($data) ? true : false;
    }

    protected function updateAgentPrimaryAddress($request)
    {
        $type = $request['type'];
        $agentId = $request['agent_id'];
        $id = $request['id'];
        switch ($type) {
            case AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL:
                $personalAddress = $this->agentPersonalAddressModel::query()
                    ->where('agent_id', '=', $agentId)
                    ->where('id', '=', $id)
                    ->first();
                $personalAddress->update(['active' => 1]);
                $this->agentPersonalAddressModel::query()
                    ->where('agent_id', $agentId)
                    ->where('id', '!=', $personalAddress->id)
                    ->update(['active' => 0]);
                $personalAddressData = $this->getAgentInfoData($personalAddress);
                $this->updateAgentInfoAddress($personalAddressData, $agentId);
                break;
            case AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS:
                $businessAddress = $this->agentBusinessAddressModel::query()
                    ->where('agent_id', '=', $agentId)
                    ->where('id', '=', $id)
                    ->first();
                $businessAddress->update(['is_primary' => 1]);
                $this->agentBusinessAddressModel::query()
                    ->where('agent_id', $agentId)
                    ->where('id', '!=', $businessAddress->id)
                    ->update(['is_primary' => 0]);
                $businessAddressData = $this->getAgentBusinessData($businessAddress);
                $this->updateAgentBusiness($businessAddressData, $agentId);
                break;
            case AgentChequeAddress::AGENT_ADDRESS_TYPE_PAYMENT:
                $chequeAddress = $this->agentChequeAddressModel::query()
                    ->where('agent_id', '=', $agentId)
                    ->where('id', '=', $id)
                    ->first();
                $chequeAddress->update(['is_primary' => 1]);
                $this->agentChequeAddressModel::query()
                    ->where('agent_id', $agentId)
                    ->where('id', '!=', $chequeAddress->id)
                    ->update(['is_primary' => 0]);
                $chequePaymentData = $this->getAgentChequePaymentData($chequeAddress);
                $this->updateAgentChequePayment($chequePaymentData, $agentId);
                break;
            default:
                return $this->failedResponse('Something went wrong.');
        }
    }


    protected function checkIfAddressIsPrimary($data)
    {
        $type = $data['type'];
        $id = $data['id'];
        switch ($type) {
            case AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL:
                $personalAddress = $this->agentPersonalAddressModel::find($id);
                if ($personalAddress instanceof AgentPersonalAddress && $personalAddress->active == 1) {
                    return ['status' => true, 'data' => $personalAddress];
                } else {
                    return ['status' => false, 'data' => $personalAddress];
                }
            case AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS:
                $businessAddress = $this->agentBusinessAddressModel::find($id);
                if ($businessAddress instanceof AgentBusinessAddress && $businessAddress->is_primary == 1) {
                    return ['status' => true, 'data' => $businessAddress];
                } else {
                    return ['status' => false, 'data' => $businessAddress];
                }
            case AgentChequeAddress::AGENT_ADDRESS_TYPE_PAYMENT:
                $chequeAddress = $this->agentChequeAddressModel::find($id);
                if ($chequeAddress instanceof AgentChequeAddress && $chequeAddress->is_primary == 1) {
                    return ['status' => true, 'data' => $chequeAddress];
                } else {
                    return ['status' => false, 'data' => $chequeAddress];
                }
            default:
                return false;
        }
    }

    protected function deleteAddress($request)
    {
        $type = $request['type'];
        $id = $request['id'];
        switch ($type) {
            case AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL:
                $personalAddress = $this->agentPersonalAddressModel::find($id);
                $personalAddress->delete();
                return true;
            case AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS:
                $businessAddress = $this->agentBusinessAddressModel::find($id);
                $businessAddress->delete();
                return true;
            case AgentChequeAddress::AGENT_ADDRESS_TYPE_PAYMENT:
                $chequeAddress = $this->agentChequeAddressModel::find($id);
                $chequeAddress->delete();
                return true;
            default:
                return false;
        }
    }

    protected function createAgentUpdate($request, $act = "", $comment = "")
    {
        $data = [
            "agent_id" => $request['agent_id'],
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent_id" => isset($request['loginUserId']) ? $request['loginUserId'] : null,
            "elgb_agent_name" => isset($request['loginUserName']) ? $request['loginUserName'] : null
        ];
        AgentUpdatesHelper::createAgentUpdateLog($data);
    }

    protected function formattedDataWithComma($data)
    {
        return !($data == null || $data === '') ? $data . ',' : '';
    }

    protected function formattedAddress($address1, $address2, $city, $state, $zip)
    {
        $address1 = $this->formattedDataWithComma($address1);
        $address2 = $this->formattedDataWithComma($address2);
        $city = $this->formattedDataWithComma($city);
        $state = $this->formattedDataWithComma($state);
        $zip = $zip ? $zip : '';
        return "{$address1} {$address2} {$city} {$state} {$zip}";
    }


    protected function formattedAddress2($address2, $city, $state, $zip)
    {
        $address2 = $this->formattedDataWithComma($address2);
        $city = $this->formattedDataWithComma($city);
        $state = $this->formattedDataWithComma($state);
        $zip = $zip ? $zip : '';
        return "{$address2} {$city} {$state} {$zip}";
    }


    protected function sendAddressEmail($address, $addressType, $statusMessage = "created", $subject = "Agent Address Created")
    {
        $agentInfo = AgentInfo::find($address->agent_id);
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->cemail;
        $date = Carbon::parse($address->created_at)->format('m/d/Y');
        $message = "This is an automated notice to notify that your following ${addressType} address has been {$statusMessage} on " . $date . ".";
        $addressDetail = $this->formattedAddress2(
            $address->address2,
            $address->city,
            $address->state,
            $address->zip
        );
        $contentData = [
            'Agent Id' => $agentInfo->agent_id,
            'Address Detail' => $address->address1 . "<br/>" . $addressDetail,
            'Type' => ucwords($addressType),
            'Primary' => $address->is_primary || $address->active ? 'Yes' : 'No'
        ];
        $emailConfigurationName = "AGENT_INFO_CHANGE";

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
        ];
        return $this->messageService->sendEmailWithContentData($emailData);
    }
}
