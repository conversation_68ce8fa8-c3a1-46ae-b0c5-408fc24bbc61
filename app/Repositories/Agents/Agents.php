<?php

namespace App\Repositories\Agents;

use App\AgentInfo;
use App\AgentAchpayment;
use App\AgentLicense;
use App\AgentUplineHistory;
use App\AgentUser;
use App\CustomeHomepage;
use App\RepContractAct;
use App\SsoUsers;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Agents extends Model
{
    use ResponseMessage;

    private $agentVip = [];
    private $level = 0;
    protected static $array_downlines;

    public function getDownlineAgents($agentID, $additionalInfo = null)
    {
        try {
            // Get agent email if additionalInfo is set, otherwise use agentID
            $agentEmail = isset($additionalInfo) ? SsoUsers::where('id', $agentID)->value('email') : null;

            $uplineQuery = AgentInfo::select(['agent_id', 'agent_code', 'agent_fname', 'agent_mname', 'agent_lname', 'agent_level', 'agent_email', 'agent_ga']);

            if ($agentEmail) {
                $upline = $uplineQuery->where('agent_email', $agentEmail)->first();
            } else {
                $upline = $uplineQuery->where('agent_id', $agentID)->first();
            }

            if (!$upline) {
                throw new \Exception('Invalid agent.', 422);
            }

            // Get downline information based on additionalInfo
            $downline = isset($additionalInfo) ? $this->getDownlineWithAdditionalInfo([$upline->agent_id]) : $this->getDownline([$agentID]);

            for ($i = count($downline) - 1; $i > 0; $i--) {
                foreach ($downline['agent_level_' . $i] as $down) {
                    $uplineKey = array_search($down['agent_ga'], array_column($downline['agent_level_' . ($i - 1)], 'agent_id'));
                    $downline['agent_level_' . ($i - 1)][$uplineKey]['downline'][] = $down;
                }
            }

            $data = [
                'agent_name' => $upline->full_name,
                'agent_id' => $upline->agent_id,
                'agent_code' => $upline->agent_code,
                'agent_level' => $upline->agent_level,
                'agent_ga' => $upline->agent_ga,
                'agent_email' => $upline->agent_email,
                'downline' => $downline['agent_level_0'] ?? [],
                'obsolete_downline' => $this->getExpiredDownline($agentID)
            ];

            return $this->successResponse('Success', $data);
        } catch (\Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getDownlineWithAdditionalInfo($agentID)
    {
        $agentDisplayInfo = CustomeHomepage::select('custom_homepage.email as AGENT_EMAIL', 'custom_homepage.phone as AGENT_PHONE')
            ->where('agent_id', $agentID)
            ->first();

        $agentListDetails = AgentInfo::whereIn('agent_ga', $agentID)
            ->get(['agent_id', 'agent_code',
                'agent_fname','agent_mname','agent_lname' ,'agent_level', 'agent_ga', 'agent_ga_effective_date', 'agent_email','agent_city','agent_zip','agent_state','agent_address1','agent_phone1','agent_phone2','agent_level_2','agent_signup_date'])
            ->map(function($agent)  use ($agentDisplayInfo) {
                if(isset($agentDisplayInfo) && isset($agentDisplayInfo->AGENT_EMAIL)) {
                    $agentEmail = $agentDisplayInfo->AGENT_EMAIL;
                } else {
                    $agentEmail = $agent->agent_email;
                }

                if(isset($agentDisplayInfo) && isset($agentDisplayInfo->AGENT_PHONE)) {
                    $agentPhone = $agentDisplayInfo->AGENT_PHONE;
                } else {
                    $agentPhone = $agent->agent_phone1;
                }
                return [
                    "agent_id" => $agent->agent_id,
                    "agent_code" => $agent->agent_code,
                    "agent_fname" => $agent->agent_fname,
                    "agent_mname" => $agent->agent_mname,
                    "agent_lname" => $agent->agent_lname,
                    "agent_level" => $agent->agent_level,
                    "agent_ga" => $agent->agent_ga,
                    'agent_ga_effective_date' => $agent->agent_ga_effective_date
                        ? $agent->agent_ga_effective_date->format('m/d/Y')
                        : null,
                    "agent_email" => $agentEmail,
                    "agent_city" => $agent->agent_city,
                    "agent_zip" => $agent->agent_zip,
                    "agent_state" => $agent->agent_state,
                    "agent_address1" => $agent->agent_address1,
                    "agent_phone1" => $agentPhone,
                    "agent_phone2" => $agent->agent_phone2,
                    "agent_level_2" => $agent->agent_level_2,
                    "agent_signup_date" => date('Y-m-d', $agent->agent_signup_date),
                ];
            })
            ->toArray();

        if (!empty($agentListDetails)) {
            $this->agentVip['agent_level_' . $this->level] = $agentListDetails;
            $agentLists = array_column($agentListDetails, 'agent_id');
            $this->level++;
            $this->getDownlineWithAdditionalInfo($agentLists);
        }

        return $this->agentVip;
    }

    public function getDownline($agentID)
    {
        $agentListDetails = AgentInfo::whereIn('agent_ga', $agentID)
            ->get(['agent_id', 'agent_code',
                DB::raw("CONCAT_WS(' ',NULLIF(agent_fname, ''), NULLIF(agent_mname, ''), NULLIF(agent_lname, '')) as agent_name"), 'agent_level', 'agent_ga', 'agent_ga_effective_date', 'agent_email'])
            ->map(function($agent) {
                return [
                    "agent_id" => $agent->agent_id,
                    "agent_code" => $agent->agent_code,
                    "agent_name" => $agent->agent_name,
                    "agent_level" => $agent->agent_level,
                    "agent_ga" => $agent->agent_ga,
                    'agent_ga_effective_date' => $agent->agent_ga_effective_date
                        ? $agent->agent_ga_effective_date->format('m/d/Y')
                        : null,
                    "agent_email" => $agent->agent_email
                ];
            })
            ->toArray();

        if (!empty($agentListDetails)) {
            $this->agentVip['agent_level_' . $this->level] = $agentListDetails;
            $agentLists = array_column($agentListDetails, 'agent_id');
            $this->level++;
            $this->getDownline($agentLists);
        }

        return $this->agentVip;
    }

    public function totalActiveAgents($filters)
    {
        $activeAgents = AgentInfo::where('agent_status', 'A')->selectRaw("agent_id as id, CONCAT(agent_fname,' ',agent_lname) as name, agent_code");
        if (isset($filters['limit'])) {
            $activeAgents = $activeAgents->take($filters['limit']);
        }
        $activeAgents = $activeAgents->orderBy('agent_fname', 'asc')->get();
        return $this->successResponse('success', $activeAgents);
    }

    private function getExpiredDownline($agentID): array
    {
        // get obsolete downlines (could be 0 or 1+)
        $obs_downlines = AgentUplineHistory::where('agent_ga', '=', $agentID)
            ->get()
            ->map(function($each_record) {
                $agent = $each_record->agent;
                if (! is_null($agent)) {
                    return [
                        "agent_id" => $agent->agent_id,
                        "agent_code" => $agent->agent_code,
                        "agent_name" => $agent->full_name,
                        "agent_level" => $agent->agent_level,
                        "agent_ga" => $each_record->agent_ga,
                        "agent_email" => $agent->agent_email,
                        'effective_date_start' => $each_record->effective_date_start
                            ? $each_record->effective_date_start->format('m/d/Y')
                            : null,
                        'effective_date_end' => $each_record->effective_date_end
                            ? $each_record->effective_date_end->format('m/d/Y')
                            : null
                    ];
                }
            })
            ->filter()
            ->values()
            ->all();

        $obs_downlines = array_map(function ($each_obs_downline) {
            $this->agentVip = [];
            $this->level = 0;

            $downline = $this->getDownline([$each_obs_downline['agent_id']]);

            for ($i = count($downline) - 1; $i > 0; $i--) {
                foreach ($downline['agent_level_' . $i] as $down) {
                    $uplineKey = array_search($down['agent_ga'], array_column($downline['agent_level_' . ($i - 1)], 'agent_id'));
                    $downline['agent_level_' . ($i - 1)][$uplineKey]['downline'][] = $down;
                }
            }

            return array_merge($each_obs_downline, ['downline' => $downline['agent_level_0'] ?? []]);
        }, $obs_downlines);

        return $obs_downlines;
    }
    public function getAgentInfo($request){
        $agent_info = AgentInfo::where('agent_email', $request->agent_email)
            ->select('agent_id', 'agent_code', 'agent_lname', 'agent_email', 'agent_lname', 'agent_level', 'agent_level_2')
            ->first();

        if ($agent_info) {
            $agent_info['rep'] = 'rep';
            return $this->successResponse('Success', $agent_info, 200);
        }

        $agent_users = AgentUser::where('username', $request->agent_email)
            ->select(
                'id', 'agent_id', 'username', 'is_root', 'status',
                'agent_code', 'is_email_valid'
            )
            ->first();

        if ($agent_users) {
            $rep_user = AgentInfo::where('agent_id', $agent_users->agent_id)
                ->select('agent_id', 'agent_code', 'agent_lname', 'agent_email', 'agent_lname', 'agent_level', 'agent_level_2')
                ->first();

            $rep_user_array = $rep_user->toArray();

            $rep_user_array['admin'] = 'admin';

            $rep_user_array['admin_users'] = $agent_users->toArray();

            return $this->successResponse('Success', $rep_user_array, 200);
        }

        return $this->failedResponse("Agent doesn't exist", 401);
    }

        public function getDownlines($agentID)
        {
            $array_agents = array();
            $agents = AgentInfo::where('agent_ga', '=', $agentID)->get();

            foreach ($agents as $agentIds) {
                $array_agents[] = $agentIds->agent_id;
                self::$array_downlines[] = $agentIds->agent_id;
            }
            $allDownlines = self::getallDownlines($array_agents);

            return $allDownlines;

        }

        //function to fetch all downline
        public static function getallDownlines($agentArray)
        {
            $array_agents = array();
            $temp_array = array();
            $array_agents = array_merge($array_agents, $agentArray);
            $data_agent = AgentInfo::whereIn('agent_ga', $agentArray)->get();

            foreach ($data_agent as $agentIds) {
                self::$array_downlines[] = $agentIds->agent_id;
                $temp_array[] = $agentIds->agent_id;
            }
            if (count($temp_array) > 0) {
                self::getallDownlines($temp_array);
            }
            return self::$array_downlines;
        }
}
