<?php

namespace App\Repositories\Agents;

use App\Mail\PolicyEmail;
use App\AgentUser;
use App\AgentInfo;
use App\AgentGroup;
use App\AgentLicenseDocument;
use App\AgentLicense;
use App\CompanyInformation;
use App\EmailSetting;
use App\EmailPreference;
use League\Flysystem\Filesystem;
use League\Flysystem\Sftp\SftpAdapter;
use Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Database\Eloquent\Model;

class AgentLicenseUpdate extends Model
{
    public function listAgentLicense($request)
    {
        return AgentLicense::where('license_agent_id',$request->agent_id)->where('license_deleted','0')->get();    
    }
    public function saveAgentLicense($request)
    {
    	$state = $request->license_state;
    	$licenseExpiryDate = $request->license_exp_date;
    	$check = AgentLicense::where('license_agent_id',$request->agent_id)->where('license_state',$state)->where('license_deleted','!=','1')->get()->count();
        if(($state == "OH")||($state == "FL")||($state == "IA")||($state == "MI")||($state == "VA")||($state == "SC"))
        {
            if($licenseExpiryDate == '')
            {
                $licenseExpiryDate = '2020-01-01';
            }
        }    
        else
        {
            if($licenseExpiryDate == '')
            {
                return array('type'=>'error', 'message'=>'Attention! You must enter an expiration date for the license!');
            }
        }
        if($check)
        {
            return array('type'=>'error', 'message'=>'LicenseDuplicate');
        }
        else
        {
        	try{
                $licenseId = AgentLicense::insertGetId([
                    'license_agent_id' => $request->agent_id, 
           	        'license_number' => $request->license_number, 
           	        'license_exp_date' => $licenseExpiryDate, 
           	        'license_state' => $state,
           	        'license_status' => 'A',
           	        'license_resident' => $request->license_resident,
           	        'license_added_date' => time()    
                ]);
            }catch(Exception $e){
                $message = 'Failed To add License: ';
                return ['error' => $e->getMessage()];            
            }  
            $action = 'License for '.$state.' is added';
            $subject = 'License Added';
            $resultEmail = ['type'=>'','message'=>''];
            $resultUpload = ['type'=>'','message'=>''];
            if($this->readEmailPref('AGTLADD','0'))
            {
                $resultEmail = $this->sendAddLicenseEmailNotification($request->agent_id,$licenseId,$action,$subject);
            }    
            if($request->hasFile('file'))
            {
                $resultUpload = $this->uploadLicenseFile($request,$licenseId);
            }
            return $this->returnStatus($resultEmail,$resultUpload);
        } 
    }
    public function sendAddLicenseEmailNotification($agentId,$licenseId,$action,$subject)
    {
        try{
            $agentInfo = AgentInfo::where('agent_id',$agentId)->first();
            if(!$agentInfo)
            {
                return array('type'=>'error','message'=>' Email not sent. Agent Information not found.'); 
            }
            $companyInformation = CompanyInformation::where('company_id','1')->first();
            $agentLicense = AgentLicense::where('license_id',$licenseId)->first();

	        if($agentLicense->license_status=="A") { $ls="Active"; }
		    if($agentLicense->license_status=="D") { $ls="Disabled"; }
		    if($agentLicense->license_status=="P") { $ls="Pending"; }
		
            if($agentLicense->license_resident=="0"){ $lr="No"; }
	        if($agentLicense->license_resident=="1"){ $lr="Yes"; }
	
            $agentEmail = $agentInfo->agent_email;
            $a1 = $companyInformation->support_800;
            $supportPhone = "$a1[0]$a1[1]$a1[2]-$a1[3]$a1[4]$a1[5]-$a1[6]$a1[7]$a1[8]$a1[9]";
            
            $emailData = [
            	'action' => $action,
            	'fname'=> $agentInfo->agent_fname,
            	'lname'=> $agentInfo->agent_lname,
            	'agentCode'=> $agentInfo->agent_code,
            	'licenseState'=> $agentLicense->license_state,
            	'licenseNumber'=> $agentLicense->license_number,
            	'licenseExpiryDate'=> $agentLicense->license_exp_date,
            	'licenseStatus'=> $ls,
            	'residentLicense'=> $lr,
            	'dateAdded'=> date("m-d-Y",$agentLicense->license_added_date),
            	'supportPhone'=> $supportPhone,           	
                'subject' => $subject,
                'templateName' => 'emailUpdateAgentLicense',
                'data' =>[],
            ];
            //$testEmail1 = '<EMAIL>';
            //$testEmail2 = '<EMAIL>';
            //Mail::to($testEmail2)->send(new PolicyEmail($emailData));
            Mail::to($agentEmail)
                ->send(new PolicyEmail($emailData));
            return array('type'=>'success','message'=>' Email sent Successfully.');            
        }catch(Exception $e){
            $message = 'Failed To send notification email: ';
            return ['error' => $e->getMessage()];           
        }
    }
    public function uploadLicenseFile($request,$licenseId)
    {
        try 
        {
            $fName = pathinfo($request->file->getClientOriginalName(), PATHINFO_FILENAME);
            $ext = pathinfo($request->file->getClientOriginalName(), PATHINFO_EXTENSION);                    
            $fileName = $fName.$ext.'_'.date('m-d-Y').'_'.$request->agent_id.'.'.$ext;
            $file_local = $request->file('file');
            $extArray = array('pdf','jpg','jpeg','gif','png','PDF','JPG','JPEG','GIF','PNG');
            if(!in_array($ext,$extArray))
            {            
                return array('type' => 'error', 'message' =>' Document is not uploaded. Invalid document type. Please upload your document in pdf or jpg or jpeg or gif or png or PDF or JPG or JPEG or GIF or PNG format.');    
            }            
            if (Storage::disk('nuerabenefits')->put('/admn/__files__/licenses/' . $fileName, file_get_contents($file_local)))
            {
                AgentLicenseDocument::insert([
                	'license_id' => $licenseId, 
                	'file_name' => $fileName, 
                	'date_added' => time(), 
                	'file_status' => '1'
                ]);
                return array('type'=>'success','message'=>' License Document Added Successfully.');
            }                   
            else
            {
                return array('type'=>'success','message'=>' License document not uploaded.');
            }
        } 
        catch (Exception $e) 
        {
            $message = 'Failed To upload License: ';
            return ['error' => $e->getMessage()];
        }           
    }
    public function editAgentLicense($request)
    {
    	$state = $request->license_state;
    	$licenseExpiryDate = $request->license_exp_date;
    	$check = AgentLicense::where('license_agent_id',$request->agent_id)->where('license_state',$state)->where('license_deleted','!=','1')->get()->count();
        if(($state == "OH")||($state == "FL")||($state == "IA")||($state == "MI")||($state == "VA")||($state == "SC"))
        {
            if($licenseExpiryDate == '')
            {
                $licenseExpiryDate = '2020-01-01';
            }
        }    
        else
        {
            if($licenseExpiryDate == '')
            {
                return array('type'=>'error', 'message'=>'Attention! You must enter an expiration date for the license!');
            }
        }
        try{        
            AgentLicense::where('license_id',$request->license_id)->where('license_id',$request->agent_id)->update([
       	        'license_number' => $request->license_number, 
       	        'license_exp_date' => $licenseExpiryDate, 
       	        'license_state' => $state,
       	        'license_status' => $request->license_status,
       	        'license_resident' => $request->resident
            ]);
        }catch(Exception $e){
            $message = 'Failed To edit License: ';
            return ['error' => $e->getMessage()];            
        } 
        $stat='';
        $types='';
        if ($request->license_status == 'A') {
            $stat = 'Approved/Updated';
            $types = 'AGTLAPP';
        }
        if ($request->license_status == 'D') {
            $stat = 'Disabled';
            $types = 'AGTLDIS';
        }
        if ($request->license_status == 'P') {
            $stat = 'Pending Approval.';
            $types = 'AGTLPAP';
        }        
        $action = 'Your License for '.$state.' is '.$stat.'.';
        $subject = $state.' License '.$stat.'.';
        $resultEmail = ['type'=>'','message'=>''];
        $resultUpload = ['type'=>'','message'=>''];
        if($this->readEmailPref($types, '0')){
            $resultEmail = $this->sendAddLicenseEmailNotification($request->agent_id,$request->license_id,$action,$subject);
        }    
        if($request->hasFile('file'))
        {
            $resultUpload = $this->uploadLicenseFile($request,$request->license_id);
        }
        return $this->returnStatus($resultEmail,$resultUpload);
    }
    public function readEmailPref($code,$type) 
    { 
	    //In backoffice one more step to check EmailSetting i.e. if email setting does not exist for COMPANY then return FALSE, is included however here EmailSetting is not used so not including that part of code
	    $q2 = EmailPreference::where('pref-code',$code)->first();
	    if($type=='0' && optional($q2)->getAttribute('pref-enabled')=='1') {
		    return 1;
	    }
	    elseif($type=='1' && optional($q2)->getAttribute('pref-pref-cc-agent')=='1') {
		    return 1;	
	    }
	    else{
		    return 0;	
	    }
    }    
    public function deleteAgentLicense($request)
    {
    	try
    	{
            $deletedRows = AgentLicense::where('license_id',$request->license_id)->where('license_agent_id',$request->agent_id)->update(['license_status'=> 'D', 'license_deleted'=>'1']);
            if ($deletedRows > 0) {
                return array('type' => 'success', 'message' =>'Agent License Deleted.');
            }
            else{
                return array('type' => 'error', 'message' =>'Agent License not found.');
            }
        }
        catch(Exception $e)
        {
            $message = 'Failed To delete License: ';
            return ['error' => $e->getMessage()];            
        }    
    }
    public function returnStatus($resultEmail,$resultUpload)
    {
        if($resultEmail['type']=='error' && $resultUpload['type']=='error')
        {
            return array('type'=>'success','message'=>'License Information Added Successfully.'.$resultUpload['message'].'.'.$resultEmail['message']);
        }
        elseif($resultEmail['type']=='error')
        {
        	if($resultUpload['type']=='success')
        	{
                return array('type'=>'success','message'=>'License Added Successfully and license document uploaded.'.$resultEmail['message']);
        	}
        	else
        	{            	
                return array('type'=>'success','message'=>'License Added Successfully.'.$resultEmail['message']);
            }
        }
        elseif($resultUpload['type']=='error')
        {
        	if($resultEmail['type']=='success')
        	{
                return array('type'=>'success','message'=>'License Added Successfully and email notification sent.'.$resultUpload['message']);            	
        	}
        	else
        	{
                return array('type'=>'success','message'=>'License Added Successfully.'.$resultUpload['message']);
            }
        }                    
        elseif($resultEmail['type']=='success' && $resultUpload['type']=='success')
        {
        	return array('type'=>'success','message'=>'License Added Successfully, Document Uploaded and email notification sent.');
        }
        elseif($resultEmail['type']=='success' || $resultUpload['type']=='success')
        {
        	if($resultEmail['type']=='success')
        	{
        	    return array('type'=>'success','message'=>'License Added Successfully and email notification sent.');
        	}
        	if($resultUpload['type']=='success')
        	{
        	    return array('type'=>'success','message'=>'License Added Successfully, Document Uploaded.');
        	}
        }        
        else
        {
        	return array('type'=>'success','message'=>'License Added Successfully.');
        }        
    }        
}