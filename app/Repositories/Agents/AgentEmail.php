<?php


namespace App\Repositories\Agents;


use App\AgentGroup;
use App\AgentInfo;
use App\AgentUser;
use App\Helpers\DecryptEcryptHelper;
use App\Helpers\GuzzleHelper;
use App\UserActivityDetail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Date;
use Storage;

class AgentEmail extends Model
{
    public function sendReminderEmailRepresentativeActivationIncomplete()
    {

        /*
        Trigger Email to Rep for Logging into Agent Dashboard
        select agents registered after January 1, 2021 (Timestamp: 1609459200) and check for agent activation progress.
        If not completed then send email.
        */
        $agentToEmailDay4 = 0;
        $agentToEmailDay6 = 0;
        $agentToEmailDay9 = 0;
        $agentToEmailDay12 = 0;
        $agentToEmailDay15 = 0;
        $totalEmailSend = 0;
        $totalFailedCount = 0;
        $sentEmailDetails = [];
        $agentIdArray = AgentInfo::where([
            ['agent_signup_date','>=',1609459200],
            ['tranning_flag','=','0'],
            ['agent_status',"=",'A'],
        ])
            ->pluck('agent_id')->toArray();
        foreach($agentIdArray as $agent){

            $agentInfo = AgentInfo::where('agent_id',$agent)->first();
            $dateDiff = ceil(abs(time() - $agentInfo->agent_signup_date)/(60*60*24));
            $emailConfiguration = NULL;
            $subject = NULL;
            $userActivityDetail = UserActivityDetail::where('user_id',$agent)->where('user_type','=','broker')->where('action','=','login')->first();
            $trainingFlag = $agentInfo->tranning_flag;
            $loginAction = optional($userActivityDetail)->web;
            $loginApp = optional($userActivityDetail)->app;
            $agentActivationDiff = ceil(abs(time() - strtotime($agentInfo->agent_activation_date))/(60*60*24)) - 1 ;
            //4 Days after Activation email send
            if($agentActivationDiff == 4){
                $emailConfiguration = 'PERSONAL_WEBSITE_EMAIL';
                $subject = 'Your Personalized Website with Nu Era Benefits Agency.';
                $changeUrl = true;
            }

            if ($loginApp == 0 && in_array($agentActivationDiff,[6,14,28,42,56])){ //send email in 6 days,2weeks,4 week,6 week,8 week of activation
                //Reminder Email if agent has not logged in to mobile app    
                $emailConfiguration = 'DOWNLOAD_THE_APP_EMAIL';
                $subject = 'Nu Era Benefits Agency on the go, download our mobile app.';
                $changeUrl = true;
            } elseif ($loginAction==0 && in_array($agentActivationDiff,[2,7,21,35,49])) { //send email in 2 days,1week , 3 week, 5week ,7 week of activation
                //Step 2: Dashboard login send a notification
                $emailConfiguration = 'BROKER_REP_BACK_OFFICE_EMAIL';
                $subject = 'Nu Era Benefits Agency: Broker-Rep Back office.';
                $changeUrl = true;
            }

            if ($emailConfiguration != NULL) {
                $result = $this->sendRepresentativeEmail($agent,$emailConfiguration,$subject,$loginAction,$loginApp,$trainingFlag,$changeUrl);
                if ( isset($result) && $result['type'] == 'success') {
                    ECHO "Email sent successfully. Agent Id: ".$agent." \n";

                    if ($dateDiff == 4) {
                        $agentToEmailDay4 += 1;
                    } elseif ($dateDiff==6) {
                        $agentToEmailDay6 += 1;
                    } elseif ($dateDiff==9) {
                        $agentToEmailDay9 += 1;
                    } elseif ($dateDiff==12) {
                        $agentToEmailDay12 += 1;
                    } elseif ($dateDiff>14) {
                        $agentToEmailDay15 += 1;
                    }
                    $totalEmailSend += 1;
                    array_push($sentEmailDetails,array('sn'=>$totalEmailSend,'agent_name'=>$agentInfo->agent_fname.' '.$agentInfo->agent_lname,'agent_email'=>$agentInfo->agent_email));
                } else {
                    ECHO "Failed to send email. Agent Id: ".$agent." \n";
                    $totalEmailSend += 1;
                }
            }else{
                ECHO "Email notification not sent. Agent Id: ".$agent." \n";
            }

            $loginAction = NULL;
            $loginApp = NULL;
            $trainingFlag = NULL;
        }
        if(count($agentIdArray)>0){
            $totalInactiveCount = count($agentIdArray);
            $agentToEmailDay4_percentage = number_format(round(($agentToEmailDay4*100)/$totalInactiveCount,2),2);
            $agentToEmailDay6_percentage = number_format(round(($agentToEmailDay6*100)/$totalInactiveCount,2),2);
            $agentToEmailDay9_percentage = number_format(round(($agentToEmailDay9*100)/$totalInactiveCount,2),2);
            $agentToEmailDay12_percentage = number_format(round(($agentToEmailDay12*100)/$totalInactiveCount,2),2);
            $agentToEmailDay15_percentage = number_format(round(($agentToEmailDay15*100)/$totalInactiveCount,2),2);
        } else {
            $totalInactiveCount=0;
            $agentToEmailDay4_percentage = number_format(0,2);
            $agentToEmailDay6_percentage = number_format(0,2);
            $agentToEmailDay9_percentage = number_format(0,2);
            $agentToEmailDay12_percentage = number_format(0,2);
            $agentToEmailDay15_percentage = number_format(0,2);
        }

        $contentData = [
            'agent_email_day_4' => $agentToEmailDay4,
            'agent_email_day_4_percentage' => $agentToEmailDay4_percentage,
            'agent_email_day_6' => $agentToEmailDay6,
            'agent_email_day_6_percentage' => $agentToEmailDay6_percentage,
            'agent_email_day_9' => $agentToEmailDay9,
            'agent_email_day_9_percentage' => $agentToEmailDay9_percentage,
            'agent_email_day_12' => $agentToEmailDay12,
            'agent_email_day_12_percentage' => $agentToEmailDay12_percentage,
            'agent_email_day_15' => $agentToEmailDay15,
            'agent_email_day_15_percentage' => $agentToEmailDay15_percentage,
            'total_inactive_agents' => $totalInactiveCount,
            'total_email_send' => $totalEmailSend,
            'total_failed_count' => $totalFailedCount,
            'sent_email_details' => $sentEmailDetails
        ];

        $toAddress = config("testemail.TEST_EMAIL") ? [config("testemail.TEST_EMAIL")] : ['<EMAIL>','<EMAIL>'];
        $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [];
        $bccAddress = config("testemail.TEST_EMAIL_BCC") ? [config("testemail.TEST_EMAIL_BCC")] : [];
        $body = [
            'email_message_configuration_name'=> 'REPRESENTATIVE_REMINDER_EMAIL_SUMMARY',
            'toAddress'=> $toAddress,
            'ccAddress'=> $ccAddress,
            'bccAddress'=> $bccAddress,
            'subject'=> 'Representative Reminder Email Summary '.date('Y-m-d'),
            'attachedFiles'=> [],
            'generalTemplateData'=> [],
            'contentData'=> $contentData
        ];

        $apiUrl = config('app.messagecenter.key').'api/v1/send-email-with-content-data';
        $responseJson = GuzzleHelper::postApi($apiUrl,[],$body);
        $response = json_decode($responseJson,true);

        if( isset($response['status_code']) && $response['status_code']=='200' && $response['status']=='success'){
            return array('type' => 'success', 'message' => 'Email Notification sent successfully with email summary.');
        }else{
            return array('type' => 'error', 'message' => 'Email Notification sent successfully.');
        }
    }

    private function sendRepresentativeEmail($agent,$emailConfiguration,$subject,$loginAction,$loginApp,$trainingFlag,$changeUrl = false)
    {
        $agentInfo = AgentInfo::where('agent_id',$agent)->first();
        $agentInfoGa = AgentInfo::where('agent_id',$agentInfo->agent_ga)->first();
        $agentInfoBex = AgentGroup::whereIn('gid',array('1434','1399','2599'))->where('agent_id',$agent)->count();
        $agentInfo123 = AgentGroup::where('gid','1881')->where('agent_id',$agent)->count();
        $agentInfoElite = AgentGroup::where('gid','1073')->where('agent_id',$agent)->count();
        $agentInfoPremier = AgentGroup::where('gid','1926')->where('agent_id',$agent)->count();
        $agentFirstName = $agentInfo->agent_fname;
        $agentLastName = $agentInfo->agent_lname;
        $agentState = $agentInfo->agent_state;
        $agentCode = $agentInfo->agent_code;
        if(isset($agentInfo->agentCustomeHomepage)) {
            $a1 = $agentInfo->agentCustomeHomepage->phone;
            $agentEmail = $agentInfo->agentCustomeHomepage->email;
        } else {
            $a1 = $agentInfo->agent_phone1;
            $agentEmail = $agentInfo->agent_email;
        }
        $agentPhone = NULL;
        if( strlen($a1) == 10){
            $agentPhone = "($a1[0]$a1[1]$a1[2]) $a1[3]$a1[4]$a1[5]-$a1[6]$a1[7]$a1[8]$a1[9]";
        }
        $b1 = optional($agentInfoGa)->agent_phone1;
        $agentPhoneGa = NULL;
        if(strlen($b1) == 10){
            $agentPhoneGa = "($b1[0]$b1[1]$b1[2]) $b1[3]$b1[4]$b1[5]-$b1[6]$b1[7]$b1[8]$b1[9]";
        }
        $agentEmailGa = $agentInfoGa->agent_email;
        $encryptedPassword = $agentInfo->agent_password;
        $agentPwd = DecryptEcryptHelper::decryptInfo($encryptedPassword);
        $agentFirstNameGa = optional($agentInfoGa)->agent_fname;
        $agentLastNameGa = optional($agentInfoGa)->agent_lname;
        $url = '';
        if($changeUrl){
            if ($agentInfo123 >= 1 && $agentInfo->agent_web_dental != '') {
                $url .= 'https://goenroll123.com/'.$agentInfo->agent_web_dental;
            } elseif ($agentInfoBex >= 1 && $agentInfo->agent_web_dental != '') {
                $url .= 'https://brokerexchanges.com/'.$agentInfo->agent_web_dental;
            } elseif ($agentInfoPremier >=1 && $agentInfo->agent_web_dental !=''){
                $url .= 'https://premierenroll.com/'.$agentInfo->agent_web_dental;
            } elseif ($agentInfoElite >=1 && $agentInfo->agent_web_dental !=''){
                $url .= 'https://eliteenroll.com/'.$agentInfo->agent_web_dental;
            }
        }else{
            if ($agentInfo123 >= 1 && $agentInfo->agent_web_dental != '' && $agentInfo->agent_ga !='10055') {
                $url .= 'https://goenroll123.com/'.$agentInfo->agent_web_dental. '</br>';
            } elseif ($agentInfoBex >= 1 && $agentInfo->agent_web_dental != '') {
                $url .= 'https://brokerexchanges.com/'.$agentInfo->agent_web_dental. '</br>';
            } elseif ($agentInfoPremier >=1 && $agentInfo->agent_web_dental !='' && $agentInfo->agent_ga !='10055'){
                $url .= 'https://premierenroll.com/'.$agentInfo->agent_web_dental. '</br>';
            } elseif ($agentInfoElite >=1 && $agentInfo->agent_web_dental !='' && $agentInfo->agent_ga !='10055'){
                $url .= 'https://eliteenroll.com/'.$agentInfo->agent_web_dental. '</br>';
            }
        }

        $contentData = [
            'rep_first_name'=> ucfirst($agentFirstName),
            'rep_last_name' => ucfirst($agentLastName),
            'rep_code'=> $agentCode,
            'rep_state'=>$agentState,
            'rep_url'=> $url,
            'rep_phone'=> $agentPhone,
            'upline_first_name'=> ucfirst($agentFirstNameGa),
            'upline_last_name'=> ucfirst($agentLastNameGa),
            'upline_email'=> $agentEmailGa,
            'upline_phone'=> $agentPhoneGa,
            'rep_email'=> $agentEmail,
            'rep_temp_password'=> $agentPwd,
            'is_app_login' => $loginApp,
            'is_dashboard_login' => $loginAction,
            'is_training' => $trainingFlag
        ];


        $toAddress = config("testemail.TEST_EMAIL") ? [config("testemail.TEST_EMAIL")] : [$agentEmail];
        $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [$agentEmailGa,'<EMAIL>'];
        $bccAddress = config("testemail.TEST_EMAIL_BCC") ? [config("testemail.TEST_EMAIL_BCC")] : [];
        $body = [
            'email_message_configuration_name'=> $emailConfiguration,
            'toAddress'=> $toAddress,
            'ccAddress'=> $ccAddress,
            'bccAddress'=> $bccAddress,
            'subject'=> $subject,
            'attachedFiles'=> [],
            'generalTemplateData'=> [],
            'contentData'=> $contentData
        ];

        $apiUrl = config('app.messagecenter.key').'api/v1/send-email-with-content-data';
        $responseJson = GuzzleHelper::postApi($apiUrl,[],$body);
        $response = json_decode($responseJson,true);

        if( isset($response['status_code']) && $response['status_code']=='200' && $response['status']=='success'){
            return array('type' => 'success', 'message' => 'Email Notification sent successfully.');
        }else{
            return array('type' => 'error', 'message' => 'Failed to send email.');
        }
    }

    public function sendAgentRegistrationWeeklyReport()
    {
        $before90DaysTimestamp = (time() - (90*86400));
        $agentInfo = AgentInfo::where('agent_signup_date','>=',$before90DaysTimestamp)->orderBy('agent_id','desc')->get();

        $mainHeading = "Code\tFname\tLname\tST\tPhone\tPhone2\tEmail\tStatus\tSign Up Date\tActivation\tMobileApp\tDashboard Login\n";
        $fileName = 'Agent_Weekly_Report_'.date('Y-m-d').'.xlsx';
        $fileDirectory = storage_path('logs/');
        $file = fopen($fileDirectory.$fileName,'w');
        fputs($file,$mainHeading);
        foreach ($agentInfo as $agent) {
            $contract = null;
            $agentUsers = AgentUser::where('agent_id',$agent->agent_id)->first();
            $userActivityDetailWeb = UserActivityDetail::where([
                ['user_id',$agent->agent_id],
                ['user_type','broker']
            ])->first();
            $activation = (optional($agentUsers)->status != 'A') ? 'X' : NULL;
            $agentSignUpDate = date('Y-m-d',$agent->agent_signup_date);
            $dashboardLogin = (optional($userActivityDetailWeb)->web == 0) ? 'X' : NULL;
            $mobileApp = (optional($userActivityDetailWeb)->app == 0) ? 'X' : NULL;
            $row = $agent->agent_code."\t".$agent->agent_fname."\t".$agent->agent_lname."\t".$agent->agent_state."\t".$agent->agent_phone1."\t".$agent->agent_phone2."\t".$agent->agent_email."\t".$agent->agent_status."\t".$agentSignUpDate."\t".$activation."\t".$mobileApp."\t".$dashboardLogin."\n";
            fputs($file,$row);
        }
        fclose($file);

        $toAddress = config("testemail.TEST_EMAIL") ? [config("testemail.TEST_EMAIL")] : ['<EMAIL>','<EMAIL>'];
        $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [];
        $bccAddress = config("testemail.TEST_EMAIL_BCC") ? [config("testemail.TEST_EMAIL_BCC")] : [];
        $body = [
            'email_message_configuration_name'=> "AGENT_WEEKLY_REPORT_EMAIL",
            'toAddress'=> $toAddress,
            'ccAddress'=> $ccAddress,
            'bccAddress'=> $bccAddress,
            'subject'=> "Agent Weekly Summary Report",
            'attachedFiles'=> [$fileName],
            'generalTemplateData'=> [],
            'contentData'=> []
        ];

        $apiUrl = config('app.messagecenter.key').'api/v1/send-email';
        $responseJson = GuzzleHelper::postApi($apiUrl,[],$body);
        $response = json_decode($responseJson,true);

        if( isset($response['status_code']) && $response['status_code']=='200' && $response['status']=='success'){
            ECHO "Email Notification with report sent successfully.\n";
        }else{
            ECHO "Failed to send email.\n";
        }

        $zip = new \ZipArchive();
        $res = $zip->open($fileDirectory.'AgentWeeklyReportArchived.zip',\ZipArchive::CREATE);
        if ( $res === TRUE) {
            $zip->addFile($fileDirectory.$fileName,$fileName);
            $zip->close();
            if ( \File::delete(storage_path('logs/'.$fileName)) ) {
                ECHO "Added to archive and deleted file : ",$fileName."\n";
            } else {
                ECHO "Added to archive but unable to delete file : ",$fileName."\n";
            }
        } else {
            ECHO "Failed to archive file.".$res."\n";
        }

    }
}
