<?php

namespace App\Repositories\Agents;

use App\ManageAgentScripts;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;

class ManageAgentScriptsRepository
{
    use ResponseMessage, Paginator;

    static $withRelations = [
        'plans',
        'agents',
    ];

    public function store($data)
    {
        try {
            // $isMedicalCode = ManageAgentScripts::where('medical_code', strtoupper($data->medical_code))->exists();
            // if ($isMedicalCode) {
            //     return $this->failedResponse('Medical code name already exists.Please try another!');
            // }
            // $data = [
            //     'agent_id' => $agent_id,
            //     'agent_downlines' => $downlines,
            //     'type' => 'exclude',
            //     'updated_by' => 'AUTO',
            //     'pt_date' => $data->pt_date,
            //     'already_processed' => false,
            //     'updated_at' => date('Y-m-j H:i:s')
            // ];
            // $isCreated =  ManageAgentScripts::create($payload);
            // if ($isCreated) {
            //     return $this->successResponse('Agent Tree Updated Successfully!');
            // }
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function update($id, $data)
    {
        try {
            $isUpdated =  ManageAgentScripts::where('agent_id', $id)->update($data);
            if ($isUpdated) {
                return $this->successResponse('Agent Tree Updated Successfully!');
            }
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            ManageAgentScripts::where('id', $id)->delete();
            DB::commit();
            return $this->successResponse('Agent Tree Deleted Successfully!');
        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();
            return $this->failedResponse($e->getMessage());
        }
    }

    public function show($code)
    {
        try {
            $data =  ManageAgentScripts::join('agent_info', 'manage_agent_scripts.agent_id', '=', 'agent_info.agent_id')
                ->where('id', $code)->select('manage_agent_scripts.*', 'agent_fname', 'agent_mname', 'agent_lname')->first();
            return $this->successResponse('Data Feteched Successfully!', $data);
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }


    public function index($filters = [])
    {
        try {
            $data = $this->paginatedList(10, $filters)
                ->appends($filters);
            return $this->formattedData($data);
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = ManageAgentScripts::query()->join('agent_info', 'manage_agent_scripts.agent_id', '=', 'agent_info.agent_id')
            ->select('manage_agent_scripts.*', 'agent_fname', 'agent_mname', 'agent_lname');
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['agent_name'])) {
            $name = strtolower($filters['agent_name']);
            $agent_id = DB::connection('mysql2')->table('agent_info')->whereRaw("(LOWER(agent_fname) like '$name%')")->pluck('agent_id')->toArray();
            $agent_id = $agent_id ? implode(',', $agent_id) : 0;
            $data->whereRaw("manage_agent_scripts.agent_id in ( $agent_id)");
        }
        if (isset($filters['agent_lname'])) {
            $name = strtolower($filters['agent_lname']);
            $agent_id = DB::connection('mysql2')->table('agent_info')->whereRaw("(LOWER(agent_lname) like '$name%')")->pluck('agent_id')->toArray();
            $agent_id = $agent_id ? implode(',', $agent_id) : 0;
            $data->whereRaw("manage_agent_scripts.agent_id in ( $agent_id)");
        }
        if (isset($filters['agent_code'])) {
            $name = strtolower($filters['agent_code']);
            $agent_id = DB::connection('mysql2')->table('agent_info')->whereRaw("(LOWER(agent_code) like '$name%')")->pluck('agent_id')->toArray();
            $agent_id = $agent_id ? implode(',', $agent_id) : 0;
            $data->whereRaw("manage_agent_scripts.agent_id in ( $agent_id)");
        }
        if (isset($filters['pt_date'])) {
            $pt_date = $filters['pt_date'];
            $data->where('manage_agent_scripts.pt_date', 'like', "$pt_date%");
        }
    }

    public function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result[] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    protected function singleFormattedItem($d)
    {
        return [
            'id' => $d->id,
            'agent_id' => $d->agent_id,
            'agent_name' => $d->agent_fname . ' ' . $d->agent_mname . ' ' . $d->agent_lname,
            'agent_downlines' => $d->agent_downlines ?: '',
            'type' => ucfirst($d->type),
            'pt_date' => $d->pt_date,
            'already_processed' => $d->already_processed,
            'updated_by' => $d->updated_by ?: '',
            'created_at' => $d->created_at,
            'updated_at' => $d->updated_at ?: '',
        ];
    }

    public function addManageAgentScriptsToAgents($data)
    {
        try {
            $payload = [
                'agent_downlines' => json_encode($data['agent_id']),
                'updated_at' => Carbon::now()
            ];
            ManageAgentScripts::where('id', $data['upline_id'])->update($payload);
            return $this->successResponse('Agent Tree successfully updated!');
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getAgentList($payload)
    {
        try {
            $agent_id = $payload->agent_id;
            $pt_date = $payload->pt_date;
            $agent_downline = DB::connection('mysql2')->table('agent_downline')->where('agent_id', $agent_id)->where('effective_date', $pt_date)->first();
            $downlines = json_decode($agent_downline->downline, true);
            $data = DB::connection('mysql2')->table('agent_info')->whereIn('agent_status', ['A', 'S'])->whereIn('agent_id', $downlines)->get(['agent_id', 'agent_fname', 'agent_mname', 'agent_lname']);
            return $this->successResponse('Success', $data);
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }



    public function getAgentDownlinesListFromAgent($id)
    {
        try {
            $response = [
                'agents' =>  json_decode(ManageAgentScripts::where('id', $id)->pluck('agent_downlines')->first(), true),
                'already_processed' => ManageAgentScripts::where('id', $id)->pluck('already_processed')->first()
            ];
            return $this->successResponse('Success', $response);
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }
}
