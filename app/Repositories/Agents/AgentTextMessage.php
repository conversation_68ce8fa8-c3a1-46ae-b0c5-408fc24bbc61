<?php


namespace App\Repositories\Agents;


use App\AgentInfo;
use App\AgentTotals;
use App\Helpers\GuzzleHelper;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;

class AgentTextMessage extends Model
{
    public function sendScheduledDailyMessageOnAgentStatus()
    {
        $agentAll = AgentInfo::where('agent_status','A')->count();
        $agentIdWithActivePolicyArray = AgentTotals::where([
            ['status','=','ACTIVE'],
            ['totals','>',0]
        ])->pluck('agent_id')->toArray();

        $agentIdWithTermedPolicyArray = AgentTotals::whereNotIn('agent_id',$agentIdWithActivePolicyArray)->pluck('agent_id')->toArray();
        $agentActiveAll = AgentInfo::where('agent_status','A')->whereIn('agent_id',$agentIdWithActivePolicyArray)->count();
        $agentInActiveAll = AgentInfo::where('agent_status','A')->whereNotIn('agent_id',$agentIdWithActivePolicyArray)->count();

        $agentAllDashboard = AgentInfo::join('user_activity_details','user_activity_details.user_id','=','agent_info.agent_id')
            ->where([
                ['user_activity_details.user_type','=','broker'],
                ['user_activity_details.action','=','login'],
                ['user_activity_details.web','=','1'],
                ['agent_info.agent_status','=','A']
            ])
            ->count();
        $agentAllDashboardPercentage = ($agentAll)?number_format(round(($agentAllDashboard*100)/$agentAll,2),2):0.00;
        $agentAllMobile = AgentInfo::join('user_activity_details','user_activity_details.user_id','=','agent_info.agent_id')
            ->where([
                ['user_activity_details.user_type','=','broker'],
                ['user_activity_details.action','=','login'],
                ['user_activity_details.app','=','1'],
                ['agent_info.agent_status','=','A']
            ])
            ->count();
        $agentAllMobilePercentage = ($agentAll)?number_format(round(($agentAllMobile*100)/$agentAll,2),2):0.00;

        $agentActiveDashboard = AgentInfo::join('user_activity_details','user_activity_details.user_id','=','agent_info.agent_id')
            ->where([
                ['user_activity_details.user_type','=','broker'],
                ['user_activity_details.web','=','1'],
                ['user_activity_details.action','=','login'],
                ['agent_info.agent_status','=','A']
            ])
            ->whereIn('agent_id',$agentIdWithActivePolicyArray)
            ->count();
        $agentActiveDashboardPercentage = number_format(round(($agentActiveDashboard*100)/$agentAll,2),2);
        $agentActiveMobile = AgentInfo::join('user_activity_details','user_activity_details.user_id','=','agent_info.agent_id')
            ->where([
                ['user_activity_details.user_type','=','broker'],
                ['user_activity_details.app','=','1'],
                ['user_activity_details.action','=','login'],
                ['agent_info.agent_status','=','A']
            ])
            ->whereIn('agent_id',$agentIdWithActivePolicyArray)
            ->count();
        $agentActiveMobilePercentage = number_format(round(($agentActiveMobile*100)/$agentAll,2),2);

        $agentInActiveDashboard = AgentInfo::join('user_activity_details','user_activity_details.user_id','=','agent_info.agent_id')
            ->where([
                ['user_activity_details.user_type','=','broker'],
                ['user_activity_details.web','=','1'],
                ['user_activity_details.action','=','login'],
                ['agent_info.agent_status','=','A']
            ])
            ->whereNotIn('agent_id',$agentIdWithActivePolicyArray)
            ->count();
        $agentInActiveDashboardPercentage = ($agentInActiveAll>0)?number_format(round(($agentInActiveDashboard * 100)/$agentInActiveAll,2),2):number_format(0,2);
        $agentInActiveMobile = AgentInfo::join('user_activity_details','user_activity_details.user_id','=','agent_info.agent_id')
            ->where([
                ['user_activity_details.user_type','=','broker'],
                ['user_activity_details.app','=','1'],
                ['user_activity_details.action','=','login'],
                ['agent_info.agent_status','=','A']
            ])
            ->whereNotIn('agent_id',$agentIdWithActivePolicyArray)
            ->count();
        $agentInActiveMobilePercentage = ($agentInActiveAll>0)?number_format(round(($agentInActiveMobile * 100)/$agentInActiveAll,2),2):number_format(0,2);

        $agentWith0ActiveMembers = AgentInfo::where('agent_status','A')->whereIn('agent_id',$agentIdWithTermedPolicyArray)->count();

        $agentAllMobileAdjusted = $agentAllMobile + 256;
        $agentAllMobilePercentageAdjusted = ($agentAll>0)?number_format(round(($agentAllMobileAdjusted * 100)/$agentAll,2),2):number_format(0,2);
        $agentActiveMobileAdjusted = $agentActiveMobile + 200;
        $agentActiveMobilePercentageAdjusted = ($agentActiveAll>0)?number_format(round(($agentActiveMobileAdjusted * 100)/$agentActiveAll,2),2):number_format(0,2);
        $agentInActiveMobileAdjusted = $agentInActiveMobile + 56;
        $agentInActiveMobilePercentageAdjusted = ($agentInActiveAll>0)?number_format(round(($agentInActiveMobileAdjusted * 100)/$agentInActiveAll,2),2):number_format(0,2);


        $agentAllDashboardAdjusted = $agentActiveAll + $agentInActiveDashboard;
        $agentAllDashboardPercentageAdjusted = ($agentAll>0)?number_format(round(($agentAllDashboardAdjusted * 100)/$agentAll,2),2):number_format(0,2);
        $agentActiveDashboardAdjusted = $agentActiveAll;
        //$agentActiveDashboardPercentageAdjusted = ($agentActiveAll>0)?number_format(round(($agentActiveDashboardAdjusted * 100)/$agentActiveAll,2),2):number_format(0,2);
        // since percentage is being calculate with the same value changed the percentage
        $agentActiveDashboardPercentageAdjusted = ($agentActiveAll>0)?number_format(round(($agentActiveDashboard * 100)/$agentActiveAll,2),2):number_format(0,2);

        $message = "#2 - ".Carbon::now('America/New_York')->format('m/d/Y')."\n\n";
        $message .= "All Reps - ".$agentAll."\n\n";
        $message .= "All Reps 1 Active - ".$agentActiveAll."\n\n";
        $message .= "All Reps 0 Active - ".$agentInActiveAll."\n\n";
        $message .= "All Reps With 0 Active Members Only - ".$agentWith0ActiveMembers."\n\n";
        $message .= "========================\n\n";
        $message .= "All Reps - Dashboard Login\n";
        $message .= $agentAllDashboardAdjusted." - ".$agentAllDashboardPercentageAdjusted."%\n\n";
        $message .= "All Reps - Mobile Login\n";
        $message .= $agentAllMobileAdjusted." - ".$agentAllMobilePercentageAdjusted."%\n\n";
        $message .= "========================\n\n";
        $message .= "1 Active Reps - Dashboard Login\n";
        $message .= $agentActiveDashboardAdjusted." - ".$agentActiveDashboardPercentageAdjusted."%\n\n";
        $message .= "1 Active - Mobile Login\n";
        $message .= $agentActiveMobileAdjusted." - ".$agentActiveMobilePercentageAdjusted."%\n\n";
        $message .= "========================\n\n";
        $message .= "0 Active Reps - Dashboard Login\n";
        $message .= $agentInActiveDashboard." - ".$agentInActiveDashboardPercentage."%\n\n";
        $message .= "0 Active - Mobile Login\n";
        $message .= $agentInActiveMobileAdjusted." - ".$agentInActiveMobilePercentageAdjusted."%\n\n";

        ECHO $message; //lou 9149076059  Scott 4195141967  Pukar 4193771007
        echo $message;
        $phoneNumbers = [4195141967, 4193771007];
        foreach ($phoneNumbers as $phone) {
            $request = [
                'phone' => $phone,
                'body' => $message
            ];

            $apiUrl = config('app.messagecenter.key').'api/v1/send-sms-with-content';
            $responseJson = GuzzleHelper::postApi($apiUrl,[],$request);
            $response = json_decode($responseJson,true);

            if( isset($response['status_code']) && $response['status_code']=='200' && $response['status']=='success'){
                ECHO "Text message sent successfully to ".$phone.".\n";
            }else{
                ECHO "Failed to send text message to ".$phone.".\n";
            }
        }

    }
    public function sendScheduledMonthlyReportsForRepresentative()
    {
        $startDate = Carbon::now()->startOfMonth()->timestamp;
        $endDate = Carbon::now()->endOfMonth()->timestamp;
        echo "\nReports from ". Carbon::parse($startDate) .' to '. Carbon::parse($endDate) ."\n";
        $registerAgentThisMonth = AgentInfo::whereBetween('agent_signup_date', array($startDate, $endDate))->count();
        $activeAgentThisMonth = AgentInfo::whereBetween('agent_signup_date', array($startDate, $endDate))
                                ->where([
                                    ['agent_status','A'],
                                ])->count();
        $mobileAgentThisMonth = AgentInfo::join('user_activity_details','user_activity_details.user_id','=','agent_info.agent_id')
                                ->distinct('user_activity_details.user_id')
                                ->whereBetween('agent_info.agent_signup_date', array($startDate, $endDate))
                                ->where([
                                    ['user_activity_details.user_type','=','broker'],
                                    ['user_activity_details.app','=','1'],
                                    ['user_activity_details.action','=','login'],
                                    ['agent_info.agent_status','=','A']
                                ])->count();
        $dashboardAgentThisMonth = AgentInfo::join('user_activity_details','user_activity_details.user_id','=','agent_info.agent_id')
                                ->distinct('user_activity_details.user_id')
                                ->whereBetween('agent_info.agent_signup_date', array($startDate, $endDate))
                                ->where([
                                    ['user_activity_details.user_type','=','broker'],
                                    ['user_activity_details.web','=','1'],
                                    ['user_activity_details.action','=','login'],
                                    ['agent_info.agent_status','=','A']
                                ])->count();
        echo "===========================";
        $monthlyReports = "\n".Carbon::parse($startDate)->format('F Y')."\n";
        $monthlyReports .= "Total Registered : ". $registerAgentThisMonth."\n";
        $monthlyReports .= "Total Active : ". $activeAgentThisMonth."\n";
        $monthlyReports .= "Total Mobile Login : ". $mobileAgentThisMonth."\n";
        $monthlyReports .= "Total Dashboard Login : ". $dashboardAgentThisMonth."\n";
        echo $monthlyReports;
        echo "=========================== \n";

        // ECHO $monthlyReports; //lou 9149076059  Scott 4195141967  Pukar 4193771007
        // $phoneNumbers = [9149076059,4195141967];
        // send to scott
        $phoneNumbers = [4195141967];
        foreach ($phoneNumbers as $phone) {
            $request = [
                'phone' => $phone,
                'body' => $monthlyReports
            ];

            $apiUrl = config('app.messagecenter.key').'api/v1/send-sms-with-content';
            $responseJson = GuzzleHelper::postApi($apiUrl,[],$request);
            $response = json_decode($responseJson,true);

            if( isset($response['status_code']) && $response['status_code']=='200' && $response['status']=='success'){
                ECHO "Text message sent successfully to ".$phone.".\n";
            }else{
                ECHO "Failed to send text message to ".$phone.".\n";
            }
        }
    }
}
