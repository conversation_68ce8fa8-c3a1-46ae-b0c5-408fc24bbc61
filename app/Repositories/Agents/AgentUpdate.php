<?php

namespace App\Repositories\Agents;


use App\Helpers\DecryptEcryptHelper;
use App\Helpers\GuzzleHelper;
use App\Helpers\SendSMSHelper;
use App\AgentUser;
use App\AgentInfo;
use App\AgentGroup;
use App\AgentInGroup;
use App\GroupInfo;
use App\CompanyInformation;
use App\CustomeHomepage;
use App\Helpers\CommonHelper;
use App\RepContractAct;
use App\Traits\ManageAgentTrait;
use App\Traits\SyncRep;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Repositories\V3\Archive\ArchiveRepository;

class AgentUpdate extends Model
{
    use ManageAgentTrait;
    const REP_IOS_APP = "https://apps.apple.com/np/app/corenroll-reps/id1527267392";
    const REP_ANDRIOD_APP = "https://play.google.com/store/apps/details?id=com.neura.corenroll";

    private $manageAgentsModel;
    private $archiveRepository;


    public function __construct(ManageAgents $manageAgentsModel , ArchiveRepository $archiveRepository)
    {
        $this->manageAgentsModel = $manageAgentsModel;
        $this->archiveRepository = $archiveRepository;
    }
    public function editAgentStatus($request,$agentUpdatesHelper)
    {
    	try{
    	    $agentInfo = AgentInfo::where('agent_id',$request->agent_id)->first();
            $agentOldStatus = $agentInfo->agent_status;
            $upline = $agentInfo->agent_ga;
    		if(AgentInfo::where('agent_id',$request->agent_id)->first())
    		{
                // AgentUser::where('agent_id',$request->agent_id)->update(['status'=>$request->status]);
                AgentInfo::where('agent_id',$request->agent_id)->update(['agent_status'=>$request->status]);

                //Log changes in agent_updates table
                $updates=[
                    'agent_id'=>$request->agent_id,
                    'elgb_act'=>'STSCHNG',
                    'elgb_act_date'=> Carbon::now()->timestamp,
                    'elgb_comment'=> 'Status Changed. Old Value: '.$agentOldStatus,
                    'elgb_agent_id'=> $request->elgb_agent_id,
                    'elgb_agent_name'=> $request->elgb_agent_name
                ];
                $agentUpdatesHelper->logAgentUpdates($updates);

                $emailStatus='';
                if ( $request->status=='A' && $request->status != $agentOldStatus) {
                    if ($agentOldStatus=='P') {
                        $emailMessageConfiguration = 'REP_ACTIVATION_EMAIL';
                        $subject = 'Representative Activation Notification';
                    } else {
                        $emailMessageConfiguration = 'REPRESENTATIVE_REACTIVATION_NOTIFICATION';
                        $subject = 'Representative Re-activation';
                    }
                    $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id,$emailMessageConfiguration,$subject);
                    if( $upline=='101301' && $agentOldStatus=='P'){
                        $emailMessageConfiguration = 'REP_APPROVAL_NOTIFICATION_KRIVELOW_DOWNLINE';
                        $subject = 'Elevate Wellness Association (EWA)- Training Session Request';
                        $emailStatusKrivelowDownline = $this->sendAgentStatusActivateEmail($request->agent_id,$emailMessageConfiguration,$subject);
                    }
                }
                elseif ($request->status == 'S' && $request->status != $agentOldStatus) {
                    $emailMessageConfiguration = 'REPRESENTATIVE_DEACTIVATION_NOTIFICATION';
                    $subject = 'Broker Account Deactivated';
                    $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id,$emailMessageConfiguration,$subject);
                }
                if(in_array($request->status,['S','D'])) {
                    $this->archiveRepository->deleteAgent($request->agent_id);
                    // $this->removeRepSSO($request->agent_id);
                } elseif($request->status == 'A') {
                    $this->archiveRepository->restoreAgent($request->agent_id);
                    // $this->addRepSSO($request->agent_id);
                }
                if ( isset($emailStatus['type']) && $emailStatus['type'] == 'success') {
                    return array('type'=>'success','message'=>'Updated agent status. Email notification sent.');
                }
                else {
                    return array('type'=>'success','message'=>'Updated agent status. Email not sent.');
                }
            }
            else {
                return array('type'=>'error','message'=>'Agent not found.');
            }
        }
        catch (Exception $e) {
            $message = 'Failed to update: ';
            return ['error' => $message.$e->getMessage()];
        }
    }
    public function activateAgent($request,$agentUpdatesHelper){
        try{
            $agentInfo = AgentInfo::where('agent_id', $request->agent_id)->first();
                if ($agentInfo) {
                AgentInfo::where('agent_id', $request->agent_id)
                ->update([
                    'agent_status' => 'A',
                ]);
                $updates = [
                    'agent_id' => $request->agent_id,
                    'elgb_act' => 'LNSCHNG',
                    'elgb_act_date' => Carbon::now()->timestamp,
                    'elgb_comment' => 'Agent status updated. ' .
                        'Updated in agent_info table. ' .
                        'Old Values: Status-' . $agentInfo->agent_status ,
                    'elgb_agent_id' => request()->header('id'),
                    'elgb_agent_name' => request()->header('name')
                ];
                $agentUpdatesHelper->logAgentUpdates($updates);
                AgentGroup::where('ag_status','!=','D')->where('agent_id',$request->agent_id)->update([
                    'ag_status'=> 'A',
                ]);
                if($agentInfo->agent_status == 'P'){
                    $emailMessageConfiguration = 'REP_ACTIVATION_EMAIL';
                    $subject = 'ACTIVATION NOTICE: Congratulations, welcome Nu Era Benefits';
                    $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id, $emailMessageConfiguration, $subject);
                    if (isset($emailStatus['type']) && $emailStatus['type'] == 'success') {
                        return array('type' => 'success', 'message' => 'Agent has been Activated. Email notification sent.');
                    } 
                }else if($agentInfo->agent_status == 'A'){
                    return array('type' => 'success', 'message' => 'Agent is already Active.');
                }
                else{
                    return array('type' => 'success', 'message' => 'Could not Activate this Agent.');
                }

            }
        } catch (Exception $e) {
            $message = 'Failed to activate agent: ';
            return ['error' => $message . $e->getMessage()];
        }
    }
    public function editAgentLevel($request,$agentUpdatesHelper): array
    {
        if ($request->premier_level == 0 && $request->ancillary_level == 0)
            return [
                'type' => 'error',
                'message' => 'Ancillary and Premier Level not set. '
                    . 'Agent\'s Level is neither Updated nor their Status Activated.'
            ];

        try {
            $agentInfo = AgentInfo::where('agent_id', $request->agent_id)->first();
            if ($agentInfo) {
                if(isset($request->agent_gid)){
                    foreach ($request->agent_gid as $gid) {
                        AgentGroup::where('agent_id', $request->agent_id)
                            ->where('gid', $gid)
                            ->update([
                                'ag_status' => 'A',
                                'lvl' => $request->premier_level
                            ]);
                    }
                }

                AgentInfo::where('agent_id', $request->agent_id)
                    ->update([
                        'agent_status' => 'A',
                        'agent_level' => $request->premier_level,
                        'agent_level_2' => $request->ancillary_level
                    ]);

                //Log changes in agent_updates table
                $updates = [
                    'agent_id' => $request->agent_id,
                    'elgb_act' => 'LNSCHNG',
                    'elgb_act_date' => Carbon::now()->timestamp,
                    'elgb_comment' => 'Agent status and level updated. ' .
                        'Updated in agent_info, agent_user and agent_groups table. ' .
                        'Old Values: Status-' . $agentInfo->agent_status .
                        ', Premier Level-' . $agentInfo->agent_level .
                        ', Ancillary Level-' . $agentInfo->agent_level_2,
                    'elgb_agent_id' => request()->header('id'),
                    'elgb_agent_name' => request()->header('name')
                ];
                $agentUpdatesHelper->logAgentUpdates($updates);
                if($agentInfo->agent_status == 'P'){
                    $emailMessageConfiguration = 'REP_ACTIVATION_EMAIL';
                    $subject = 'ACTIVATION NOTICE: Congratulations, welcome Nu Era Benefits';
                    $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id, $emailMessageConfiguration, $subject);
                }else{
                    if ($agentInfo->agent_level != $request->premier_level || $agentInfo->agent_level_2 != $request->ancillary_level) {
                        $emailMessageConfiguration = 'REPRESENTATIVE_LEVEL_CHANGED_NOTIFICATION';
                        $subject = 'Agent Level Changed';
                        if ($agentInfo->agent_level != $request->premier_level && $agentInfo->agent_level_2 != $request->ancillary_level)
                            $subject = 'Premier and Ancillary Level Changed';
                        elseif ($agentInfo->agent_level != $request->premier_level)
                            $subject = 'Premier Level Changed';
                        elseif ($agentInfo->agent_level_2 != $request->ancillary_level)
                            $subject = 'Ancillary Level Changed';
                        $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id, $emailMessageConfiguration, $subject);
                    }
                }

                if (isset($emailStatus['type']) && $emailStatus['type'] == 'success') {
                    return array('type' => 'success', 'message' => 'Premier and Ancillary Level of Agent Updated. Email notification sent.');
                } else {
                    return array('type' => 'success', 'message' => 'Premier and Ancillary Level of Agent Updated. Email notification not sent.');
                }
            } else {
                return array('type' => 'error', 'message' => 'Agent not found.');
            }
        } catch (Exception $e) {
            $message = 'Failed to update: ';
            return ['error' => $message . $e->getMessage()];
        }
    }

    public function editAgentLevelStatus($request, $agentUpdatesHelper)
    {
        try {
            $agentInfo = AgentInfo::where('agent_id', $request->agent_id)->first();
            if ($agentInfo) {
                AgentUser::where('agent_id', $request->agent_id)->update(['status' => 'A']);
                if(isset($request->agent_gid)){
                    foreach ($request->agent_gid as $gid) {
                        AgentGroup::where('agent_id', $request->agent_id)
                            ->where('gid', $gid)
                            ->update([
                                'ag_status' => 'A',
                                'lvl' => $request->premier_level
                            ]);
                    }
                }
                if($request->agent_status == 'P')
                {
                    AgentInfo::where('agent_id', $request->agent_id)
                        ->update([
                            'agent_status' => 'A',
                            'agent_level' => $request->premier_level,
                            'agent_level_2' => $request->ancillary_level,
                            'agent_activation_date' => Carbon::now()
                        ]);

                    if(DB::table('rep_referrals')->where('rep_id',$request->agent_id)->exists())
                    {
                        DB::table('rep_referrals')->where('rep_id',$request->agent_id)->update([
                            'is_approved' => 1,
                            'approved_date' => Carbon::now()
                        ]);
                    }
                } else {
                    AgentInfo::where('agent_id', $request->agent_id)
                    ->update([
                        'agent_status' => 'A',
                        'agent_level' => $request->premier_level,
                        'agent_level_2' => $request->ancillary_level,
                    ]);
                }

                //Log changes
                $updates = [
                    'agent_id' => $request->agent_id,
                    'elgb_act' => 'LNSCHNG',
                    'elgb_act_date' => Carbon::now()->timestamp,
                    'elgb_comment' => 'Agent status and level updated. ' .
                        'Updated in agent_info, agent_user and agent_groups table. ' .
                        'Old Values: Status-' . $agentInfo->agent_status .
                        ', Premier Level-' . $agentInfo->agent_level .
                        ', Ancillary Level-' . $agentInfo->agent_level_2,
                    'elgb_agent_id' => request()->header('id'),
                    'elgb_agent_name' => request()->header('name')
                ];
                $agentUpdatesHelper->logAgentUpdates($updates);
                $changeUrl = false;
                if ($agentInfo->agent_status != 'A' ||
                    $agentInfo->agent_level != $request->premier_level ||
                    $agentInfo->agent_level_2 != $request->ancillary_level) {
                    $agent_phone = $agentInfo->agent_phone2 ? $agentInfo->agent_phone2
                        : ($agentInfo->agent_phone1 ? $agentInfo->agent_phone1 : null);
                    $smsStatus = false;
                    if ($agent_phone) {
                        $smsText = "Download your Rep App to stay connected with Elevate Wellness Association:" . PHP_EOL . "Apple:" . self::REP_IOS_APP . PHP_EOL . "Android:" . self::REP_ANDRIOD_APP;
                        $smsStatus = SendSMSHelper::sendSms($agent_phone, $smsText);
                    }

                    if ($agentInfo->agent_level != $request->premier_level || $agentInfo->agent_level_2 != $request->ancillary_level) {
                        $emailMessageConfiguration = 'REPRESENTATIVE_LEVEL_CHANGED_NOTIFICATION';
                        $subject = 'Agent Level Changed';
                        if ($agentInfo->agent_level != $request->premier_level && $agentInfo->agent_level_2 != $request->ancillary_level)
                            $subject = 'Premier and Ancillary Level Changed';
                        elseif ($agentInfo->agent_level != $request->premier_level)
                            $subject = 'Premier Level Changed';
                        elseif ($agentInfo->agent_level_2 != $request->ancillary_level)
                            $subject = 'Ancillary Level Changed';
                        $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id, $emailMessageConfiguration, $subject);
                    }
                    if ($agentInfo->agent_status != 'A') {
                        if ($agentInfo->agent_status == 'D' || $agentInfo->agent_status == 'S') {
                            $emailMessageConfiguration = 'REPRESENTATIVE_REACTIVATION_NOTIFICATION';
                            $subject = 'Representative Re-activation';
                        } else {
                            $emailMessageConfiguration = 'REP_ACTIVATION_EMAIL';
                            $subject = 'ACTIVATION NOTICE: Congratulations, welcome Nu Era Benefits';
                            $changeUrl = true;
                        }
                        $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id, $emailMessageConfiguration, $subject,$changeUrl);
                        $upline = $agentInfo->agent_ga;
                        if ($upline == '101301' && $agentInfo->agent_status == 'P') {
                            $emailMessageConfiguration = 'REP_APPROVAL_NOTIFICATION_KRIVELOW_DOWNLINE';
                            $subject = 'Elevate Wellness Association (EWA)- Training Session Request';
                            $emailStatusKrivelowDownline = $this->sendAgentStatusActivateEmail($request->agent_id, $emailMessageConfiguration, $subject);
                        }
                    }
                }

                    if($this->isBex($request->agent_id)){
                        $contract = 'BEX PREMIER ' . $request->premier_level. '-BEX ANCILLARY ' . $request->ancillary_level;
                    }
                    else{
                        $contract = 'ALC '.$request->ancillary_level . '-PEC ' . $request->premier_level;
                    }

                // if ($existing_levels['Pbex'] != 0)
                //     $contractPreBex = 'BEX PREMIER ' . $existing_levels['Pbex'];

                // if ($existing_levels['Abex'] != 0)
                //     $contractBex = 'BEX ANCILLARY ' . $existing_levels['Abex'];

                // if ($existing_levels['fegliAncillary'] != 0)
                //     $contractFegliAncillary = 'FEGLI ANCILLARY ' . $existing_levels['fegliAncillary'];

                // if ($existing_levels['fegliPremier'] != 0)
                //     $contractFegliPremier = 'FEGLI PREMIER ' . $existing_levels['fegliPremier'];

                // if ($existing_levels['patriotAncillary'] != 0)
                //     $contractPatriotAncillary = 'PATRIOT ANCILLARY ' . $existing_levels['patriotAncillary'];

                // if ($existing_levels['patriotPremier'] != 0)
                //     $contractPatriotPremier = 'PATRIOT PREMIER ' . $existing_levels['patriotPremier'];

                // $contract = '';
                // $contracts = ['Ac', 'Pre', 'PreBex', 'Bex', 'FegliPremier', 'FegliAncillary', 'PatriotPremier', 'PatriotAncillary'];
                // foreach ($contracts as $eachContractName)
                // {
                //     $contractVar = 'contract' . $eachContractName;
                //     if (isset($$contractVar))
                //         $contract = strlen($contract) != 0
                //             ? $contract . '-' . $$contractVar
                //             : $$contractVar;
                // }

                $this->manageAgentsModel->createRepContractAct($request, $contract);

                if (isset($emailStatus['type']) && $emailStatus['type'] == 'success' && $smsStatus) {
                    return array('type' => 'success', 'message' => 'Premier and Ancillary Level of Agent Updated. Email notification and SMS sent.');
                } elseif (isset($emailStatus['type']) && $emailStatus['type'] == 'success') {
                    return array('type' => 'success', 'message' => 'Premier and Ancillary Level of Agent Updated. Email notification sent.');
                } else {
                    return array('type' => 'success', 'message' => 'Premier and Ancillary Level of Agent Updated. Email notification not sent.');
                }
            } else {
                return array('type' => 'error', 'message' => 'Agent not found.');
            }
        } catch (Exception $e) {
            $message = 'Failed to update: ';
            return ['error' => $message . $e->getMessage()];
        }
    }
    public function createRepContractAct($request, $contract, $categoryContract = null, $carrierContract = null, $action = 'contract sent')
    {
        try {
            $data = [
                'action' => $action,
                'aid' => $request->agent_id,
                'userid' => request()->header('id'),
                'contracts' => $contract,
                'contract_type' => $request->contract_type,
                'ts' => time(),
                'ipaddress' => request()->ip()
            ];
            return  RepContractAct::query()
                ->create($data);
            
        } catch (\Throwable $th) {
            return $this->failedResponse('Something Went Wrong');
        }
    }

    public function editAgentGroup($request)
    {
        if( !AgentInfo::where('agent_id',$request->agent_id)->first())
        {
            return array('type' => 'error', 'message' => 'Agent Not found.');
        }
        AgentInfo::where('agent_id',$request->agent_id)->update([
            'admin_only'=>$request->admin_only,
            'idilusplan'=>$request->idilusplan,
            'enaplan'=>$request->enaplan,
            'amgplan'=>$request->amgplan,
            'eliteblue'=>$request->eliteblue
        ]);
        AgentGroup::where('agent_id',$request->agent_id)->delete();
        foreach($request->agency_id as $gid)
        {
            AgentGroup::insert([
                'agent_id' => $request->agent_id,
                'gid' => $gid,
                'ts' => time()
            ]);
        }
        return array('type'=>'success','message'=>'Agent Group Information Updated');
    }
    public function editAgentSession($request,$agentUpdatesHelper)
    {
        try{
            $agentInfo = AgentInfo::where('agent_id',$request->agent_id)->first();
            if ($agentInfo) {
                AgentInfo::where('agent_id',$request->agent_id)->update(['tranning_flag'=>$request->status]);
            } else {
                return array('type'=>'error','message'=>'Agent not found.');
            }

            //Log changes
            $updates=[
                'agent_id'=>$request->agent_id,
                'elgb_act'=>'SESCHNG',
                'elgb_act_date'=> Carbon::now()->timestamp,
                'elgb_comment'=> 'Agent session. Old Values: Session'.$agentInfo->tranning_flag,
                'elgb_agent_id'=> $request->elgb_agent_id,
                'elgb_agent_name'=> $request->elgb_agent_name
            ];
            $agentUpdatesHelper->logAgentUpdates($updates);

            $emailStatus='';
            if ($request->status == '1') {
                $emailMessageConfiguration = 'REGISTRATION_ACTIVATION_COMPLETED_NOTIFICATION';
                $subject = 'Registration And Activation Complete';
                $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id,$emailMessageConfiguration,$subject);
            } else {
                $emailMessageConfiguration = 'REGISTRATION_ACTIVATION_INCOMPLETE_REMINDER';
                $subject = 'Activation Incomplete';
                $emailStatus = $this->sendAgentStatusActivateEmail($request->agent_id,$emailMessageConfiguration,$subject);
            }
            if(isset($emailStatus['type']) && $emailStatus['type'] == 'success') {
                return array('type'=>'success','message'=>'Updated agent training status. Email notification sent.');
            } else {
                return array('type'=>'error','message'=>'Updated agent training status. Unable to send email, agent information not found.');
            }
        }
        catch(Exception $e){
            $message = 'Failed to update: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function changeAgentUpline($request,$agentUpdatesHelper)
    {
        try{
            $agentInfo = AgentInfo::where('agent_id',$request->agent_id)->first();
            $uplineAgent = AgentInfo::where('agent_code',$request->upline_code)->first();
            $oldUpline = $agentInfo->agent_ga;
            if($agentInfo && $uplineAgent)
            {
                $agentInfo->agent_ga = $uplineAgent->agent_id;
                $agentInfo->save();

                //Log changes in agent_updates table
                $updates=[
                    'agent_id'=>$request->agent_id,
                    'elgb_act'=>'UPLCHNG',
                    'elgb_act_date'=> Carbon::now()->timestamp,
                    'elgb_comment'=> 'Upline changed. Old Value: '.$oldUpline,
                    'elgb_agent_id'=> $request->elgb_agent_id,
                    'elgb_agent_name'=> $request->elgb_agent_name
                ];
                $agentUpdatesHelper->logAgentUpdates($updates);

                if( $uplineAgent->agent_id != $oldUpline ){
                    $emailMessageConfiguration = 'REPRESENTATIVE_UPLINE_CHANGE_NOTIFICATION';
                    $subject = 'Upline Representative Changed';
                    $response = $this->sendAgentStatusActivateEmail($request->agent_id,$emailMessageConfiguration,$subject);
                }

                if(isset($response['type']) && $response['type'] == 'success') {
                    return array('type' => 'success', 'message' => 'Upline representative changed and Email notification send successfully.');
                }else{
                    return array('type' => 'success', 'message' => 'Upline representative changed successfully. Email not sent');
                }
            }
            else
            {
                return array('type'=>'error','message'=>'Agent not found.');
            }
        }catch(Exception $e){
            $message = 'Failed To send notification email: ';
            return ['error' => $message.$e->getMessage()];
        }
    }
    public function sendAgentStatusActivateEmail($agent_id,$emailMessageConfiguration,$subject,$changeUrl=false)
    {
        try{
            $attachedFiles=[];
            $contentData = $this->getEmailContent($agent_id,$changeUrl);

            $toAddress = config("testemail.TEST_EMAIL") ? [config("testemail.TEST_EMAIL")] : [$contentData['rep_email']];
            $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [$contentData['upline_email']];
            $bccAddress = config("testemail.TEST_EMAIL_BCC") ? [config("testemail.TEST_EMAIL_BCC")] : [];

            if($emailMessageConfiguration == 'REPRESENTATIVE_UPLINE_CHANGE_NOTIFICATION' || $emailMessageConfiguration=='REPRESENTATIVE_DEACTIVATION_NOTIFICATION' || $emailMessageConfiguration=='REPRESENTATIVE_LEVEL_CHANGED_NOTIFICATION' ){
                $ccAddress=[];
                $bccAddress=[];
            }

            if($emailMessageConfiguration=='REGISTRATION_ACTIVATION_COMPLETED_NOTIFICATION' || $emailMessageConfiguration=='REGISTRATION_ACTIVATION_INCOMPLETE_REMINDER'){
                $bccAddress=[];
            }

            if($emailMessageConfiguration=='REP_APPROVAL_NOTIFICATION_KRIVELOW_DOWNLINE'){
                $attachedFiles = ["ElevateKit-2021.pdf"];
            }

            $brentDownline = CommonHelper::getAgentDownLine(605);
            if($emailMessageConfiguration == 'REP_ACTIVATION_EMAIL' && in_array($agent_id, $brentDownline)){
                $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [$contentData['upline_email'],'<EMAIL>'];
            }

            $body = [
                'email_message_configuration_name'=> $emailMessageConfiguration,
                'toAddress'=> $toAddress,
                'ccAddress'=> $ccAddress,
                'bccAddress'=> $bccAddress,
                'subject'=> $subject,
                'attachedFiles'=> $attachedFiles,
                'generalTemplateData'=> [],
                'contentData'=> $contentData
            ];

            $apiUrl = config('app.messagecenter.key').'api/v1/send-email-with-content-data';
            $responseJson = GuzzleHelper::postApi($apiUrl,[],$body);
            $response = json_decode($responseJson,true);

            if( isset($response['status_code']) && $response['status_code']=='200' && $response['status']=='success'){
                return array('type' => 'success', 'message' => 'Email Notification sent successfully.');
            }else{
                return array('type' => 'error', 'message' => 'Unable to send email.');
            }
        }
        catch(Exception $e)
        {
            $message = 'Failed To send notification email: ';
            return ['error' => $e->getMessage()];
        }
    }
    private function getEmailContent($agent_id,$changeUrl=false)
    {
        $agentInfo = AgentInfo::where('agent_id',$agent_id)->first();
        if(!$agentInfo)
        {
            return array('type'=>'error','message'=>'Agent Information not found.');
        }
        $agentInfoGa = AgentInfo::where('agent_id',$agentInfo->agent_ga)->first();
        $agentInfoBex = AgentGroup::whereIn('gid',array('1434','1399','2599'))->where('agent_id',$agent_id)->count();
        $agentInfo123 = AgentGroup::where('gid','1881')->where('agent_id',$agent_id)->count();
        $agentInfoElite = AgentGroup::where('gid','1073')->where('agent_id',$agent_id)->count();
        $agentInfoPremier = AgentGroup::where('gid','1926')->where('agent_id',$agent_id)->count();
        $agentFirstName = $agentInfo->agent_fname;
        $agentLastName = $agentInfo->agent_lname;
        $agentState = $agentInfo->agent_state;
        $agentCode = $agentInfo->agent_code;
        if(isset($agentInfo->agentCustomeHomepage)) {
            $a1 = $agentInfo->agentCustomeHomepage->phone;
            $agentEmail = $agentInfo->agentCustomeHomepage->email;
        } else {
            $a1 = $agentInfo->agent_phone1;
            $agentEmail = $agentInfo->agent_email;
        }
        $agentPhone = NULL;
        if( strlen($a1) == 10){
            $agentPhone = "($a1[0]$a1[1]$a1[2]) $a1[3]$a1[4]$a1[5]-$a1[6]$a1[7]$a1[8]$a1[9]";
        }
        $b1 = is_null($agentInfoGa) ? null : $agentInfoGa->agent_phone1;
        $agentPhoneGa = NULL;
        if(strlen($b1) == 10){
            $agentPhoneGa = "($b1[0]$b1[1]$b1[2]) $b1[3]$b1[4]$b1[5]-$b1[6]$b1[7]$b1[8]$b1[9]";
        }
        $agentEmailGa = isset($agentInfoGa, $agentInfoGa->agent_email) ? $agentInfoGa->agent_email : null;
        $encryptedPassword = $agentInfo->agent_temp_password;
        $agentPwd = DecryptEcryptHelper::decryptInfo($encryptedPassword);
        $agentFirstNameGa = optional($agentInfoGa)->agent_fname;
        $agentLastNameGa = optional($agentInfoGa)->agent_lname;

        $displayInfo=CustomeHomepage::where('agent_id',$agentInfoGa->agent_id)->first();
        $uplineDisplayName=$agentInfoGa->agent_web_name ;

        $url = '';
        if($changeUrl){
            if ($agentInfo123 >= 1 && $agentInfo->agent_web_dental != '') {
                $url .= 'https://goenroll123.com/'.$agentInfo->agent_web_dental;
            } elseif ($agentInfoBex >= 1 && $agentInfo->agent_web_dental != '') {
                $url .= 'https://brokerexchanges.com/'.$agentInfo->agent_web_dental;
            } elseif ($agentInfoPremier >=1 && $agentInfo->agent_web_dental !=''){
                $url .= 'https://premierenroll.com/'.$agentInfo->agent_web_dental;
            } elseif ($agentInfoElite >=1 && $agentInfo->agent_web_dental !=''){
                $url .= 'https://eliteenroll.com/'.$agentInfo->agent_web_dental;
            }
        }else{
            if ($agentInfo123 >= 1 && $agentInfo->agent_web_dental != '' && $agentInfo->agent_ga !='10055') {
                $url .= 'https://goenroll123.com/'.$agentInfo->agent_web_dental. '</br>';
            } elseif ($agentInfoBex >= 1 && $agentInfo->agent_web_dental != '') {
                $url .= 'https://brokerexchanges.com/'.$agentInfo->agent_web_dental. '</br>';
            } elseif ($agentInfoPremier >=1 && $agentInfo->agent_web_dental !='' && $agentInfo->agent_ga !='10055'){
                $url .= 'https://premierenroll.com/'.$agentInfo->agent_web_dental. '</br>';
            } elseif ($agentInfoElite >=1 && $agentInfo->agent_web_dental !='' && $agentInfo->agent_ga !='10055'){
                $url .= 'https://eliteenroll.com/'.$agentInfo->agent_web_dental. '</br>';
            }
        }

        $agentInGroup = AgentInGroup::where('agent_id',$agent_id)->get();
        $i=count($agentInGroup)-1;
        $agentInGroupFinal = '';
        foreach($agentInGroup as $aa)
        {
            $groupInfo = GroupInfo::where('gid',$aa->gid)->first();
            $agentInGroupFinal.=$groupInfo->gname;
            if($i>0)
            {
                $agentInGroupFinal.=",";
            }
        }

        $companyInformation = CompanyInformation::where('company_id','1')->first();
        $c1 = $companyInformation->support_800;
        $supportPhone = NULL;
        if(strlen($c1) == 10){
            $supportPhone = "$c1[0]$c1[1]$c1[2]-$c1[3]$c1[4]$c1[5]-$c1[6]$c1[7]$c1[8]$c1[9]";
        }

        $contentData = [
            'rep_first_name'=> $agentFirstName,
            'rep_last_name'=> $agentLastName,
            'rep_code'=> $agentCode,
            'rep_state'=> $agentState,
            'rep_phone'=> $agentPhone,
            'rep_url'=> $url,
            'upline_first_name'=>$uplineDisplayName ? $uplineDisplayName: $agentFirstNameGa,
            'upline_last_name'=> $uplineDisplayName ? '' :$agentLastNameGa,
            'upline_email'=>$displayInfo ? $displayInfo->email: $agentEmailGa,
            'upline_phone'=>$displayInfo ? CommonHelper::format_phone($displayInfo->phone): $agentPhoneGa,
            'rep_email'=> $agentEmail,
            'rep_temp_password'=> $agentPwd,
            'support_phone'=>$supportPhone,
            'level' => $agentInfo->agent_level,
            'level_2' => $agentInfo->agent_level_2,
            'date' => date('m/d/Y'),
            'access_code' => strtolower($agentCode)
        ];
        
        return $contentData;
    }
}
