<?php

namespace App\Repositories\Agents;

use App\AgentInfo;
use App\Traits\GetUserStat;
use App\Traits\ResponseMessage;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use phpseclib\System\SSH\Agent;

class AgentDashboard extends model
{
    use ResponseMessage, GetUserStat;

    public function totalAnalytics($fdate = null, $tdate = null)
    {
        $totalAnalytics = AgentInfo::selectRaw("count(agent_id) as total_agent")
            ->selectRaw("SUM(CASE WHEN agent_status = 'A' THEN 1 ELSE 0 END) AS total_active_agent")
            ->selectRaw("SUM(CASE WHEN agent_status = 'D' THEN 1 ELSE 0 END) AS total_disabled_agent")
            ->selectRaw("SUM(CASE WHEN agent_status = 'S' THEN 1 ELSE 0 END) AS total_suspended_agent")
            ->selectRaw("SUM(CASE WHEN agent_status = 'P' THEN 1 ELSE 0 END) AS total_pending_agent");

        if (!is_null($fdate) && !is_null($tdate)) {
            $totalAnalytics = $totalAnalytics->whereRaw("FROM_UNIXTIME(agent_signup_date) >= '$fdate' AND FROM_UNIXTIME(agent_signup_date) < DATE_ADD('$tdate', INTERVAL 1 day)");
        }
        $totalAnalytics = $totalAnalytics->get();

        $data = ['totalAnalytics' => $totalAnalytics[0]];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalYearProgress($fyear, $tyear)
    {
        $res = ['status' => 'error', 'message' => 'format error'];
        if ($fyear < $tyear) {
            $x = $tyear - $fyear;
            $totalyears = [];
            if ($x > 9) {
                return $res;
            }
            for ($i = 0; $i <= $x; $i++) {
                $fyear = strval($fyear);
                $yearlyAgentData = AgentInfo::whereRaw("FROM_UNIXTIME(agent_signup_date) LIKE '$fyear-%'")
                    ->selectRaw('COUNT(agent_id) as total_count')
                    ->selectRaw("SUM(CASE WHEN agent_status = 'A' THEN 1 ELSE 0 END) as active_count")
                    ->selectRaw("SUM(CASE WHEN agent_status = 'D' THEN 1 ELSE 0 END) as disabled_count")
                    ->selectRaw("SUM(CASE WHEN agent_status = 'S' THEN 1 ELSE 0 END) as suspended_count")
                    ->selectRaw("SUM(CASE WHEN agent_status = 'P' THEN 1 ELSE 0 END) AS pending_count")
                    ->get();
                //For ACTIVE member policies
                $totalActiveAgent[$i] = $yearlyAgentData[0]->active_count;
                //For DISABLED member policies
                $totalDisabledAgent[$i] = $yearlyAgentData[0]->disabled_count;
                //For Suspended member policies
                $totalSuspendedAgent[$i] = $yearlyAgentData[0]->suspended_count;
                //For Pending Counts
                $totalPendingAgent[$i] = $yearlyAgentData[0]->pending_count;

                //For Total Counts
                $totalCounts[$i] = $yearlyAgentData[0]->total_count;
                array_push($totalyears, $fyear);
                $fyear++;
            }
        } else {
            return $res;
        }
        $activeAgents = [
            'name' => 'Active',
            'data' => $totalActiveAgent
        ];
        $disabledAgents = [
            'name' => 'Disabled',
            'data' => $totalDisabledAgent
        ];
        $suspendedAgents = [
            'name' => 'Suspended',
            'data' => $totalSuspendedAgent
        ];
        $pendingAgents = [
            'name' => 'Pending',
            'data' => $totalPendingAgent
        ];

        $totalCounts = [
            'name' => 'Total Counts',
            'data' => $totalCounts
        ];
        $yearProgress = [
            $totalCounts,
            $activeAgents,
            $disabledAgents,
            $suspendedAgents,
            $pendingAgents
        ];
        $data = [
            'totalYears' => $totalyears,
            'totalProgressOfYear' => $yearProgress
        ];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalMonthsProgress($fdate, $tdate)
    {
        $res = ['status' => 'error', 'message' => 'format error'];
        $fyear = substr($fdate, 0, 4);
        $tyear = substr($tdate, 0, 4);
        $from_month = substr($fdate, 5, 2);
        $to_month = substr($tdate, 5, 2);
        $m = 0;
        $totalMonthProgess = [];
        $from_month > $to_month? $diff = $to_month + 12 - $from_month : $diff = $to_month - $from_month;
        if ($from_month > $to_month) {
            if ($diff >= 6 || ($tyear - $fyear > 1)) {
                return $res;
            }
            if ($fyear == $tyear) {
                return $res;
            }
            for ($i = 0; $i <= $diff; $i++) {
                $monthlyData = $this->getMonthlyData($fyear, $from_month);
                $totalMonthProgess[$i] = $monthlyData[0];
                if ($from_month == 12) {
                    $fyear++;
                    $fyear = strval($fyear);
                    $from_month = 1;
                } else {
                    $from_month++;
                }
                $from_month = strval($from_month);
                strlen($from_month) < 2 ? $from_month = "0".$from_month : $from_month;
            }
        } else {
            if (($diff >= 6) || ($fyear != $tyear)) {
                return $res;
            }
            for($i = 0; $i <= $diff; $i++) {
                $monthlyData = $this->getMonthlyData($fyear, $from_month);
                $totalMonthProgess[$i] = $monthlyData[0];
                $from_month++;
                $from_month = strval($from_month);
                strlen($from_month) < 2 ? $from_month = "0".$from_month : $from_month;
            }
        }
        $data = $totalMonthProgess;
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function totalWeekProgress()
    {
        $weeklyData= AgentInfo::whereRaw("YEARWEEK(from_unixtime(agent_signup_date,'%Y-%m-%d')) = YEARWEEK(now())")
        ->selectRaw("DAYNAME(from_unixtime(agent_signup_date)) as day,COUNT(agent_id) as total_count")
        ->selectRaw("SUM(CASE WHEN agent_status = 'A' THEN 1 ELSE 0 END) as active_count")
        ->selectRaw("SUM(CASE WHEN agent_status = 'D' THEN 1 ELSE 0 END) as disabled_count")
        ->selectRaw("SUM(CASE WHEN agent_status = 'S' THEN 1 ELSE 0 END) as suspended_count")
        ->selectRaw("SUM(CASE WHEN agent_status = 'P' THEN 1 ELSE 0 END) AS pending_count")
        ->groupBy('day')
        ->get();

        for($i=0;$i<count($weeklyData);$i++){

            $days[$i] = $weeklyData[$i]->day;
            $dates[$i] = $weeklyData[$i]->date;
            $totalCounts[$i] =$weeklyData[$i]->total_count;
            $activeCounts[$i] =$weeklyData[$i]->active_count;
            $disabledCounts[$i] =$weeklyData[$i]->disabled_count;
            $suspendedCounts[$i] =$weeklyData[$i]->suspended_count;
            $pendingCounts[$i] =$weeklyData[$i]->pending_count;
        }

        $activeAgents = [];
        $disabledAgents = [];
        $suspendedAgents = [];
        $pendingAgents = [];
        $totalAgents = [];

        // Make data Zero if there are no records
        $daynames = ['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];
        $index = 0;

        foreach($daynames as $day)
        {
            if(isset($days) AND in_array($day,$days))
            {
                array_push($activeAgents,(int)$activeCounts[$index]);
                array_push($disabledAgents,(int)$disabledCounts[$index]);
                array_push($suspendedAgents,(int)$suspendedCounts[$index]);
                array_push($pendingAgents,(int)$pendingCounts[$index]);
                array_push($totalAgents,(int)$totalCounts[$index]);

                $index++;
            }
            else{
                array_push($activeAgents,0);
                array_push($disabledAgents,0);
                array_push($suspendedAgents,0);
                array_push($pendingAgents,0);
                array_push($totalAgents,0);
            }
        }
        $days = [
            'name' => 'Days',
            'data' => $daynames
        ];
        $totalAgents =[
            'name' => 'Total Counts',
            'data' => $totalAgents
        ];
        $activeAgents =[
            'name' => 'Active',
            'data' => $activeAgents
        ];
        $disabledAgents =[
            'name' => 'Disabled',
            'data' => $disabledAgents
        ];
        $suspendedAgents =[
            'name' => 'Suspended',
            'data' => $suspendedAgents
        ];
        $pendingAgents =[
            'name' => 'Pending',
            'data' => $pendingAgents
        ];
        $data = [$days,$totalAgents,$activeAgents,$disabledAgents,$suspendedAgents,$pendingAgents];
        $response = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];

        return $response;
    }

    public function agentGroupByOtherFactor()
    {
        $filters['agent_status'] = 'A';
        //total active agents
        $totalActiveAgentQuery = AgentInfo::query();
        $totalActiveAgent = $this->filterContent($totalActiveAgentQuery, $filters)->get()->count('agent_id');

        //By state
        $byStateQuery = AgentInfo::query();
        $byStateQuery = $byStateQuery->where('agent_state', '!=', null)
            ->where('agent_state', '!=', '')
            ->selectRaw('agent_state, COUNT(agent_id) AS count')
            ->groupBy('agent_state')
            ->havingRaw('COUNT(agent_id) >= 1')
            ->orderBy('count', 'DESC');
        $byState = $this->filterContent($byStateQuery, $filters)->get();

        //By App
       /* $byAppQuery = AgentInfo::query();
        $byAppQuery = $byAppQuery->join('user_activity_details as u', 'u.user_id', 'agent_info.agent_id')
            ->selectRaw("SUM(CASE WHEN device = 'iOS' THEN 1 ELSE 0 END) AS ios_count")
            ->selectRaw("SUM(CASE WHEN device = 'android' THEN 1 ELSE 0 END) AS android_count")
            ->where([
                ['u.user_type', 'broker'],
                ['u.action', 'login']
            ]);
        $byApp = $this->filterContent($byAppQuery, $filters)->get();
       */
       $userDetails = $this->userTotalLogin()['data']['all_time_login'];
        $byApp['ios_count'] = $userDetails['ios']['agent'];
        $byApp['android_count'] = $userDetails['android']['agent'];

        //By Dashboard
        $ByDashboardAccessQuery = AgentInfo::query();
        $ByDashboardAccessQuery = $ByDashboardAccessQuery->join('user_activity_details as u', 'agent_info.agent_id', 'u.user_id')
            ->where([
                ['u.web', 1],
                ['u.user_type', 'broker'],
                ['u.action', 'login']
            ]);
        $ByDashboardAccess = $this->filterContent($ByDashboardAccessQuery, $filters)->selectRaw('u.web as access, count(distinct u.user_id) AS count')->get();
        $totalNonAccess = $totalActiveAgent - $ByDashboardAccess[0]->count;
        $dashboardNonAccess = [
            'access' => "0",
            'count' => strval($totalNonAccess)
        ];
        $dashboardAndCount = [
            $ByDashboardAccess[0],
            $dashboardNonAccess
        ];
        $byDashboard = [
            'dashboardAndCount' => $dashboardAndCount
        ];

        //by contract
        $byContractSignedQuery = AgentInfo::query();
        $byContractSignedQuery = $byContractSignedQuery->where('contract_status', 1)
            ->selectRaw('contract_status as signed, COUNT(agent_id) AS count');
        $byContractSigned = $this->filterContent($byContractSignedQuery, $filters)->get();
        $byContractNotSigned = $totalActiveAgent - $byContractSigned[0]->count;
        $contractNotSigned = [
            'signed' => 0,
            'count' => strval($byContractNotSigned)
        ];
        $byContract = [
            $byContractSigned[0],
            $contractNotSigned
        ];

        //by age
        $today = date('Y-m-d');
        $eighteenYears = 18;
        $twentyFourYears = 24;
        $twentyFiveYears = 25;
        $twentyNineYears = 29;
        $thirtyYears = 30;
        $thirtyFourYears = 34;
        $thirtyFiveYears = 35;
        $fiftyYears = 50;

        $byAgeQuery = AgentInfo::selectRaw("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) >= 18 AND TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) <= 24 THEN 1 ELSE 0 END) as eighteenToTwentyfour")
            ->selectRaw("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) >= 25 AND TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) <= 29 THEN 1 ELSE 0 END) as twentyfiveToTwentynine")
            ->selectRaw("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) >= 30 AND TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) <= 34 THEN 1 ELSE 0 END) as thirtyToThirtyfour")
            ->selectRaw("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) >= 35 AND TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) <= 50 THEN 1 ELSE 0 END) as thirtyfiveToFifty")
            ->selectRaw("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) > 50 THEN 1 ELSE 0 END) as fiftyPlus");
        $findByAge = $this->filterContent($byAgeQuery, $filters)->get();
        $date = [
            'eighteenYears' => $eighteenYears,
            'twentyFourYears' => $twentyFourYears,
            'twentyFiveYears' => $twentyFiveYears,
            'twentyNineYears' => $twentyNineYears,
            'thirtyYears' => $thirtyYears,
            'thirtyFourYears' => $thirtyFourYears,
            'thirtyFiveYears' => $thirtyFiveYears,
            'fiftyYears' => $fiftyYears,
            'fiftyPlusYears' => $fiftyYears,
        ];
        $byAge = [
            'filterDate' => $date,
            'ageCount' => $findByAge[0]
        ];

        //by website
        $byWebsiteQuery = AgentInfo::query();
        $byWebsiteQuery = $byWebsiteQuery->where('weburl', '!=', null)
            ->groupBy('weburl')
            ->havingRaw('count(agent_id) >= 1')
            ->selectRaw('weburl, count(agent_id) as count')
            ->orderBy('count', 'desc');
        $byWebsite = $this->filterContent($byWebsiteQuery, $filters)->get();

        //by member
        $byMemberInfo = AgentInfo::join('agent_totals as at', 'agent_info.agent_id', 'at.agent_id')
            ->selectRaw('agent_info.agent_id, SUM(totals) as sum_totals')
            ->where('agent_info.agent_status', 'A')
            ->groupBy('agent_info.agent_id');

        $byMember = Db::table(DB::raw("({$byMemberInfo->toSql()}) as m"))
            ->mergeBindings($byMemberInfo->getQuery())
            ->selectRaw("SUM(CASE WHEN m.sum_totals >= 1 AND m.sum_totals <= 10 THEN 1 ELSE 0 END) AS oneToTen_members")
            ->selectRaw("SUM(CASE WHEN m.sum_totals >= 11 AND m.sum_totals <= 30 THEN 1 ELSE 0 END) AS elevenToThirty_members")
            ->selectRaw("SUM(CASE WHEN m.sum_totals >= 31 AND m.sum_totals <= 50 THEN 1 ELSE 0 END) AS thirtyoneToFifty_members")
            ->selectRaw("SUM(CASE WHEN m.sum_totals >50 THEN 1 ELSE 0 END) AS fiftyPlus_members")
            ->get();
        $zero_members = $totalActiveAgent - $byMember[0]->oneToTen_members - $byMember[0]->elevenToThirty_members - $byMember[0]->thirtyoneToFifty_members - $byMember[0]->fiftyPlus_members;
        $byMember[0]->zero_members = $zero_members;

        //by level
        $byLevelQuery = AgentInfo::query();
        $byLevel = $byLevelQuery->selectRaw("SUM(CASE WHEN agent_level = 1 AND agent_status = 'A' THEN 1 ELSE 0 END) AS active_one,
            SUM(CASE WHEN agent_level = 1 AND (agent_status = 'D' OR agent_status = 'S') THEN 1 ELSE 0 END) AS inactive_one,
            SUM(CASE WHEN agent_level = 2 AND agent_status = 'A' THEN 1 ELSE 0 END) AS active_two,
            SUM(CASE WHEN agent_level = 2 AND (agent_status = 'D' OR agent_status = 'S') THEN 1 ELSE 0 END) AS inactive_two,
            SUM(CASE WHEN agent_level = 3 AND agent_status = 'A' THEN 1 ELSE 0 END) AS active_three,
            SUM(CASE WHEN agent_level = 3 AND (agent_status = 'D' OR agent_status = 'S') THEN 1 ELSE 0 END) AS inactive_three,
            SUM(CASE WHEN agent_level = 4 AND agent_status = 'A' THEN 1 ELSE 0 END) AS active_four,
            SUM(CASE WHEN agent_level = 4 AND (agent_status = 'D' OR agent_status = 'S') THEN 1 ELSE 0 END) AS inactive_four,
            SUM(CASE WHEN agent_level = 5 AND agent_status = 'A' THEN 1 ELSE 0 END) AS active_five,
            SUM(CASE WHEN agent_level = 5 AND (agent_status = 'D' OR agent_status = 'S') THEN 1 ELSE 0 END) AS inactive_five,
            SUM(CASE WHEN agent_level = 6 AND agent_status = 'A' THEN 1 ELSE 0 END) AS active_six,
            SUM(CASE WHEN agent_level = 6 AND (agent_status = 'D' OR agent_status = 'S') THEN 1 ELSE 0 END) AS inactive_six,
            SUM(CASE WHEN agent_level = 7 AND agent_status = 'A' THEN 1 ELSE 0 END) AS active_seven,
            SUM(CASE WHEN agent_level = 7 AND (agent_status = 'D' OR agent_status = 'S') THEN 1 ELSE 0 END) AS inactive_seven")
            ->get();

        //by products
        $byProductQuery = AgentInfo::query();
        $byProductQuery = $byProductQuery->join('plan_overview as p', 'agent_info.agent_id', 'p.p_agent_num')
            ->selectRaw("COUNT(DISTINCT CASE WHEN pl_type = 'vision' THEN p.p_agent_num END) AS vision,
                COUNT(DISTINCT CASE WHEN pl_type IN ('MM') THEN p.p_agent_num END) AS medical,
                COUNT(DISTINCT CASE WHEN pl_type IN ('RX') THEN p.p_agent_num END) AS rx,
                COUNT(DISTINCT CASE WHEN pl_type IN ('HOSPITAL') THEN p.p_agent_num END) AS hospital,
                COUNT(DISTINCT CASE WHEN pl_type IN ('LM') THEN p.p_agent_num END) AS limitedMed,
                COUNT(DISTINCT CASE WHEN pl_type IN ('DENT', 'THEFT-DENT') THEN p.p_agent_num END) AS dental,
                COUNT(DISTINCT CASE WHEN pl_type IN ('ADI','CIADD','AME','LTD','GAP','AD','ASHIP', 'STDCANCER') THEN p.p_agent_num END) AS supplemental,
                COUNT(DISTINCT CASE WHEN pl_type IN ('DC','DISCOUNT','TELEMEDICINE','THEFT') THEN p.p_agent_num END) AS lifestyle,
                COUNT(DISTINCT CASE WHEN pl_type IN ('LIFE','TL') THEN p.p_agent_num END) AS term_life,
                COUNT(DISTINCT CASE WHEN pl_type IN ('ACCIDENT') THEN p.p_agent_num END) AS accident,
                COUNT(DISTINCT CASE WHEN pl_type IN ('CRITICAL', 'CI') THEN p.p_agent_num END) AS critical,
                COUNT(DISTINCT CASE WHEN pl_type IN ('DI') THEN p.p_agent_num END) AS di")
                ->where('p.pstatus', 1);
        $byProduct = $this->filterContent($byProductQuery, $filters)->get();

        //by downline
        $byDownlineQuery = AgentInfo::query();
        $byDownline = $byDownlineQuery->join('agent_info as ai', 'agent_info.agent_id', 'ai.agent_ga')
            ->selectRaw('agent_info.agent_id, agent_info.agent_code, agent_info.agent_fname, agent_info.agent_lname, count(*) as count')
            ->where([
                ['ai.agent_ga', '!=', null],
                ['ai.agent_status', '=', 'A']
            ])
            ->groupBy('ai.agent_ga')
            ->havingRaw('count(ai.agent_id) >= 1')
            ->orderBy('count', 'DESC')
            ->get();

        //by group
        $oneToTen_groups = $this->manageByGroup(1, 10);
        $elevenToTwenty_groups = $this->manageByGroup(11, 20);
        $twentyoneToThirty_groups = $this->manageByGroup(21, 30);
        $thirtyPlus_groups = $this->manageByGroup(30, null);
        $zero_group = $totalActiveAgent - $oneToTen_groups - $elevenToTwenty_groups - $twentyoneToThirty_groups - $thirtyPlus_groups;
        $byGroup = [
            'zero_group' => $zero_group,
            'oneToTen_groups' => $oneToTen_groups,
            'elevenToTwenty_groups' => $elevenToTwenty_groups,
            'twentyoneToThirty_groups' => $twentyoneToThirty_groups,
            'thirtyPlus_groups' => $thirtyPlus_groups
        ];


        $data = [
            'totalActiveAgent' => $totalActiveAgent,
            'groupingByState' => $byState,
            'groupingByApp' => $byApp,
            'groupingByDashboard' => $byDashboard,
            'groupingByContract' => $byContract,
            'groupingByAge' => $byAge,
            'groupingByWebsite' => $byWebsite,
            'groupingByMember' => $byMember[0],
            'groupingByLevel' => $byLevel[0],
            'groupingByProduct' => $byProduct[0],
            'groupingByDownline' => $byDownline,
            'groupingByGroup' => $byGroup
        ];
        $res = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $data];
        return $res;
    }

    public function getMonthlyData($fyear, $from_month)
    {
        $monthlyData = AgentInfo::whereRaw("FROM_UNIXTIME(agent_signup_date) LIKE '$fyear-$from_month%'")
            ->selectRaw('COUNT(agent_id) as total_count')
            ->selectRaw("SUM(CASE WHEN agent_status = 'A' THEN 1 ELSE 0 END) AS active_count")
            ->selectRaw("SUM(CASE WHEN agent_status = 'D' THEN 1 ELSE 0 END) AS disabled_count")
            ->selectRaw("SUM(CASE WHEN agent_status = 'S' THEN 1 ELSE 0 END) AS suspended_count")
            ->selectRaw("SUM(CASE WHEN agent_status = 'P' THEN 1 ELSE 0 END) AS pending_count")
            ->get();
        $monthlyData[0]->year = $fyear;
        $monthlyData[0]->month = $from_month;
        $ymdate = "$fyear-$from_month-01";
        $last_date = date("Y-m-t", strtotime($ymdate));
        $monthlyData[0]->last_date = $last_date;
        return $monthlyData;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['agent_status'])) {
            return $data->where('agent_status', 'A');
        }
    }

    protected function manageByGroup($from = null, $to = null)
    {
        if (isset($from) && isset($to)) {
            $agent_ids = AgentInfo::join('agents_ingroup as g', 'g.agent_id', 'agent_info.agent_id')
                ->havingRaw("count(*) >= $from")
                ->havingRaw("count(*) <= $to");
        } else if (isset($from)) {
            $agent_ids = AgentInfo::join('agents_ingroup as g', 'g.agent_id', 'agent_info.agent_id')
                ->havingRaw("count(*) > $from");
        } else if (isset($to)){
            $agent_ids = AgentInfo::join('agents_ingroup as g', 'g.agent_id', 'agent_info.agent_id')
                ->havingRaw("count(*) < $to");
        }
        $agent_ids = $agent_ids->groupBy('g.agent_id')
            ->select('g.agent_id')
            ->where('agent_info.agent_status', 'A');
        $count = DB::table(DB::raw("({$agent_ids->toSql()}) as a"))
            ->mergeBindings($agent_ids->getQuery())
            ->count('a.agent_id');
        return $count;
    }

    public function productsSection()
    {
        $plans = DB::table('plan_overview')
            ->selectRaw("COUNT(DISTINCT CASE WHEN rider = '0' AND pstatus = '1' THEN userid END) AS plan_med_active,
                COUNT(DISTINCT CASE WHEN rider = '1' AND pstatus = '1' THEN userid END) AS plan_rider_active,
                COUNT(DISTINCT CASE WHEN rider = '2' AND pstatus = '1' THEN userid END) AS plan_sa_active,

                COUNT(DISTINCT CASE WHEN rider = '0' AND pstatus = '3' THEN userid END) AS plan_med_wd,
                COUNT(DISTINCT CASE WHEN rider = '1' AND pstatus = '3' THEN userid END) AS plan_rider_wd,
                COUNT(DISTINCT CASE WHEN rider = '2' AND pstatus = '3' THEN userid END) AS plan_sa_wd,

                COUNT(DISTINCT CASE WHEN rider = '0' AND pstatus = '2' THEN userid END) AS plan_med_termed,
                COUNT(DISTINCT CASE WHEN rider = '1' AND pstatus = '2' THEN userid END) AS plan_rider_termed,
                COUNT(DISTINCT CASE WHEN rider = '2' AND pstatus = '2' THEN userid END) AS plan_sa_termed")
            ->get();

        $response = ['status' => 'success', 'message' => 'Data fetched successfully', 'data' => $plans];
        return $response;
    }

    public function topAgentsByCategory(){
        $medicalListing = DB::table('userinfo_policy_address')
        ->join('plan_overview', 'userinfo_policy_address.policy_id', '=', 'plan_overview.policy_id')
        ->join('agent_info', 'userinfo_policy_address.agent_id', '=', 'agent_info.agent_id')
        ->select('agent_info.agent_id', DB::raw("CONCAT(agent_info.agent_fname, ' ', COALESCE(agent_info.agent_mname, ''), ' ', COALESCE(agent_info.agent_lname, '')) AS agent_name"), DB::raw('COUNT(DISTINCT userinfo_policy_address.userid) AS user_count'))
        ->where('plan_overview.pl_type', 'MM')
        ->where('plan_overview.pstatus', 1)
        ->where('plan_overview.Approval', 1)
        ->groupBy('userinfo_policy_address.agent_id', 'agent_info.agent_id', 'agent_info.agent_fname', 'agent_info.agent_mname', 'agent_info.agent_lname')
        ->orderByDesc('user_count')
        ->limit(25)
        ->get();

        $ihaHelthListing = DB::table('userinfo_policy_address')
        ->join('plan_overview', 'userinfo_policy_address.policy_id', '=', 'plan_overview.policy_id')
        ->join('agent_info', 'userinfo_policy_address.agent_id', '=', 'agent_info.agent_id')
        ->select(
            'agent_info.agent_id',
            DB::raw("CONCAT(agent_info.agent_fname, ' ', COALESCE(agent_info.agent_mname, ''), ' ', COALESCE(agent_info.agent_lname, '')) AS agent_name"),
            DB::raw('COUNT(DISTINCT userinfo_policy_address.userid) AS user_count')
        )
        ->where('plan_overview.pstatus', 1)
        ->where('plan_overview.cid', 77)
        ->where('plan_overview.Approval', 1)
        ->groupBy('userinfo_policy_address.agent_id', 'agent_info.agent_id', 'agent_info.agent_fname', 'agent_info.agent_mname', 'agent_info.agent_lname')
        ->orderByDesc('user_count')
        ->limit(25)
        ->get();

        $lifelineMedicalListing = DB::table('userinfo_policy_address')
        ->join('plan_overview', 'userinfo_policy_address.policy_id', '=', 'plan_overview.policy_id')
        ->join('agent_info', 'userinfo_policy_address.agent_id', '=', 'agent_info.agent_id')
        ->select(
            'agent_info.agent_id',
            DB::raw("CONCAT(agent_info.agent_fname, ' ', COALESCE(agent_info.agent_mname, ''), ' ', COALESCE(agent_info.agent_lname, '')) AS agent_name"),
            DB::raw('COUNT(DISTINCT userinfo_policy_address.userid) AS user_count')
        )
        ->where('plan_overview.pstatus', 1)
        ->where('plan_overview.cid', 65)
        ->where('plan_overview.Approval', 1)
        ->groupBy('userinfo_policy_address.agent_id', 'agent_info.agent_id', 'agent_info.agent_fname', 'agent_info.agent_mname', 'agent_info.agent_lname')
        ->orderByDesc('user_count')
        ->limit(25)
        ->get();

        $ridersListing = DB::table('userinfo_policy_address')
        ->join('plan_overview', 'userinfo_policy_address.policy_id', '=', 'plan_overview.policy_id')
        ->join('agent_info', 'userinfo_policy_address.agent_id', '=', 'agent_info.agent_id')
        ->select('agent_info.agent_id', DB::raw("CONCAT(agent_info.agent_fname, ' ', COALESCE(agent_info.agent_mname, ''), ' ', COALESCE(agent_info.agent_lname, '')) AS agent_name"), DB::raw('COUNT(DISTINCT userinfo_policy_address.userid) AS user_count'))
        ->where('plan_overview.rider', 1)
        ->where('plan_overview.pstatus', 1)
        ->where('plan_overview.Approval', 1)
        ->groupBy('userinfo_policy_address.agent_id', 'agent_info.agent_id', 'agent_info.agent_fname', 'agent_info.agent_mname', 'agent_info.agent_lname')
        ->orderByDesc('user_count')
        ->limit(25)
        ->get();

        $standaloneListing = DB::table('userinfo_policy_address')
        ->join('plan_overview', 'userinfo_policy_address.policy_id', '=', 'plan_overview.policy_id')
        ->join('agent_info', 'userinfo_policy_address.agent_id', '=', 'agent_info.agent_id')
        ->select('agent_info.agent_id', DB::raw("CONCAT(agent_info.agent_fname, ' ', COALESCE(agent_info.agent_mname, ''), ' ', COALESCE(agent_info.agent_lname, '')) AS agent_name"), DB::raw('COUNT(DISTINCT userinfo_policy_address.userid) AS user_count'))
        ->where('plan_overview.rider', 2)
        ->where('plan_overview.pstatus', 1)
        ->where('plan_overview.Approval', 1)
        ->where('plan_overview.pl_type','!=', 'MM')
        ->groupBy('userinfo_policy_address.agent_id', 'agent_info.agent_id', 'agent_info.agent_fname', 'agent_info.agent_mname', 'agent_info.agent_lname')
        ->orderByDesc('user_count')
        ->limit(25)
        ->get();
    
        $dentVisionListing = DB::table('userinfo_policy_address')
        ->join('agent_info', 'userinfo_policy_address.agent_id', '=', 'agent_info.agent_id')
        ->select('agent_info.agent_id', DB::raw("CONCAT(agent_info.agent_fname, ' ', COALESCE(agent_info.agent_mname, ''), ' ', COALESCE(agent_info.agent_lname, '')) AS agent_name"), DB::raw('COUNT(DISTINCT userinfo_policy_address.userid) AS user_count'))
        ->whereIn('userinfo_policy_address.policy_id', function ($query) {
            $query->select('policy_id')
                ->from('plan_overview')
                ->whereIn('pl_type', ['DENT', 'VISION'])
                ->where('pstatus', 1)
                ->where('Approval', 1)
                ->groupBy('policy_id')
                ->havingRaw('COUNT(DISTINCT pl_type) = 2');
        })
        ->groupBy('userinfo_policy_address.agent_id', 'agent_info.agent_id', 'agent_info.agent_fname', 'agent_info.agent_mname', 'agent_info.agent_lname')
        ->orderByDesc('user_count')
        ->limit(25)
        ->get();

        $response = ['status' => 'success', 'message' => 'Data fetched successfully',     'data' => [
            'medicalListing' => $medicalListing,
            'ridersListing' => $ridersListing,
            'dentVisionListing' => $dentVisionListing,
            'ihaHelthListing' => $ihaHelthListing,
            'lifelineMedicalListing' => $lifelineMedicalListing,
            'standaloneListing' => $standaloneListing,
        ]];
        return $response;
    }

}
