<?php

namespace App\Repositories\Agents\v2;

use App\AgentInfo;
use App\Helpers\GuzzleHelper;
use App\Service\MessageService;
use App\Traits\Paginator;
use App\Traits\SyncRep;
use Illuminate\Database\Eloquent\Model;
use App\Http\Resources\SuccessResource;
use App\Http\Resources\ErrorResource;
use App\RepContract;
use App\RepContractAct;
use App\RepContractsDetail;
use App\Traits\ManageAgentTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Symfony\Component\HttpFoundation\Response;

class ManageAgents extends Model
{
    use Paginator, SyncRep, ManageAgentTrait;

    /**
     * @var AgentAchRepository
     */
    private $agentAchRepository;

    public function saveContractLevels($request){
        $agentInfo = AgentInfo::query()
        ->where('agent_id', '=', $request->agent_id)
        ->select('agent_email', 'agent_level', 'agent_fname', 'agent_mname', 'agent_lname')
        ->first();
        if (!$agentInfo)
        return response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_CONFLICT,
                'message' => 'No Agent found.'
            ]), Response::HTTP_CONFLICT
        );
        \Log::channel('repcontactlog')->info('Rep contract level request data: '.json_encode(Request::all()));
        DB::beginTransaction();
        try {
            $postData = Request::all();
            $id = base64_encode($postData['agent_id']);
            $contractEmail = '';
            $corenrollUrl = config('corenroll.corenroll_dashboard_url');
            $formattedString = '';

            if (($agentInfo->agent_level == '1' || $agentInfo->agent_level == '0')) {
                $url['w9'] = '';
            } else if(!$this->hasW9Contract($request->agent_id)) {
                $url['w9'] = url($corenrollUrl . 'w9-form/personal_info?id=' . $id);
            }
            else{
                $url['w9'] = '';
            }

            if($postData['contract_type'] === 'N-CatContrs'){
                $data = [
                    'ancillary' => $postData['data']['agent_level_alc'],
                    'premier' => $postData['data']['agent_level_pec'],
                    'bexPremier' => $postData['data']['agent_level_pec_bex'],
                    'bexAncillary' => $postData['data']['agent_level_alc_bex'],
                    'fegliAncillary' => $postData['data']['agent_level_fegli_ancillary'],
                    'patriotAncillary' => $postData['data']['agent_level_patriot_ancillary'],
                ];
                $idArray = [];
                foreach ($data as $key => $val){
                    $formatNonCatContractData = $this->formatNonCatContractData($key, $postData['agent_id']);
                    $saveNonCatRepContract = $this->saveRepContract($formatNonCatContractData);
                    $idArray[$key] = $saveNonCatRepContract->contract_id;
                    $formatedNonCatContractDetailData = $this->formatContractDetailsData($val , $key , $saveNonCatRepContract->contract_id);
                    $this->saveRepContractDetail($formatedNonCatContractDetailData);
                }
                if($data['ancillary'] != 0 && !$this->checkAlreadySigned($postData['agent_id'],'ancillary',$data['ancillary'])){
                    $url['level'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($idArray['ancillary']). '&contract_type=standardContracting');
                    $url['levelNum'] = $data['ancillary'];
                    $formattedString .= 'ALC ' . $data['ancillary'] . '-';
                    $contractEmail .= "<a href=" . $url['level'] . ">Smart Contract Level " . $url['levelNum'] . "</a><br/>";
                }

                if($data['premier'] != 0 && !$this->checkAlreadySigned($postData['agent_id'],'premier',$data['premier'])){
                    $url['levelPre'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($idArray['premier']). '&contract_type=standardContracting');
                    $url['levelPreNum'] = $data['premier'];
                    $formattedString .= 'PEC ' . $data['premier'] . '-';
                    $contractEmail .= "<a href=" . $url['levelPre'] . ">EWA Contract (Union Only) Level " . $url['levelPreNum'] . "</a><br/>";
                }

                if ($data['bexPremier'] != 0 && !$this->checkAlreadySigned($postData['agent_id'],'premier',$data['premier'])) {
                    $url['levelPreBex'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($idArray['bexPremier']). '&contract_type=standardContracting');
                    $url['levelPreBexNum'] = $data['bexPremier'];
                    $formattedString .= 'BEX PREMIER ' . $data['bexPremier'] . '-';
                    $contractEmail .= "<a href=" . $url['levelPreBex'] . ">BEX Premier Level " . $url['levelPreBexNum'] . "</a><br/>";
                }

                if ($data['bexAncillary'] != 0 && !$this->checkAlreadySigned($postData['agent_id'],'bexAncillary',$data['bexAncillary'])) {
                    $url['levelAlcBex'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($idArray['bexAncillary']). '&contract_type=standardContracting');
                    $url['levelAlcBexNum'] = $data['bexAncillary'];
                    $formattedString .= 'BEX ANCILLARY ' . $data['bexAncillary'] . '-';
                    $contractEmail .= "<a href=" . $url['levelAlcBex'] . ">BEX Ancillary Level " . $url['levelAlcBexNum'] . "</a><br/>";
                }

                if ($data['fegliAncillary'] != 0 && !$this->checkAlreadySigned($postData['agent_id'],'fegliAncillary',$data['fegliAncillary'])) {
                    $url['levelFegliAncillary'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($idArray['fegliAncillary']). '&contract_type=standardContracting');
                    $url['levelFegliAncillaryNum'] = $data['fegliAncillary'];
                    $formattedString .= 'FEGLI ANCILLARY ' . $data['fegliAncillary'] . '-';
                    $contractEmail .= "<a href=" . $url['levelFegliAncillary'] . ">GPBP (FEGLI) Level " . $url['levelFegliAncillaryNum'] . "</a><br/>";
                }

                if ($data['patriotAncillary'] != 0 && !$this->checkAlreadySigned($postData['agent_id'],'patriotAncillary',$data['patriotAncillary'])) {
                    $url['levelPatriotAncillary'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($idArray['patriotAncillary']). '&contract_type=standardContracting');
                    $url['levelPatriotAncillaryNum'] = $data['patriotAncillary'];
                    $formattedString .= 'PATRIOT ANCILLARY ' . $data['patriotAncillary'] . '-';
                    $contractEmail .= "<a href=" . $url['levelPatriotAncillary'] . ">Patriot Medical Level " . $url['levelPatriotAncillaryNum'] . "</a><br/>";
                }


                // self::updateAncillaryInAgentInfo($data['ancillary'] ? $data['ancillary'] : $data['bexAncillary'] , $data['premier'] ? $data['premier'] : $data['bexPremier'] ,$request->agent_id);
            }
            else if($request->contract_type === 'catContrs'){
                $data = [
                    'medical' => $postData['data']['agent_level_med'],
                    'dental' => $postData['data']['agent_level_dent'],
                    'vision' => $postData['data']['agent_level_vision'],
                    'termLife' => $postData['data']['agent_level_term_life'],
                    'bundled' => $postData['data']['agent_level_bundled'],
                    'limitedMedical' => $postData['data']['agent_level_lim_med'],
                    'accident' => $postData['data']['agent_level_accident'],
                    'critical' => $postData['data']['agent_level_critical'],
                    'hospital' => $postData['data']['agent_level_hospital'],
                    'lifeStyle' => $postData['data']['agent_level_life_style'],
                    'pet' => $postData['data']['agent_level_pet'],
                    'guaranteedIssue' => $postData['data']['agent_level_guarn_issue'],
                    'teleMedicine' => $postData['data']['agent_level_tele_med'],
                ];

                    $formatedContractData = $this->formatContractData('categoryL9',$postData['agent_id']);
                    $saveRepContract = $this->saveRepContract($formatedContractData);
                    foreach($data as $key => $value){
                        $formattedString .= strtoupper($key) . ' ' . $value . '-';;
                        $formatedContractDetailData = $this->formatContractDetailsData($value , $key , $saveRepContract->contract_id);
                        $this->saveRepContractDetail($formatedContractDetailData);
                    }


                    $type = 'categoryL9';
                    $url['levelcategoryL9'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($saveRepContract->contract_id). '&contract_type=categoryL9');
                    $contractEmail .= "<a href=\"" . $url['levelcategoryL9'] . "\">Patriot Medical - Multi Level Contract "  . "</a><br/>";
            }
            else if($request->contract_type === 'catContrsL7'){
                $data = [
                    'medical' => $postData['data']['agent_level_medL7'],
                    'dental' => $postData['data']['agent_level_dentL7'],
                    'dentalSolstice' => $postData['data']['agent_level_dentSolsticeL7'],
                    'vision' => $postData['data']['agent_level_visionL7'],
                    'visionSolstice' => $postData['data']['agent_level_visionSolsticeL7'],
                    'termLife' => $postData['data']['agent_level_term_lifeL7'],
                    'bundled' => $postData['data']['agent_level_bundledL7'],
                    'limitedMedical' => $postData['data']['agent_level_lim_medL7'],
                    'accident' => $postData['data']['agent_level_accidentL7'],
                    'critical' => $postData['data']['agent_level_criticalL7'],
                    'hospital' => $postData['data']['agent_level_hospitalL7'],
                    'lifeStyle' => $postData['data']['agent_level_life_styleL7'],
                    'pet' => $postData['data']['agent_level_petL7'],
                    'guaranteedIssue' => $postData['data']['agent_level_guarn_issueL7'],
                    'teleMedicine' => $postData['data']['agent_level_tele_medL7'],
                ];

                    $formatedContractData = $this->formatContractData('categoryL7',$postData['agent_id']);
                    $saveRepContract = $this->saveRepContract($formatedContractData);
                    foreach($data as $key => $value){
                        $formattedString .= strtoupper($key) . ' L7 ' . $value . '-';;
                        $formatedContractDetailData = $this->formatContractDetailsData($value , $key , $saveRepContract->contract_id);
                        $this->saveRepContractDetail($formatedContractDetailData);
                    }

                    $type = 'categoryL7';
                    $url['levelcategoryL7'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($saveRepContract->contract_id). '&contract_type=categoryL7');
                    $contractEmail .= "<a href=\"" . $url['levelcategoryL7'] . "\">Category Smart-Ancillary Contract "  . "</a><br/>";

            }
            else if ($request->contract_type === 'carrContrs'){

                $data = [
                    'local713' => $postData['data']['agent_level_local713'],
                    'cigna' => $postData['data']['agent_level_cigna'],
                    'l713Anthem' => $postData['data']['agent_level_l713Anthem'],
                    'northwellAnthem' => $postData['data']['agent_level_northwellAnthem'],
                    'IHAHealth' => $postData['data']['agent_level_IHAHealth'],
                    'ENANAWU' => $postData['data']['agent_level_ENANAWU'],
                    'lifelineMedical' => $postData['data']['agent_level_lifelineMedical'],
                    'solsticeBenefits' => $postData['data']['agent_level_solsticeBenefits'],
                    'optionsPlus' => $postData['data']['agent_level_optionsPlus'],
                    'metropolitanLifeInsuranceCompany' => $postData['data']['agent_level_metropolitanLifeInsuranceCompany'],
                    'pinnacleBenefitsService' => $postData['data']['agent_level_pinnacleBenefitsService'],
                    'prudential' => $postData['data']['agent_level_prudential'],
                    'beyondMedical' => $postData['data']['agent_level_beyondMedical'],
                ];

                    $formatedContractData = $this->formatContractData('carrier',$postData['agent_id']);
                    $saveRepContract = $this->saveRepContract($formatedContractData);
                    foreach($data as $key => $value){
                        $formattedString .= strtoupper($key) . ' ' . $value . '-';
                        $formatedContractDetailData = $this->formatContractDetailsData($value , $key , $saveRepContract->contract_id);
                        $this->saveRepContractDetail($formatedContractDetailData);
                    }


                    $type = 'carrier';
                    $url['levelcarrier'] = url($corenrollUrl . 'contract-form/personal_info?type='.'&contract_id=' . $this->base64_encode($saveRepContract->contract_id). '&contract_type=carrier');
                    $contractEmail .= "<a href=\"" . $url['levelcarrier'] . "\">Carrier Contract "  . "</a><br/>";
            }
            if($contractEmail === ''){
                return response()->json(
                    new SuccessResource([
                        'statusCode' => Response::HTTP_OK,
                        'message' => 'These levels are already signed.',
                    ]), Response::HTTP_OK
                );
            }
            $this->createRepContractAct($request, $formattedString);
            if(!isset($request->sendEmail) || ($request->sendEmail == 1)) {
                \Log::channel('repcontactlog')->info('Email send with contract email: '.$contractEmail);
                $this->sendContractEmail($agentInfo,$url,$contractEmail);
            }else{
                \Log::channel('repcontactlog')->info(' Not send with contract email: '.$contractEmail);
            }

            DB::commit();
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Representative Contracts Sent.',
                ]), Response::HTTP_OK
            );

        }
        catch (\Exception $e){
            return response()->json(
                new ErrorResource([
                    'statusCode' => Response::HTTP_CONFLICT,
                    'message' => $e->getMessage()
                ]), Response::HTTP_CONFLICT
            );
        }
    }
    public function formatContractData($contract_type , $agent_id){
        return [
            'agent_id' => $agent_id,
            'contract_type' => $contract_type,
            'contract_display_name' => $this->getContractDisplayName($contract_type),
            'is_completed' => 'no',
        ];

    }

    public function formatContractDetailsData($value, $key, $contract_id){
        return [
            'contract_id' => $contract_id,
            'contract_item_name' => $key,
            'contract_level' => $value,
            'contract_item_display_name' => $this->getContractItemDisplayName($key),

        ];

    }

    public function formatNonCatContractData($key, $agent_id){
        return [
            'agent_id' => $agent_id,
            'contract_type' => $key,
            'contract_display_name' => 'Non-Category Contract',
            'is_completed' => 'no',
        ];

    }


    public function saveRepContract($data){
        $repContract = new RepContract;
        return $repContract->saveRepContract($data);
    }

    public function saveRepContractDetail($data){
        $repContractDetail = new RepContractsDetail;
        return $repContractDetail->saveRepContractDetails($data);
    }

    public function base64_encode($value){
        return base64_encode($value);
    }

    protected function hasW9Contract($agentId)
    {
        $url = config('corenroll.corenroll_api_url') . "api/v2/hasW9Contract/{$agentId}/check";
        $responseJson = GuzzleHelper::getApi($url, []);
        $response = json_decode($responseJson, true);
        if ($response['success'] == true) {
            return $response['data']['hasContract'];
        } else {
            return false;
        }
    }

    public function sendContractEmail($agentInfo,$url,$contractEmail){
        $messageService = new MessageService();
        $template_body = "Hello {$agentInfo->fullname},<br/><br/>";
        $template_body .= "<strong>Your Association Representative Contract(s) are ready.</strong><br>";
        $template_body .= "Please click the links below to fill the contract form, complete and submit.<br/>";
        $template_body .= $contractEmail;
        if ($url['w9'] != '') {
            $template_body .= "<br/> Please note the link(s), expire in 5 days.<br/> <br/> Please complete this with contract and submit:<br/><a href=" . $url['w9'] . ">W-9 Form</a><br/> ";
        }
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $post = [
            'email_message_configuration_name' => "NUERA_API_GENERIC",
            'toAddress' => $toAddress,
            'subject' => "Representative Contract",
            'generalTemplateData' => [],
            'contentTemplate' => $template_body
        ];

        return $messageService->sendEmailWithContentTemplate($post);
    }

    // public static function updateAncillaryInAgentInfo($level, $preLevel, $agentId)
    // {
    //     $agent = AgentInfo::where('agent_id', $agentId)->first();
    //     if ($agent) {
    //         $agent->agent_level_2 = $level;
    //         $agent->agent_level = $preLevel;
    //         $agent->save();
    //     }

    // }

    public function getContractDisplayName($contract_type){
        $contractWebName = [
            'categoryL7' => 'Category Smart-Ancillary Contract',
            'categoryL9' => 'Patriot Medical - Multi Level Contract',
            'carrier'=> 'Carrier Contract'
        ];

            return isset($contractWebName[$contract_type]) ? $contractWebName[$contract_type] : null;
    }

    public function getContractItemDisplayName($key){
        $contractItemDisplayName = [
            'ancillary' =>  'Smart Contract',
            'premier' => 'EWA Contract (Union Only) ',
            'bexPremier' => 'BEX Premier Contract',
            'bexAncillary' => 'BEX Ancillary Contract',
            'fegliAncillary' => 'GPBP (FEGLI) Contract',
            'patriotAncillary' => 'Patriot Medical',
            'medical' => 'Medical',
            'dental' => 'Dental',
            'vision' => 'Vision',
            'termLife' => 'Term Life',
            'bundled' => 'Bundles',
            'limitedMedical' => 'Limited Medical',
            'accident' => 'Accident',
            'critical' => 'Critical',
            'hospital' => 'Hospital',
            'lifeStyle' => 'Life Style',
            'pet' => 'Pet',
            'guaranteedIssue' => 'Guaranteed Issue',
            'teleMedicine' => 'Telemedicine',
            'dentalSolstice'=>'Dental Solstice',
            'visionSolstice'=>'Vision Solstice',
            'local713' => 'Local 713',
            'cigna' => 'Cigna',
            'l713Anthem' => 'L713 Anthem',
            'northwellAnthem' => 'Northwell Anthem',
            'IHAHealth' => 'IHA Health',
            'ENANAWU' => 'ENANAWU',
            'lifelineMedical' => 'Lifeline Medical',
            'solsticeBenefits' => 'Solstice Benefits',
            'optionsPlus' => 'Options Plus',
            'metropolitanLifeInsuranceCompany' => 'Metropolitan Life Insurance Company',
            'pinnacleBenefitsService' => 'Pinnacle Benefits Services LLC',
            'prudential' => 'Prudential',
            'beyondMedical' => 'Beyond Medical',
        ];

            return isset($contractItemDisplayName[$key]) ? $contractItemDisplayName[$key] : null;
    }

    public function getContractActionsNew($agentId)
    {
        $data = RepContract::query()
            ->with('repContractDetail')
            ->where('agent_id', '=', $agentId)
            ->orderBy('contract_id', 'DESC')
            ->get();
        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'data' => $data,
                'message' => 'Contracts log fetched.',
            ]), Response::HTTP_OK
        );
        return $data;
    }

    public function contractLevelsNew($agentId)
    {
        $agentInfo = AgentInfo::find($agentId);
        $data = RepContract::query()
            ->with('repContractDetail')
            ->where('agent_id', '=', $agentId)
            ->orderBy('contract_id', 'DESC')
            ->where('is_completed','yes')
            ->whereIn('contract_id', function ($query) use($agentId) {
                $query->select(DB::raw('MAX(contract_id)'))
                    ->from('rep_contract')
                    ->where('agent_id',$agentId)
                    ->where('is_completed','yes')
                    ->groupBy('contract_type');
            })
            ->get();
        if($data->isEmpty()){
            $data = collect();
        }
        else{

            $latestContract = $data->first();
            $contract_display_name = $latestContract->contract_display_name;
        }

        [$alc, $bex, $prem, $Pbex, $fegliAncillary, $fegliPremier, $patriotAncillary,
            $medical, $dental, $vision, $termLife, $Bundles, $limitedMedical, $accident, $critical, $hospital, $lifeStyle, $pet, $guaranteedIssue, $teleMedicine,
            $medicalL7, $dentalL7, $visionL7, $dentalSolsticeL7, $visionSolsticeL7, $termLifeL7, $BundlesL7, $limitedMedicalL7, $accidentL7, $criticalL7, $hospitalL7, $lifeStyleL7, $petL7, $guaranteedIssueL7, $teleMedicineL7,
            $local713, $cigna, $l713Anthem, $northwellAnthem, $IHAHealth, $ENANAWU, $lifelineMedical, $solsticeBenefits, $optionsPlus, $metropolitanLifeInsuranceCompany, $pinnacleBenefitsService, $prudential, $beyondMedical] =
           [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
           if(!$this->isBex($agentId)){
            $alc = $agentInfo->agent_level_2;
            $prem = $agentInfo->agent_level;
           }
           else{
            $bex =  $agentInfo->agent_level_2 ;
            $Pbex = $agentInfo->agent_level;
           }
        if(count($data) > 0){
            foreach ($data as $val) {
                if (isset($val->repContractDetail)) {
                    foreach ($val->repContractDetail as $con_value) {
                        if ($con_value->contract_item_name == 'patriotAncillary') {
                            $patriotAncillary = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'fegliAncillary') {
                            $fegliAncillary = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'medical' && $val->contract_type === 'categoryL9') {
                            $medical = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'dental' && $val->contract_type === 'categoryL9') {
                            $dental = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'vision' && $val->contract_type === 'categoryL9') {
                            $vision = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'termLife' && $val->contract_type === 'categoryL9') {
                            $termLife = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'bundled' && $val->contract_type === 'categoryL9') {
                            $Bundles = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'limitedMedical' && $val->contract_type === 'categoryL9') {
                            $limitedMedical = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'accident' && $val->contract_type === 'categoryL9') {
                            $accident = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'critical' && $val->contract_type === 'categoryL9') {
                            $critical = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'hospital' && $val->contract_type === 'categoryL9') {
                            $hospital = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'lifeStyle' && $val->contract_type === 'categoryL9') {
                            $lifeStyle = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'pet' && $val->contract_type === 'categoryL9') {
                            $pet = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'guaranteedIssue' && $val->contract_type === 'categoryL9') {
                            $guaranteedIssue = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'teleMedicine' && $val->contract_type === 'categoryL9') {
                            $teleMedicine = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'medical' && $val->contract_type === 'categoryL7') {
                            $medicalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'dental' && $val->contract_type === 'categoryL7') {
                            $dentalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'dentalSolstice' && $val->contract_type === 'categoryL7') {
                            $dentalSolsticeL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'vision' && $val->contract_type === 'categoryL7') {
                            $visionL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'visionSolstice' && $val->contract_type === 'categoryL7') {
                            $visionSolsticeL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'termLife' && $val->contract_type === 'categoryL7') {
                            $termLifeL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'bundled' && $val->contract_type === 'categoryL7') {
                            $BundlesL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'limitedMedical' && $val->contract_type === 'categoryL7') {
                            $limitedMedicalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'accident' && $val->contract_type === 'categoryL7') {
                            $accidentL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'critical' && $val->contract_type === 'categoryL7') {
                            $criticalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'hospital' && $val->contract_type === 'categoryL7') {
                            $hospitalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'lifeStyle' && $val->contract_type === 'categoryL7') {
                            $lifeStyleL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'pet' && $val->contract_type === 'categoryL7') {
                            $petL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'guaranteedIssue' && $val->contract_type === 'categoryL7') {
                            $guaranteedIssueL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'teleMedicine' && $val->contract_type === 'categoryL7') {
                            $teleMedicineL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'local713') {
                            $local713 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'cigna') {
                            $cigna = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'l713Anthem') {
                            $l713Anthem = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'northwellAnthem') {
                            $northwellAnthem = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'IHAHealth') {
                            $IHAHealth = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'ENANAWU') {
                            $ENANAWU = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'lifelineMedical') {
                            $lifelineMedical = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'solsticeBenefits') {
                            $solsticeBenefits = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'optionsPlus') {
                            $optionsPlus = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'metropolitanLifeInsuranceCompany') {
                            $metropolitanLifeInsuranceCompany = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'pinnacleBenefitsService') {
                            $pinnacleBenefitsService = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'prudential') {
                            $prudential = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'beyondMedical') {
                            $beyondMedical = $con_value->contract_level;
                        }
                    }
                }
            }
        }
            $contractValue =  [
                'alc' => (int)$alc,
                'Pbex' => (int)$Pbex,
                'Abex' => (int)$bex,
                'pec' => (int)$prem,
                'fegliAncillary' => (int) $fegliAncillary,
                'fegliPremier' => (int) $fegliPremier,
                'patriotAncillary' => (int) $patriotAncillary,
                'med' => (int)$medical,
                'dent' => (int)$dental,
                'vision' => (int)$vision,
                'term_life' => (int)$termLife,
                'bundled' => (int)$Bundles,
                'lim_med' => (int)$limitedMedical,
                'accident' => (int)$accident,
                'critical' => (int)$critical,
                'hospital' => (int)$hospital,
                'life_style' => (int)$lifeStyle,
                'pet' => (int)$pet,
                'guarn_issue' => (int)$guaranteedIssue,
                'tele_med' => (int)$teleMedicine,
                'local713' => (int)$local713,
                'cigna' => (int)$cigna,
                'l713Anthem' => (int)$l713Anthem,
                'northwellAnthem' => (int)$northwellAnthem,
                'IHAHealth' => (int)$IHAHealth,
                'ENANAWU' => (int)$ENANAWU,
                'lifelineMedical' => (int)$lifelineMedical,
                'solsticeBenefits' => (int)$solsticeBenefits,
                'optionsPlus' => (int)$optionsPlus,
                'metropolitanLifeInsuranceCompany' => (int)$metropolitanLifeInsuranceCompany,
                'pinnacleBenefitsService' => (int)$pinnacleBenefitsService,
                'prudential' => (int)$prudential,
                'beyondMedical' => (int)$beyondMedical,
                'medL7' => (int)$medicalL7,
                'dentL7' => (int)$dentalL7,
                'dentSolsticeL7' => (int)$dentalSolsticeL7,
                'visionL7' => (int)$visionL7,
                'visionSolsticeL7' => (int)$visionSolsticeL7,
                'term_lifeL7' => (int)$termLifeL7,
                'bundledL7' => (int)$BundlesL7,
                'lim_medL7' => (int)$limitedMedicalL7,
                'accidentL7' => (int)$accidentL7,
                'criticalL7' => (int)$criticalL7,
                'hospitalL7' => (int)$hospitalL7,
                'life_styleL7' => (int)$lifeStyleL7,
                'petL7' => (int)$petL7,
                'guarn_issueL7' => (int)$guaranteedIssueL7,
                'tele_medL7' => (int)$teleMedicineL7,
                'contract_type' => isset($contract_display_name) && $contract_display_name === 'Non-Category Contract' ? 'N-CatContrs' :
                (isset($contract_display_name) && $contract_display_name === 'Category Patriot-Ancillary Contract' ? 'catContrs' :
                (isset($contract_display_name) && $contract_display_name === 'Category Smart-Ancillary Contract' ? 'catContrsL7' :
                (isset($contract_display_name) && $contract_display_name === 'Carrier Contract'?'carrContrs':'N-CatContrs')))
            ];



        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'data' => $contractValue,
                'message' => 'Contract levels fetched.',
            ]), Response::HTTP_OK
        );
    }

    public function contractLevelsNewAdmin($agentId)
    {
        $agentInfo = AgentInfo::find($agentId);
        $data = RepContract::query()
            ->with('repContractDetail')
            ->where('agent_id', '=', $agentId)
            ->orderBy('contract_id', 'DESC')
            ->first();
        if (!empty($data)) {
            $contract_display_name = $data->contract_display_name;
            if ($contract_display_name === 'Non-Category Contract') {
                $data = RepContract::query()
                ->with('repContractDetail')
                ->where('contract_display_name', 'Non-Category Contract')
                ->where('agent_id',$agentId)
                ->whereIn('contract_id', function ($query) use($agentId) {
                    $query->select(DB::raw('MAX(contract_id)'))
                        ->from('rep_contract')
                        ->where('agent_id',$agentId)
                        ->where('contract_display_name', 'Non-Category Contract')
                        ->groupBy('contract_type');
                })
                ->get();
            } else {
                $data = collect([$data]);
            }
        } else {
            $data = collect();
        }

        [$alc, $bex, $prem, $Pbex, $fegliAncillary, $fegliPremier, $patriotAncillary,
            $medical, $dental, $vision, $termLife, $Bundles, $limitedMedical, $accident, $critical, $hospital, $lifeStyle, $pet, $guaranteedIssue, $teleMedicine,
            $medicalL7, $dentalL7, $visionL7, $dentalSolsticeL7, $visionSolsticeL7, $termLifeL7, $BundlesL7, $limitedMedicalL7, $accidentL7, $criticalL7, $hospitalL7, $lifeStyleL7, $petL7, $guaranteedIssueL7, $teleMedicineL7,
            $local713, $cigna, $l713Anthem, $northwellAnthem, $IHAHealth, $ENANAWU, $lifelineMedical, $solsticeBenefits, $optionsPlus, $metropolitanLifeInsuranceCompany, $pinnacleBenefitsService, $prudential, $beyondMedical] =
           [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
           if(!$this->isBex($agentId)){
            $alc = $agentInfo->agent_level_2;
            $prem = $agentInfo->agent_level;
           }
           else{
            $bex =  $agentInfo->agent_level_2 ;
            $Pbex = $agentInfo->agent_level;
           }
        if(count($data) > 0){
            foreach ($data as $val) {
                if (isset($val->repContractDetail)) {
                    foreach ($val->repContractDetail as $con_value) {
                        if ($con_value->contract_item_name == 'patriotAncillary') {
                            $patriotAncillary = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'fegliAncillary') {
                            $fegliAncillary = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'medical' && $val->contract_type === 'categoryL9') {
                            $medical = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'dental' && $val->contract_type === 'categoryL9') {
                            $dental = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'vision' && $val->contract_type === 'categoryL9') {
                            $vision = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'termLife' && $val->contract_type === 'categoryL9') {
                            $termLife = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'bundled' && $val->contract_type === 'categoryL9') {
                            $Bundles = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'limitedMedical' && $val->contract_type === 'categoryL9') {
                            $limitedMedical = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'accident' && $val->contract_type === 'categoryL9') {
                            $accident = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'critical' && $val->contract_type === 'categoryL9') {
                            $critical = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'hospital' && $val->contract_type === 'categoryL9') {
                            $hospital = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'lifeStyle' && $val->contract_type === 'categoryL9') {
                            $lifeStyle = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'pet' && $val->contract_type === 'categoryL9') {
                            $pet = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'guaranteedIssue' && $val->contract_type === 'categoryL9') {
                            $guaranteedIssue = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'teleMedicine' && $val->contract_type === 'categoryL9') {
                            $teleMedicine = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'medical' && $val->contract_type === 'categoryL7') {
                            $medicalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'dental' && $val->contract_type === 'categoryL7') {
                            $dentalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'dentalSolstice' && $val->contract_type === 'categoryL7') {
                            $dentalSolsticeL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'vision' && $val->contract_type === 'categoryL7') {
                            $visionL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'visionSolstice' && $val->contract_type === 'categoryL7') {
                            $visionSolsticeL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'termLife' && $val->contract_type === 'categoryL7') {
                            $termLifeL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'bundled' && $val->contract_type === 'categoryL7') {
                            $BundlesL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'limitedMedical' && $val->contract_type === 'categoryL7') {
                            $limitedMedicalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'accident' && $val->contract_type === 'categoryL7') {
                            $accidentL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'critical' && $val->contract_type === 'categoryL7') {
                            $criticalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'hospital' && $val->contract_type === 'categoryL7') {
                            $hospitalL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'lifeStyle' && $val->contract_type === 'categoryL7') {
                            $lifeStyleL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'pet' && $val->contract_type === 'categoryL7') {
                            $petL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'guaranteedIssue' && $val->contract_type === 'categoryL7') {
                            $guaranteedIssueL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'teleMedicine' && $val->contract_type === 'categoryL7') {
                            $teleMedicineL7 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'local713') {
                            $local713 = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'cigna') {
                            $cigna = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'l713Anthem') {
                            $l713Anthem = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'northwellAnthem') {
                            $northwellAnthem = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'IHAHealth') {
                            $IHAHealth = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'ENANAWU') {
                            $ENANAWU = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'lifelineMedical') {
                            $lifelineMedical = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'solsticeBenefits') {
                            $solsticeBenefits = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'optionsPlus') {
                            $optionsPlus = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'metropolitanLifeInsuranceCompany') {
                            $metropolitanLifeInsuranceCompany = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'pinnacleBenefitsService') {
                            $pinnacleBenefitsService = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'prudential') {
                            $prudential = $con_value->contract_level;
                        }
                        if ($con_value->contract_item_name == 'beyondMedical') {
                            $beyondMedical = $con_value->contract_level;
                        }
                    }
                }
            }
        }
            $contractValue =  [
                'alc' => (int)$alc,
                'Pbex' => (int)$Pbex,
                'Abex' => (int)$bex,
                'pec' => (int)$prem,
                'fegliAncillary' => (int) $fegliAncillary,
                'fegliPremier' => (int) $fegliPremier,
                'patriotAncillary' => (int) $patriotAncillary,
                'med' => (int)$medical,
                'dent' => (int)$dental,
                'vision' => (int)$vision,
                'term_life' => (int)$termLife,
                'bundled' => (int)$Bundles,
                'lim_med' => (int)$limitedMedical,
                'accident' => (int)$accident,
                'critical' => (int)$critical,
                'hospital' => (int)$hospital,
                'life_style' => (int)$lifeStyle,
                'pet' => (int)$pet,
                'guarn_issue' => (int)$guaranteedIssue,
                'tele_med' => (int)$teleMedicine,
                'local713' => (int)$local713,
                'cigna' => (int)$cigna,
                'l713Anthem' => (int)$l713Anthem,
                'northwellAnthem' => (int)$northwellAnthem,
                'IHAHealth' => (int)$IHAHealth,
                'ENANAWU' => (int)$ENANAWU,
                'lifelineMedical' => (int)$lifelineMedical,
                'solsticeBenefits' => (int)$solsticeBenefits,
                'optionsPlus' => (int)$optionsPlus,
                'metropolitanLifeInsuranceCompany' => (int)$metropolitanLifeInsuranceCompany,
                'pinnacleBenefitsService' => (int)$pinnacleBenefitsService,
                'prudential' => (int)$prudential,
                'beyondMedical' => (int)$beyondMedical,
                'medL7' => (int)$medicalL7,
                'dentL7' => (int)$dentalL7,
                'dentSolsticeL7' => (int)$dentalSolsticeL7,
                'visionL7' => (int)$visionL7,
                'visionSolsticeL7' => (int)$visionSolsticeL7,
                'term_lifeL7' => (int)$termLifeL7,
                'bundledL7' => (int)$BundlesL7,
                'lim_medL7' => (int)$limitedMedicalL7,
                'accidentL7' => (int)$accidentL7,
                'criticalL7' => (int)$criticalL7,
                'hospitalL7' => (int)$hospitalL7,
                'life_styleL7' => (int)$lifeStyleL7,
                'petL7' => (int)$petL7,
                'guarn_issueL7' => (int)$guaranteedIssueL7,
                'tele_medL7' => (int)$teleMedicineL7,
                'contract_type' => isset($contract_display_name) && $contract_display_name === 'Non-Category Contract' ? 'N-CatContrs' :
                (isset($contract_display_name) && $contract_display_name === 'Category Patriot-Ancillary Contract' ? 'catContrs' :
                (isset($contract_display_name) && $contract_display_name === 'Category Smart-Ancillary Contract' ? 'catContrsL7' :
                (isset($contract_display_name) && $contract_display_name === 'Carrier Contract'?'carrContrs':'N-CatContrs')))
            ];



        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'data' => $contractValue,
                'message' => 'Contract levels fetched.',
            ]), Response::HTTP_OK
        );
    }

    public function checkAlreadySigned($agent_id, $contract_type, $level){
        return RepContract::where('agent_id', $agent_id)
        ->where('contract_type', $contract_type)
        ->where('is_completed', 'yes')
        ->whereHas('repContractDetail', function ($q) use ($level) {
            $q->where('contract_level', $level);
        })
        ->exists();
    }

    public function createRepContractAct($request, $contract, $categoryContract = null, $carrierContract = null)
    {
        try {
            $data = [
                'action' => 'contracts sent',
                'aid' => $request->agent_id,
                'userid' => isset($request->repid) ? $request->repid : request()->header('id'),
                'source' => $request->action_origin,
                'contracts' => $contract,
                'contract_type' => $request->contract_type,
                'ts' => time(),
                'ipaddress' => request()->ip()
            ];
            return  RepContractAct::query()
                ->create($data);

        } catch (\Throwable $th) {
            return $this->failedResponse('Something Went Wrong');
        }
    }

}
