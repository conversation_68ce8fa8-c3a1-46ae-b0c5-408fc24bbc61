<?php

namespace App\Repositories\Agents;

use App\AgentAchpayment;
use App\AgentAchPaymentBankDetail;
use App\AgentInfo;
use App\Helpers\AgentUpdatesHelper;
use App\Helpers\CommonHelper;
use App\Service\MessageService;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class AgentAchRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var AgentAchpayment
     */
    private $model;
    /**
     * @var AgentInfo
     */
    private $agentInfo;
    /**
     * @var AgentAchPaymentBankDetail
     */
    private $achPaymentBankDetail;
    /**
     * @var MessageService
     */
    private $messageService;

    /**
     * AgentAchRepository constructor.
     * @param AgentAchpayment $model
     * @param AgentInfo $agentInfo
     * @param AgentAchPaymentBankDetail $achPaymentBankDetail
     * @param MessageService $messageService
     */
    public function __construct(
        AgentAchpayment $model,
        AgentInfo $agentInfo,
        AgentAchPaymentBankDetail $achPaymentBankDetail,
        MessageService $messageService
    )
    {
        $this->model = $model;
        $this->agentInfo = $agentInfo;
        $this->achPaymentBankDetail = $achPaymentBankDetail;
        $this->messageService = $messageService;
    }

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->orderBy('achpayment_default', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['achagent_id'])) {
            $data->where('achagent_id', '=', $filters['achagent_id']);
        }
    }

    protected function checkAchPayment($agentId)
    {
        return $this->model::query()
            ->where('achagent_id', '=', $agentId)
            ->exists();
    }

    public function getAgentAchPayments($agentId)
    {
        $agentInfo = $this->agentInfo->find($agentId);
        if (!$agentInfo) return $this->failedResponse('Agent not found.');
        if (!$this->checkAchPayment($agentId)) return $this->failedResponse('Agent Ach data not found.');
        $data = $this->achPaymentBankDetail::query()
            ->where('agent_id', '=', $agentId)
            ->orderBy('is_primary', 'DESC')
            ->get();
        return $this->successResponse("Success", $data);
    }


    public function createAchPayment($request)
    {
        $agentId = $request['agent_id'];
        $primary = isset($request['is_primary']) ? $request['is_primary'] : 0;
        $sendEmail = isset($request['send_email']) ? $request['send_email'] : 0;
        DB::beginTransaction();
        try {

            if (!$this->checkAchPayment($agentId)) {
                //create ach payment
                $achPaymentData = $this->setAchPaymentData($request);
                unset($request['is_primary'], $request['achpayment_default']);
                $this->model::create($achPaymentData);
            }
            //create ach payment detail
            $achPaymentDetailData = $request;
            $achPaymentDetailData['is_primary'] = $primary;
            $data = $this->achPaymentBankDetail::create($achPaymentDetailData);

            //update related primary ach payment detail
            if ($primary == 1) {
                $this->updatePrimaryAchPayment($data);
            }

            $successMessage = "Ach payment added successfully.";
            //update to agent_updates
            $this->createAgentUpdate($request, \App\AgentUpdate::ACT_BANKINGS_INFO, $successMessage);

            //send email
            if ($sendEmail == 1) {
                $this->sendAchPaymentEmail($data);
            }

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse("Failed to add ach payment.");
        }
    }

    protected function saveRepAchSignature($image, $fileName)
    {
        if ($image) {
            $img = str_replace('data:image/png;base64,', '', $image);
            $img = str_replace(' ', '+', $img);
            if (!$this->checkValidImage($img)) {
                throw new Exception('Invalid image.');
            }
            $dataImage = base64_decode($img);
            Storage::disk('corenroll')->put('/CORENROLL/bank_sign/' . $fileName, ($dataImage));
            return $fileName;
        }
        return '';
    }

    private function checkValidImage($file)
    {
        $fileData = base64_decode($file);
        $fileOpen = finfo_open();
        $mime_type = finfo_buffer($fileOpen, $fileData, FILEINFO_MIME_TYPE);
        $file_type = explode('/', $mime_type)[0];
        return ($file_type == 'image') ? true : false;
    }

    public function setPrimaryAchPayment($request)
    {
        $data = $this->achPaymentBankDetail::find($request['id']);
        if (!$data) return $this->failedResponse("No Ach Payment found.");
        $checkPrimaryAchPayment = $this->checkIfPaymentIsPrimary($request['id']);
        if (!$checkPrimaryAchPayment) {
            DB::beginTransaction();
            try {
                $data->update(['is_primary' => 1]);

                //update related primary ach payment detail
                $this->updatePrimaryAchPayment($data);

                $successMessage = "Ach Payment successfully set to primary.";

                //update to agent_updates
                $request['agent_id'] = $data->agent_id;
                $this->createAgentUpdate($request, \App\AgentUpdate::ACT_BANKINGS_INFO, $successMessage);

                //send email
                $this->sendAchPaymentEmail($data, 'set to primary', 'Set Primary Bank');

                DB::commit();
                return $this->successResponse($successMessage);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse("Failed to set ach payment primary.");
            }
        } else {
            return $this->failedResponse('This ach payment is already set to primary.', 409);
        }

    }

    public function deleteAchPayment($request)
    {
        $data = $this->achPaymentBankDetail::find($request['id']);
        if (!$data) return $this->failedResponse("No Ach Payment found.");
        $checkPrimaryAchPayment = $this->checkIfPaymentIsPrimary($request['id']);
        if (!$checkPrimaryAchPayment) {
            try {
                $data->delete();
                $successMessage = "Ach Payment deleted successfully.";
                //update to agent_updates
                $request['agent_id'] = $data->agent_id;
                $this->createAgentUpdate($request, \App\AgentUpdate::ACT_BANKINGS_INFO, $successMessage);

                //send email
                $this->sendAchPaymentEmail($data, 'deleted', 'Delete Bank');
                return $this->successResponse($successMessage);
            } catch (\Throwable $th) {
                return $this->failedResponse("Failed to delete this ach payment.");
            }
        } else {
            return $this->failedResponse('Failed to delete this ach payment.', 409);
        }
    }

    protected function getAgentInfo($agentId)
    {
        return $this->agentInfo::query()->where('agent_id', '=', $agentId)->first();
    }


    protected function setAchPaymentData($data)
    {
        if (is_array($data)) {
            $data = (object)$data;
        }
        return [
            "achpayment_name" => $data->payee_name,
            "achpayment_bankname" => $data->bank_name,
            "achpayment_routing" => $data->routing,
            "achpayment_account" => $data->account,
            "achagent_id" => $data->agent_id,
            "achpayment_default" => 1,
            "account_type" => isset($data->account_type) ? $data->account_type : "",
            "account_holder_type" => isset($data->account_holder_type) ? $data->account_holder_type : "",
            "bank_sign" => isset($data->bank_sign) ? $data->bank_sign : "",
        ];
    }

    protected function setAgentInfoBrokerData($data)
    {
        $maskedAccount = isset($data->account) ? CommonHelper::encryptInfo($data->account) : "";
        return [
            "broker_bank_name" => $data->payee_name,
            "broker_bank_acct" => $data->bank_name,
            "broker_routing_num" => $data->routing,
            "broker_acct_num" => $maskedAccount,
        ];
    }

    public function setRelatedAchPaymentDetailPrimaryOff($achPaymentDetailId, $agentId)
    {
        return $this->achPaymentBankDetail::query()
            ->where('agent_id', '=', $agentId)
            ->where('id', '!=', $achPaymentDetailId)
            ->update(['is_primary' => 0]);
    }


    protected function checkIfPaymentIsPrimary($id)
    {
        return $this->achPaymentBankDetail::query()
            ->where([
                'id' => $id,
                'is_primary' => 1
            ])
            ->exists();
    }

    protected function updateAgentInfoBroker($data)
    {
        $agentId = $data->agent_id;
        $agentInfo = $this->getAgentInfo($agentId);
        $agentData = $this->setAgentInfoBrokerData($data);
        return $agentInfo->update($agentData);
    }

    protected function updateAchPayment($data)
    {
        $agentId = $data->agent_id;
        $achData = $this->setAchPaymentData($data);
        return $this->model::query()
            ->where('achagent_id', '=', $agentId)
            ->update($achData);
    }

    protected function updatePrimaryAchPayment($data)
    {
        $agentId = $data->agent_id;
        //set related agent payment detail is_primary =0
        $this->setRelatedAchPaymentDetailPrimaryOff($data->id, $agentId);

        //update agent info
        $this->updateAgentInfoBroker($data);

        //update achpayment
        $this->updateAchPayment($data);
    }

    public function updatePaymentMethodWithAchPayment($request)
    {
        $agentId = $request['agent_id'];
        $primary = 1;
        $sendEmail = isset($request['send_email']) ? $request['send_email'] : 0;
        DB::beginTransaction();
        try {
            AgentInfo::query()
                ->where('agent_id', $request['agent_id'])
                ->update([
                    'agent_payment_method' => $request['payment_method'],
                    'payby' => $request['payment_method']
                ]);
            $successMessage = "Agent Payment method updated and ach payment added successfully.";

            if ($request['payment_method'] == AgentInfo::AGENT_PAYMENT_METHOD_ACH) {

                //create ach payment
                $achPaymentData = $this->setAchPaymentData($request);
                unset($request['achpayment_default'], $request['payment_method']);
                $this->model::create($achPaymentData);
                //create ach payment detail
                $achPaymentDetailData = $request;
                $achPaymentDetailData['is_primary'] = $primary;
                $data = $this->achPaymentBankDetail::create($achPaymentDetailData);

                //update related primary ach payment detail
                if ($primary == 1) {
                    $this->updatePrimaryAchPayment($data);
                }

                //update to agent_updates
                $this->createAgentUpdate($request, \App\AgentUpdate::ACT_BANKINGS_INFO, $successMessage);

                //send email
                if ($sendEmail == 1) {
                    $emailStatusMessage = 'created and payment method has been updated';
                    $emailSubject = 'Payment Method Updated and Bank Created.';
                    $this->sendAchPaymentEmail($data, $emailStatusMessage, $emailSubject);
                }
            }
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse("Failed to update agent payment method with ach.");
        }
    }

    protected function sendAchPaymentEmail($achPayment, $statusMessage = "created", $subject = "Agent Bank Created")
    {
        $agentInfo = AgentInfo::find($achPayment->agent_id);
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->cemail;
        $date = Carbon::parse($achPayment->created_at)->format('m/d/Y');
        $message = "This is an automated notice to notify that your following bank has been {$statusMessage} on " . $date . ".";

        $bankDetail = "Account Name : {$achPayment->bank_name}<br/>";
        $bankDetail .= "Account Holder : {$achPayment->payee_name}<br/>";
        $bankDetail .= "Routing Number : {$achPayment->routing}<br/>";
        $bankDetail .= "Account Number : {$achPayment->account}";
        $contentData = [
            'Agent Id' => $agentInfo->agent_id,
            'Payment Method' => $agentInfo->agent_payment_method,
            'Bank Detail' => $bankDetail,
            'Primary' => $achPayment->is_primary ? 'Yes' : 'No'
        ];
        $emailConfigurationName = "AGENT_INFO_CHANGE";

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
        ];
        return $this->messageService->sendEmailWithContentData($emailData);
    }

    protected function createAgentUpdate($request, $act = "", $comment = "")
    {
        $data = [
            "agent_id" => $request['agent_id'],
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent_id" => isset($request['loginUserId']) ? $request['loginUserId'] : null,
            "elgb_agent_name" => isset($request['loginUserName']) ? $request['loginUserName'] : null
        ];
        AgentUpdatesHelper::createAgentUpdateLog($data);
    }

}
