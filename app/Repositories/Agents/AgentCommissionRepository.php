<?php

namespace App\Repositories\Agents;

use App\CommissionHistory;
use App\Helpers\CommonHelper;
use App\NbCommissionDataSummary;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\AgentPNCheckInfo;

class AgentCommissionRepository extends Model
{
    public $months = array(
        1 => 'jan', 2 => 'feb', 3 => 'mar',  4 => 'apr',  5 => 'may',  6 => 'jun',
        7 => 'jul', 8 => 'aug', 9 => 'sep', 10 => 'oct', 11 => 'nov', 12 => 'dec'
    );

    /**
     * @param $agent_id
     * @param $year
     * @param $month
     * @return array
     */
    public function fetchAgentCommissionDetailForMonth($agent_id, $year, $month): array
    {
        $month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $month);
        $prior_month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $month, true);

        if (isset($prior_month_commission)) {
            $prior_month_comm_total = (float) $prior_month_commission->direct
                + (float) $prior_month_commission->downline
                + (float) $prior_month_commission->special
                + (float) $prior_month_commission->association
                + (float) $prior_month_commission->onetime
                + (float) $prior_month_commission->affiliate
                + (float) $prior_month_commission->adjustments_credit
                - (float) $prior_month_commission->adjustment_debit
                - (float) $prior_month_commission->refunds
                + (float) $prior_month_commission->enrollercode;
        }

        if (isset($month_commission)) {
            $month_comm_total = (float) $month_commission->direct
                + (float) $month_commission->downline
                + (float) $month_commission->special
                + (float) $month_commission->association
                + (float) $month_commission->onetime
                + (float) $month_commission->affiliate
                + (float) $month_commission->adjustments_credit
                - (float) $month_commission->adjustment_debit
                - (float) $month_commission->refunds
                + (float) $month_commission->enrollercode;

            $date_last_day_of_month = date("Y-m-t", strtotime("{$year}-{$month}"));
            $month_comm_paid =  CommissionHistory::where([
                ['agentid', $agent_id],
                ['paid_in_month', $date_last_day_of_month]
            ])
                ->pluck('paid_amount')->sum();

            $month_detail = [
                'commission' => CommonHelper::formatDecimalWithoutRounding($month_comm_total, 2),
                'delta' => $this->calculateCommissionDelta(
                    $month_comm_total,
                    isset($prior_month_commission) ? $prior_month_comm_total : 0
                ),
                'paid' => CommonHelper::formatDecimalWithoutRounding($month_comm_paid, 2),
                'unpaid' => CommonHelper::formatDecimalWithoutRounding($month_comm_total - $month_comm_paid, 2),
                'refund' => CommonHelper::formatDecimalWithoutRounding($month_commission->refunds, 2)
            ];
        } else {
            $month_detail = [
                'commission' => null,
                'delta' => null,
                'paid' => null,
                'unpaid' => null,
                'refund' => null
            ];
        }
        return $month_detail;
    }

    public function fetchAgentCommissionDetailForMonthV3($agent_id, $year, $month): array
    {
        $month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $month);
        $prior_month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $month, true);

        if (isset($prior_month_commission)) {
            $prior_month_comm_total = (float) $prior_month_commission->direct
                + (float) $prior_month_commission->downline
                + (float) $prior_month_commission->special
                + (float) $prior_month_commission->association
                + (float) $prior_month_commission->onetime
                + (float) $prior_month_commission->affiliate
                + (float) $prior_month_commission->adjustments_credit
                - (float) $prior_month_commission->adjustment_debit
                - (float) $prior_month_commission->refunds
                + (float) $prior_month_commission->enrollercode;
        }

        if (isset($month_commission)) {
            $comm = $this->getCommissionAmounts($agent_id,$year,$month);
            $total_process = $comm['process'] + $comm['process_other'];
            $month_comm_total = (float) $month_commission->direct
                + (float) $month_commission->downline
                + (float) $month_commission->special
                + (float) $month_commission->association
                + (float) $month_commission->onetime
                + (float) $month_commission->affiliate
                + (float) $month_commission->adjustments_credit
                - (float) $month_commission->adjustment_debit
                - (float) $month_commission->refunds
                + (float) $month_commission->enrollercode;

            $date_last_day_of_month = date("Y-m-t", strtotime("{$year}-{$month}"));
            $month_comm_paid =  CommissionHistory::where([
                ['agentid', $agent_id],
                ['paid_in_month', $date_last_day_of_month]
            ])
                ->pluck('paid_amount')->sum();

            $month_detail = [
                'commission' => CommonHelper::formatDecimalWithoutRounding($month_comm_total, 2),
                'delta' => $this->calculateCommissionDelta(
                    $month_comm_total,
                    isset($prior_month_commission) ? $prior_month_comm_total : 0
                ),
                'premium' => [
                    'paid' => CommonHelper::formatDecimalWithoutRounding($comm['paid'],2),
                    'unpaid' =>  $comm['unpaid'],
                    'process' =>  round($comm['process'],2)
                ],
                'other' => [
                    'paid' => CommonHelper::formatDecimalWithoutRounding($comm['paid_other'],2),
                    'unpaid' => $comm['unpaid_other'],
                    'process' => round($comm['process_other'],2)
                ],
                'total' => [
                    'paid' => CommonHelper::formatDecimalWithoutRounding($month_comm_paid, 2),
                    'unpaid' => CommonHelper::formatDecimalWithoutRounding($month_comm_total - $month_comm_paid, 2),
                    'process' => round($total_process,2),
                ],
                'refund' => CommonHelper::formatDecimalWithoutRounding($month_commission->refunds, 2)
            ];
        } else {
            $month_detail = [
                'commission' => null,
                'delta' => null,
                'premium' => null,
                'other' => null,
                'total' => null,
                'refund' => null
            ];
        }
        return $month_detail;
    }

    /**
     * @param $agent_id
     * @param $year
     * @return array|null
     */
    public function fetchAgentYearlyAdjustment($agent_id, $year): ?array
    {
        $adjustment_per_year = [];
        for ($i = 1; $i <= 12; $i++) {
            $month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $i);
            if (isset($month_commission->adjustments_credit) && $month_commission->adjustments_credit != 0) {
                $adjustment_per_year[$this->months[$i]] = [
                    'type' => 'credit',
                    'amount' => (float) $month_commission->adjustments_credit,
                    'note' => $month_commission->adjustments_credit_notes
                ];
            } elseif (isset($month_commission->adjustment_debit) && $month_commission->adjustment_debit != 0) {
                $adjustment_per_year[$this->months[$i]] = [
                    'type' => 'debit',
                    'amount' => (float) $month_commission->adjustment_debit,
                    'note' => $month_commission->adjustment_debit_notes
                ];
            } else {
                $adjustment_per_year[$this->months[$i]] = null;
            }
        }

        return array_filter($adjustment_per_year) ? $adjustment_per_year : null;
    }

    /**
     * @param $agent_id
     * @param $year
     * @param $month
     * @param string $attachment_type
     * @return array
     */
    public function categorizeAgentYearlyAttachment($agent_id, $year): array
    {
        $attachment_per_cat = [];
        for ($i = 1; $i <= 12; $i++) {
            $summary = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $i);
            if (isset($summary)) {
                foreach (NbCommissionDataSummary::$commission_attachment_category as $each_cat) {
                    $pdf = $each_cat . '_pdf';
                    $csv = $each_cat . '_csv';
                    $attachment_per_cat[$each_cat][$this->months[$i]] = [
                      'pdf' => $summary->$pdf,
                      'csv' => $summary->$csv
                    ];
                }
            } else {
                foreach (NbCommissionDataSummary::$commission_attachment_category as $each_cat) {
                    $attachment_per_cat[$each_cat][$this->months[$i]] = null;
                }
            }
        }
        return $attachment_per_cat;
    }

    /**
     * @param $agent_id
     * @param $year
     * @param $month
     * @param bool $prior_month true to fetch a month-prior commission
     * @return mixed
     */
    protected function fetchAgentCommissionSummaryByDate($agent_id, $year, $month, bool $prior_month = false)
    {
        $month = str_pad($month, 2, '0', STR_PAD_LEFT);
        $yearMonth = "{$year}-{$month}";
        $yearMonth = ($prior_month)
            ? Carbon::parse($yearMonth)->subMonthsNoOverflow()->format('Y-m')
            : $yearMonth;

        return NbCommissionDataSummary::where('agent_id', '=', $agent_id)
            ->where('paid_through_date', 'LIKE', $yearMonth . "%")
            ->first();
    }

    /**
     * @param $current
     * @param $prior
     * @return float|null
     */
    private function calculateCommissionDelta($current, $prior): ?float
    {
        if ($prior != 0) {
            $percentage_change = CommonHelper::formatDecimalWithoutRounding(
                ((($current - $prior) / $prior) * 100), 2
            );
            return ($percentage_change != 0) ? $percentage_change : null;
        } else return null;
    }

    /**
     * @param $agent_id
     * @param $year
     * @return array
     */
    public function categorizeAgentYearlyCommission($agent_id, $year): array
    {
        $commission_per_category = [];
        for ($i = 1; $i <= 12; $i++) {
            $month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $i);
            $prior_month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $i, true);

            if (isset($month_commission)) {
                foreach (NbCommissionDataSummary::$commission_category as $each) {
                    $commission_per_category[$each][$this->months[$i]] =
                        ($month_commission->$each != 0)
                            ? CommonHelper::formatDecimalWithoutRounding($month_commission->$each, 2)
                            : null;
                }
            } else {
                foreach (NbCommissionDataSummary::$commission_category as $each) {
                    $commission_per_category[$each][$this->months[$i]] = null;
                }
            }
        }

        return $commission_per_category;
    }

    /**
     * @param $agent_id
     * @param $year
     * @return array
     */
    public function categorizeAgentYearlyCommissionDelta($agent_id, $year): array
    {
        $delta_per_category = [];
        for ($i = 1; $i <= 12; $i++) {
            $month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $i);
            $prior_month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $i, true);

            if (isset($prior_month_commission)) {
                foreach (NbCommissionDataSummary::$commission_category as $each) {
                    $delta_per_category[$each][$this->months[$i]] = $this->calculateCommissionDelta(
                        isset($month_commission) ? $month_commission->$each : 0,
                        $prior_month_commission->$each
                    );
                }
            } else {
                foreach (NbCommissionDataSummary::$commission_category as $each) {
                    $delta_per_category[$each][$this->months[$i]] = null;
                }
            }
        }

        return $delta_per_category;
    }

    /**
     * @param $agent_id
     * @param $year
     * @return array
     */
    public function categorizeAgentYearlyCommissionDate($agent_id, $year): array
    {
        $date_per_month = [];
        for ($i = 1; $i <= 12; $i++) {
            $month_commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $i, true);

            $date_per_month[$this->months[$i]] = isset($month_commission->created_at)
                ? Carbon::parse($month_commission->created_at)->format('M d')
                : null;
        }

        return $date_per_month;
    }

    /**
     * @param $agent_id
     * @param $year
     * @param $month
     * @return array|null[]
     */
    public function formatAgentYearlyCommissionSummaryForMobile($agent_id, $year, $month): array
    {
        $response = [];

        $year_commission = $this->categorizeAgentYearlyCommission($agent_id, $year);
        $year_delta = $this->categorizeAgentYearlyCommissionDelta($agent_id, $year);
        $year_comm_date = $this->categorizeAgentYearlyCommissionDate($agent_id, $year);
        $year_attachment = $this->categorizeAgentYearlyAttachment($agent_id, $year);

        foreach (NbCommissionDataSummary::$commission_category as $each_cat) {
            foreach ($this->months as $each_month) {
                if (in_array($each_cat, array_keys(NbCommissionDataSummary::$commission_to_attachment_map))) {
                    $response[$each_cat][$each_month] = [
                        'commission' => $year_commission[$each_cat][$each_month],
                        'delta' => $year_delta[$each_cat][$each_month],
                        'date' => $year_comm_date[$each_month],
                        'pdf' =>
                            $year_attachment[NbCommissionDataSummary::$commission_to_attachment_map[$each_cat]][$each_month]['pdf']
                            ?? null,
                        'csv' =>
                            $year_attachment[NbCommissionDataSummary::$commission_to_attachment_map[$each_cat]][$each_month]['csv']
                            ?? null
                    ];
                } else {
                    $response[$each_cat][$each_month] = [
                        'commission' => $year_commission[$each_cat][$each_month],
                        'delta' => $year_delta[$each_cat][$each_month],
                        'date' => $year_comm_date[$each_month]
                    ];
                }
            }
        }

        $response = $this->sanitizeCommissionCategory($response);

        $response['adjustment'] = $this->fetchAgentYearlyAdjustment($agent_id, $year);
        $response['month'] = $this->fetchAgentCommissionDetailForMonth($agent_id, $year, $month);

        return $response;
    }

    public function formatAgentYearlyCommissionSummaryForMobileV3($agent_id, $year, $month): array
    {
        $response = [];

        $year_commission = $this->categorizeAgentYearlyCommission($agent_id, $year);
        $year_delta = $this->categorizeAgentYearlyCommissionDelta($agent_id, $year);
        $year_comm_date = $this->categorizeAgentYearlyCommissionDate($agent_id, $year);
        $year_attachment = $this->categorizeAgentYearlyAttachment($agent_id, $year);

        foreach (NbCommissionDataSummary::$commission_category as $each_cat) {
            foreach ($this->months as $each_month) {
                if (in_array($each_cat, array_keys(NbCommissionDataSummary::$commission_to_attachment_map))) {
                    $response[$each_cat][$each_month] = [
                        'commission' => $year_commission[$each_cat][$each_month],
                        'delta' => $year_delta[$each_cat][$each_month],
                        'date' => $year_comm_date[$each_month],
                        'pdf' =>
                            $year_attachment[NbCommissionDataSummary::$commission_to_attachment_map[$each_cat]][$each_month]['pdf']
                            ?? null,
                        'csv' =>
                            $year_attachment[NbCommissionDataSummary::$commission_to_attachment_map[$each_cat]][$each_month]['csv']
                            ?? null
                    ];
                } else {
                    $response[$each_cat][$each_month] = [
                        'commission' => $year_commission[$each_cat][$each_month],
                        'delta' => $year_delta[$each_cat][$each_month],
                        'date' => $year_comm_date[$each_month]
                    ];
                }
            }
        }

        for ($i = 1; $i <= 12; $i++) {
            $comm = $this->getCommissionAmounts($agent_id,$year,$i);
            $response['premium'][$this->months[$i]] = [
                'paid' => number_format(round($comm['paid'],2),2),
                'unpaid' => number_format($comm['unpaid'],2),
                'process' => number_format(round($comm['process'],2),2)
            ];
            $response['other'][$this->months[$i]] = [
                'paid' => number_format(round($comm['paid_other'],2),2),
                'unpaid' => number_format((float)$comm['unpaid_other'],2),
                'process' => number_format(round($comm['process_other'],2),2)
            ];
        }
        $response = $this->sanitizeCommissionCategory($response);

        $response['adjustment'] = $this->fetchAgentYearlyAdjustment($agent_id, $year);
        $response['month'] = $this->fetchAgentCommissionDetailForMonthV3($agent_id, $year, $month);

        return $response;
    }

    public function getCommissionAmounts($agent_id,$year,$month){
        $pt_date = Carbon::parse($year . '-' . $month)->endOfMonth()->format('Y-m-d');
        $commission = $this->fetchAgentCommissionSummaryByDate($agent_id, $year, $month);
        $adjustment_total = (double)$commission['adjustments_credit'] - ((double)$commission['adjustment_debit'] + (double)$commission['refunds']);
   
        $total_commission       = (double)$commission['direct'] + (double)$commission['downline'] + (double)$commission['enrollercode'] + (double)$adjustment_total;
        $total_commission_other = (double)$commission['special'] + (double)$commission['onetime'] + (double)$commission['affiliate'] +  (double)$commission['association'];
        // Payment History
        $paynote_payment = AgentPNCheckInfo::where(['agent_id' => $agent_id,'pt_date' => $pt_date ])->whereNull('comm_type')->orderBy('id','DESC')->first();
        if(isset($paynote_payment)){
            $paynote_paid       = AgentPNCheckInfo::where(['agent_id' => $agent_id,'pt_date' => $pt_date,'status'=>'processed'])->whereNull('comm_type')->sum('amount');
            $paynote_process    = AgentPNCheckInfo::where(['agent_id' => $agent_id,'pt_date' => $pt_date,'status'=>'pending'])->whereNull('comm_type')->sum('amount');
        }else{
            $paynote_paid = 0;
            $paynote_process = 0;
        }
        $total  = round($total_commission,2);
        $paid   = (double)$paynote_paid;
        $unpaid = $paid!=0?round(($total - $paid),2):round($total,2);
        $process = (double)$paynote_process;

        $paynote_payment_other = AgentPNCheckInfo::where(['agent_id' => $agent_id,'pt_date' => $pt_date,'comm_type'=>'OTHERS'])->orderBy('id','DESC')->first();
        if(isset($paynote_payment_other)){
            $paynote_paid_other     = AgentPNCheckInfo::where(['agent_id' => $agent_id,'pt_date' => $pt_date,'comm_type'=>'OTHERS','status'=>'processed' ])->sum('amount');
            $paynote_process_other  = AgentPNCheckInfo::where(['agent_id' => $agent_id,'pt_date' => $pt_date,'comm_type'=>'OTHERS','status'=>'pending' ])->sum('amount');
        }else{
            $paynote_paid_other     = 0;
            $paynote_process_other  = 0;
        }
        
        $total_other    = (float) round($total_commission_other,2);
        $paid_other     = (double)$paynote_paid_other;
        $unpaid_other   = $paid_other!=0?round($total_other-$paid_other,2):round($total_other,2);
        $process_other  = (double)$paynote_process_other;

        return [
            'paid' => $paid,
            'unpaid' => $unpaid,
            'process' => $process,
            'paid_other' => $paid_other,
            'unpaid_other' => $unpaid_other,
            'process_other' => $process_other
        ];
    }



    /**
     * @param array $data
     * @return array
     * iterates through each_month of each_commission_type
     * if delta and commission (in a month) are empty, assigns null to that month
     * if all months in a commission_type have null, assigns null to that commission_type
     */
    private function sanitizeCommissionCategory(array $data): array
    {
        $data['enroller'] = $data['enrollercode'];
        unset($data['enrollercode']);

        return $data = array_map(function ($each_cat) {
            $has_data = false;

            $each_cat = array_map(function($each_month) {
                if(!empty($each_month['unpaid']) || !empty($each_month['paid']))
                    return $each_month;
                if ( empty($each_month['commission']) && empty($each_month['delta']) )
                    return $each_month = null;

                return $each_month;
            }, $each_cat);

            foreach ($each_cat as $each_month)
                if (! is_null($each_month)) {
                    $has_data = true;
                    break;
                }

            return ($has_data) ? $each_cat : null;
        }, $data);
    }
}