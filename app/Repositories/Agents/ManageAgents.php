<?php

namespace App\Repositories\Agents;

use App\AdminUser;
use App\AgentAchPaymentBankDetail;
use App\AgentGroup;
use App\AgentInfo;
use App\AgentAchpayment;
use App\AgentInGroup;
use App\AgentLicense;
use App\AgentNote;
use App\AgentUplineScheduler;
use App\AgentUser;
use App\CustomeHomepage;
use App\GroupInfo;
use App\GroupUser;
use App\Helpers\GuzzleHelper;
use App\Http\Resources\SuccessResource;
use App\PlanOverview;
use App\Helpers\AgentUpdatesHelper;
use App\Helpers\CommonHelper;
use App\Http\Resources\ErrorResource;
use App\NbCommissionDataSummary;
use App\PolicyNote;
use App\RepContractAct;
use App\Service\CustomValidationService;
use App\Service\AgentService;
use App\Service\MessageService;
use App\SsoUsers;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use App\Traits\SyncRep;
use App\UserActivityDetail;
use App\UserInfoPolicyAddress;
use App\Helpers\EmailSendHelper;
use App\Policy;
use App\RepContractCarrier;
use App\Repositories\Members\ClientDetailUpdate;
use App\RepContractCategories;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\RepContract;
use App\Repositories\V3\Archive\ArchiveRepository;
use App\Traits\ManageAgentTrait;
use Symfony\Component\Console\Helper\Helper;
use Symfony\Component\HttpFoundation\Response;

class ManageAgents extends Model
{
    use Paginator, ResponseMessage, SyncRep, ManageAgentTrait;

    /**
     * @var AgentAchRepository
     */
    private $agentAchRepository;
    private $archiveRepository;

    static $withRelations = [
        'getGroup',
        'getLicense',
        'getTotals',
        'getTotalsNew',
        'getAgentGa',
        'userActivity',
        'agentBusiness'
    ];


    public function __construct(ArchiveRepository $archiveRepository)
    {
        $this->archiveRepository = $archiveRepository;
    }


    public function manageAgents()
    {
        $data = AgentInfo::with('getGroup')
            ->with('getLicense')
            ->with('getTotals')
            ->get();
        return $data;
    }

    public function getAgentDetails($request)
    {
        $details = AgentInfo::where('agent_id', $request->agent_id)
            ->with('getGroup')
            ->with('getLicense')
            ->with('getTotals')
            ->first();
        $bankDetail = '';
        if ($details->agent_payment_method == 'ach') {
            $bankDetail = AgentAchpayment::where('achagent_id', $request->agent_id)
                ->orderBy('achpayment_id', 'desc')
                ->first();
        }
        return ['data' => $details, 'bank' => $bankDetail];
    }

    public function getClientDetails($request)
    {
        $data = UserInfoPolicyAddress::where('agent_id', $request->agent_id)
            ->with('getGroup')->with('policyUpdates')
            ->with('plans')->with('policy')->with('creditCardInfo')
            ->with('recurringCreditCardInfo')->with('eftInfo')->with('recurringEftInfo')
            ->get();
        return $data;
    }

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->orderBy('agent_info.agent_id', 'desc')
            ->groupBy('agent_info.agent_id')
            ->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = AgentInfo::query()
            ->with(self::$withRelations);
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {

        if(isset($filters['plan_ids']) && count($filters['plan_ids'])){
            $data->Join('userinfo_policy_address', function ($join) {
                $join->on('agent_info.agent_id', '=', 'userinfo_policy_address.agent_id')
                    ->select('userinfo_policy_address.policy_id');
            })->join('plan_overview', function ($join) use($filters) {
                $join->on('userinfo_policy_address.policy_id', '=', 'plan_overview.policy_id')
                ->whereIn('plan_overview.pid', $filters['plan_ids']);
            });
        }

        if (isset($filters['agent_signup_date'])) {
            $data->where('agent_signup_date', '=', $filters['agent_signup_date']);
        }

        if (isset($filters['business_name'])) {
            $data->whereHas('agentBusiness', function ($q) use ($filters) {
                $q->where('business_name', 'LIKE', '%' . $filters['business_name'] . '%');
            });
        }


        if (isset($filters['agent_status'])) {
            // filter Disabled and suspended
            if ($filters['agent_status'] == 'DS') {
                $data->whereIn('agent_status', ['D', 'S']);
            } else if ($filters['agent_status'] == 'all') {
                $data->withTrashed();
            } elseif($filters['agent_status'] == 'R'){
                $data->onlyTrashed();
            } else {
                $data->where('agent_status', $filters['agent_status']);
            }
        } else {
            $data->withTrashed();
        }

        if (isset($filters['agent_code'])) {
                $data->where('agent_code', '=', $filters['agent_code']);
        }

        if (isset($filters['agent_email'])) {
            $data->where('agent_email', 'like', '%' . $filters['agent_email']. '%');
        }

        if (isset($filters['agent_phone1'])) {
            $data->where('agent_phone1', '=', $filters['agent_phone1']);
        }

        if (isset($filters['state'])) {
            $data->where('agent_state', '=', $filters['state']);
        }

        if (isset($filters['agent_level'])) {
            $data->where('agent_level', '=', $filters['agent_level']);
        }

        if (isset($filters['agent_ga'])) {
            $data->whereHas('getAgentGa', function ($q) use ($filters) {
                $q->where('agent_code', '=', $filters['agent_ga']);
            });
        }

        if (isset($filters['agent_fname'])) {
            $data->where('agent_fname', 'LIKE', '%' . $filters['agent_fname'] . '%');
        }

        if (isset($filters['agent_mname'])) {
            $data->where('agent_mname', 'LIKE', '%' . $filters['agent_mname'] . '%');
        }

        if (isset($filters['agent_lname'])) {
            $data->where('agent_lname', 'LIKE', '%' . $filters['agent_lname'] . '%');
        }

        if (isset($filters['agent_name'])) {
            $concat = $this->concatFullname('agent_fname', 'agent_mname', 'agent_lname');
            $data->where(DB::raw($concat), 'LIKE', '%' . $filters['agent_name'] . '%');
        }

        if (isset($filters['signup_date_from']) || isset($filters['signup_date_to'])) {
            $fromDate = isset($filters['signup_date_from']) ? strtotime($filters['signup_date_from']) : $this->getFirstSignupDate();
            $toDate = isset($filters['signup_date_to']) ? strtotime($filters['signup_date_to']) : Carbon::now()->unix();
            $nextDate = Carbon::parse($toDate)->addDay()->unix();
            $data->whereBetween('agent_signup_date', [$fromDate, $nextDate]);
        }

        if (isset($filters['aIds'])) {
            $data->whereIn('agent_id', $filters['aIds']);
        }

        // filter age
        if (isset($filters['from_age'])) {
            $from_age = $filters['from_age'];
            if (isset($filters['to_age'])) {
                $to_age = $filters['to_age'];
                $data->whereRaw("TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) >= $from_age AND TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) <= $to_age");
            } else {
                $data->whereRaw("TIMESTAMPDIFF(YEAR, agent_dob, CURDATE()) > $from_age");
            }
        }

        // filter state
        if (isset($filters['agent_state'])) {
            $data->where('agent_state', $filters['agent_state']);
        }

        // filter app
        if (isset($filters['mobile_app_device']) and in_array($filters['mobile_app_device'], AgentInfo::$mobileAppDevices)) {
            $data->whereHas('userActivity', function ($query) use ($filters) {
                $query->where([
                    ['app', 1],
                    ['device', $filters['mobile_app_device']]
                ]);
            });
        }

        // filter dashboard_access
        if (isset($filters['dashboard_access'])) {
            $dashboardAccess = filter_var($filters['dashboard_access'], FILTER_VALIDATE_BOOLEAN);
            if ($dashboardAccess == true) {
                $data->where('agent_status', '=', AgentInfo::STATUS_APPROVED)
                    ->whereHas('userActivity', function ($query) {
                        $query->where('web', '=', 1);
                    });
            } else {
                $data
                    ->where('agent_status', '=', AgentInfo::STATUS_APPROVED)
                    ->whereDoesntHave('userActivity')
                    ->orWhereHas('userActivity', function ($query) {
                        $query->where('web', '=', 0);
                    });
            }
        }

        //filter website
        if (isset($filters['website'])) {
            $data->where('weburl', $filters['website']);
        }

        //filter contract sign
        if (isset($filters['contract_sign'])) {
            $contract_sign = filter_var($filters['contract_sign'], FILTER_VALIDATE_BOOLEAN);
            if ($contract_sign == true) {
                $data->where('contract_status', '=', AgentInfo::CONTRACT_SIGNED);
            } else {
                $data->where('contract_status', '=', null)
                    ->orWhere('contract_status', '=', AgentInfo::CONTRACT_NOT_SIGNED);
            }
        }

        // filter member
        if (isset($filters['member_range_from']) || isset($filters['member_range_to'])) {
            if ($filters['member_range_from'] == 0 && $filters['member_range_to'] == 0) {
                $data->whereDoesntHave('getTotals');
            } else {
                $data->whereHas('getTotals', function ($query) use ($filters) {
                    if (isset($filters['member_range_from']) && isset($filters['member_range_to'])) {
                        $query->havingRaw("SUM(totals) BETWEEN {$filters['member_range_from']} AND {$filters['member_range_to']}");
                    } else if (isset($filters['member_range_from'])) {
                        $query->havingRaw("SUM(totals) > {$filters['member_range_from']}");
                    } else if (isset($filters['member_range_to'])) {
                        $query->havingRaw("SUM(totals) < {$filters['member_range_to']}");
                    }
                });
            }
        }

        // filter product
        if (isset($filters['product'])) {
            $agentIds = $this->getAgentIdsFromProduct($filters['product']);
            $data->whereIn('agent_id', $agentIds);
        }

        // filter group
        if (isset($filters['group_count_from']) || isset($filters['group_count_to'])) {
            if ($filters['group_count_from'] == 0 && $filters['group_count_from'] == 0) {
                $data->whereDoesntHave('getGroup');
            } else {
                $data->whereHas('getGroup', function ($query) use ($filters) {
                    $query->groupBy('agent_id');
                    if (isset($filters['group_count_from']) && isset($filters['group_count_to'])) {
                        $query->havingRaw("COUNT(*) BETWEEN {$filters['group_count_from']} AND {$filters['group_count_to']}");
                    } else if (isset($filters['group_count_from'])) {
                        $query->havingRaw("COUNT(*) > {$filters['group_count_from']}");
                    } else if (isset($filters['group_count_to'])) {
                        $query->havingRaw("COUNT(*) < {$filters['group_count_to']}");
                    }
                });
            }
        }

        if (isset($filters['active_member_cases'])) {
            $activeMemberCasesFilter = $filters['active_member_cases'];
            switch ($activeMemberCasesFilter) {
                case '0':
                    $data->whereDoesntHave('getTotalsNew');
                    break;
                case '9':
                    $data->whereHas('getTotalsNew', function ($query) {
                        $query->where('status', 'ACTIVE')->havingRaw("SUM(totals) BETWEEN 1 AND 9");
                    });
                    break;
                case '10':
                    $data->whereHas('getTotalsNew', function ($query) {
                        $query->where('status', 'ACTIVE')->havingRaw("SUM(totals) > 9");
                    });
                    break;
            }
        }

        if (isset($filters['group_ids'])) {
            $data->whereHas('getGroup', function ($q) use ($filters) {
                $q->whereIn('gid', $filters['group_ids']);
            });
        }

        if (isset($filters['agent_ssn']) && $filters['agent_ssn'] != '') {
            // if the SSN is 4 digits, match the last 4 digits
            if(strlen($filters['agent_ssn']) == 4){
                $data->whereRaw('RIGHT(agent_info.agent_ssn, 4) = ?', [$filters['agent_ssn']]);
            } elseif(strlen($filters['agent_ssn']) == 9){
                // if the SSN is 9 digits, match the full SSN
                $data->whereRaw('agent_info.agent_ssn = ?', [$filters['agent_ssn']]);
            }
        }

        if(isset($filters['agent_ein']) && $filters['agent_ein'] != '') {
            $data->whereRaw('agent_info.tin = ?', [$filters['agent_ein']]); // EIN and TIN are same thing
        }
    }

    protected function concatFullname($firstName, $middleName, $lastName)
    {
        $sqlQuery = "CONCAT_WS(' ',";
        $sqlQuery .= "CASE $firstName WHEN '' THEN NULL ELSE $firstName END,";
        $sqlQuery .= "CASE $middleName WHEN '' THEN NULL ELSE $middleName END,";
        $sqlQuery .= "CASE $lastName  WHEN '' THEN NULL ELSE $lastName END)";
        return $sqlQuery;
    }

    public function paginatedFormattedList($limit, $filters = [])
    {
        if (isset($filters['agent_ssn'])){
            if(!is_numeric($filters['agent_ssn']) || !(strlen($filters['agent_ssn']) == 9 || strlen($filters['agent_ssn']) == 4)){
                throw new Exception('Invalid SSN format', Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }

        if (isset($filters['agent_ein'])){
            if(!is_numeric($filters['agent_ein']) || strlen($filters['agent_ein']) != 9){
                throw new Exception('Invalid EIN format', Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }

        $data = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($data);
    }

    public function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    protected function uplineData($d)
    {
        return [
            'agent_id' => $d->agent_id,
            'agent_ga' => $d->agent_ga,
            'agent_code' => $d->agent_code,
            'agent_fname' => $d->agent_fname,
            'agent_mname' => $d->agent_mname,
            'agent_lname' => $d->agent_lname,
            'agent_level' => $d->agent_level,
            'agent_fullname' => $d->fullname,
            'agent_email' => $d->agent_email,
            'agent_status' => $d->agent_status,
            'agent_phone1' => $d->agent_phone1,
            'agent_phone2' => $d->agent_phone2,
        ];
    }

    protected function licenseData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result[] = [
                "license_id" => $d->license_id,
                "license_agent_id" => $d->license_agent_id ?: "N/A",
                "license_state" => $d->license_state ?: "N/A",
                "license_number" => $d->license_number ?: "N/A",
                "license_exp_date" => $this->filterDate($d->license_exp_date),
                "license_status" => $d->license_status,
                "formatted_license_status" => isset($d->license_status) ? array_search($d->license_status, AgentLicense::$statuses) : null,
                "license_resident" => $d->license_resident,
                "license_added_date" => $this->filterDate($d->license_added_date),
                "license_deleted" => $d->license_deleted
            ];
        }
        return $result;
    }

    protected function getTotalData($data)
    {
        [$values, $total] = [[], 0];
        foreach ($data as $d) {
            $values[strtolower($d->status)] = $d->totals ?: 0;
            $total += $d->totals;
        }
        return [
            'data' => $data,
            'values' => $values,
            'total' => $total,
        ];
    }

    protected function singleFormattedItem($d)
    {

        ini_set('max_execution_time', 500);
        $totals = isset($d->getTotalsNew) ? $this->getTotalData($d->getTotalsNew) : null;
        $licenses = isset($d->getLicense) ? $this->licenseData($d->getLicense) : null;
        $groups = isset($d->getGroup) ? $d->getGroup : null;
        $upline = isset($d->getAgentGa) ? $this->uplineData($d->getAgentGa) : null;
        $downlineCheck = $this->getDownlineAgentsExists($d->agent_id);
        $benefitStoreUrl = config('app.benefit_store_url') . "agent-home?agent_id=" . base64_encode($d->agent_id);
        $contracts = $this->getSignedContractLevels($d->agent_id, 'list');
        $checkContract = $this->checkContract($d->agent_id);
        $customValidationService = new CustomValidationService();
        $isSwitchable = $d->agent_status === 'A' && $this->isSwitchable($d->agent_email);
        return [
            'agent_id' => $d->agent_id,
            'agent_code' => $d->agent_code,
            'agent_fname' => $d->agent_fname,
            'agent_mname' => $d->agent_mname,
            'agent_lname' => $d->agent_lname,
            'agent_fullname' => $d->fullname,
            'agent_email' => $d->agent_email,
            'agent_ga' => $d->agent_ga,
            'agent_ga_effective_date' => $d->agent_ga_effective_date
                ? $d->agent_ga_effective_date->format('m/d/Y')
                : null,
            'agent_signup_date' => Carbon::createFromTimestamp($d->agent_signup_date)->format('m/d/Y'),
            'agent_level' => $d->agent_level,
            'agent_level_2' => $d->agent_level_2,
            'agent_status' => $d->agent_status,
            'formatted_agent_status' => isset($d->agent_status) ? array_search($d->agent_status, AgentInfo::$statuses) : null,
            'agent_phone1' => $d->agent_phone1,
            'agent_phone2' => $d->agent_phone2,
            'admin_only' => $d->admin_only,
            'agent_image' => $d->full_image,
            'downline_exists' => $downlineCheck,
            'benefit_store_url' => $benefitStoreUrl,
            'is_email_valid' => $customValidationService->validateEmailNeverBounce($d->agent_email),
            'is_phone1_valid' => $customValidationService->validatePhoneNumVerify($d->agent_phone1),
            'is_phone2_valid' => $customValidationService->validatePhoneNumVerify($d->agent_phone2),
            'upline' => $upline,
            'groups' => $groups,
            'licenses' => $licenses,
            'totals' => $totals,
            'hasContracts' => $checkContract,
            'contracts' => $contracts,
            'note_icon_color' => $this->getLatestNoteColor($d->agent_id),
            'only_bex_contract' => $this->isBex($d->agent_id),
            'agent_ssn' => $d->agent_ssn,
            'isSwitchable' => $isSwitchable,
            'agentBusiness'=>$d->agentBusiness
        ];
    }


    public function getContractActions($agentId)
    {
        $data = RepContractAct::query()
            ->where('aid', '=', $agentId)
            ->orderBy('rcid', 'DESC')
            ->get();
        $result = [];
        foreach ($data as $d) {
            $d->contracts = strtr($d->contracts, array(
                'FEGLI ANCILLARY' => 'GPBP(FEGLI)',
                'fegliAncillary' => 'GPBP(FEGLI)',
                'patriotAncillary' => 'PATRIOT MEDICAL',
                'PEC' => 'EWA',
                'premier' => 'EWA',
                'ancillary' => 'SMART',
                'PATRIOT ANCILLARY' => 'PATRIOT MEDICAL',
                'ALC' => 'SMART',
                'categoryL7' => 'Category Smart-Ancillary',
                'categoryL9' => 'Patriot Medical - Multi Level Contract',
                'carrier' => 'Carrier',
            ));
            $timestamp = Carbon::createFromTimestamp($d->ts)->format('M-jS-Y H:i:s');
            $collection = collect($d);
            $collection->put('username', $this->getUserName($d));
            $collection->put('timestamp', $timestamp);
            $result[] = $collection->all();
        }
        return $result;
    }

    public function getRepContractAct($agentId){
        return RepContractAct::query()
        ->where('aid', '=', $agentId)
        ->where('action', '=', 'contracts sent')
        ->orderBy('rcid', 'DESC')
        ->pluck('contracts')
        ->first();
    }

    public function getContractLevels($agentId)
    {
        $data = RepContractAct::query()
            ->where('aid', '=', $agentId)
            ->where('action', '=', 'contracts sent')
            ->orderBy('rcid', 'DESC')
            ->select('contracts','contract_type')
            ->first();
            [$alc, $bex, $prem, $Pbex, $fegliAncillary, $fegliPremier, $patriotAncillary, $patriotPremier,
            $medical, $dental, $vision, $termLife, $Bundles, $limitedMedical, $accident, $critical, $hospital, $lifeStyle, $pet, $guaranteedIssue, $teleMedicine,
            $medicalL7, $dentalL7, $visionL7, $dentalSolsticeL7, $visionSolsticeL7, $termLifeL7, $BundlesL7, $limitedMedicalL7, $accidentL7, $criticalL7, $hospitalL7, $lifeStyleL7, $petL7, $guaranteedIssueL7, $teleMedicineL7,
            $local713,$cigna,$l713Anthem,$northwellAnthem,$IHAHealth,$ENANAWU,$lifelineMedical,$solsticeBenefits,$optionsPlus,$metropolitanLifeInsuranceCompany,$pinnacleBenefitsService,$prudential,$beyondMedical] =
               [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
               if($data){
            $contractLevels = explode('-', $data->contracts);

            foreach ($contractLevels as $c) {
                if (str_contains($c, 'ALC') == true) {
                    $alc = substr($c, 4, 1);
                }elseif (str_contains($c, 'BEX ANCILLARY') == true) {
                    if($this->isBex($agentId)){
                        $alc = substr($c, 14, 1);
                    }
                    $bex = substr($c, 14, 1);
                }elseif (str_contains($c, 'BEX') == true) {
                    if($this->isBex($agentId)){
                        $alc = substr($c, 4, 1);
                    }
                    $bex = substr($c, 4, 1);
                }elseif (str_contains($c, 'PEC') == true) {
                    $prem = substr($c, 4, 1);
                }
                if (str_contains($c, 'BEX PREMIER')) {
                    if($this->isBex($agentId)){
                        $prem = substr($c, 12, 1);
                    }
                        $Pbex = substr($c, 12, 1);
                }
                if (str_contains($c, 'FEGLI ANCILLARY')) {
                    $fegliAncillary = substr($c, 16, 1);
                }
                if (str_contains($c, 'FEGLI PREMIER')) {
                    $fegliPremier = substr($c, 14, 1);
                }
                if (str_contains($c, 'PATRIOT ANCILLARY')) {
                    $patriotAncillary = substr($c, 18, 1);
                }
                if (str_contains($c, 'PATRIOT PREMIER')) {
                    $patriotPremier = substr($c, 16, 1);
                }
                if (str_contains($c, 'LIMITED MEDICAL L7')) {
                    $limitedMedicalL7 = substr($c, 19, 1);
                }
                else if (str_contains($c, 'LIMITED MEDICAL')) {
                    $limitedMedical = substr($c, 16, 1);
                }
                else if (str_contains($c, 'MEDICAL L7')) {
                    $medicalL7 = substr($c, 11, 1);
                }
                else if (str_contains($c, 'MEDICAL')) {
                    $medical = substr($c, 8, 1);
                }
                if (str_contains($c, 'DENTAL L7')) {
                    $dentalL7 = substr($c, 10, 1);
                }
                else if (str_contains($c, 'DENTAL SOLSTICE L7')) {
                    $dentalSolsticeL7 = substr($c, 19, 1);
                }
                else if (str_contains($c, 'DENTAL')) {
                    $dental = substr($c, 7, 1);
                }
                if (str_contains($c, 'VISION L7')) {
                    $visionL7 = substr($c, 10, 1);
                }
                else if (str_contains($c, 'VISION SOLSTICE L7')) {
                    $visionSolsticeL7 = substr($c, 19, 1);
                }
                else if (str_contains($c, 'VISION')) {
                    $vision = substr($c, 7, 1);
                }
                if (str_contains($c, 'TERM LIFE L7')) {
                    $termLifeL7 = substr($c, 13, 1);
                }
                else if (str_contains($c, 'TERM LIFE')) {
                    $termLife = substr($c, 10, 1);
                }
                if (str_contains($c, 'BUNDLES L7')) {
                    $BundlesL7 = substr($c, 11, 1);
                }
                else if (str_contains($c, 'BUNDLES')) {
                    $Bundles = substr($c, 8, 1);
                }
                if (str_contains($c, 'ACCIDENT L7')) {
                    $accidentL7 = substr($c, 12, 1);
                }
                else if (str_contains($c, 'ACCIDENT')) {
                    $accident = substr($c, 9, 1);
                }
                if (str_contains($c, 'CRITICAL L7')) {
                    $criticalL7 = substr($c, 12, 1);
                }
                else if (str_contains($c, 'CRITICAL')) {
                    $critical = substr($c, 9, 1);
                }
                if (str_contains($c, 'HOSPITAL L7')) {
                    $hospitalL7 = substr($c, 12, 1);
                }
                else if (str_contains($c, 'HOSPITAL')) {
                    $hospital = substr($c, 9, 1);
                }
                if (str_contains($c, 'LIFESTYLE L7')) {
                    $lifeStyleL7 = substr($c, 13, 1);
                }
                else if (str_contains($c, 'LIFESTYLE')) {
                    $lifeStyle = substr($c, 10, 1);
                }
                if (str_contains($c, 'PET L7')) {
                    $petL7 = substr($c, 7, 1);
                }
                else if (str_contains($c, 'PET')) {
                    $pet = substr($c, 4, 1);
                }
                if (str_contains($c, 'GUARANTEED ISSUE L7')) {
                    $guaranteedIssueL7 = substr($c, 20, 1);
                }
                else if (str_contains($c, 'GUARANTEED ISSUE')) {
                    $guaranteedIssue = substr($c, 17, 1);
                }
                if (str_contains($c, 'TELEMEDICINE L7')) {
                    $teleMedicineL7 = substr($c, 16, 1);
                }
                else if (str_contains($c, 'TELEMEDICINE')) {
                    $teleMedicine = substr($c, 13, 1);
                }
                if (str_contains($c, 'LOCAL713')) {
                    $local713 = substr($c, 9, 1);
                }
                if (str_contains($c, 'CIGNA')) {
                    $cigna = substr($c, 6, 1);
                }
                if (str_contains($c, 'L713 ANTHEM')) {
                    $l713Anthem = substr($c, 12, 1);
                }
                if (str_contains($c, 'NORTHWELL ANTHEM')) {
                    $northwellAnthem = substr($c, 17, 1);
                }
                if (str_contains($c, 'IHAHEALTH')) {
                    $IHAHealth = substr($c, 10, 1);
                }
                if (str_contains($c, 'ENANAWU')) {
                    $ENANAWU = substr($c, 8, 1);
                }
                if (str_contains($c, 'LIFELINE MEDICAL')) {
                    $lifelineMedical = substr($c, 17, 1);
                }
                if (str_contains($c, 'SOLSTICE BENEFITS')) {
                    $solsticeBenefits = substr($c, 18, 1);
                }
                if (str_contains($c, 'OPTIONPLUS')) {
                    $optionsPlus = substr($c, 11, 1);
                }
                if (str_contains($c, 'METROPOLITAN LIFE INSURANCE COMPANY')) {
                    $metropolitanLifeInsuranceCompany = substr($c, 36, 1);
                }
                if (str_contains($c, 'PINNACLE BENEFITS SERVICE')) {
                    $pinnacleBenefitsService = substr($c, 26, 1);
                }
                if (str_contains($c, 'PRUDENTIAL')) {
                    $prudential = substr($c, 11, 1);
                }
                if (str_contains($c, 'BEYOND MEDICAL')) {
                    $beyondMedical = substr($c, 15, 1);
                }
            }
        }

        return [
            'alc' => (int)$alc,
            'Pbex' => (int)$Pbex,
            'Abex' => (int)$bex,
            'pec' => (int)$prem,
            'fegliAncillary' => (int) $fegliAncillary,
            'fegliPremier' => (int) $fegliPremier,
            'patriotAncillary' => (int) $this->calculatePatriotAncillary($patriotAncillary , $patriotPremier),
            'med' => (int)$medical,
            'dent' => (int)$dental,
            'vision' => (int)$vision,
            'term_life' => (int)$termLife,
            'bundled' => (int)$Bundles,
            'lim_med' => (int)$limitedMedical,
            'accident' => (int)$accident,
            'critical' => (int)$critical,
            'hospital' => (int)$hospital,
            'life_style' => (int)$lifeStyle,
            'pet' => (int)$pet,
            'guarn_issue' => (int)$guaranteedIssue,
            'tele_med' => (int)$teleMedicine,
            'local713' => (int)$local713,
            'cigna' => (int)$cigna,
            'l713Anthem' => (int)$l713Anthem,
            'northwellAnthem' => (int)$northwellAnthem,
            'IHAHealth' => (int)$IHAHealth,
            'ENANAWU' => (int)$ENANAWU,
            'lifelineMedical' => (int)$lifelineMedical,
            'solsticeBenefits' => (int)$solsticeBenefits,
            'optionsPlus' => (int)$optionsPlus,
            'metropolitanLifeInsuranceCompany' => (int)$metropolitanLifeInsuranceCompany,
            'pinnacleBenefitsService' => (int)$pinnacleBenefitsService,
            'prudential' => (int)$prudential,
            'beyondMedical' => (int)$beyondMedical,
            'medL7' => (int)$medicalL7,
            'dentL7' => (int)$dentalL7,
            'dentSolsticeL7' => (int)$dentalSolsticeL7,
            'visionL7' => (int)$visionL7,
            'visionSolsticeL7' => (int)$visionSolsticeL7,
            'term_lifeL7' => (int)$termLifeL7,
            'bundledL7' => (int)$BundlesL7,
            'lim_medL7' => (int)$limitedMedicalL7,
            'accidentL7' => (int)$accidentL7,
            'criticalL7' => (int)$criticalL7,
            'hospitalL7' => (int)$hospitalL7,
            'life_styleL7' => (int)$lifeStyleL7,
            'petL7' => (int)$petL7,
            'guarn_issueL7' => (int)$guaranteedIssueL7,
            'tele_medL7' => (int)$teleMedicineL7,
            'contract_type' => isset($data->contract_type) && $data->contract_type ? $data->contract_type : 'N-CatContrs'
        ];
    }

    public function calculatePatriotAncillary(int $patriotAncillary , int $patriotPremier) :int
    {
        if ($patriotAncillary >= $patriotPremier) {
            return $patriotAncillary;
        } else {
            return $patriotPremier;
        }
    }

    protected function getFirstSignupDate()
    {
        return (int)AgentInfo::query()
                ->whereNotNull('agent_signup_date')
                ->orderBy('agent_signup_date', 'ASC')->first()->agent_signup_date
            ?? strtotime(Carbon::create('1990')->format('Y-m-d'));
    }

    public function sendContractEmail($request)
    {
        $agentInfo = AgentInfo::query()
            ->where('agent_id', '=', $request->agent_id)
            ->select('agent_email', 'agent_level', 'agent_fname', 'agent_mname', 'agent_lname')
            ->first();
        if (!$agentInfo) return $this->failedResponse('No Agent found');

        DB::beginTransaction();
        try {
            $id = base64_encode($request->agent_id);
            $contractEmail = '';
            $corenrollUrl = config('corenroll.corenroll_dashboard_url');
            $messageService = new MessageService();

            if($request->contract_type === 'N-CatContrs'){
                $level = $request->agent_level_alc;
                $levelPre = $request->agent_level_pec;
                $levelPreBex = $request->agent_level_pec_bex;
                $levelAlcBex = $request->agent_level_alc_bex;
                $levelFegliAncillary = $request->agent_level_fegli_ancillary;
                $levelPatriotAncillary = $request->agent_level_patriot_ancillary;
                if($level != 0){
                    $type = 'smart-Ancillary';
                    $url['level'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $level . '&id=' . $id. '&contract_type=nonCategory');
                    $url['levelNum'] = $level;
                    $contracAc = 'ALC ' . $level;
                    $contractEmail .= "<a href=" . $url['level'] . ">Smart - Ancillary Contract " . $url['levelNum'] . "</a><br/>";
                }

                if($levelPre != 0){
                    $type = 'eWAContract(UnionOnly)';
                    $url['levelPre'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelPre . '&id=' . $id. '&contract_type=nonCategory');
                    $url['levelPreNum'] = $levelPre;
                    $contracPre = 'PEC ' . $levelPre;
                    $contractEmail .= "<a href=" . $url['levelPre'] . ">EWA Contract (Union Only) " . $url['levelPreNum'] . "</a><br/>";
                }

                if ($levelPreBex != 0) {
                    $type = 'bexPremier';
                    $url['levelPreBex'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelPreBex . '&id=' . $id. '&contract_type=nonCategory');
                    $url['levelPreBexNum'] = $levelPreBex;
                    $contracPreBex = 'BEX PREMIER ' . $levelPreBex;
                    $contractEmail .= "<a href=" . $url['levelPreBex'] . ">BEX Premier Level " . $url['levelPreBexNum'] . "</a><br/>";
                }

                if ($levelAlcBex != 0) {
                    $type = 'bexAncillary';
                    $url['levelAlcBex'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelAlcBex . '&id=' . $id. '&contract_type=nonCategory');
                    $url['levelAlcBexNum'] = $levelAlcBex;
                    $contracBex = 'BEX ANCILLARY ' . $levelAlcBex;
                    $contractEmail .= "<a href=" . $url['levelAlcBex'] . ">BEX Ancillary Level " . $url['levelAlcBexNum'] . "</a><br/>";
                }

                if ($levelFegliAncillary != 0) {
                    $type = 'gPBP(FEGLI)';
                    $url['levelFegliAncillary'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelFegliAncillary . '&id=' . $id. '&contract_type=nonCategory');
                    $url['levelFegliAncillaryNum'] = $levelFegliAncillary;
                    $contracFegliAncillary = 'FEGLI ANCILLARY ' . $levelFegliAncillary;
                    $contractEmail .= "<a href=" . $url['levelFegliAncillary'] . ">GPBP (FEGLI) Contract Level " . $url['levelFegliAncillaryNum'] . "</a><br/>";
                }


                if ($levelPatriotAncillary != 0) {
                    $type = 'patriotAncillary';
                    $url['levelPatriotAncillary'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelPatriotAncillary . '&id=' . $id. '&contract_type=nonCategory');
                    $url['levelPatriotAncillaryNum'] = $levelPatriotAncillary;
                    $contracPatriotAncillary = 'PATRIOT ANCILLARY ' . $levelPatriotAncillary;
                    $contractEmail .= "<a href=" . $url['levelPatriotAncillary'] . ">Patriot Ancillary Level " . $url['levelPatriotAncillaryNum'] . "</a><br/>";
                }
            }
            else if($request->contract_type === 'catContrs'){
                $levelMed = $request->agent_level_med;
                $levelDent = $request->agent_level_dent;
                $levelVision = $request->agent_level_vision;
                $levelTermLife = $request->agent_level_term_life;
                $levelBundled = $request->agent_level_bundled;
                $levelLimMed = $request->agent_level_lim_med;
                $levelAccident = $request->agent_level_accident;
                $levelCritical = $request->agent_level_critical;
                $levelHospital = $request->agent_level_hospital;
                $levelLifeStyle = $request->agent_level_life_style;
                $levelPet = $request->agent_level_pet;
                $levelGuarnIssue = $request->agent_level_guarn_issue;
                $levelTeleMed = $request->agent_level_tele_med;

                if ($levelMed != 0) {
                    $type = 'medical';
                    $url['levelMedical'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelMed . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelMedicalNum'] = $levelMed;
                    $contracMedical = 'MEDICAL ' . $levelMed;
                    $contractEmail .= "<a href=" . $url['levelMedical'] . ">Medical Level " . $url['levelMedicalNum'] . "</a><br/>";
                }

                if ($levelDent != 0) {
                    $type = 'dental';
                    $url['levelDental'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelDent . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelDentalNum'] = $levelDent;
                    $contracDental = 'DENTAL ' . $levelDent;
                    $contractEmail .= "<a href=" . $url['levelDental'] . ">Dental Level " . $url['levelDentalNum'] . "</a><br/>";
                }

                if ($levelVision != 0) {
                    $type = 'vision';
                    $url['levelVision'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelVision . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelVisionNum'] = $levelVision;
                    $contracVision = 'VISION ' . $levelVision;
                    $contractEmail .= "<a href=\"" . $url['levelVision'] . "\">Vision Level " . $url['levelVisionNum'] . "</a><br/>";
                }

                if ($levelTermLife != 0) {
                    $type = 'termLife';
                    $url['levelTermLife'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelTermLife . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelTermLifeNum'] = $levelTermLife;
                    $contracTermLife = 'TERM LIFE ' . $levelTermLife;
                    $contractEmail .= "<a href=\"" . $url['levelTermLife'] . "\">Term Life Level " . $url['levelTermLifeNum'] . "</a><br/>";
                }

                if ($levelBundled != 0) {
                    $type = 'bundled';
                    $url['levelBundled'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelBundled . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelBundledNum'] = $levelBundled;
                    $contracBundles = 'BUNDLES ' . $levelBundled;
                    $contractEmail .= "<a href=\"" . $url['levelBundled'] . "\">Bundled Level " . $url['levelBundledNum'] . "</a><br/>";
                }

                if ($levelLimMed != 0) {
                    $type = 'limitedMedical';
                    $url['levelLimMed'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelLimMed . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelLimMedNum'] = $levelLimMed;
                    $contracLimitedMedical = 'LIMITED MEDICAL ' . $levelLimMed;
                    $contractEmail .= "<a href=\"" . $url['levelLimMed'] . "\">Limited Medical Level " . $url['levelLimMedNum'] . "</a><br/>";
                }

                if ($levelAccident != 0) {
                    $type = 'accident';
                    $url['levelAccident'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelAccident . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelAccidentNum'] = $levelAccident;
                    $contracAccident = 'ACCIDENT ' . $levelAccident;
                    $contractEmail .= "<a href=\"" . $url['levelAccident'] . "\">Accident Level " . $url['levelAccidentNum'] . "</a><br/>";
                }

                if ($levelCritical != 0) {
                    $type = 'critical';
                    $url['levelCritical'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelCritical . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelCriticalNum'] = $levelCritical;
                    $contracCritical = 'CRITICAL ' . $levelCritical;
                    $contractEmail .= "<a href=\"" . $url['levelCritical'] . "\">Critical Level " . $url['levelCriticalNum'] . "</a><br/>";
                }
                if ($levelHospital != 0) {
                    $type = 'hospital';
                    $url['levelHospital'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelHospital . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelHospitalNum'] = $levelHospital;
                    $contracHospital = 'HOSPITAL ' . $levelHospital;
                    $contractEmail .= "<a href=\"" . $url['levelHospital'] . "\">Hospital Level " . $url['levelHospitalNum'] . "</a><br/>";
                }

                if ($levelLifeStyle != 0) {
                    $type = 'lifeStyle';
                    $url['levelLifeStyle'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelLifeStyle . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelLifeStyleNum'] = $levelLifeStyle;
                    $contracLifeStyle = 'LIFESTYLE ' . $levelLifeStyle;
                    $contractEmail .= "<a href=\"" . $url['levelLifeStyle'] . "\">Lifestyle Level " . $url['levelLifeStyleNum'] . "</a><br/>";
                }

                if ($levelPet != 0) {
                    $type = 'pet';
                    $url['levelPet'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelPet . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelPetNum'] = $levelPet;
                    $contracPet = 'PET ' . $levelPet;
                    $contractEmail .= "<a href=\"" . $url['levelPet'] . "\">Pet Level " . $url['levelPetNum'] . "</a><br/>";
                }

                if ($levelGuarnIssue != 0) {
                    $type = 'guaranteedIssue';
                    $url['levelGuarnIssue'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelGuarnIssue . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelGuarnIssueNum'] = $levelGuarnIssue;
                    $contracGuaranteedIssue = 'GUARANTEED ISSUE ' . $levelGuarnIssue;
                    $contractEmail .= "<a href=\"" . $url['levelGuarnIssue'] . "\">Guaranteed Issue Level " . $url['levelGuarnIssueNum'] . "</a><br/>";
                }

                if ($levelTeleMed != 0) {
                    $type = 'teleMedicine';
                    $url['levelTeleMed'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelTeleMed . '&id=' . $id. '&contract_type=categoryL9');
                    $url['levelTeleMedNum'] = $levelTeleMed;
                    $contracTelemedicine = 'TELEMEDICINE ' . $levelTeleMed;
                    $contractEmail .= "<a href=\"" . $url['levelTeleMed'] . "\">Telemedicine Level " . $url['levelTeleMedNum'] . "</a><br/>";
                }
            }
            else if($request->contract_type === 'catContrsL7'){
                $levelMed = $request->agent_level_medL7;
                $levelDent = $request->agent_level_dentL7;
                $levelDentSolstice = $request->agent_level_dentSolsticeL7;
                $levelVision = $request->agent_level_visionL7;
                $levelVisionSolstice = $request->agent_level_visionSolsticeL7;
                $levelTermLife = $request->agent_level_term_lifeL7;
                $levelBundled = $request->agent_level_bundledL7;
                $levelLimMed = $request->agent_level_lim_medL7;
                $levelAccident = $request->agent_level_accidentL7;
                $levelCritical = $request->agent_level_criticalL7;
                $levelHospital = $request->agent_level_hospitalL7;
                $levelLifeStyle = $request->agent_level_life_styleL7;
                $levelPet = $request->agent_level_petL7;
                $levelGuarnIssue = $request->agent_level_guarn_issueL7;
                $levelTeleMed = $request->agent_level_tele_medL7;

                if ($levelMed != 0) {
                    $type = 'medical';
                    $url['levelMedical'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelMed . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelMedicalNum'] = $levelMed;
                    $contracMedical = 'MEDICAL L7 ' . $levelMed;
                    $contractEmail .= "<a href=" . $url['levelMedical'] . ">Medical Level " . $url['levelMedicalNum'] . "</a><br/>";
                }

                if ($levelDent != 0) {
                    $type = 'dental';
                    $url['levelDental'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelDent . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelDentalNum'] = $levelDent;
                    $contracDental = 'DENTAL L7 ' . $levelDent;
                    $contractEmail .= "<a href=" . $url['levelDental'] . ">Dental Level " . $url['levelDentalNum'] . "</a><br/>";
                }

                if ($levelDentSolstice != 0) {
                    $type = 'dentalSolstice';
                    $url['levelDentalSolstice'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelDentSolstice . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelDentalSolsticeNum'] = $levelDentSolstice;
                    $contracDentalSolstice = 'DENTAL SOLSTICE L7 ' . $levelDentSolstice;
                    $contractEmail .= "<a href=" . $url['levelDentalSolstice'] . ">Dental Solstice Level " . $url['levelDentalSolsticeNum'] . "</a><br/>";
                }

                if ($levelVision != 0) {
                    $type = 'vision';
                    $url['levelVision'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelVision . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelVisionNum'] = $levelVision;
                    $contracVision = 'VISION L7 ' . $levelVision;
                    $contractEmail .= "<a href=\"" . $url['levelVision'] . "\">Vision Level " . $url['levelVisionNum'] . "</a><br/>";
                }

                if ($levelVisionSolstice != 0) {
                    $type = 'visionSolstice';
                    $url['levelVisionSolstice'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelVisionSolstice . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelVisionSolsticeNum'] = $levelVisionSolstice;
                    $contracVisionSolstice = 'VISION SOLSTICE L7 ' . $levelVisionSolstice;
                    $contractEmail .= "<a href=\"" . $url['levelVisionSolstice'] . "\">Vision Solstice Level " . $url['levelVisionSolsticeNum'] . "</a><br/>";
                }

                if ($levelTermLife != 0) {
                    $type = 'termLife';
                    $url['levelTermLife'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelTermLife . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelTermLifeNum'] = $levelTermLife;
                    $contracTermLife = 'TERM LIFE L7 ' . $levelTermLife;
                    $contractEmail .= "<a href=\"" . $url['levelTermLife'] . "\">Term Life Level " . $url['levelTermLifeNum'] . "</a><br/>";
                }

                if ($levelBundled != 0) {
                    $type = 'bundled';
                    $url['levelBundled'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelBundled . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelBundledNum'] = $levelBundled;
                    $contracBundles = 'BUNDLES L7 ' . $levelBundled;
                    $contractEmail .= "<a href=\"" . $url['levelBundled'] . "\">Bundled Level " . $url['levelBundledNum'] . "</a><br/>";
                }

                if ($levelLimMed != 0) {
                    $type = 'limitedMedical';
                    $url['levelLimMed'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelLimMed . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelLimMedNum'] = $levelLimMed;
                    $contracLimitedMedical = 'LIMITED MEDICAL L7 ' . $levelLimMed;
                    $contractEmail .= "<a href=\"" . $url['levelLimMed'] . "\">Limited Medical Level " . $url['levelLimMedNum'] . "</a><br/>";
                }

                if ($levelAccident != 0) {
                    $type = 'accident';
                    $url['levelAccident'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelAccident . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelAccidentNum'] = $levelAccident;
                    $contracAccident = 'ACCIDENT L7 ' . $levelAccident;
                    $contractEmail .= "<a href=\"" . $url['levelAccident'] . "\">Accident Level " . $url['levelAccidentNum'] . "</a><br/>";
                }

                if ($levelCritical != 0) {
                    $type = 'critical';
                    $url['levelCritical'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelCritical . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelCriticalNum'] = $levelCritical;
                    $contracCritical = 'CRITICAL L7 ' . $levelCritical;
                    $contractEmail .= "<a href=\"" . $url['levelCritical'] . "\">Critical Level " . $url['levelCriticalNum'] . "</a><br/>";
                }
                if ($levelHospital != 0) {
                    $type = 'hospital';
                    $url['levelHospital'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelHospital . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelHospitalNum'] = $levelHospital;
                    $contracHospital = 'HOSPITAL L7 ' . $levelHospital;
                    $contractEmail .= "<a href=\"" . $url['levelHospital'] . "\">Hospital Level " . $url['levelHospitalNum'] . "</a><br/>";
                }

                if ($levelLifeStyle != 0) {
                    $type = 'lifeStyle';
                    $url['levelLifeStyle'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelLifeStyle . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelLifeStyleNum'] = $levelLifeStyle;
                    $contracLifeStyle = 'LIFESTYLE L7 ' . $levelLifeStyle;
                    $contractEmail .= "<a href=\"" . $url['levelLifeStyle'] . "\">Lifestyle Level " . $url['levelLifeStyleNum'] . "</a><br/>";
                }

                if ($levelPet != 0) {
                    $type = 'pet';
                    $url['levelPet'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelPet . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelPetNum'] = $levelPet;
                    $contracPet = 'PET L7 ' . $levelPet;
                    $contractEmail .= "<a href=\"" . $url['levelPet'] . "\">Pet Level " . $url['levelPetNum'] . "</a><br/>";
                }

                if ($levelGuarnIssue != 0) {
                    $type = 'guaranteedIssue';
                    $url['levelGuarnIssue'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelGuarnIssue . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelGuarnIssueNum'] = $levelGuarnIssue;
                    $contracGuaranteedIssue = 'GUARANTEED ISSUE L7 ' . $levelGuarnIssue;
                    $contractEmail .= "<a href=\"" . $url['levelGuarnIssue'] . "\">Guaranteed Issue Level " . $url['levelGuarnIssueNum'] . "</a><br/>";
                }

                if ($levelTeleMed != 0) {
                    $type = 'teleMedicine';
                    $url['levelTeleMed'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelTeleMed . '&id=' . $id. '&contract_type=categoryL7');
                    $url['levelTeleMedNum'] = $levelTeleMed;
                    $contracTelemedicine = 'TELEMEDICINE L7 ' . $levelTeleMed;
                    $contractEmail .= "<a href=\"" . $url['levelTeleMed'] . "\">Telemedicine Level " . $url['levelTeleMedNum'] . "</a><br/>";
                }
            }
            else if ($request->contract_type === 'carrContrs'){
                $levelLocal713 = $request->agent_level_local713;
                $levelCigna = $request->agent_level_cigna;
                $levelL713Anthem = $request->agent_level_l713Anthem;
                $levelNorthwellAnthem = $request->agent_level_northwellAnthem;
                $levelIHAHealth = $request->agent_level_IHAHealth;
                $levelENANAWU = $request->agent_level_ENANAWU;
                $levelLifelineMedical = $request->agent_level_lifelineMedical;
                $levelSolsticeBenefits = $request->agent_level_solsticeBenefits;
                $levelOptionsPlus = $request->agent_level_optionsPlus;
                $levelMetropolitanLifeInsuranceCompany = $request->agent_level_metropolitanLifeInsuranceCompany;
                $levelPinnacleBenefitsService = $request->agent_level_pinnacleBenefitsService;
                $levelPrudential = $request->agent_level_prudential;
                $levelBeyondMedical = $request->agent_level_beyondMedical;

                if ($levelLocal713 != 0) {
                    $type = 'local713';
                    $url['levelLocal713'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelLocal713 . '&id=' . $id. '&contract_type=carrier');
                    $url['levelLocal713Num'] = $levelLocal713;
                    $contracLocal713 = 'LOCAL713 ' . $levelLocal713;
                    $contractEmail .= "<a href=" . $url['levelLocal713'] . ">Local713 Level " . $url['levelLocal713Num'] . "</a><br/>";
                }

                if ($levelCigna != 0) {
                    $type = 'cigna';
                    $url['levelCigna'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelCigna . '&id=' . $id. '&contract_type=carrier');
                    $url['levelCignaNum'] = $levelCigna;
                    $contracCigna = 'CIGNA ' . $levelCigna;
                    $contractEmail .= "<a href=" . $url['levelCigna'] . ">Cigna Level " . $url['levelCignaNum'] . "</a><br/>";
                }

                if ($levelL713Anthem != 0) {
                    $type = 'l713Anthem';
                    $url['levelL713Anthem'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelL713Anthem . '&id=' . $id. '&contract_type=carrier');
                    $url['levelL713AnthemNum'] = $levelL713Anthem;
                    $contracL713Anthem = 'L713 ANTHEM ' . $levelL713Anthem;
                    $contractEmail .= "<a href=\"" . $url['levelL713Anthem'] . "\">L713 Anthem Level " . $url['levelL713AnthemNum'] . "</a><br/>";
                }

                if ($levelNorthwellAnthem != 0) {
                    $type = 'northwellAnthem';
                    $url['levelNorthwellAnthem'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelNorthwellAnthem . '&id=' . $id. '&contract_type=carrier');
                    $url['levelNorthwellAnthemNum'] = $levelNorthwellAnthem;
                    $contracNorthwellAnthem = 'NORTHWELL ANTHEM ' . $levelNorthwellAnthem;
                    $contractEmail .= "<a href=\"" . $url['levelNorthwellAnthem'] . "\">Northwell Anthem Level " . $url['levelNorthwellAnthemNum'] . "</a><br/>";
                }

                if ($levelIHAHealth != 0) {
                    $type = 'iHAHealth';
                    $url['levelIHAHealth'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelIHAHealth . '&id=' . $id. '&contract_type=carrier');
                    $url['levelIHAHealthNum'] = $levelIHAHealth;
                    $contracIHAHealth = 'IHAHEALTH ' . $levelIHAHealth;
                    $contractEmail .= "<a href=\"" . $url['levelIHAHealth'] . "\">IHAHealth Level " . $url['levelIHAHealthNum'] . "</a><br/>";
                }

                if ($levelENANAWU != 0) {
                    $type = 'eNANAWU';
                    $url['levelENANAWU'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelENANAWU . '&id=' . $id. '&contract_type=carrier');
                    $url['levelENANAWUNum'] = $levelENANAWU;
                    $contracENANAWU = 'ENANAWU ' . $levelENANAWU;
                    $contractEmail .= "<a href=\"" . $url['levelENANAWU'] . "\">ENANAWU Level " . $url['levelENANAWUNum'] . "</a><br/>";
                }

                if ($levelLifelineMedical != 0) {
                    $type = 'lifelineMedical';
                    $url['levelLifelineMedical'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelLifelineMedical . '&id=' . $id. '&contract_type=carrier');
                    $url['levelLifelineMedicalNum'] = $levelLifelineMedical;
                    $contracLifelineMedical = 'LIFELINE MEDICAL ' . $levelLifelineMedical;
                    $contractEmail .= "<a href=\"" . $url['levelLifelineMedical'] . "\">Lifeline Medical Level " . $url['levelLifelineMedicalNum'] . "</a><br/>";
                }

                if ($levelSolsticeBenefits != 0) {
                    $type = 'solsticeBenefits';
                    $url['levelSolsticeBenefits'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelSolsticeBenefits . '&id=' . $id. '&contract_type=carrier');
                    $url['levelSolsticeBenefitsNum'] = $levelSolsticeBenefits;
                    $contracSolsticeBenefits = 'SOLSTICE BENEFITS ' . $levelSolsticeBenefits;
                    $contractEmail .= "<a href=\"" . $url['levelSolsticeBenefits'] . "\">Solstice Benefits Level " . $url['levelSolsticeBenefitsNum'] . "</a><br/>";
                }
                if ($levelOptionsPlus != 0) {
                    $type = 'optionsPlus';
                    $url['levelOptionsPlus'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelOptionsPlus . '&id=' . $id. '&contract_type=carrier');
                    $url['levelOptionsPlusNum'] = $levelOptionsPlus;
                    $contracOptionsPlus = 'OPTIONPLUS ' . $levelOptionsPlus;
                    $contractEmail .= "<a href=\"" . $url['levelOptionsPlus'] . "\">Options Plus Level " . $url['levelOptionsPlusNum'] . "</a><br/>";
                }

                if ($levelMetropolitanLifeInsuranceCompany != 0) {
                    $type = 'metropolitanLifeInsuranceCompany';
                    $url['levelMetropolitanLifeInsuranceCompany'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelMetropolitanLifeInsuranceCompany . '&id=' . $id. '&contract_type=carrier');
                    $url['levelMetropolitanLifeInsuranceCompanyNum'] = $levelMetropolitanLifeInsuranceCompany;
                    $contracMetropolitanLifeInsuranceCompany = 'METROPOLITAN LIFE INSURANCE COMPANY ' . $levelMetropolitanLifeInsuranceCompany;
                    $contractEmail .= "<a href=\"" . $url['levelMetropolitanLifeInsuranceCompany'] . "\">Metropolitan Life Insurance Company Level " . $url['levelMetropolitanLifeInsuranceCompanyNum'] . "</a><br/>";
                }

                if ($levelPinnacleBenefitsService != 0) {
                    $type = 'pinnacleBenefitsService';
                    $url['levelPinnacleBenefitsService'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelPinnacleBenefitsService . '&id=' . $id. '&contract_type=carrier');
                    $url['levelPinnacleBenefitsServiceNum'] = $levelPinnacleBenefitsService;
                    $contracPinnacleBenefitsService = 'PINNACLE BENEFITS SERVICE ' . $levelPinnacleBenefitsService;
                    $contractEmail .= "<a href=\"" . $url['levelPinnacleBenefitsService'] . "\">Pinnacle Benefits Service Level " . $url['levelPinnacleBenefitsServiceNum'] . "</a><br/>";
                }

                if ($levelPrudential != 0) {
                    $type = 'prudentialIssue';
                    $url['levelPrudential'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelPrudential . '&id=' . $id. '&contract_type=carrier');
                    $url['levelPrudentialNum'] = $levelPrudential;
                    $contracPrudential = 'PRUDENTIAL ' . $levelPrudential;
                    $contractEmail .= "<a href=\"" . $url['levelPrudential'] . "\">Prudential Level " . $url['levelPrudentialNum'] . "</a><br/>";
                }

                if ($levelBeyondMedical != 0) {
                    $type = 'beyondMedical';
                    $url['levelBeyondMedical'] = url($corenrollUrl . 'contract-form/personal_info?type=' . $type . '&level=' . $levelBeyondMedical . '&id=' . $id. '&contract_type=carrier');
                    $url['levelBeyondMedicalNum'] = $levelBeyondMedical;
                    $contracBeyondMedical = 'BEYOND MEDICAL ' . $levelBeyondMedical;
                    $contractEmail .= "<a href=\"" . $url['levelBeyondMedical'] . "\">Beyond Medical Level " . $url['levelBeyondMedicalNum'] . "</a><br/>";
                }
            }


            if (($agentInfo->agent_level == '1' || $agentInfo->agent_level == '0')) {
                $url['w9'] = '';
            } else if(!$this->hasW9Contract($request->agent_id)) {
                $url['w9'] = url($corenrollUrl . 'w9-form/personal_info?id=' . $id);
            }
            else{
                $url['w9'] = '';
            }

            // Prepare Email Content
            $template_body = "Hello {$agentInfo->fullname},<br/><br/>";
            $template_body .= "<strong>Your Association Representative Contract(s) are ready.</strong><br>";
            $template_body .= "Please click the links below to fill the contract form, complete and submit.<br/>";
            $template_body .= $contractEmail;
            if ($url['w9'] != '') {
                $template_body .= "<br/> Please note the link(s), expire in 5 days.<br/> <br/> Please complete this with contract and submit:<br/><a href=" . $url['w9'] . ">W-9 Form</a><br/> ";
            }
            $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
            $post = [
                'email_message_configuration_name' => "NUERA_API_GENERIC",
                'toAddress' => $toAddress,
                'subject' => "Representative Contract",
                'generalTemplateData' => [],
                'contentTemplate' => $template_body
            ];

            $email_status = $messageService->sendEmailWithContentTemplate($post);

            if ($email_status) {
                $contract = '';
                $contractCategory = '';
                $contractCarrier = '';
                $contracts = ['Ac', 'Pre', 'PreBex', 'Bex', 'FegliPremier', 'FegliAncillary', 'PatriotAncillary',
                 'Medical', 'Dental', 'Vision', 'DentalSolstice', 'VisionSolstice', 'TermLife', 'Bundles', 'LimitedMedical', 'Accident', 'Critical', 'Hospital', 'LifeStyle', 'Pet', 'GuaranteedIssue', 'Telemedicine','Local713','Cigna','L713Anthem',
                 'NorthwellAnthem','IHAHealth','ENANAWU','LifelineMedical','SolsticeBenefits','OptionsPlus','MetropolitanLifeInsuranceCompany','PinnacleBenefitsService','Prudential','BeyondMedical'];
                $contractsCategory = ['Medical', 'Dental', 'Vision', 'DentalSolstice', 'VisionSolstice', 'TermLife', 'Bundles', 'LimitedMedical', 'Accident', 'Critical', 'Hospital', 'LifeStyle', 'Pet', 'GuaranteedIssue', 'Telemedicine'];
                $contractsCarrier = ['Local713','Cigna','L713Anthem','NorthwellAnthem','IHAHealth','ENANAWU','LifelineMedical','SolsticeBenefits','OptionsPlus','MetropolitanLifeInsuranceCompany','PinnacleBenefitsService','Prudential','BeyondMedical'];
                foreach ($contracts as $eachContractName)
                {
                    $contractVar = 'contrac' . $eachContractName;
                    if (isset($$contractVar))
                        $contract = strlen($contract) != 0
                            ? $contract . '-' . $$contractVar
                            : $$contractVar;
                }


                foreach ($contracts as $eachContractName)
                {
                        if(in_array($eachContractName, $contractsCategory)){
                            $contractVar = 'contrac' . $eachContractName;
                            if (isset($$contractVar))
                                $contractCategory = strlen($contractCategory) != 0
                                    ? $contractCategory . '-' . $$contractVar
                                    : $$contractVar;
                        }
                }

                foreach ($contracts as $eachContractName)
                {
                        if(in_array($eachContractName, $contractsCarrier)){
                            $contractVar = 'contrac' . $eachContractName;
                            if (isset($$contractVar))
                                $contractCarrier = strlen($contractCarrier) != 0
                                    ? $contractCarrier . '-' . $$contractVar
                                    : $$contractVar;
                        }
                }
                $repContractResponse = $this->createRepContractAct($request, $contract, $contractCategory, $contractCarrier);

                if ($repContractResponse->isSuccessful()) {
                    if( $request->contract_type === 'N-CatContrs'){
                        self::updateAncillaryInAgentInfo($level ? $level : $levelAlcBex , $levelPre ? $levelPre : $levelPreBex ,$request->agent_id);
                    }
                    DB::commit();
                }
            } else {
                DB::rollBack();
                return $this->failedResponse('Failed to send representative contracts.');
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->failedResponse($exception->getMessage(), Response::HTTP_CONFLICT);
        }
        return $repContractResponse;
    }

    public function getSignedContractLevels($agentId, $type = null)
    {
        $imageCollect = [];
        $listCollect = [];
        $query = RepContract::query()
        ->with('repContractDetail')
        ->where('agent_id', '=', $agentId)
        ->orderBy('contract_id', 'DESC')
        ->where('is_completed','yes')
        ->whereIn('contract_id', function ($query) use($agentId) {
            $query->select(DB::raw('MAX(contract_id)'))
                ->from('rep_contract')
                ->where('agent_id',$agentId)
                ->where('is_completed','yes')
                ->groupBy('contract_type');
        });
        if ($type === 'list') {
            $data = $query->with('repContractDetail')->get();
            $listCollect = [];
            $tempCollect = [];
            foreach ($data as $keyda => $val) {
                if ($val['contract_type'] !== 'ancillary' &&
                    $val['contract_type'] !== 'premier' &&
                    $val['contract_type'] !== 'bexPremier' &&
                    $val['contract_type'] !== 'bexAncillary') {
                    $temp = [];
                    $temp['contract_type'] = $this->formatContractType($this->getContractType($val['contract_type']));
                    $temp['contract_display_name'] = $this->getContractDisplayName($val['contract_display_name']) ;
                    $temp['data'] = [];
                    if (isset($val->repContractDetail)) {
                        foreach ($val->repContractDetail as $rep_detail => $detail) {
                            $temp['data'][] = [
                                'contract_item_display_name' => $this->getContractDisplayName( $detail['contract_item_display_name']),
                                'contract_level' => $detail['contract_level'],
                            ];
                        }
                    }
                    $tempCollect[] = $temp;
                }
            }
            $listCollect = array_values($tempCollect);
            return $listCollect;
        } else if ($type === 'image') {
            $data = $query->where(function ($query) {
                $query->whereNotNull('screenshot')
                      ->where('screenshot', '<>', '');
            })->get();

            foreach ($data as $key=> $imageData) {
                $contractDisplayName = $this->getContractName($imageData['contract_display_name']) ;
                $index = count($imageCollect[$contractDisplayName] ?? []);
                $imageCollect[$contractDisplayName][$index] = [
                    'contract_type' => $this->formatContractType($this->getContractType($imageData['contract_type'])),
                    'contract_type_org'=> $imageData['contract_type'],
                    'contract_id' => $imageData['contract_id'],
                    'images' => [],
                ];
                $url = config('filesystems.disks.s3-second.url');
                $cleanPath = str_replace($url, '', $imageData['screenshot']);
                $imageFolder = Storage::disk('s3-second')->allFiles($cleanPath);
                $imageCollect[$contractDisplayName][$index]['screenshotCompleted'] = false;
                foreach ($imageFolder as $folderKey => $value) {
                    if(pathinfo($value, PATHINFO_EXTENSION) !== 'pdf'){
                        $imageCollect[$contractDisplayName][$index]['images'][] = [
                            'name' => $this->formatContractType(str_replace(['.png', '.pdf'], '', basename($value))) ,
                            'links' => $url . $value,
                        ];
                    }
                    else{
                        if(!isset($imageCollect[$contractDisplayName][$index]['screenshot'])){
                            $imageCollect[$contractDisplayName][$index]['screenshot'] = $url . $value;
                            $imageCollect[$contractDisplayName][$index]['name'] = $this->formatContractType($this->getContractType($imageData['contract_type']));
                            $imageCollect[$contractDisplayName][$index]['screenshotCompleted'] = true;
                        }

                    }

                }
            }
            $url = config('corenroll.corenroll_api_url') . "api/v2/w9Contract/{$agentId}";
            $responseJson = GuzzleHelper::getApi($url, []);
            $response = json_decode($responseJson, true);
            if ($response['success'] === true) {
                $imageCollect['W9 Contract'] = [
                                        [
                        'contract_id' => $response['data']['id'],
                        'contract_type'=> "W9",
                        'contract_type_org'=>'w9',
                        'images'=>[
                            [
                                'name'=> "Agreement",
                                'links'=>$response['data']['certification_screenshot']
                            ]
                        ],
                        'screenshotCompleted'=> is_null($response['data']['w9_contract_pdf']) ? false : true,
                        'screenshot'=>$response['data']['w9_contract_pdf'],
                        'name'=> "W9"
                    ]
                ];
            }
            return $imageCollect;
        }

        return "not found";
    }

    public function getContractDisplayName($contractDisplayName){
        $contractDisplayMappings = [
            'Non-Category Contract' => 'Standard Contracting',
            'categoryL9' => 'Patriot Medical - Multi Level Contract',
            'Patriot Contract' => 'Patriot Medical',
            'Patriot - Ancillary Contract' => 'Patriot Medical'
        ];

        $formattedContractDisplayName = $contractDisplayMappings[$contractDisplayName] ?? $contractDisplayName;

        return $formattedContractDisplayName;
    }

    public function getContractType($contractType)
{
    $contractTypeMappings = [
        'patriotAncillary' => 'Patriot Medical',
        'ancillary' => 'Smart Contract',
        'premier' => 'EWA Contract (Union Only)',
        'categoryL7' =>	'Category Smart-Ancillary Contract',
        'categoryL9' =>	'Patriot Medical - Multi Level Contract',
    ];

    $formattedContractType = $contractTypeMappings[$contractType] ?? $contractType;

    return $formattedContractType;
}

public function getContractName($contractName){
    $contractNameMappings = [
        'Non-Category Contract' => 'Standard Contracting',
        'categoryL9' => 'Patriot Medical - Multi Level Contract',
    ];

    $formattedContractName = $contractNameMappings[$contractName] ?? $contractName;

    return $formattedContractName;
}


    public function getNewSignedContractLevels($agentId, $type = null)
    {
        $contractData = $this->getContractLevels($agentId);
        $query = DB::connection('corapi-mysql')
            ->table('temporary_contracts')
            ->where('agentid', $agentId)
            ->select('contract_type', 'is_complete', 'updated_at','fee_level');
        $result = $query->get();
    if ($result->count() == 0) {
        return "not found";
    }
    if ($type === 'list') {
        $filteredContracts = collect($result)
            ->where('is_complete', 'completed')
            ->sortByDesc('updated_at')
            ->unique('contract_type');
    } elseif ($type === 'image') {
        $completedContracts = collect($result)
            ->where('is_complete', 'completed')
            ->sortByDesc('updated_at')
            ->unique('contract_type')
            ->keyBy('contract_type');

        $nonCompletedContracts = collect($result)
            ->where('is_complete', '!==', 'completed')
            ->sortByDesc('updated_at')
            ->unique('contract_type')
            ->reject(function ($contract) use ($completedContracts) {
                return $completedContracts->has($contract->contract_type);
            })
            ->keyBy('contract_type');

        $filteredContracts = $completedContracts->merge($nonCompletedContracts);

    }

    $structuredContract = $this->structureContractData($contractData , $type);

    if (count($filteredContracts)) {
        $signedImages = [];
        foreach ($filteredContracts as $key => $res) {
            $contractType = $res->contract_type;
            if (isset($structuredContract[$contractType])) {
                $signedImages[$key]['name'] = $this->formatContractType($contractType);
                $signedImages[$key]['key'] = $contractType;
                if ($type === 'list') {
                    $signedImages[$key]['value'] =  $res->fee_level;
                }
            }
        }
        sort($signedImages);
        return $signedImages;
    }
    return "not found";
    }

    public function structureContractData($contractData , $type){
        $data = $contractData;
    $a = [];
    if($type === 'image'){
        if (isset($data['alc'])) {
            $a['ancillary'] = $data['alc'];
        }
        if (isset($data['Pbex'])) {
            $a['bexPremier'] = $data['Pbex'];
        }
        if (isset($data['Abex'])) {
            $a['bexAncillary'] = $data['Abex'];
        }
        if (isset($data['pec'])) {
            $a['premier'] = $data['pec'];
        }
    }
    if (isset($data['fegliAncillary'])) {
        $a['fegliAncillary'] = $data['fegliAncillary'];
    }
    if (isset($data['patriotAncillary'])) {
        $a['patriotAncillary'] = $data['patriotAncillary'];
    }
    if (isset($data['patriotPremier'])) {
        $a['patriotPremier'] = $data['patriotPremier'];
    }
    if (isset($data['med'])) {
        $a['medical'] = $data['med'];
    }
    if (isset($data['dent'])) {
        $a['dental'] = $data['dent'];
    }
    if (isset($data['vision'])) {
        $a['vision'] = $data['vision'];
    }
    if (isset($data['term_life'])) {
        $a['termLife'] =$data['term_life'];
    }
    if (isset($data['bundled'])) {
        $a['bundled'] = $data['bundled'];
    }
    if (isset($data['lim_med'])) {
        $a['limitedMedical'] = $data['lim_med'];
    }
    if (isset($data['accident'])) {
        $a['accident'] = $data['accident'];
    }
    if (isset($data['critical'])) {
        $a['critical'] = $data['critical'];
    }
    if (isset($data['hospital'])) {
        $a['hospital'] = $data['hospital'];
    }
    if (isset($data['life_style'])) {
        $a['lifeStyle'] = $data['life_style'];
    }
    if (isset($data['pet'])) {
        $a['pet'] = $data['pet'];
    }
    if (isset($data['guarn_issue'])) {
        $a['guaranteedIssue'] = $data['guarn_issue'];
    }
    if (isset($data['tele_med'])) {
        $a['teleMedicine'] = $data['tele_med'];
    }
    if (isset($data['local713'])) {
        $a['local713'] = $data['local713'];
    }
    if (isset($data['cigna'])) {
        $a['cigna'] = $data['cigna'];
    }
    if (isset($data['l713Anthem'])) {
        $a['l713Anthem'] = $data['l713Anthem'];
    }
    if (isset($data['northwellAnthem'])) {
        $a['northwellAnthem'] = $data['northwellAnthem'];
    }
    if (isset($data['IHAHealth'])) {
        $a['IHAHealth'] = $data['IHAHealth'];
    }
    if (isset($data['ENANAWU'])) {
        $a['ENANAWU'] = $data['ENANAWU'];
    }
    if (isset($data['lifelineMedical'])) {
        $a['lifelineMedical'] = $data['lifelineMedical'];
    }
    if (isset($data['solsticeBenefits'])) {
        $a['solsticeBenefits'] = $data['solsticeBenefits'];
    }
    if (isset($data['optionsPlus'])) {
        $a['optionsPlus'] = $data['optionsPlus'];
    }
    if (isset($data['metropolitanLifeInsuranceCompany'])) {
        $a['metropolitanLifeInsuranceCompany'] = $data['metropolitanLifeInsuranceCompany'];
    }
    if (isset($data['pinnacleBenefitsService'])) {
        $a['pinnacleBenefitsService'] = $data['pinnacleBenefitsService'];
    }
    if (isset($data['prudential'])) {
        $a['prudential'] = $data['prudential'];
    }
    if (isset($data['beyondMedical'])) {
        $a['beyondMedical'] = $data['beyondMedical'];
    }
    if (isset($data['medL7'])) {
        $a['medL7'] = $data['medL7'];
    }
    if (isset($data['dentL7'])) {
        $a['dentL7'] = $data['dentL7'];
    }
    if (isset($data['dentSolsticeL7'])) {
        $a['dentSolsticeL7'] = $data['dentSolsticeL7'];
    }
    if (isset($data['visionL7'])) {
        $a['visionL7'] = $data['visionL7'];
    }
    if (isset($data['visionSolsticeL7'])) {
        $a['visionSolsticeL7'] = $data['visionSolsticeL7'];
    }
    if (isset($data['term_lifeL7'])) {
        $a['term_lifeL7'] = $data['term_lifeL7'];
    }
    if (isset($data['bundledL7'])) {
        $a['bundledL7'] = $data['bundledL7'];
    }
    if (isset($data['lim_medL7'])) {
        $a['lim_medL7'] = $data['lim_medL7'];
    }
    if (isset($data['accidentL7'])) {
        $a['accidentL7'] = $data['accidentL7'];
    }
    if (isset($data['criticalL7'])) {
        $a['criticalL7'] = $data['criticalL7'];
    }
    if (isset($data['hospitalL7'])) {
        $a['hospitalL7'] = $data['hospitalL7'];
    }
    if (isset($data['life_styleL7'])) {
        $a['life_styleL7'] = $data['life_styleL7'];
    }
    if (isset($data['petL7'])) {
        $a['petL7'] = $data['petL7'];
    }
    if (isset($data['guarn_issueL7'])) {
        $a['guarn_issueL7'] = $data['guarn_issueL7'];
    }
    if (isset($data['tele_medL7'])) {
        $a['tele_medL7'] = $data['tele_medL7'];
    }
    return $a;
    }

    public function createRepContractAct($request, $contract, $categoryContract = null, $carrierContract = null)
    {
        try {
            $data = [
                'action' => 'contracts sent',
                'aid' => $request->agent_id,
                'userid' => request()->header('id'),
                'contracts' => $contract,
                'contract_type' => $request->contract_type,
                'ts' => time(),
                'ipaddress' => request()->ip()
            ];
           $nonCatagoryContract =  RepContractAct::query()
                ->create($data);
                if($carrierContract){
                    $this->createRepCarrierContract($request, $nonCatagoryContract->rcid ,  $carrierContract);
                }
            return $this->createRepCategoryContract($request, $nonCatagoryContract->rcid ,  $categoryContract);

        } catch (\Throwable $th) {
            return $this->failedResponse('Something Went Wrong');
        }
    }

    public function createRepCategoryContract($request, $id,  $categoryContract)
    {
        try {
            if($categoryContract){
                $data = [
                    'contract_act_id' => $id,
                    'action' => 'contracts sent',
                    'agent_id' => $request->agent_id,
                    'userid' => request()->header('id'),
                    'contract' => $categoryContract,
                    'ipaddress' => request()->ip()
                ];
               RepContractCategories::query()
                    ->create($data);
            }
        return $this->successResponse('Representative Contracts Sent.');

        } catch (\Throwable $th) {
            return $this->failedResponse('Something Went Wrong');
        }
    }

    public function createRepCarrierContract($request, $id,  $carrierContract)
    {
        try {
            if($carrierContract){
                $data = [
                    'contract_act_id' => $id,
                    'action' => 'contracts sent',
                    'agent_id' => $request->agent_id,
                    'userid' => request()->header('id'),
                    'contract' => $carrierContract,
                    'ipaddress' => request()->ip()
                ];
                return RepContractCarrier::query()
                    ->create($data);
            }

        } catch (\Throwable $th) {
            return $this->failedResponse('Something Went Wrong');
        }
    }

    public function agentDetail($agentId)
    {
        $agent = AgentInfo::find($agentId);
        if (!$agent) return $this->failedResponse('Agent not found');
        $data = $this->agentDetailData($agent);
        return $this->successResponse('Success', $data);
    }


    protected function agentBasicInfoData($d)
    {
        $customValidationService = new CustomValidationService();
        $filterDate = isset($d->agent_dob) ? $this->filterDob($d->agent_dob) : "N/A";
        return [
            'agent_id' => $d->agent_id,
            'agent_code' => $d->agent_code,
            'agent_fname' => $d->agent_fname,
            'agent_mname' => $d->agent_mname,
            'agent_lname' => $d->agent_lname,
            'agent_fullname' => $d->fullname,
            'agent_email' => $d->agent_email,
            'agent_ga' => $d->agent_ga,
            'agent_ga_effective_date' => $d->agent_ga_effective_date
                ? $d->agent_ga_effective_date->format('m/d/Y')
                : null,
            'agent_signup_date' => Carbon::createFromTimestamp($d->agent_signup_date)->format('m/d/Y'),
            'agent_level' => $d->agent_level,
            'agent_level_2' => $d->agent_level_2,
            'contracts' => $this->getSignedContractLevels($d->agent_id, 'list'),
            'agent_status' => $d->agent_status,
            'formatted_agent_status' => isset($d->agent_status) ? array_search($d->agent_status, AgentInfo::$statuses) : null,
            'agent_phone1' => $d->agent_phone1,
            'agent_phone2' => $d->agent_phone2,
            'admin_only' => $d->admin_only,
            'agent_image' => $d->full_image,
            'agent_ssn' => $d->agent_ssn,
            'maskedSsn' => isset($d->agent_ssn) ? "XXXX-XX-" . substr((int)$d->agent_ssn, -4) : 'N/A',
            'dob' => $filterDate,
            'vip' => $d->vip,
            'agent_address1' => $d->agent_address1,
            'agent_address2' => $d->agent_address2,
            'agent_state' => $d->agent_state,
            'agent_city' => $d->agent_city,
            'agent_zip' => $d->agent_zip,
            'agent_payment_method' => $d->agent_payment_method,
            'is_email_valid' => $customValidationService->validateEmailNeverBounce($d->agent_email),
            'is_phone1_valid' => $customValidationService->validatePhoneNumVerify($d->agent_phone1),
            'is_phone2_valid' => $customValidationService->validatePhoneNumVerify($d->agent_phone2),
        ];
    }

    protected function agentDetailData($agent)
    {
        $countNewMembers = $this->newAgentMember($agent->agent_id) ?: 0;
        $licenses = isset($agent->getLicense) ? $this->licenseData($agent->getLicense) : null;
        $groups = isset($agent->getGroup) ? $agent->getGroup : null;
        $displaySetting = $this->getDisplaySetting($agent->agent_id);
        $businessHour = $this->getBusinessHour($agent->agent_id);
        $upline = isset($agent->getAgentGa) ? $this->uplineData($agent->getAgentGa) : null;
        $achPayment = $this->primaryAchPayment($agent->agent_id);
        $basicInfo = $this->agentBasicInfoData($agent);
        $totals = isset($agent->getTotals) ? $this->getTotalData($agent->getTotals) : null;
        $webLinks = $this->agentWebLinks($agent);
        return [
            'basicInfo' => $basicInfo,
            'upline' => $upline,
            'groups' => $groups,
            'licenses' => $licenses,
            'totals' => $totals,
            'count_new_members' => $countNewMembers,
            'achPayment' => $achPayment,
            'webLinks' => $webLinks,
            'tranning_flag' => $agent->tranning_flag ?: 0,
            'display_setting' => $displaySetting,
            'business_hour' => $businessHour,
            'only_bex_contract' => $this->isBex($agent->agent_id),
        ];
    }

    protected function getDisplaySetting($agent_id){
        try{
            $displayInfo = AgentInfo::select('agent_web_name','agent_bio','custom_homepage.phone','custom_homepage.email','custom_homepage.banner_text','custom_homepage.help_text','custom_homepage.tagline')
            ->join('custom_homepage','custom_homepage.agent_id','=','agent_info.agent_id')
            ->where('custom_homepage.agent_id',$agent_id)
            ->get();
            if(!$displayInfo->isEmpty()){
                $displayInfoAgent['display_phone'] = $displayInfo[0]['phone'];
                $displayInfoAgent['display_name'] = $displayInfo[0]['agent_web_name'];
                $displayInfoAgent['agent_bio'] = $displayInfo[0]['bio'];
                $displayInfoAgent['display_email'] = $displayInfo[0]['email'];
                $displayInfoAgent['banner_text'] = $displayInfo[0]['banner_text'];
                $displayInfoAgent['help_text'] = $displayInfo[0]['help_text'];
                $displayInfoAgent['tagline'] = $displayInfo[0]['tagline'];
                return $displayInfoAgent;
            }
            else{
                return [];
            }

        }
        catch (\Throwable $th) {
            return $this->failedResponse("NO Display setting found for this agent.");
        }
    }

    public function getAgentNotes($request){
        $agentNotes = AgentNote::query()->where('agent_id',$request->agent_id)->orderBy('date', 'desc')->paginate(10);
        foreach($agentNotes as $key => $agNote){
            $agentNoteData[$key]['aid'] = $agNote['aid'];
            $agentNoteData[$key]['agent_id'] = $agNote['agent_id'];
            $agentNoteData[$key]['agent_email'] = $agNote['agent_email'];
            $agentNoteData[$key]['subject'] = $agNote['subject'];
            $agentNoteData[$key]['message'] = $agNote['message'];
            $agentNoteData[$key]['date'] = date('m/d/Y',$agNote['date']);
            $agentNoteData[$key]['added_by'] = $agNote['added_by'];
            $agentNoteData[$key]['status'] = $agNote['status'];
            $agentNoteData[$key]['attachment'] = ($agNote['Attachment'] != '' && file_exists('uploads/agent_note_files' . '/' . $agNote['Attachment'])) ? $request->root() . '/api/v1/download-attachment/' . $agNote['Attachment'] : '';
        }

        $paginatedData = [
            'data' => $agentNoteData ?? [],
            'links' => $this->links($agentNotes),
            'meta' => $this->meta($agentNotes)
        ];

            return $this->successResponse('Success', $paginatedData);
    }

    protected function getBusinessHour($agent_id){
        $businessHour = AgentInfo::select('agent_timezone','agent_bio','hoursa','hoursb','hoursata','hoursatb','hoursatc','hoursuna','hoursunb','hoursunc')->where('agent_id',$agent_id)->get();
        $businessHr['timezone'] = $businessHour[0]['agent_timezone'];
        $businessHr['bio'] = $businessHour[0]['agent_bio'];
        $businessHr['from'] = $businessHour[0]['hoursa'];
        $businessHr['to'] = $businessHour[0]['hoursb'];
        $businessHr['sat_from'] = $businessHour[0]['hoursata'];
        $businessHr['sat_to'] = $businessHour[0]['hoursatb'];
        $businessHr['sat_open'] = $businessHour[0]['hoursatc'];
        $businessHr['sun_from'] = $businessHour[0]['hoursuna'];
        $businessHr['sun_to'] = $businessHour[0]['hoursunb'];
        $businessHr['sun_open'] = $businessHour[0]['hoursunc'];
        return $businessHr;
    }

    protected function newAgentMember($agentId)
    {
        $refts = strtotime("first day of this month midnight");
        return UserInfoPolicyAddress::query()
            ->where('agent_id', '=', $agentId)
            ->where('edate', '>', $refts)
            ->count();
    }

    protected function filterDate($date)
    {
        if ($date == '0000-00-00') {
            return 'N/A';
        } else {
            if (Carbon::createFromTimestamp($date)) {
                return Carbon::createFromTimestamp($date)->format('m/d/Y');
            } elseif (Carbon::createFromFormat('Y-m-d', $date)) {
                return Carbon::parse($date)->format('m/d/Y');
            } elseif (Carbon::createFromFormat('m/d/Y', $date)) {
                return $date;
            } else {
                return 'N/A';
            }
        }
    }

    protected function filterDob($date)
    {
        try {
            $unix = Carbon::parse($date)->unix();
            return Carbon::createFromTimestamp($unix)->format('m/d/Y');
        } catch (\Throwable $th) {
            return "N/A";
        }

    }


    public function agentWebLinks($data)
    {
        $agentId = $data->agent_id;
        $agentWeb = $data->agent_web;
        $agentInfoElite = $this->countAgentGroup($agentId, 'elite');
        $agentInfoBrokerExchange = $this->countAgentGroup($agentId, 'brokerExchange');
        $benefitStoreUrl = config('app.benefit_store_url') . "agent-home?agent_id=" . base64_encode($agentId);
        $links = [
            'agent_web' => $agentWeb ?: '',
            'premierenroll' => $agentInfoElite >= 1 ? 'https://premierenroll.com/' . $agentWeb : null,
            'eliteEnroll' => $agentInfoElite >= 1 ? 'https://eliteenroll.com/' . $agentWeb : null,
            'brokerExchanges' => $agentInfoBrokerExchange >= 1 ? 'https://brokerexchanges.com/' . $agentWeb : null,
            'goEnroll' => $agentInfoElite >= 1 ? 'https://goenroll123.com/' . $agentWeb : null,
            'benefitStore' => config('app.benefit_store_url') . $agentWeb,
            'benefitStoreUrl' => $benefitStoreUrl
        ];
        if ($agentWeb == null) {
            $links = null;
        }
        return $links;
    }

    public function countAgentGroup($agentId, $webType = 'elite')
    {
        $agentGroup = AgentGroup::query();
        switch ($webType) {
            case 'elite':
                $agentGroup
                    ->where([
                        'agent_id' => $agentId,
                        'gid' => 1073
                    ])
                    ->count();
                break;
            case 'brokerExchange':
                $agentGroup
                    ->whereIn('gid', [1434, 1399])
                    ->where('agent_id', '=', $agentId);
                break;
            default:
                return null;
        }
        return $agentGroup->count();
    }

    public function getDownlineReps($agentId, $limit = 10, $filters = [])
    {
        $agent = AgentInfo::find($agentId);
        if (!$agent) return $this->failedResponse('Agent not found');
        $query = AgentInfo::query()
            ->where('agent_ga', '=', $agent->agent_id)
            ->whereNotIn('agent_status', [AgentInfo::STATUS_DISABLED])
            ->orderBy('agent_id', 'DESC')
            ->with(self::$withRelations);
        $this->filterContent($query, $filters);
        $data = $query->paginate($limit)->appends($filters);
        try {
            $result = $this->formattedData($data);
            return $this->successResponse('Success', $result);
        } catch (\Throwable $th) {
            return $this->failedResponse("Something went wrong");
        }
    }

    public function getAgentLicenses($agentId, $limit = 10, $filters = [])
    {
        $agent = AgentInfo::find($agentId);
        if (!$agent) return $this->failedResponse('Agent not found');
        $query = AgentLicense::query()
            ->where('license_agent_id', '=', $agent->agent_id)
            ->where('license_deleted', '=', 0)
            ->orderBy('license_agent_id', 'DESC');
        $this->licenseFilterContent($query, $filters);
        $data = $query->paginate($limit)->appends($filters);
        try {
            $paginatedData = [
                'data' => $this->licenseData($data),
                'links' => $this->links($data),
                'meta' => $this->meta($data)
            ];
            return $this->successResponse('Success', $paginatedData);
        } catch (\Throwable $th) {
            return $this->failedResponse($th->getMessage());
        }
    }

    protected function licenseFilterContent($data, $filters = [])
    {
        if (isset($filters['license_number'])) {
            $data->where('license_number', '=', $filters['license_number']);
        }
    }

    protected function primaryAchPayment($agentId)
    {
        return AgentAchPaymentBankDetail::query()
            ->where([
                'agent_id' => $agentId,
                'is_primary' => 1
            ])
            ->first();
    }

    public function updatePaymentMethod($request)
    {
        $sendEmail = isset($request['send_email']) ? $request['send_email'] : 0;
        $agentInfo = AgentInfo::query()
            ->where('agent_id', '=', $request['agent_id'])
            ->first();
        $alreadySetMessage = "This payment method is already set to type " . $agentInfo->agent_payment_method;
        if ($agentInfo->agent_payment_method == $request['payment_method']) return $this->failedResponse($alreadySetMessage);
        DB::beginTransaction();
        try {
            $agentInfo
                ->update([
                    'agent_payment_method' => $request['payment_method'],
                    'payby' => $request['payment_method']
                ]);
            $successMessage = 'Agent payment method updated successfully.';
            //update to agent_updates
            $this->createAgentUpdate($request, \App\AgentUpdate::ACT_BANKINGS_INFO, $successMessage);

            //send email
            if ($sendEmail == 1) {
                $this->sendPaymentUpdateEmail($agentInfo);
            }
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
//            return $this->successResponse($th->getMessage());
            return $this->successResponse('Failed to update payment method.');
        }
    }

    protected function sendPaymentUpdateEmail($agentInfo)
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your following payment has been updated on " . $date . ".";
        $contentData = [
            'Agent Id' => $agentInfo->agent_id,
            'Payment Method' => $agentInfo->agent_payment_method,
        ];
        $emailConfigurationName = "AGENT_INFO_CHANGE";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Agent Payment Method Update",
            'message' => $message,
            'data' => $contentData,
        ];
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    public function getUplineReps($agentId)
    {
        $agentInfo = AgentInfo::find($agentId);
        if (!$agentInfo) return $this->failedResponse('No Agent Found.');
        $uplineRep = $agentInfo->getAgentGa;
        if ($uplineRep) {
            $uplineReps = AgentInfo::query()
                ->where('agent_level', '>=', $agentInfo->agent_level)
                ->where('agent_status', '=', AgentInfo::STATUS_APPROVED)
                ->where('agent_id', '!=', $agentInfo->agent_id)
                ->where('agent_id', '!=', $uplineRep->agent_id)
                ->orderBy('agent_level', 'ASC')
                ->get();
            if ($uplineReps->isNotEmpty()) {
                $data = $this->formattedUplineReps($uplineReps);
                return $this->successResponse('Success', $data);
            } else {
                return $this->failedResponse('No Upline Reps Found.');
            }
        } else {
            return $this->failedResponse('No Upline Rep Found.');
        }
    }

    protected function formattedUplineReps($uplineReps)
    {
        $result = [];
        foreach ($uplineReps as $d) {
            $result[] = [
                'upline_agent_id' => $d->agent_id,
                'upline_agent_level' => $d->agent_level,
                'upline_agent_code' => $d->agent_code,
                'upline_agent_fname' => $d->agent_fname,
                'upline_agent_mname' => $d->agent_mname,
                'upline_agent_lname' => $d->agent_lname,
                'upline_agent_fullname' => $d->fullname,
                'upline_agent_email' => $d->agent_email,
                'upline_agent_ga' => $d->agent_ga,
                'formatted_upline_agent_name' => "{$d->agent_code} - {$d->fullname} (lvl: {$d->agent_level})"
            ];
        }
        return $result;
    }

    public function updateUplineRep($request)
    {
        $agentInfo = AgentInfo::find($request['agent_id']);
        $currentUpline = $agentInfo->getAgentGa;
        if (!$currentUpline) return $this->failedResponse('No Upline Found.');
        $requestedUplineRep = AgentInfo::find($request['agent_ga']);
        //check agent level and requested agent level
        $checkRepLevel = $this->checkUplineLevel($agentInfo, $requestedUplineRep);
        $checkUplineStatus = $this->checkUplineStatus($requestedUplineRep);
        if (!$checkRepLevel) return $this->failedResponse('Selected Upline Rep level must be greater than current rep level.', 409);
        if (!$checkUplineStatus) return $this->failedResponse('Selected Upline Rep level must be active.', 409);
        DB::beginTransaction();
        try {
            $agentInfo->update([
                'agent_ga' => $request['agent_ga']
            ]);
            $successMessage = "Upline Rep updated successfully.";
            //update to agent_updates
            $request['changed_from'] = $currentUpline->agent_id;
            $request['changed_to'] = $request['agent_ga'];
            $this->createAgentUpdate($request, \App\AgentUpdate::ACT_UPLINE, $successMessage);
            //send email
            $this->sendUpdatedUpineEmail($agentInfo);

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to update upline rep.');
        }
    }

    public function scheduleUpdateUplineRep($request,$returnResponse = true)
    {
        $agent_id = AgentInfo::withTrashed()->find($request['agent_id']);
        $agent_ga = AgentInfo::withTrashed()->find($request['agent_ga']);

        // if current agent_ga is same as agent_ga that is to be assigned
        if ($agent_id->agent_ga == $agent_ga->agent_id) {
            // check for other existing schedule earlier than current schedule effective_date_start
            $existing_schedule = AgentUplineScheduler::where('agent_id', '=', $request['agent_id'])
                ->whereDate('effective_date_start', '<', $request['effective_date'])
                ->where('agent_ga', '!=', $agent_id->agent_ga);
            if (! $existing_schedule->exists()) {
                return response()->json(
                    new ErrorResource([
                        'statusCode' => Response::HTTP_CONFLICT,
                        'message' => 'Requested Upline ID is same as current Upline ID.'
                    ]), Response::HTTP_CONFLICT
                );
            }
        }

        $checkRepLevel = $this->checkUplineLevel($agent_id, $agent_ga);
        $checkUplineStatus = $this->checkUplineStatus($agent_ga);

        if (!$checkRepLevel)
            return response()->json(
                new ErrorResource([
                    'statusCode' => Response::HTTP_CONFLICT,
                    'error' => "Downline Level: {$agent_id->agent_level}, Upline Level: {$agent_ga->agent_level}",
                    'message' => 'Selected Upline Rep level must be greater than current rep level.'
                ]), Response::HTTP_CONFLICT
            );

        if (!$checkUplineStatus)
            return response()->json(
                new ErrorResource([
                    'statusCode' => Response::HTTP_CONFLICT,
                    'message' => 'Selected Upline Rep Status must be active.'
                ]), Response::HTTP_CONFLICT
            );

        DB::beginTransaction();
        try {
            $schedule = AgentUplineScheduler::firstOrNew([
                'agent_id' => $request['agent_id'],
                'effective_date_start' => $request['effective_date']
            ]);
            $schedule->agent_ga = $request['agent_ga'];
            $schedule->schedule_type = $request['schedule_type'];
            $schedule->save();

            if($request['schedule_type'] === 'instant'){
                $command = "update:agent-upline-assign";
                $arguments = [
                    'date' => $request['effective_date'],
                    '--report' => true,
                ];
                $result = Artisan::call($command, $arguments);
                if ($result !== 0) {
                    return response()->json(
                        new ErrorResource([
                            'statusCode' => Response::HTTP_CONFLICT,
                            'message' => 'Error while updating the upline instantly.'
                        ]), Response::HTTP_CONFLICT
                    );
                }
            }

            $message = "New Upline Agent ({$schedule->upline->full_name} - Code: {$schedule->upline->agent_code}) " .
            ($request['schedule_type'] === 'scheduled' ? 'change scheduled' : 'changed instantly') .
            " at {$schedule->effective_date_start->format('m/d/Y')}.";

            $this->createAgentUpdate($request, \App\AgentUpdate::ACT_UPLINE, $message);
            DB::commit();
            if($returnResponse){
                return response()->json(
                    new SuccessResource([
                        'statusCode' => Response::HTTP_OK,
                        'message' => $message,
                    ]), Response::HTTP_OK
                );
            }
        }
        catch (\Exception $e) {
            DB::rollBack();

            $error_code = in_array($e->getCode(), Response::$statusTexts, true)
                ? $e->getCode()
                : Response::HTTP_UNPROCESSABLE_ENTITY;
            return response()->json(
                new ErrorResource([
                    'statusCode' => $error_code,
                    'error' => $e->getMessage(),
                    'message' => 'Failed to schedule Upline change.'
                ]), $error_code
            );
        }
    }

    public function getScheduledUpdateUplineRep($request)
    {
        $schedules = AgentUplineScheduler::where('agent_id', '=', $request['agent_id'])
            ->where('schedule_type', '!=', 'instant')
            ->orderBy('effective_date_start', 'asc')
            ->get(['id', 'agent_ga', 'effective_date_start', 'created_at'])
            ->map(function($eachSchedule) {
                return [
                    'id' => $eachSchedule->id,
                    'upline' => [
                        'id' => $eachSchedule->upline->agent_id,
                        'name' => $eachSchedule->upline->full_name,
                        'code' => $eachSchedule->upline->agent_code,
                        'level' => $eachSchedule->upline->agent_level,
                    ],
                    'effective_date_start' => $eachSchedule->effective_date_start->format('m/d/Y'),
                    'created_at' => $eachSchedule->created_at->format('m/d/Y'),
                ];
            })->all();

        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Scheduled Data fetched successfully.',
                'data' => $schedules
            ]), Response::HTTP_OK
        );
    }

    protected function checkUplineLevel($agentInfo, $requestedUpline)
    {
        if ($agentInfo->agent_level <= $requestedUpline->agent_level) {
            return true;
        } else {
            return false;
        }
    }

    protected function checkUplineStatus($requestedUpline)
    {
        if ($requestedUpline->agent_status == AgentInfo::STATUS_APPROVED) {
            return true;
        } else {
            return false;
        }
    }

    protected function sendUpdatedUpineEmail($agentInfo)
    {
        $newUpline = $agentInfo->getAgentGa;
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $contentData = "<h2>Hello {$agentInfo->fullname}</h2>";
        $contentData .= "<p>As requested , your upline change is complete.</p>";
        $contentData .= "<p>";
        $contentData .= "Please contact {$newUpline->fullname} ({$newUpline->agent_email})";
        $contentData .= " for any questions and have a training session to understand our products and services better.";
        $contentData .= "</p>";
        $contentData .= "<p>We look forward to helping you build a strong business.</p>";
        $contentData .= "Best Wishes,<br/>";
        $contentData .= "Elevate Wellness Assocation";
        $emailConfigurationName = "REPRESENTATIVE_UPLINE_CHANGE_NOTIFICATION";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Representative Upline Change.",
            'contentTemplate' => $contentData,
        ];
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentTemplate($emailData);
    }


    public function getAgentGroups($agentId, $filters = [])
    {
        $agentInfo = AgentInfo::find($agentId);
        if (!$agentInfo) return $this->failedResponse("No Agent Found");
        $agentGroupIds = $agentInfo->getGroup()->pluck('gid');
        $query = GroupInfo::query()
            ->whereNotIn('gid', $agentGroupIds)
            ->where('gstatus', '=', GroupInfo::STATUS_ACTIVE);
        if (isset($filters['query'])) {
            $query->where(function ($subQuery) use ($filters) {
                $subQuery->orWhere('gcode', 'LIKE', '%' . $filters['query'] . '%');
                $subQuery->orWhere('gname', 'LIKE', '%' . $filters['query'] . '%');
                $subQuery->orWhere('gcontact_fname', 'LIKE', '%' . $filters['query'] . '%');
                $subQuery->orWhere('gcontact_lname', 'LIKE', '%' . $filters['query'] . '%');
                $subQuery->orWhere('gtype', 'LIKE', '%' . $filters['query'] . '%');
            });
        }
        $groups = $query->get();
        $data = [];
        foreach ($groups as $d) {
            $data[] = [
                'gid' => $d->gid,
                'gcode' => $d->gcode,
                'gname' => $d->gname,
                'gcontact_fname' => $d->gcontact_fname,
                'gcontact_lname' => $d->gcontact_lname,
                'gtype' => $d->gtype,
                'formatted_gname' => "{$d->gcode} - {$d->gname}"
            ];
        }
        return $this->successResponse('Success', $data);
    }

    public function addAgentGroup($request)
    {
        $agentInfo = AgentInfo::find($request['agent_id']);
        $gids = $request['gids'];
        DB::beginTransaction();
        try {
            $count = $this->addAgentInGroup($gids, $agentInfo);
            if ($count['count'] > 0) {
                $successMessage = "{$count['count']} group(s) added successfully.";
                //update to agent_updates
                $this->createAgentUpdate($request, \App\AgentUpdate::ACT_ADD_GROUP, $successMessage);
                //sendEmail
                $this->sendAddGroupEmail($agentInfo, $count['addedGids']);

                DB::commit();
                return $this->successResponse($successMessage);
            } else {
                DB::rollBack();
                return $this->failedResponse('This group(s) are already added.', 409);
            }
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to add group(s).');
        }

    }

    protected function getGroup($gId)
    {
        return GroupInfo::find($gId);
    }

    protected function checkAgentGroupExist($gid, $agentId)
    {
        return AgentGroup::query()
            ->where([
                'agent_id' => $agentId,
                'gid' => $gid
            ])
            ->exists();
    }

    protected function addAgentInGroup($gids, $agentInfo)
    {
        $count = 0;
        $addedGids = [];
        foreach ($gids as $g) {
            $groupInfo = $this->getGroup($g);
            $data = [
                'agent_id' => $agentInfo->agent_id,
                'gid' => $groupInfo->gid,
                'ts' => time(),
                'isadmin' => isset($agentInfo->admin_only) ? $agentInfo->admin_only : 0,
                'lvl' => $agentInfo->agent_level,
                'ag_status' => $agentInfo->agent_status,
                'lvl2' => $agentInfo->agent_level_2,
            ];
            $checkAgentInGroup = $this->checkAgentGroupExist($groupInfo->gid, $agentInfo->agent_id);
            if (!$checkAgentInGroup) {
                $agentInGroup = AgentGroup::create($data);
                array_push($addedGids, $agentInGroup->gid);
                $count++;
            }
        }
        return [
            'count' => $count,
            'addedGids' => $addedGids
        ];
    }

    protected function getAgentUserActivityQuery($agentId)
    {
        return UserActivityDetail::query()
            ->where([
                'user_id' => $agentId,
                'user_type' => UserActivityDetail::USER_TYPE_BROKER
            ]);
    }

    public function getAgentUserActivityDetail($agentId)
    {
        $agent = AgentInfo::find($agentId);
        if (!$agent) return $this->failedResponse('No Agent found.');
        $data = $this->getAgentUserActivityQuery($agentId)->first();
        if (!$data) return $this->failedResponse('No Agent Activity found.');
        $data['formatted_act_time'] = Carbon::parse($data->act_time)->format('m/d/Y H:i:s');
        return $this->successResponse('Success', $data);
    }

    public function editAgentImage($request)
    {
        $agentInfo = AgentInfo::find($request['agent_id']);
        $image = $request['agent_img'];
        DB::beginTransaction();
        try {
            $imageName = $this->uploadAgentImage($image, $agentInfo->agent_img);
            if (isset($imageName)) {
                $agentInfo->update([
                    'agent_img' => $imageName
                ]);
            }
            $successMessage = "Image uploaded successfully.";
            //update to agent_updates
            $this->createAgentUpdate($request, \App\AgentUpdate::ACT_IMAGE, $successMessage);
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to upload image.');
        }
    }

    protected function removeAgentImage($imageName)
    {
        $previousFileNamePath = "/img/agents/{$imageName}";
        if (Storage::disk('corenroll')->exists($previousFileNamePath)) {
            Storage::disk('corenroll')->delete($previousFileNamePath);
        }
        $s3PreviousFileNamePath = "/agent/logo/{$imageName}";
        if (Storage::disk('s3-third')->exists($s3PreviousFileNamePath)) {
            Storage::disk('s3-third')->delete($s3PreviousFileNamePath);
        }
    }

    protected function uploadAgentImage($image, $previousFileName = "")
    {
        $imageName = '';
        if ($image instanceof UploadedFile) {
            $fileName = pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME);
            $ext = $image->getClientOriginalExtension();
            $time = time();
            $imageName = "{$fileName}-{$time}.{$ext}";
            //checking and deleting previous file.
            if ($previousFileName) {
                $this->removeAgentImage($previousFileName);
            }
            //uploading new file
            $s3FilePath = "/agent/logo/{$imageName}";
            Storage::disk('corenroll')->put('/img/agents/' . $imageName, file_get_contents($image));
            Storage::disk('s3-third')->put($s3FilePath, file_get_contents($image));
        }
        return $imageName;
    }


    public function checkIfAgentHaveActiveDownlines($agent_id){
        try{
            $query =  AgentInfo::withTrashed()->select(DB::raw('concat(agent_fname," ",agent_lname) as agent_name'))->where('agent_ga', '=', $agent_id)
                ->orderBy('agent_id', 'DESC')->pluck('agent_name')->toArray();

            $agent_ids = AgentInfo::withTrashed()->where('agent_ga', '=', $agent_id)
                ->orderBy('agent_id', 'DESC')->pluck('agent_id')->toArray();
            $agent_ga = AgentInfo::where('agent_id',$agent_id)->where('agent_status',AgentInfo::STATUS_APPROVED)->first();
            $agentGaDetails = $agent_ga ? AgentInfo::where('agent_id',$agent_ga->agent_ga)->first() : null;
            $details = [
                'active_agents' => $query,
                'active_members' => PlanOverview::where('p_agent_num',$agent_id)->exists(),
                'agent_ids' => $agent_ids,
                'agent_ga' =>  $agentGaDetails ? $agentGaDetails->agent_id : 20,
                'agent_ga_name' => $agentGaDetails ? $agentGaDetails->agent_fname . ' ' . $agentGaDetails->agent_lname : 'Nuera Admin'
            ];
            return $this->successResponse('Success', $details);
        }catch(Exception $e){
            return $this->failedResponse("Something went wrong");
        }
    }

    public function changeAgentUpline($agent_ga,$agent_ids){
        try{
            $eff_date = date('d') > 10 ? date('Y-m-01') : date("Y-m-01", strtotime("-1 months"));
            foreach($agent_ids as $id){
                $request = [
                    'agent_id' => $id,
                    'agent_ga' => $agent_ga,
                    'schedule_type' => 'instant',
                    'effective_date' => $eff_date
                ];
                // return $request;
                $this->scheduleUpdateUplineRep($request,false);
            }
            return $this->successResponse('Success');
        }catch(Exception $e){
            DB::rollBack();
            return $this->failedResponse("Something went wrong");
        }
    }

    public function deleteAgent($request)
    {
        $agentInfo = AgentInfo::where('agent_id', '=', $request['agent_id'])
//            ->where(function ($subQuery) {
//                $subQuery->orWhere('agent_fname', 'LIKE', '%test%');
//                $subQuery->orWhere('agent_lname', 'LIKE', '%test%');
//            })
            ->first();

        //temp check test agent
        if (!isset($agentInfo)) {
            return $this->failedResponse('Invalid Agent ID. The Agent does not exist.');
        }
        DB::beginTransaction();
        try {
            if(PlanOverview::where('p_agent_num',$request['agent_id'])->exists()){
                $agentGaInfo = AgentInfo::where('agent_id',$agentInfo->agent_ga)->first();
                $clientUpdateModel = new ClientDetailUpdate();
                $policies = Policy::where('p_agent_num',$request['agent_id'])->get();
                $eff_date = date('d') > 10 ? date('Y-m-01') : date("Y-m-01", strtotime("-1 months"));
                foreach($policies as $p){
                    $sendPayload = [
                        'agent_code' => $agentGaInfo->agent_code,
                        'elgb_new_effdate' => $eff_date,
                        'policy_id' => $p->policy_id,
                        'reason' => 'agent removed'
                    ];
                    $clientUpdateModel->UpdatePolicyAgent((object) $sendPayload,false);
                }
            }
            //deleting groups
            $this->deleteCollection($agentInfo->agentGroups);
            //deleting licenses
            $this->deleteCollection($agentInfo->getLicense);
            // update downline agent agent_ga to null
            // no change to the agent_ga since agents are soft-deleted
//            if ($agentInfo->downlineAgents()->exists()) {
//                foreach ($agentInfo->downlineAgents as $d) {
//                    if ($d instanceof AgentInfo) {
//                        $d->update(['agent_ga' => null]);
//                    }
//                }
//            }
            //delete personal addresses
            $this->deleteCollection($agentInfo->personalAddresses);
            //delete agent business
            if (isset($agentInfo->agentBusiness)) {
                $agentInfo->agentBusiness->delete();
            }
            //delete business addresses
            $this->deleteCollection($agentInfo->businessAddresses);
            //delete check payment
            if (isset($agentInfo->agentCheckPayment)) {
                $agentInfo->agentCheckPayment->delete();
            }
            //delete chequeAddresses
            $this->deleteCollection($agentInfo->chequeAddresses);
            //delete achPayment
            if (isset($agentInfo->achPayment)) {
                $agentInfo->achPayment->delete();
            }
            //delete achPaymentBankDetails
            $this->deleteCollection($agentInfo->achPaymentBankDetails);
            //remove agent image from corenroll
            // if ($agentInfo->agent_image) {
            //     $this->removeAgentImage($agentInfo->agent_image);
            // }
            //delete agent

            // AgentUser::where('agent_id',$request['agent_id'])->update(['status'=> 'R']);
            $this->archiveRepository->deleteAgent($request['agent_id']);
            $agentInfo->delete();

            DB::commit();
            // $this->removeRepSSO($agentInfo->agent_id);
            return $this->successResponse('Agent deleted successfully.');
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to delete agent.');
        }
    }

    protected function deleteCollection($collection)
    {
        if (isset($collection)) {
            foreach ($collection as $c) {
                $c->delete();
            }
        }
    }

    protected function agentPersonalRequestInfoData($request)
    {   $customValidationService = new CustomValidationService();
        $agentInfo = [
            "agent_fname" => $request['agent_fname'],
            "agent_lname" => $request['agent_lname'],
            "agent_email" => $request['agent_email'],
            "agent_phone2" => $request['agent_phone2'],
            "agent_dob" => $request['agent_dob'],
            "agent_ssn" => $request['agent_ssn'],
        ];
            $agentInfo["agent_phone1"] = $request['agent_phone1'];
            $agentInfo["is_phone1_valid"] = $request['agent_phone1'] ?  'VALID' : false;
            $agentInfo["phone1_line_type"] = $request['agent_phone1'] ? 'mobile' : null;
            $agentInfo["agent_phone2"] = $request['agent_phone2'];
            $agentInfo["is_phone2_valid"] = $request['agent_phone2'] ? 'VALID' : false;
            $agentInfo["phone2_line_type"] = $request['agent_phone2'] ? $customValidationService->validatePhoneNumVerify( $request['agent_phone2'], true)['data']['line_type'] : null;
        return $agentInfo;
    }

    protected function agentPersonalRequestInfoDataNuera($request)
    {   $customValidationService = new CustomValidationService();
        $agentInfo = [
            "agent_fname" => $request['first_name'],
            "agent_lname" => $request['last_name'],
            "agent_email" => $request['email'],
            "agent_phone1" => $request['mobile_number'],
            "agent_zip" => $request['zip_code'],
            "agent_city" => $request['city'],
            "agent_state" => $request['state'],
            "agent_address_1" => $request['address_1'],
        ];
            $agentInfo["is_phone1_valid"] = $request['agent_phone1'] ?  'VALID' : false;
            $agentInfo["phone1_line_type"] = $request['agent_phone1'] ? 'mobile' : null;
        return $agentInfo;
    }

    protected function createAgentUpdate($request, $act = "", $comment = "")
    {
        $data = [
            "agent_id" => $request['agent_id'],
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent_id" => $request['loginUserId'] ?? request()->header('id'),
            "elgb_agent_name" => $request['loginUserName'] ?? request()->header('name'),
            "changed_from" => $request['changed_from'] ?? null,
            "changed_to" => $request['changed_to'] ?? null
        ];
        AgentUpdatesHelper::createAgentUpdateLog($data);
    }

    public function editPersonalInfo($request)
    {
        if(!$request['agent_phone1'] && !$request['agent_phone2']){
            return $this->failedResponse("Enter at least one contact number (Mobile or Phone).");
        }
        $agentInfo = AgentInfo::find($request['agent_id']);
        $existingEmail = $agentInfo->agent_email;

        if($existingEmail !== $request['agent_email'] && $this->checkAlreadyExists($request['agent_email'])){
            return $this->failedResponse("Email already exists.");
        }
        DB::beginTransaction();
        try {
            $data = $this->agentPersonalRequestInfoData($request);
            $agentInfo->update($data);
            $syncResp = $this->syncRepSSO($request['agent_email'], $request['agent_phone1'] , $existingEmail);
            $agent_users = AgentUser::query()->where('agent_id', $request['agent_id'])->first();
            if($agent_users){
                $agent_users->phone = $request['agent_phone1'];
                $agent_users->username = $request['agent_email'];
                $agent_users->update();
            }else{
                DB::rollBack();
                return $this->failedResponse("Agent doesn't exist in Agent User");
            }
            $successMessage = "Agent's Personal information updated successfully.";
            $agentUpdateMessage = $successMessage;
            if (!$syncResp) {
                $agentUpdateMessage .= " SSO doesn't exist for this agent, hence info didn't synced.";
            }
            //update to agent_updates
            $this->createAgentUpdate($request, \App\AgentUpdate::ACT_PERSONAL_INFO, $agentUpdateMessage);
            //send email
            $this->sendPersonalInfoEmail($agentInfo);
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->failedResponse($e->getMessage());
        }
    }

    protected function sendPersonalInfoEmail($agentInfo)
    {
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your personal information has been updated on " . $date . ".";
        if(isset($agentInfo->agentCustomeHomepage)) {
            $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agentCustomeHomepage->email;
            $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agentCustomeHomepage->email;
            $agentEmail = $agentInfo->agentCustomeHomepage->email;
            $b1 = $agentInfo->agentCustomeHomepage->phone;
        } else {
            $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
            $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
            $agentEmail = $agentInfo->agent_email;
            $b1 = $agentInfo->agent_phone1;
        }
        $contentData = [
            'Name' => $agentInfo->fullname,
            'Email' => $agentEmail,
            'Phone' => CommonHelper::format_phone($b1),
            'SSN' => CommonHelper::maskSsn($agentInfo->agent_ssn),
            'Dob' => CommonHelper::maskDob($agentInfo->agent_dob),
        ];
        $emailConfigurationName = "AGENT_INFO_CHANGE";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Agent Personal Information Update",
            'message' => $message,
            'data' => $contentData,
        ];
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    protected function sendAddGroupEmail($agentInfo, $gids)
    {
        ;
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that following groups are added " . $date . ".";
        $groups = AgentInGroup::query()
            ->where('agent_id', '=', $agentInfo->agent_id)
            ->whereIn('gid', $gids)
            ->select(['gid', 'gcode', 'gname'])
            ->get();
        $groupNames = [];
        foreach ($groups as $group) {
            $groupNames[] = "{$group->gcode} - {$group->gname}";
        }
        $formattedGroupNames = implode("<br/>", $groupNames);
        $contentData = [
            'Agent Name' => $agentInfo->fullname,
            'Groups' => $formattedGroupNames
        ];
        $emailConfigurationName = "AGENT_INFO_CHANGE";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Agent Group Added",
            'message' => $message,
            'data' => $contentData,
        ];
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    protected function getAgentIdsFromProduct($products)
    {
        $productArr = explode(",", $products);
        $agentIds = PlanOverview::where('pstatus', 1)
            ->whereIn('pl_type', $productArr)
            ->pluck('p_agent_num');
        return $agentIds;
    }

    public function getDownlineAgentsExists($agentID)
    {
        $agents = AgentInfo::where('agent_ga', $agentID)->exists();
        return (int)$agents;
    }

    public function getAgentUpdates($agentId)
    {
        $agent = AgentInfo::find($agentId);
        if (!$agent) return $this->failedResponse('Agent not found');
        $data = \App\AgentUpdate::query()
            ->where('agent_id', '=', $agentId)
            ->orderBy('elgb_id', 'DESC')
            ->paginate(10);
        $result = [];
        foreach ($data as $d) {
            $result [] = [
                "elgb_id" => $d->elgb_id,
                "agent_id" => $d->agent_id,
                "elgb_act" => $d->elgb_act ?: "N/A",
                "elgb_act_date" => Carbon::createFromTimestamp($d->elgb_act_date)->format('m/d/Y') ?: "N/A",
                "elgb_act_date_original" => $d->elgb_act_date,
                "elgb_comment" => $d->elgb_comment ?: "N/A",
                "elgb_agent" => $d->elgb_agent_id ?: "N/A",
                "elgb_agentname" => $d->elgb_agent_name ?: "N/A",
            ];
        }
        $paginatedData = [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
        try {
            return $this->successResponse('Success', $paginatedData);
        } catch (\Throwable $th) {
            return $this->failedResponse("Failed to fetch data.");
        }
    }

    protected function checkContract($agentId)
    {
        $url = config('corenroll.corenroll_api_url') . "api/v2/contract/{$agentId}/check";
        $responseJson = GuzzleHelper::getApi($url, []);
        $response = json_decode($responseJson, true);
        if ($response['success'] == true) {
            return $response['data']['hasContract'];
        } else {
            return false;
        }
    }

    protected function hasW9Contract($agentId)
    {
        $url = config('corenroll.corenroll_api_url') . "api/v2/hasW9Contract/{$agentId}/check";
        $responseJson = GuzzleHelper::getApi($url, []);
        $response = json_decode($responseJson, true);
        if ($response['success'] == true) {
            return $response['data']['hasContract'];
        } else {
            return false;
        }
    }

    /**
     * @param $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * updating agent info
     */
    public function updateWebsite($request)
    {
        $webAccessCode = $request['agent_web'];
        /**
         * @var $agentInfo AgentInfo
         */
        $agentInfo = AgentInfo::find($request['agent_id']);
        $previousWebAccessCode = $agentInfo->agent_web;

        DB::beginTransaction();
        try {
            $agentInfo->agent_web = $webAccessCode;
            $agentInfo->agent_web_dental = $webAccessCode;
            $agentInfo->agent_web_medical = $webAccessCode;
            $agentInfo->oscar_web = $webAccessCode;

            if ($agentInfo->isDirty(['agent_web', 'agent_web_dental', 'agent_web_medical', 'oscar_web'])) {
                $agentInfo->save();

                $successMessage = "Personalized sites web access code - {$previousWebAccessCode} updated to {$agentInfo->agent_web} successfully.";
                $this->createAgentUpdate($request, \App\AgentUpdate::ACT_PERSONAL_INFO, $successMessage);

                //send email
                $agentService = new AgentService();
                $agentService->sendAgentWebsiteEmail($agentInfo);
            } else {
                $successMessage = 'Provided Web access code is same as existing. No change made.';
            }

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to update.');
        }
    }

    /**
     * @param $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * removing group from agents
     * removing from agent_group table
     */
    public function removeGroup($request)
    {
        $agentGroup = AgentGroup::query()
            ->where([
                ['agent_id', '=', $request['agent_id']],
                ['gid', '=', $request['group_id']]
            ])
            ->first();
        if (!$agentGroup) return $this->failedResponse('Agent Group not found');
        $agentInfo = AgentInfo::find($request['agent_id']);
        $groupInfo = GroupInfo::find($request['group_id']);
        DB::beginTransaction();
        try {
            //remove from agent group
            $agentGroup->delete();
            //update to agent_updates
            $successMessage = "Group - {$groupInfo->gname} removed successfully.";
            $this->createAgentUpdate($request, \App\AgentUpdate::ACT_ADD_GROUP, $successMessage);

            //send email
            $agentService = new AgentService();
            $agentService->sendGroupRemoveEmail($agentInfo, $groupInfo);
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to remove group');
        }
    }

    public static function updateAncillaryInAgentInfo($level, $preLevel, $agentId)
    {
        $agent = AgentInfo::where('agent_id', $agentId)->first();
        if ($agent) {
            $agent->agent_level_2 = $level;
            $agent->agent_level = $preLevel;
            $agent->save();
        }

    }

    public function updateDisplaySetting($displayData){
        DB::beginTransaction();
        try {
        AgentInfo::where('agent_id', $displayData['agent_id'])->update([
            'agent_web_name' => $displayData['display_name'],
        ]);
        CustomeHomepage::firstOrCreate(
            ['agent_id' => $displayData['agent_id']],[
            'phone' => $displayData['display_phone'],
            'email' => $displayData['display_email'],
            'banner_text' => $displayData['banner_text'],
            'help_text' => $displayData['help_text'],
            'tagline' => $displayData['tagline']
        ]);
        $this->createAgentUpdate($displayData, \App\AgentUpdate::AGENT_DISPLAY_SETTING, "Agent Display Setting for {$displayData['agent_id']} has changed");
        DB::commit();
        return $this->successResponse('Display Setting has been updated sucessfully');
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to Update Display Setting');
        }
    }

    public function singleExportFormattedItem($data)
    {
        $result = [];
        foreach ($data as $d) {
            $totals = isset($d->getTotals) ? $this->getTotalData($d->getTotals) : null;
            $licenses = isset($d->getLicense) ? $this->licenseData($d->getLicense) : null;
            $groups = isset($d->getGroup) ? $d->getGroup : null;
            $upline = isset($d->getAgentGa) ? $this->uplineData($d->getAgentGa) : null;
            $benefitStoreUrl = config('app.benefit_store_url') . "agent-home?agent_id=" . base64_encode($d->agent_id);
            $contracts = $this->getContractLevels($d->agent_id);
            $result[]=  [
                'agent_id' => $d->agent_id,
                'agent_code' => $d->agent_code,
                'agent_fname' => $d->agent_fname,
                'agent_mname' => $d->agent_mname,
                'agent_lname' => $d->agent_lname,
                'agent_fullname' => $d->fullname,
                'agent_email' => $d->agent_email,
                'agent_ga' => $d->agent_ga,
                'agent_signup_date' => Carbon::createFromTimestamp($d->agent_signup_date)->format('m/d/Y'),
                'agent_level' => $d->agent_level,
                'agent_status' => $d->agent_status,
                'formatted_agent_status' => isset($d->agent_status) ? array_search($d->agent_status, AgentInfo::$statuses) : null,
                'agent_phone1' => $d->agent_phone1,
                'agent_phone2' => $d->agent_phone2,
                'admin_only' => $d->admin_only,
                'agent_image' => $d->full_image,
                'benefit_store_url' => $benefitStoreUrl,
                'upline' => $upline,
                'groups' => $groups,
                'licenses' => $licenses,
                'totals' => $totals,
                'contracts' => $contracts
            ];
        }
        return $result;
    }


    public function saveBusinessInfo($request){
        try{
            $body['hoursb'] = $request['to'];
            $body['hoursa'] = $request['from'];
            $body['hoursatc'] = $request['sat_open'];
            $body['hoursata'] = $request['sat_from'];
            $body['hoursatb'] = $request['sat_to'];
            $body['hoursuna'] = $request['sun_from'];
            $body['hoursunb'] = $request['sun_to'];
            $body['hoursunc'] = $request['sun_open'];
            $body['agent_bio'] = $request['bio'];
            $body['agent_timezone'] = $request['timezone'];
            AgentInfo::where('agent_id', $request['agent_id'])->update($body);
            $data = [
                "agent_id" => $request->agent_id,
                "elgb_act" => \App\AgentUpdate::ACT_BUSINESS_INFO,
                "elgb_comment" => 'Business Info Updated Sucessfully',
                "elgb_act_date" => time(),
                "elgb_agent_id" => isset($request['loginUserId']) ? $request['loginUserId'] : null,
                "elgb_agent_name" => isset($request['loginUserName']) ? $request['loginUserName'] : null
            ];
            AgentUpdatesHelper::createAgentUpdateLog($data);
            return $this->successResponse('Info updated successfully.');


    } catch (\Exception $e) {
        return $this->failedResponse($e);
    }
    }

    private function getLatestNoteColor($agentId)
    {
        $latestNote = AgentNote::where('agent_id', '=', $agentId)->orderBy('aid', 'desc')->first();
        try {
            return isset($latestNote)
                ? PolicyNote::$colors[ array_search($latestNote['status'], PolicyNote::$statuses, false) ]
                : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    function getAgentDetail($id){
        return AgentInfo::where('agent_id',$id)->select('agent_email as email', 'agent_phone1 as phone', 'agent_code as code', 'agent_fname as fname', 'agent_mname as mname', 'agent_lname as lname')->first();
    }


    public function restoreTrashedAgent($agent_id)
    {
        try{
            DB::beginTransaction();
            $agentInfo = AgentInfo::withTrashed()->where('agent_id', $agent_id)->first();
            if($agentInfo) {
                $existingAgent = AgentInfo::where(function($query) use ($agentInfo) {
                    $query->where('agent_email', $agentInfo->agent_email)
                          ->orWhere('agent_phone1', $agentInfo->agent_phone1);
                })
                ->where(function($query) {
                    $query->where('agent_status', 'A')
                          ->orWhere('agent_status', 'P');
                })
                ->first();

                if ($existingAgent) {
                    $message = $existingAgent->agent_email == $agentInfo->agent_email ?
                        'email is already used by another agent' :
                        'agent phone is already used by another agent';
                    return $this->failedResponse('Failed to restore agent as the ' . $message . '.');
                }

                if(isset($agentInfo->agent_level) &&  $agentInfo->agent_level > 0 || isset($agentInfo->agent_level_2) && $agentInfo->agent_level_2 > 0) {
                    $status = "A";
                } else {
                    $status = "P";
                }
                $agentInfo->update(['agent_status' => $status, 'deleted_at' => null]);
            //     AgentUser::where('agent_id',$agent_id)->update(['status'=> $status]);

                $agentInfo->restore();

                //restore agent groups
                $agentInfo->agentGroups()->restore();
                $agentInfo->agentGroups()->rawUpdate(['ag_status'=>$agentInfo->status]);

                //restore liscence
                $agentInfo->getLicense()->restore();
                $agentInfo->getLicense()->rawUpdate(['license_status'=>$agentInfo->status,'license_deleted' => 0]);

                //restore personal details
                $agentInfo->personalAddresses()->restore();
                $agentInfo->personalAddresses()->rawUpdate(['active' => 1]);

                //restore agent buisness
                $agentInfo->agentBusiness()->restore();

                //restore business addresses
                $agentInfo->businessAddresses()->restore();

                //restore check payment
                $agentInfo->agentCheckPayment()->restore();

                $agentInfo->chequeAddresses()->restore();

                $agentInfo->achPayment()->restore();

                //restore achPaymentBankDetails
                $agentInfo->achPaymentBankDetails()->restore();

                $this->archiveRepository->restoreAgent($agent_id);
                DB::commit();
            }

            return $this->successResponse('Agent restored successfully.');
        }catch(Exception $e){
            return $e->getMessage();
            DB::rollBack();
            return $this->failedResponse('Failed to restore agent.');
        }
    }

    public function getAgentCommissionDetails($agent_id,$request)
    {
        try{
            if(date('d') > 10){
                $currentMonth = date('F',strtotime(date('Y-m-d') .' -1 month'));
                $pt_date = date('Y-m-t',strtotime(date('Y-m-d') .' -1 month'));
            }else{
                $currentMonth = date('F',strtotime(date('Y-m-d') .' -2 month'));
                $pt_date = date('Y-m-t',strtotime(date('Y-m-d') .' -2 month'));
            }
            $user_id = isset($request->userid) ? $request->userid : '';
            if($agent_id == 605 && $user_id == config('commission.user_id')){
                return $this->successResponse('Success',[]);
            }
            $year = date('Y');
            $NbComm = NbCommissionDataSummary::where('agent_id',$agent_id);
            $data = $NbComm->where('paid_through_date', 'like', "$year%")->get()->toArray();
            $latestData = $NbComm->where('paid_through_date',"$pt_date")->first();
            $total = $latestData->total + $latestData->adjustments_credit - $latestData->adjustment_debit - $latestData->refunds;
            $totalYearCommission = array_sum(array_column($data,'total'));
            $totalCredit = array_sum(array_column($data,'adjustments_credit'));
            $totalDebit = array_sum(array_column($data,'adjustment_debit'));
            $totalRefunds = array_sum(array_column($data,'refunds'));
            $finalYear = $totalYearCommission + $totalCredit - $totalDebit - $totalRefunds;
            return $this->successResponse('Success',[
                'current_month' => $currentMonth,
                'current_month_commission' => $latestData ? number_format($total,2) : '0.00',
                'total_commission' => $totalYearCommission ? number_format($finalYear,2) : '0.00'
            ]);
        }catch(Exception $e){
            return $this->failedResponse('Failed to fetch data.');
        }
    }

    public function changeAgentPassword($request)
    {
        try{
        $user = SsoUsers::where([['email',$request->agent_email],['user_type','A']])->first();
        $agent = AgentUser::where('username', $user->email)->first();
            $user->password = Hash::make($request->confirm_password);
            if ($user->save()) {
                if ($agent) {
                    $agent->password = $user->password;
                    if ($agent->save()) {
                        AgentInfo::where('agent_id', $agent->agent_id)->update(['agent_password' => $user->password]);
                        return $this->successResponse('Password updated successfully.');
                    }
                }
            }
            return $this->failedResponse('Password update failed.');
        }catch(Exception $e){
            return $this->failedResponse($e->getMessage());
        }
    }

    public function onboardingSyncNeuraAcm($request){
        DB::beginTransaction();


        try {
            // Find agent info
            $agentInfo = AgentInfo::where('agent_email', $request->email)->firstOrFail();
            $existingEmail = $agentInfo->agent_email;
            // Update agent personal info
            if($agentInfo){
                $data = $this->agentPersonalRequestInfoDataNuera($request);
                $agentInfo->update($data);
                $syncResp = $this->syncRepSSO($request['agent_email'], $request['agent_phone1'] , $existingEmail);
                $agent_users = AgentUser::query()->where('agent_id', $agentInfo->agent_id)->first();
                if($agent_users){
                    $agent_users->phone = $data['agent_phone1'];
                    $agent_users->update();
                }else{
                    DB::rollBack();
                    return $this->failedResponse("Agent doesn't exist in Agent User");
                }
                if (!$syncResp) {
                    DB::rollBack();
                    return $this->failedResponse("Agent does't exist in sso");
                }
                // Upload agent image
                if ($request->hasFile('image')) {
                    $imageName = $this->uploadAgentImage($request->file('image'), $agentInfo->image);
                    $agentInfo->update(['agent_img' => $imageName]);
                }

                // Commit transaction
                DB::commit();
                return response()->json(
                    new SuccessResource([
                        'statusCode' => JsonResponse::HTTP_OK,
                        'message' => "Agent's Personal information updated successfully",
                    ]),
                    JsonResponse::HTTP_OK
                );
            }
            else{
                return response()->json(['message' => "Agent not found."], JsonResponse::HTTP_NO_CONTENT);

            }

        } catch (Exception $e) {
            // Rollback transaction on failure
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    function getUserName($d) {
        if ($d->userid === 0 || is_null($d->userid)) {
            return 'Rep Dashboard';
        }

        if ($d->source === 'rep') {
            return $this->getRepUserName($d->userid);
        }

        return $this->getAdminUserName($d->userid);
    }

    function getRepUserName($userId) {
        $repDetails = AgentInfo::query()->where('agent_id', '=', $userId)->first();

        return $repDetails ? trim("{$repDetails->agent_fname} {$repDetails->agent_mname} {$repDetails->agent_lname}") : '';
    }

    function getAdminUserName($userId) {
        $userDetails = AdminUser::find($userId);
        $userName = null;

        if ($userDetails) {
            $userName = SsoUsers::where('email', $userDetails->email)->where('user_type','ADMIN')->first();
        }

        return $userName->name ?? '';
    }

    public function getContractNameList()
    {
        $dataCollection = [];
        $contractLevels = [];
        $data = RepContract::query()
            ->with(['repContractDetail'])
            ->groupBy('contract_display_name', 'contract_type')
            ->get();

        if (!$data->isEmpty()) {
            foreach ($data as $contract) {
                if ($contract['contract_display_name'] === 'Non-Category Contract') {
                    foreach ($contract->repContractDetail as $detail) {
                        $contractItemDisplayName = $detail['contract_item_display_name'];
                        $displayName = $this->getContractListDisplayName($contractItemDisplayName);

                        if (!in_array($displayName, $dataCollection) && $contractItemDisplayName !== null) {
                            array_push($dataCollection, $displayName);
                            $contractLevels[$displayName] = $this->getContractListLevels($displayName);
                        }
                    }
                } else {
                    if ($contract['contract_display_name'] !== null) {
                        $displayName = $this->getContractListDisplayName($contract['contract_display_name']);
                        array_push($dataCollection, $displayName);
                        $contractLevels[$displayName] = $this->getContractListLevels($displayName);
                    }
                }
            }
        }

        return  [
                'names' => $dataCollection,
                'levels' => $contractLevels,
        ];
    }

    public function getContractListLevels($contractName)
    {
        switch ($contractName) {
            case "Smart Contract":
            case "EWA Contract (Union Only) ":
            case "GPBP (FEGLI) Contract":
            case "Category Smart-Ancillary Contract":
            case "Carrier Contract":
                return ["Level 1", "Level 2", "Level 3", "Level 4", "Level 5", "Level 6", "Level 7"];
            case "BEX Ancillary Contract":
            case "BEX Premier Contract":
                return ["Level 1", "Level 2", "Level 3", "Level 4", "Level 5", "Level 6"];
            case "Patriot Medical":
            case "Patriot Medical - Multi Level Contract":
                return ["Level 1", "Level 2", "Level 3", "Level 4", "Level 5", "Level 6", "Level 7", "Level 8", "Level 9"];
            default:
                return [];
        }
    }

    public function getContractListDisplayName($key)
    {
        $contractItemDisplayName = [
            'Category Patriot-Ancillary Contract' => 'Patriot Medical - Multi Level Contract',
            'Smart - Ancillary Contract' => 'Smart Contract',
            'Patriot Contract' => 'Patriot Medical',
            'Patriot - Ancillary Contract' => 'Patriot Medical'
        ];

        return isset($contractItemDisplayName[$key]) ? $contractItemDisplayName[$key] : $key;
    }

    public function isSwitchable($agent_email){
        $agentEmail = $agent_email;
        $adminId = request()->header('id');
        $ssoUser = SSOUsers::where('email', $agentEmail)->where('user_type', 'A')->first();
        return $ssoUser &&
               Agentuser::where('username', $agentEmail)->where('status', 'A')->exists()&&
               AdminUser::where('id', $adminId)
                       ->whereIn('username', config('app.rep_switchable_admins'))
                       ->exists();
    }
}
