<?php


namespace App\Repositories\Agents;

use App\AgentFileGeneration;
use App\Jobs\ExportExcelFileAgent;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;


class AgentReportRepository
{

    use Paginator, ResponseMessage;

    /**
     * @var ManageAgents
     */
    private $agentRepository;

    public function __construct(ManageAgents $agentRepository)
    {
        $this->agentRepository = $agentRepository;
    }

    public function generate_export($filters =[]){
        $data['added_by'] = request()->header('name');
        $data['added_by_id'] = request()->header('id');
        ExportExcelFileAgent::dispatch($filters , $data)->onQueue('processing');
    }

    public function getAgentReport($request){
        $agentFileDetail = AgentFileGeneration::fetchagentReport($request['id']);
        $agntExpLst = $agentFileDetail->map(function ($agentExpList) {
            return collect($agentExpList->toArray())
                ->only(['id', 'file_path', 'file_name', 'file_generation_started', 'file_generation_ended', 'no_of_record', 'added_by', 'added_by_id', 'created_at', 'updated_at', 'file_download_link'])
                ->all();
        });
        $paginatedData = [
            'data'=>$agntExpLst,
            'links' => $this->links($agentFileDetail),
            'meta' => $this->meta($agentFileDetail)
        ];
        return $paginatedData;  
    }
}