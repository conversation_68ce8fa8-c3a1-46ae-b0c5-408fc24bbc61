<?php

namespace App\Repositories;

use App\Helpers\EmailSendHelper;
use App\Helpers\PolicyUpdateHelper;
use App\PaymentEft;
use App\Policy;
use App\RecurringSelfPaymentInfo;
use App\UserInfo;
use App\AgentInfo;
use App\Dependent;
use App\GroupInfo;
use App\PlanOverview;
use App\DependentPolicy;
use App\PpoUpload;
use App\AgentAppsUpload;
use App\UpfileLocc;
use App\UserInfoPolicyAddress;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use DB;
use DateTime;
use App\Helpers\GuzzleHelper;
use App\Helpers\SendNotificationHelper;
use App\PlanPolicy;
use App\PlanPricingDisplay;
use App\Repositories\Plans\PlanChangeOnAddress;
use App\Traits\ResponseMessage;
use stdClass;

class PolicyFeatures extends Model
{
    private  $PlanChangeOnAddress;

    public function __construct()
    {
        $this->PlanChangeOnAddress = new PlanChangeOnAddress();
    }

    public function filterChild26()
    {
        $dependents = Dependent::where('d_relate', 'C')
            ->whereRaw(" (CASE WHEN(LOCATE('/',`d_dob`) > 0)THEN TIMESTAMPDIFF(MONTH,DATE_FORMAT(STR_TO_DATE(`d_dob`,'%m/%d/%Y'),'%Y-%m-%d'),CURDATE())ELSE TIMESTAMPDIFF(MONTH,DATE_FORMAT(`d_dob`,'%Y-%m-%d'),CURDATE())END) >= 309")
            ->pluck('did');
        $planOverview = PlanOverview::where('pl_type', 'MM')->where('status', 'ACTIVE')->pluck('policy_id');

        return $this->getInfoFromDependents($dependents, $planOverview);
    }

    public function filterSpouse65()
    {
        $dependents = Dependent::where('d_relate', 'S')
            ->whereRaw('timestampdiff(month,d_dob,curdate()) >= 777')
            ->pluck('did');
        $planOverview = PlanOverview::where('pl_type', 'MM')->where('status', 'ACTIVE')->pluck('policy_id');
        return $this->getInfoFromDependents($dependents, $planOverview);
    }

    public function filterMember65()
    {
        $policy = [];
        $users = UserInfo::whereRaw('timestampdiff(month,cdob,curdate()) >= 777')
            ->pluck('userid');
        $planOverview = PlanOverview::where('pl_type', 'MM')->where('status', 'ACTIVE')->pluck('policy_id');
        $getEmpData = UserInfoPolicyAddress::whereIn('userid', $users)
            ->whereIn('policy_id', $planOverview)->where('status', 'ACTIVE')
            ->groupby('policy_id')->orderBy('policy_id', 'desc')
            ->get();
        foreach ($getEmpData as $key => $u) {
            //$groupInfo = $u->getGroup()->first();
           // if ($groupInfo->estnumemp == null && $groupInfo->estnumemp < 11) {
                $policy[$key]['policy_info'] = $u;
                $policy[$key]['plan_details'] = app('App\Policy')->getPlanOverviewFromPolicy($u->policy_id);
           // }
        }
        return $policy;
    }

    public function getInfoFromDependents($dependents, $planOverview)
    {
        $policy = [];
        $dependent = DependentPolicy::whereIn('dependent_id', $dependents)
            ->whereIn('policy_id', $planOverview)->get();
        foreach ($dependent as $key => $d) {
            $getEmpData = UserInfoPolicyAddress::where('policy_id', $d->policy_id)
                ->where('status', 'ACTIVE')->orderBy('policy_id', 'desc')->first();
            if ($getEmpData) {
                $groupInfo = $getEmpData->getGroup()->first();
                //if ($groupInfo->estnumemp == null && $groupInfo->estnumemp < 11) {
                    $policy[$key]['policy_info'] = $getEmpData;
                    $policy[$key]['dep_info'] = Dependent::where('did', $d->dependent_id)->get();
                    $policy[$key]['plan_details'] = app('App\Policy')->getPlanOverviewFromPolicy($getEmpData->policy_id);
                //}
            }
        }
        return $policy;
    }

    public function addPolicyBillDate(Request $request)
    {
        try {
            $reason = $request->reason;
            $agentID = $request->aid;
            $policyID = $request->policy_id;
            $billDate = $request->bill_date;
            $sendEmail = $request->sendEmail;
            if (!UserInfoPolicyAddress::where('policy_id', $policyID)->count()) {
                return ['error' => 'Policy not found.'];
            }
            $status = Policy::where('policy_id', $policyID)->update(['bill_date' => $billDate]);
            if ($status) {
                $data = [
                    'elgb_act' => 'BDC',
                    'elgb_act_date' => time(),
                    'elgb_agent' => $agentID,
                    'elgb_file_date' => date('Y-m-d'),
                    'elgb_term_date' => null,
                    'elgb_policyid' => $policyID,
                    'elgb_comment' => $reason ? $reason : "New billing date added to policy."
                ];
                PolicyUpdateHelper::updateEligibility($data);
                if ($sendEmail) {
                    if (EmailSendHelper::readEmailPref('BDC', 0)) {
//                        EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($policyID, 'Billing Date', "billing date has been added", 'billingdate', 'BDC');
                        EmailSendHelper::sendUserPolicyActionEmail($policyID, 'Billing Date', "billing date has been added", 'billingdate', 'BDC');
                    }
                }
                $data = ['success' => 'Policy billing date added.'];
            } else {
                $data = ['error' => 'Failed adding billing date.'];
            }
            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function getGroupPolicyListing()
    {
        try {
            $groupData = GroupInfo::where('gstatus', 1)->orderBy('gname', 'ASC')->get(['gid', 'gname']);
            return $groupData;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function updateGroupPolicy($request)
    {
        try {
            $policyID = $request->policy_id;
            $groupID = $request->gid;
            $agentID = $request->aid;
            $reason = $request->reason;
            $sendEmail = $request->sendEmail;
            if (!UserInfoPolicyAddress::where('policy_id', $policyID)->count()) {
                return ['error' => 'Policy not found'];
            }
            if (!GroupInfo::where('gid', $groupID)->where('gstatus', 1)->count()) {
                return ['error' => 'Group not found'];
            }
            $status = Policy::where('policy_id', $policyID)->update(['eid' => $groupID]);
            if ($status) {
                $this->commonAssociationCheckDetailUpdate($request);
                $data = [
                    'elgb_act' => 'UGP',
                    'elgb_act_date' => time(),
                    'elgb_agent' => $agentID,
                    'elgb_file_date' => date('Y-m-d'),
                    'elgb_term_date' => null,
                    'elgb_policyid' => $policyID,
                    'elgb_comment' => $reason ? $reason : "New policy group updated."
                ];
                PolicyUpdateHelper::updateEligibility($data);
                if ($sendEmail) {
//                    EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($policyID, 'Policy Group Updated', "policy group has been updated", 'billingdate', 'UGP');
                    EmailSendHelper::sendPolicyActionEmail($policyID, 'Policy Group Updated', "policy group has been updated", 'billingdate', 'UGP');
                }
            }
            return ['success' => 'Policy group updated successfully.'];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function updateWaiveStatementFee($request)
    {
        try {
            $policyID = $request->policy_id;
            $agentID = $request->aid;
            $reason = ($request->stmt_fee_flag == 1) ? 'Statement fee waived.' : 'Statement fee charged.';
            $reason .= ' ' . $request->reason;
            $waiveStmtFee = ($request->stmt_fee_flag == 1) ? 1 : 0;
            $sendEmail = $request->sendEmail;
            if (!UserInfoPolicyAddress::where('policy_id', $policyID)->count()) {
                return ['error' => 'Policy not found'];
            }
            $status = Policy::where('policy_id', $policyID)->update(['waive_stmt_fee' => $waiveStmtFee]);
            if ($status) {
                $data = [
                    'elgb_act' => 'FEE',
                    'elgb_act_date' => time(),
                    'elgb_agent' => $agentID,
                    'elgb_file_date' => date('Y-m-d'),
                    'elgb_term_date' => null,
                    'elgb_policyid' => $policyID,
                    'elgb_comment' => $reason ? $reason : "Statement fee " . $waiveStmtFee,
                ];
                PolicyUpdateHelper::updateEligibility($data);
                if ($sendEmail) {
//                    EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($policyID, 'Waive Statement Fee', "waive statement fee has been updated", 'waivestatementfee', 'FEE');
                    EmailSendHelper::sendPolicyActionEmail($policyID, 'Waive Statement Fee', "waive statement fee has been updated", 'waivestatementfee', 'FEE');
                }
            }
            return ['success' => 'Waive statement fee updated.'];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function updatePaymentRecurringStatus($request)
    {
        try {
            $policyID = $request->policy_id;
            $agentID = $request->aid;
            $reason = $request->reason;
            $policydetails = UserInfoPolicyAddress::where('policy_id', $policyID)->first();
            if (!$policydetails instanceof UserInfoPolicyAddress || !RecurringSelfPaymentInfo::where('policy_id', $policyID)->count()) {
                return ['error' => 'Policy not found'];
            }
            Policy::where('policy_id', $policyID)->update(['recurring_self_payment_id' => null]);
            $status = RecurringSelfPaymentInfo::where('policy_id', $policyID)->update(['status' => 'INACTIVE']);
            if ($status) {
                $data = [
                    'elgb_act' => 'BIC',
                    'elgb_act_date' => time(),
                    'elgb_agent' => $agentID,
                    'elgb_file_date' => date('Y-m-d'),
                    'elgb_term_date' => null,
                    'elgb_policyid' => $policyID,
                    'elgb_comment' => $reason ? $reason : 'Self Recurring Payment Reverted Back'
                ];
                PolicyUpdateHelper::updateEligibility($data);
                $data = [];
                if (strtoupper($policydetails->recurring_payment_type) == 'EFT') {
                    $bankDetails = PaymentEft::where('bank_id', $policydetails->recurring_payment_id)->first();
                    if ($bankDetails instanceof PaymentEft) {
                        $data = [
                            'Acount Name' => $bankDetails->bank_accountname,
                            'Bank Name' => $bankDetails->bank_name,
                            'Routing No' => $bankDetails->bank_routing,
                            'Account Number' => "XXXXXXX" . $bankDetails->bank_account4,
                        ];
                    }
                }
//                EmailSendHelper::sendGenericPolicyEmailUser($policydetails->userid, 'Billing Information Change', "", 'selfrecurringstatus', $data, 'BIC');
                EmailSendHelper::sendUserPolicyActionEmail($policydetails->userid, 'Billing Information Change', "", 'selfrecurringstatus', $data, 'BIC');
                return ['success' => 'Recurring payment status updated successfully.'];
            }
            return ['error' => 'Error updating status'];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function showUploadedFiles($request)
    {
        try {

            $dataPolicyDocument = AgentAppsUpload::where('pid', $request->policy_id)->where('grp', 'idilus')->orderBy('date_added', 'DESC')->get();

            $dataPpo = PpoUpload::where('policy_id', $request->policy_id)->orderBy('date_upload', 'DESC')->get();

            $dataLetter = UpfileLocc::where('policy_id', $request->policy_id)->get();

            $data = [
                'dataPolicyDocument' => $dataPolicyDocument,
                'dataPpo' => $dataPpo,
                'dataLetter' => $dataLetter,
            ];
            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }

    }

    //to get aging off deps
    public function agingOffDeps()
    {
        $mainData = array();
        $planOverview = PlanOverview::where('pstatus', 1)
            ->whereNotIn('tier',['IO'])
            // ->limit(5)
            ->groupBy('policy_id')
            ->get('policy_id');
        $planOverview->each->makeHidden(['formatted_price_male_nons']);

        $dependents = DependentPolicy::whereIn('policy_id', $planOverview)
            ->join('dependents','dependent_policies.dependent_id','=','dependents.did')
            ->whereRaw('timestampdiff(month,dependents.d_dob,curdate()) < 314 and timestampdiff(month,dependents.d_dob,curdate()) > 308')
            ->where('dependents.d_relate', 'C')
            ->select('dependent_policies.dependent_id','dependent_policies.policy_id','dependents.d_fname','dependents.d_lname','dependents.d_relate','dependents.d_dob','dependents.d_gender','dependents.userid')
            ->get();

        foreach ($dependents as $key => $d) {
            $tempData = [];
            $agentData = AgentInfo::join('userinfo_policy_address','userinfo_policy_address.agent_id','=','agent_info.agent_id')
                ->where('userinfo_policy_address.policy_id',$d->policy_id)
                ->select('agent_info.agent_id','agent_info.agent_fname','agent_info.agent_lname','agent_info.agent_email','userinfo_policy_address.userid','userinfo_policy_address.cfname','userinfo_policy_address.clname','userinfo_policy_address.cemail','userinfo_policy_address.phone1')
                ->get();

            $planData = PlanOverview::where('pstatus','1')
                    ->where('policy_id',$d->policy_id)
                    ->whereNotIn('is_assoc',['1'])
                    ->whereNotIn('tier',['IO','IS'])
                    ->get('web_display_name');
            $planData->each->makeHidden(['formatted_price_male_nons']);

            $depAge = $this->calculateAge($d->d_dob);
            if ($depAge->y == 25 && $depAge->m == 9 && $depAge->d == 0) {
                $tempData['userdata'] = $agentData;
                $tempData['dependent'] = $d;
                $tempData['plandata'] = $planData;
                $tempData['agingdate'] = 90;
                $tempData['depage'] = $depAge;
                $mainData[] = $tempData;
                // return $mainData;
                $emailFlag = $this->sendAgingEmail($tempData);
            }elseif ($depAge->y == 25 && $depAge->m == 10 && $depAge->d == 0) {
                $tempData['userdata'] = $agentData;
                $tempData['dependent'] = $d;
                $tempData['plandata'] = $planData;
                $tempData['agingdate'] = 60;
                $tempData['depage'] = $depAge;
                $mainData[] = $tempData;
                $emailFlag = $this->sendAgingEmail($tempData);
            }elseif($depAge->y == 25 && $depAge->m == 11 && $depAge->d == 0){
                $tempData['userdata'] = $agentData;
                $tempData['dependent'] = $d;
                $tempData['plandata'] = $planData;
                $tempData['agingdate'] = 30;
                $tempData['depage'] = $depAge;
                $mainData[] = $tempData;
                $emailFlag = $this->sendAgingEmail($tempData);
            }elseif($depAge->y == 26 && $depAge->m == 0 && $depAge->d == 0){
                $tempData['userdata'] = $agentData;
                $tempData['dependent'] = $d;
                $tempData['plandata'] = $planData;
                $tempData['agingdate'] = 0;
                $tempData['depage'] = $depAge;
                $mainData[] = $tempData;
                $emailFlag = $this->sendAgingEmail($tempData);
            }

        }


        if (count($mainData) > 0) {
            if ($emailFlag) {
                return ["statusCode"=>200,"status"=>"Success","message"=>"Email sent successfully","data"=>$mainData];
            }else{
                return ["statusCode"=>401,"status"=>"Failed","message"=>"Failed to send email","data"=>$mainData];
            }

        }else{
            return ["statusCode"=>401,"status"=>"Success","message"=>"No data"];
        }
    }

    //php function to calculate age
    public function calculateAge($dob)
    {
        $bday = new DateTime($dob);

        // Get today's date
        $now = new DateTime();
        $diff = $now->diff($bday);
        $ageY = $diff->y;
        $ageM = $diff->m;
        $ageD = $diff->d;

        return $diff;
    }

    public function sendAgingEmail($record)
    {
        if ($record['agingdate'] == 90) {
            $msg = "reaching Age 26 in 90 Days";
            $sub = "90 Day Notification of Dependents Aging Off";
        }elseif ($record['agingdate'] == 60) {
            $msg = "reaching Age 26 in 60 Days";
            $sub = "60 Day Notification of Dependents Aging Off";
        }elseif ($record['agingdate'] == 30) {
            $msg = "reaching Age 26 in 30 Days";
            $sub = "30 Day Notification of Dependents Aging Off";
        }else{
            $msg = "now Age 26";
            $sub = "Dependents Aging Off Today";
        }
        $agentName = $record['userdata'][0]->agent_fname.' '.$record['userdata'][0]->agent_lname;
        $memberName = $record['userdata'][0]->cfname.' '.$record['userdata'][0]->clname;
        $agentEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $record['userdata'][0]->agent_email;
        $userEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $record['userdata'][0]->cemail;
        $userPhone = $record['userdata'][0]->phone1;
        $userPhone = substr($userPhone, -10, -7) . "-" . substr($userPhone, -7, -4) . "-" . substr($userPhone, -4);

        $messageTop = "Our records indicate that the following dependents are ".$msg.".";

        $messageTop2 = "The dependents listed below will no longer be eligible for coverage under the plan(s) listed at the end of the calendar month in which they turn age 26.";
        $messageBot = "We recommend that you take immediate action, as there are options available for dependents who will lose coverage due to age.";
        $messageBot2 = "If our records are incorrect and the dependent(s) listed will not reach age 26 on the date listed, please contact the member(s) immediately and verify the correct dependent(s) date of birth.";


        $messageBot3 = "Thank you for your immediate attention,";
        $messageBot4 = "Elevate Wellness Association";
        $depName = $record['dependent']->d_fname.' '.$record['dependent']->d_lname;
        $depAge = $record['depage']->y.'years '.$record['depage']->m.'month '.$record['depage']->d.' day';
        $depDob = $record['dependent']->d_dob;
        $planData = '';
        foreach ($record['plandata'] as $plans) {
            $planData .= $plans->web_display_name."<br>";
        }

        $data = [
            'agentName' => $agentName,
            'agentEmail' => $agentEmail,
            'memberName' => $memberName,
            'userEmail' => $userEmail,
            'userPhone' => $userPhone,
            'messageTop' => $messageTop,
            'messageTop2' => $messageTop2,
            'messageBot' => $messageBot,
            'messageBot2' => $messageBot2,
            'messageBot3' => $messageBot3,
            'messageBot4' => $messageBot4,
            'depName' => $depName,
            'depAge' => $depAge,
            'plans' => $planData,
            'subject' => $sub,
            'ddob' => $depDob,

        ];
        // print_r($data);
        $emailFlag = $this->sendEmailWithMessageCenter($data);
        return $emailFlag;
    }

    public function sendEmailWithMessageCenter($data)
    {
        $messageCenterBaseUrl = config('app.messagecenter.key') ?? 'https://qa-api-msg.purenroll.com/';
        $url = $messageCenterBaseUrl . "api/v1/send-email-with-content-data";
        $requestData = [
            'email_message_configuration_name' => 'AGING_OFF',
            'toAddress' => [$data['agentEmail']],
            'bccAddress' => [],
            'subject' => $data['subject'],
            'attachedFiles' => [],
            'generalTemplateData' => [],
            'contentData' => [
                'countData' => 1,
                'name' => '',
                'content' => [
                    'agentName' => $data['agentName'],
                    'messageTop' => $data['messageTop'],
                    'messageTop2' => $data['messageTop2'],
                    'messageBot' => $data['messageBot'],
                    'messageBot2' => $data['messageBot2'],
                    'messageBot3' => $data['messageBot3'],
                    'messageBot4' => $data['messageBot4'],
                    'reason' => '',
                    'data' => [
                        'Primary' => $data['memberName'],
                        'Dependent' => $data['depName'],
                        'Dependent Age' => $data['depAge'],
                        'Dependent DOB' => $data['ddob'],
                        'Current Coverage' => $data['plans'],
                        'Email' => $data['userEmail'],
                        'Phone' => $data['userPhone']
                    ]
                ]
            ]
        ];

        $responseJson = GuzzleHelper::postApi($url, [], $requestData);
        $response = json_decode($responseJson, true);
        if (isset($response['status_code']) && $response['status_code'] == '200' && $response['status'] == 'success') {
            return true;
        } else {
            return false;
        }
    }

    public function commonAssociationCheckDetailUpdate($request){
        $current_assoc = PlanOverview::where('policy_id', $request->policy_id)->where('is_assoc',1)->where('pstatus', 1)->select('plan_id')->first();
        $policy_id = $request->policy_id;
        if(isset($current_assoc->plan_id)){
            $this->updateAssociationPricing($policy_id);
        }
        else{
            $checkAssociationPricing = $this->checkAssociationPricing($request);
            if(!empty((array)$checkAssociationPricing ) && !empty($checkAssociationPricing->$policy_id)){
                    $result = array_filter((array)$checkAssociationPricing, function ($value) {
                        return !empty($value);
                    });
                    if (!empty($result)){
                        $this->compareAndUpdateAssociations($result);
                    }
            }
        }
    }

    protected function compareAndUpdateAssociations($result) {

        $highestPrice = -1.0;
        $PlanWithPriceArray = [];
        $comapreCollection = new stdClass();
        foreach ($result as $key => $subArray) {
            foreach ($subArray as $item) {
                if($item instanceof stdClass){
                    $item = (array)$item;
                }
                $planPricingId = $item['plan_pricing_id'];
                $priceMaleNons = $this->getPriceMaleNons($planPricingId);
                $priceMaleNons = floatval($priceMaleNons);
                if ($priceMaleNons > 0.0) {
                    if($priceMaleNons > $highestPrice){
                        $highestPrice = $priceMaleNons;
                    }
                    array_push($PlanWithPriceArray , array_merge($item, ['policy_id' => $key,'price' => $priceMaleNons]));
                }
            }
        }
        foreach ($PlanWithPriceArray as $plan){
            if($plan['price'] >= $highestPrice){
                $this->updateAssociationPricing($plan['policy_id']);
            }
            if($plan['price'] < $highestPrice){
                $this->termPlan($plan['plan_pricing_id'], $plan['policy_id']);
            }
        }
            return true;
    }

    protected function getPriceMaleNons($planPricingId) {
        $price = PlanPricingDisplay::where('plan_pricing_id', $planPricingId)->where('is_assoc',1)->select('price_male_nons')->first();
        if(!isset($price->price_male_nons)){
            return -2.0;
        }
        return number_format($price->price_male_nons / 100, 2, '.', '');
    }

    protected function updateAssociationPricing($policy_id){
        $existing_plans = PlanOverview::where('policy_id',$policy_id)->where('pstatus',1)->select('pid','plan_id')->get();
        $assoc_id = $this->PlanChangeOnAddress->computeAssociationFees($existing_plans, $policy_id);
            if(count($assoc_id)){
                $planIds = collect($assoc_id)->filter(function ($value) {
                    if(is_object($value)){
                        $value = (array)$value;
                    }
                    return PlanPolicy::where('plan_id', $value['plan_pricing_id'])->exists();
                })->pluck('plan_pricing_id')->toArray();
                if($planIds == $existing_plans->pluck('plan_id')->toArray()){
                    return true;
                }
                else{
                    if (!empty($planIds)) {
                        $planToAddId =  array_diff($planIds , $existing_plans->pluck('plan_id')->toArray());
                        $planToTermId =  array_diff($existing_plans->pluck('plan_id')->toArray() , $planIds);
                        $policiesTypes = Policy::where('policy_id', '=', $policy_id)->select('pptype','efees')->first();
                        $insertPlan = PlanPolicy::create(
                                [
                                    'policy_num' => $policy_id,
                                    'plan_id' => array_values($planToAddId)[0],
                                    'peffective_date' => date('Y-m-01', strtotime('+1 month')),
                                    'pterm_date' => null,
                                    'pstatus' => 1,
                                    "date" => time(),
                                    "ppptype" => $policiesTypes['pptype'],
                                    "pefees" => $policiesTypes['efees'],
                                    "is_approved" => 1,
                                ]
                            );
                            $data = [
                                'elgb_act' => 'SPC',
                                'elgb_act_date' => time(),
                                'elgb_file_date' => date('Y-m-d'),
                                'elgb_term_date' => null,
                                'elgb_policyid' => $policy_id,
                                'elgb_comment' => "Member Assoiation Fee has been Added"
                            ];
                            PolicyUpdateHelper::updateEligibility($data);
                    }
                    if(count($planToTermId)){
                        $this->termPlan(array_values($planToTermId)[0], $policy_id);
                    }
                }

        }
    }

    protected function termPlan($plan_id , $policy_id){
        PlanPolicy::where('policy_num', $policy_id)
        ->where('plan_id', $plan_id)
        ->where('pstatus', 1)
        ->update([
            'pterm_date' => date("Y-m-t"),
            'pstatus' => 3,
        ]);
        $data = [
            'elgb_act' => 'TMO',
            'elgb_act_date' => time(),
            'elgb_file_date' => date('Y-m-d'),
            'elgb_term_date' => null,
            'elgb_policyid' => $policy_id,
            'elgb_comment' => "Member Assoiation Fee has been Switched"
        ];
        PolicyUpdateHelper::updateEligibility($data);
    }

    protected function checkAssociationPricing($request){
        $assoc_collection = new stdClass();
        $policy_idCollection = PlanOverview::where('policy_userid', PlanOverview::where('policy_id', $request->policy_id)->select('policy_userid as user_id')->first()->user_id)
        ->where('status', 'Active')
        ->where('is_assoc',1)
        ->groupBy('policy_id')
        ->pluck('policy_id')
        ->toArray();
        $existing_plans = PlanOverview::whereIn('policy_id',$policy_idCollection)->where('pstatus',1)->select('pid','plan_id','policy_id','is_assoc')->get();
        $filteredPlans = $existing_plans->groupBy('policy_id')->flatMap(function ($plans) use ($request) {
            $hasAssoc = $plans->contains('is_assoc', 1);
            return $plans->filter(function ($plan) use ($hasAssoc , $request) {
                return $plan->policy_id == $request->policy_id || ($hasAssoc);
            });
        });
        $mappPolicy = $filteredPlans->groupBy('policy_id');
        foreach ($mappPolicy as $policy_id => $mapped_policy) {
            $assoc_collection->$policy_id = $this->PlanChangeOnAddress->computeAssociationFees($mapped_policy, $policy_id);
        }
        return $assoc_collection;
    }

    public function checkIfPolicyHasExtraHealth($policy_id)
    {
        return PlanOverview::query()->where('policy_id', $policy_id)
            ->whereHas('hasExtraHelthPlans')
            ->exists();
    }

    public function checkIfPolicyWithdrawnWithinLimit($policy_id)
    {
        return UserInfoPolicyAddress::query()
            ->where('policy_id', $policy_id)
            ->whereRaw('effective_date >= NOW() - INTERVAL 15 DAY')
            ->exists();
    }

}
