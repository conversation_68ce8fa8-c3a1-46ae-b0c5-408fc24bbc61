<?php

namespace App\Repositories;

use App\TemplateDesign;
use App\EmailTemplate;
use App\EmailQueues;
use Exception;
use Illuminate\Database\Eloquent\Model;

class EmailFeatures extends Model
{
    public function getEmailTemplate($id=0)
    {
        if($id==0){
            $emailTemplate = TemplateDesign::orderBy('id')->get();
        }else{
            $emailTemplate = TemplateDesign::where('id',$id)->first();
        }
        return $emailTemplate;
    }

    public function storeTemplate($data)
    {
        try{
            TemplateDesign::insert($data);
        } catch (Exception $e) {
            $message = $e->getMessage();
            return $message;
        }
        $message = "Email Template Insertion Success.";
        return $message;
    }

    public function updateTemplate($data,$id)
    {
        try{
            TemplateDesign::where('id',$id)
                    ->update($data);
        } catch (Exception $e) {
            $message = $e->getMessage();
            return $message;
        }
        $message = "Email Template Update Success.";
        return $message;
    }

    //get email html outline
    public function getEmailOutine($id)
    {
        $emailDesign = EmailTemplate::where('id',$id)->first();
        return $emailDesign;
    }

    //get email content design categorywise
    public function getEmailByCategory($categoryname)
    {
        $data = TemplateDesign::where('categoryname',$categoryname)->first();
        return $data;
    }

    //save email in queue
    public function saveQueue($data)
    {
        $firstName = $data['first_name'];
        $lastName = $data['last_name'];
        $content = $data['content'];
        $emailHtml = $this->getEmailOutine(1);
        $emailHtml->template= str_replace('#firstname#',$firstName, $emailHtml->template);
        $emailHtml->template= str_replace('#lastname#', $lastName, $emailHtml->template);
        $emailHtml->template= str_replace('#Content#',$content, $emailHtml->template);
        $emailHtml->template= str_replace('#current_year#',date('Y-m'), $emailHtml->template);
        $curdate = date('Y-m-j H:i:s');
        $emailqueue = [
            'to_email' =>json_encode($data['to_email']),
            'to_name' =>$data['to_name'],
            'cc' =>json_encode($data['cc']),
            'bcc'=>json_encode($data['bcc']),
            'from_email' => $data['from_email'],
            'from_name' =>$data['from_name'],
            'subject' =>$data['subject'],
            'body' =>$emailHtml->template,
            'sent_status' => 0,
            'send_date'=>$curdate,
            'send_time'=>$curdate,
            'weburl' => $data['weburl'],
        ];
        $saveQueue = $this->saveEmailQueue($emailqueue);
        return $saveQueue;
    }

    public function saveEmailQueue($emailQueue)
    {
        try{
            $emailid = EmailQueues::insertGetId($emailQueue);
            $message = "Email Queue Insertion Success.";
            return json_encode(array('type'=>'success','message'=>$message,'response'=>$emailid));
        } catch (Exception $e) {
            $message = $e->getMessage();
            return $message;
        }
    }
}
