<?php

namespace App\Repositories\MemberResource;

use App\Http\Resources\MemberFileResource;
use App\Plan;
use App\PlanCert;
use App\PlanOverview;
use App\PlanPricingDisplay;
use App\Repositories\PolicyFeatures;
use App\UhcId;
use App\UserInfo;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Exception;
use Illuminate\Support\Facades\DB;

class MemberResourceFeatures extends Model
{
    protected $member = '';

    public function getMemberResource($request)
    {
        try {
            $policyDetails = UserInfoPolicyAddress::where('policy_id', $request->policy_id)->first();
            if(!$policyDetails instanceof UserInfoPolicyAddress) {
                throw new \Exception('Invalid policy id.');
            }
            $member = UserInfo::where('userid',$policyDetails->userid)->first();
            $t_array = array();
            // $data = $this->formataData('Resources', 'Tax Form 1095', 'https://corenroll.com/_pdfdocs/tax_form_f1095a.pdf');
            // array_push($t_array, $data);
            $message = '';
            if ($member instanceof UserInfo) {
                $this->member = $member;
                foreach ($member->getPolicyList as $singlePolicy) {
                    if ($request->has('policy_id') && $request->policy_id) {
                        if ($request->policy_id != $singlePolicy->policy_id) {
                            continue;
                        }
                    }
                    $plans = $singlePolicy->getPolicyPlan;
                    $t_array = $this->getPlanCards($plans, $singlePolicy, $t_array);
                }
            }
            $tempArray = array();
            $uniqueData = array();
            foreach ($t_array as $val) {
                if (isset($val['filename'])) {
                    if (in_array($val['filename'], $tempArray) || empty($val)) {
                        continue;
                    }
                    array_push($tempArray, $val['filename']);
                    array_push($uniqueData, $val);
                }
            }
            $t_array = $uniqueData;
            return [
                'card_message' => $message,
                'headers' => ['Resources'],
                'files' => array_filter($t_array)
            ];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }

    }

    public function getPlanCards($plans, $singlePolicy, $t_array, $member = null)
    {
        if($member != null) {
            $this->member = $member;
        }
        $date = date('Y-m-d', time());
        foreach ($plans as $plan_) {
            if (!($plan_->pstatus == 1 || ($plan_->pterm_date && (int)$plan_->pstatus === 2 && strtotime($plan_->pterm_date) > strtotime(date('Y-m-d')) ))  ) continue;
            $plan = Plan::find($plan_->pid);
            $digitalCard = $this->checkDigitalCard($plan, $singlePolicy);
            $notDigitalCardCarrier = $this->checkNotDigitalCardCarrier($plan);
            switch ($plan->cardfilename) {
                case '_temp_uhc_cards_dhcook.php':
                    $uh_userid = 'S' . $singlePolicy->policy_userid;
                    $uhcid = UHCId::findBySubid($uh_userid);
                    if ($uhcid instanceof UHCId) {
                        $data = $this->formataData(
                            'Resources',
                            $plan->cardfilename_display,
                            $this->formatURL($plan->cardfilename, $singlePolicy->policy_id),
                            $plan_->p_ai
                        );
                        array_push($t_array, $data);
                    }
                    break;
                case '_temp_cigna_cards_cook_elite.php':
                default:
                    if ($digitalCard || $notDigitalCardCarrier){
                        $data = $this->formataData(
                            'Resources',
                            $plan->cardfilename_display,
                            $this->formatURL($plan->cardfilename, $singlePolicy->policy_id),
                            $plan_->p_ai
                        );
                        // if ($singlePolicy->effective_date <= $date || $plan->carrier === 92) {
                        //     array_push($t_array, $data);
                        // }
                        array_push($t_array, $data);

                    }
                    break;
            }
            if ($plan->welcomeletterfile != null) {
                $data = $this->formataData('Resources', $plan->welcomeletterfilename, $this->formatNueraURL
                ($plan->welcomeletterfile, $singlePolicy->policy_id), $plan_->p_ai);
                array_push($t_array, $data);
            }
            if ($plan->medical_questionnaire != null) {
                $data = $this->formataData('Resources', $plan->medical_questionnaire_name, $this->formatNueraURL
                ($plan->medical_questionnaire, $singlePolicy->policy_id), $plan_->p_ai);
                array_push($t_array, $data);
            }
            if ($plan->certfile != null) {
                $data = $this->formataData('Resources', $plan->certfilename, $this->formatNueraURL($plan->certfile,
                    $singlePolicy->policy_id), $plan_->p_ai);
                array_push($t_array, $data);
            }

            $hasIHA = $plan_->ihaDashboardStatus;
            if ($hasIHA && $hasIHA->showMemberContract()) {
                $data = $this->formataData('Resources', 'Member Contract Pdf', $this->formatFileURL('member-contract-pdf',
                    $plan_->p_ai), $plan_->p_ai);
                array_push($t_array, $data);
            }

            if ($plan->enrollment_forms != null && $this->compareString('_temp_elite.php', $plan->enrollment_forms)) {
                $data = $this->formataData('Resources', $plan->enrollment_forms_name, $this->formatNueraURL
                ($plan->enrollment_forms, $singlePolicy->policy_id), $plan_->p_ai);
                array_push($t_array, $data);
            }
            $t_array = $this->getCertCards($plan, $singlePolicy, $t_array, $plan_->p_ai);
            $t_array = $this->getPlanDocuments($plan, $singlePolicy, $t_array, $plan_->p_ai,$digitalCard);
            $t_array = $this->getPlanTerminationDocuments($t_array, $plan_->p_ai);
            if ($plan instanceof Plan) {
                $_7kEnable = $plan->toArray();
                if ($_7kEnable['7k_enable']) {
                    $t_array = $this->get7kenableCerficate($t_array, $plan_->p_ai);
                }
            }
        }
        return $t_array;
    }

    public function getPlanDocuments($plan, $member, $t_array,$p_ai = null,$checkDigitalCard =null)
    {
        $date = date('Y-m-d', time());
        $plandocs = $plan->getDocs;
        foreach ($plandocs as $plandoc) {
                if ($plandoc->planDocName) {
                    if($plandoc->planDocName == 'EBA IHA CERTIFICATE' || (strpos(strtolower($plandoc->planDocName), 'digital certificate')) ) {
                        if ($checkDigitalCard){
                            $data = $this->formataData('Resources', $plandoc->planDocName, 'https://corenroll.com/_pdfdocs/'.$plandoc->planDoc.'?id=' . base64_encode($member->policy_id), $p_ai);
                            // if ($member->effective_date <= $date) {
                                array_push($t_array, $data);
                            // }
                        }
                    } else {
                        $data = $this->formataData('Resources', $plandoc->planDocName, 'https://corenroll.com/_pdfdocs/' .
                            $plandoc->planDoc, $p_ai);
                        array_push($t_array, $data);
                    }
                }
        }
        return $t_array;
    }

    public function getPlanTerminationDocuments($t_array, $p_ai = null)
    {
        $data = $this->formataData('Resources', 'Change/Termination Form', 'https://corenroll.com/_web_pdf/EW-TCF-TerminationChangeForm-Fillable.pdf', $p_ai);
        array_push($t_array, $data);
        return $t_array;
    }

    public function getCertCards($plan, $singlePolicy, $t_array, $p_ai = null)
    {
        $certs = PlanCert::findByPidAndState($plan->pid, $this->member->customerAddress->state);
        if ($certs instanceof PlanCert) {
            $data = $this->formataData('Resources', $certs->cert_name, 'https://nuerabenefits.com/_pdfdocs/_temp_afli_cert.php?id=' . base64_encode
                ($singlePolicy->policy_id), $p_ai);
            array_push($t_array, $data);
            $data = $this->formataData('Resources', $certs->cert_addon1_name, 'https://nuerabenefits.com/_pdfdocs/_afli_certs_/' . $certs->state . '/' . $certs->cert_addon1,  $p_ai);
            array_push($t_array, $data);
        }
        return $t_array;
    }

    public function formatURL($cardname, $policyid)
    {
        $base_url = 'https://corenroll.com/_pdfdocs/';
        return $base_url . $cardname . '?id=' . base64_encode($policyid);
    }

    public function formatNueraURL($cardname, $policyid)
    {
        $base_url = 'https://nuerabenefits.com/_pdfdocs/';
        return $base_url . $cardname . '?id=' . base64_encode($policyid);
    }

    public function formatFileURL($cardname, $p_ai)
    {
        $base_url = 'https://files.purenroll.com/dashboard/member/';
        return $base_url . $cardname . '?p_ai=' . base64_encode((string) $p_ai);
    }

    public function formataData($header, $filename, $url, $p_ai = null)
    {
        $data = [];
        if ($filename != '') {
            $data['header'] = $header;
            $data['url'] = $url;
            $data['filename'] = $filename;
            $data['p_ai'] = $p_ai;

        }
        return $data;
    }

    public function get7kenableCerficate($t_array, $p_ai = null)
    {
        $data = $this->formataData('Resources', 'Prudential 7K Term Life Certificate', 'https://corenroll.com/_pdfdocs/Prudential_7k_certificate.pdf', $p_ai);
        array_push($t_array, $data);
        return $t_array;
    }
    private function compareString($needle, $haystack) {
        return strpos($haystack, $needle) === false;
    }

    public function getNewRates($policy_id){
        $userDetails = UserInfoPolicyAddress::where('status', 'ACTIVE')
        ->where('policy_id', $policy_id)
        ->with(['plans' => function($q){
            $q->where('pl_type', 'mm')
            ->where('pstatus', 1);
        }])
        ->first();
        return [
            'plan_name' => $userDetails->plans[0]->web_display_name,
            'carrier_id' => $userDetails->plans[0]->cid,
            'tier' => $userDetails->plans[0]->tier,
            'tier_info' => $userDetails->plans[0]->getTierDetails(),
            'new_rate' => $this->getRenewalPlanPrice($userDetails->plans[0]),
            'start_date' => Carbon::now()->startOfYear()->format('Y-m-d'),
            'end_date' => Carbon::now()->endOfYear()->format('Y-m-d'),
            'user_info' => [
                'first_name' => $userDetails->cfname,
                'last_name' => $userDetails->clname,
                'address1' => $userDetails->address1,
                'address2' => $userDetails->address2,
                'city' => $userDetails->city,
                'state' => $userDetails->state,
                'zip' => $userDetails->zip,
            ],
            'agent_name' => $userDetails->agent_fname.' '.$userDetails->agent_lname,
            'agent_phone' => $this->formatPhone($userDetails->agent_phone1)
        ];
    }


    public function getNewRatesRenewal($policy_id)
    {

        $targetPids = explode(',', config('app.TARGET_PIDS_FOR_RENEWAL'));
        $userDetails = UserInfoPolicyAddress::where('status', 'ACTIVE')
            ->where('policy_id', $policy_id)
            ->with(['plans' => function($q) use ($targetPids) {
                $q->whereIn('pid', $targetPids)
                    ->where('pstatus', 1);
            }])
            ->first();

            $userInfo = [
                'first_name' => $userDetails->cfname,
                'last_name' => $userDetails->clname,
                'address1' => $userDetails->address1,
                'address2' => $userDetails->address2,
                'city' => $userDetails->city,
                'state' => $userDetails->state,
                'zip' => $userDetails->zip,
            ];

            $otherDetails = [
                'start_date' => Carbon::now()->startOfYear()->format('Y-m-d'),
                'end_date' => Carbon::now()->endOfYear()->format('Y-m-d'),
                'agent_name' => $userDetails->agent_fname.' '.$userDetails->agent_lname,
                'agent_phone' => $this->formatPhone($userDetails->agent_phone1)
            ];


            $planInfo = [];
            foreach ($userDetails->plans as $plan) {
                $planInfo[] = [
                    'pid' => $plan->pid,
                    'plan_name' => $plan->web_display_name,
                    'carrier_id' => $plan->cid,
                    'tier' => $plan->tier,
                    'tier_info' => $plan->getTierDetails(),
                    'new_rate' => $this->getRenewalPlanPrice($plan),
                ];
            }

            return [
                'plan_info' => $planInfo,
                'user_info' => $userInfo,
                'other_info' => $otherDetails
            ];


    }

    public function getSolisticCardDetail($policy_id)
    {
        $userDetails = UserInfoPolicyAddress::where('status', 'ACTIVE')
            ->where('policy_id', $policy_id)
            ->first();

        $userInfo = [
            'first_name' => $userDetails->cfname,
            'last_name' => $userDetails->clname,
            'address1' => $userDetails->address1,
            'address2' => $userDetails->address2,
            'city' => $userDetails->city,
            'state' => $userDetails->state,
            'zip' => $userDetails->zip,
        ];

        $otherDetails = [
            'start_date' => Carbon::now()->startOfYear()->format('Y-m-d'),
            'end_date' => Carbon::now()->endOfYear()->format('Y-m-d'),
            'agent_name' => $userDetails->agent_fname . ' ' . $userDetails->agent_lname,
            'agent_phone' => $this->formatPhone($userDetails->agent_phone1)
        ];

        $baseQuery = PlanOverview::where('policy_id', $policy_id);
        $solisticExists = (clone $baseQuery)
                ->where('cid', 11)
                ->where('pid', '>', 1896)
                ->where('pstatus', 1)
                ->exists();

        $planInfo = [];

        if($solisticExists) {
            $solisticPlans = (clone $baseQuery)
                ->where('cid', 11)
                ->where('pid', '>', 1896)
                ->where('pstatus', 1)
                ->get();

            foreach ($solisticPlans as $plan) {
                $planInfo[] = [
                    'pid' => $plan->pid,
                    'plan_name' => $plan->web_display_name,
                    'carrier_id' => $plan->cid,
                    'is_assoc' => $plan->is_assoc,
                    'new_rate' => $this->getRenewalPlanPrice($plan),
                ];
            }

            // Association Plans (is_assoc = 1)
            $assocPlans = (clone $baseQuery)
                ->where('is_assoc', 1)
                ->get();

            foreach ($assocPlans as $plan) {
                $planInfo[] = [
                    'pid' => $plan->pid,
                    'plan_name' => $plan->web_display_name,
                    'carrier_id' => $plan->cid,
                    'is_assoc' => $plan->is_assoc,
                    'new_rate' => $this->getRenewalPlanPrice($plan),
                ];
            }
        }

        return [
            'plan_info' => $planInfo,
            'user_info' => $userInfo,
            'other_info' => $otherDetails
        ];
    }

    public function getDeltaDentalCardDetail($policy_id)
    {

        $targetPids = explode(',', config('app.TARGET_PIDS_FOR_DELTA_DENTAL_RENEWAL'));
        $userDetails = UserInfoPolicyAddress::where('status', 'ACTIVE')
            ->where('policy_id', $policy_id)
            ->with(['plans' => function($q) use ($targetPids) {
                $q->whereIn('pid', $targetPids)
                    ->where('pstatus', 1);
            }])
            ->first();

            $userInfo = [
                'first_name' => $userDetails->cfname,
                'last_name' => $userDetails->clname,
                'address1' => $userDetails->address1,
                'address2' => $userDetails->address2,
                'city' => $userDetails->city,
                'state' => $userDetails->state,
                'zip' => $userDetails->zip,
            ];

            $otherDetails = [
                'start_date' => Carbon::now()->startOfYear()->format('Y-m-d'),
                'end_date' => Carbon::now()->endOfYear()->format('Y-m-d'),
                'agent_name' => $userDetails->agent_fname.' '.$userDetails->agent_lname,
                'agent_phone' => $this->formatPhone($userDetails->agent_phone1)
            ];


            $planInfo = [];
            foreach ($userDetails->plans as $plan) {
                $planInfo[] = [
                    'pid' => $plan->pid,
                    'plan_name' => $plan->web_display_name,
                    'carrier_id' => $plan->cid,
                    'tier' => $plan->tier,
                    'tier_info' => $plan->getTierDetails(),
                    'new_rate' => $plan->plan_new_price,
                ];
            }

            return [
                'plan_info' => $planInfo,
                'user_info' => $userInfo,
                'other_info' => $otherDetails
            ];


    }

    private function getRenewalPlanPrice(PlanOverview $planOverview)
    {
        $policyFeature = new PolicyFeatures;
        $age = $policyFeature->calculateAge($planOverview->user_dob);
        $query = PlanPricingDisplay::query();
        $age = $age->y;
        if ($planOverview->plan_pricing_type == '2') {
            $query = $query->where([
                ['age1', '<=', $age],
                ['age2', '>=', $age],
            ]);
        }

        $planPricingDisplay = $query->where([
            'pid' => $planOverview->pid,
            'tier' => $planOverview->tier,
            'pricing_status' => 1,
        ])->first(['price_male_nons']);

        if (!$planPricingDisplay) return 0.00;

        return $planPricingDisplay->price_male_nons;
    }

    private function formatPhone($phone)
    {
        $phone = preg_replace("/[^0-9]/", "", $phone);
        if (strlen($phone) == 7) {
            return preg_replace("/([0-9]{3})([0-9]{4})/", "$1-$2", $phone);
        } elseif (strlen($phone) == 10) {
            return preg_replace("/([0-9]{3})([0-9]{3})([0-9]{4})/", "($1) $2-$3", $phone);
        } else {
            return $phone;
        }
    }
    const CHECK_CARRIER_FOR_DIGITAL_CARD= [67,79,77,11,62, 65];

    public function checkDigitalCard($plan, $singlePolicy)
    {
        $checkPlanForDigitalCard = static::CHECK_CARRIER_FOR_DIGITAL_CARD;
        return DB::table('plan_policies_member as pm')
            ->where('pm.policy_id', '=', $singlePolicy->policy_id)
            ->where('pm.pid', '=', $plan->pid)
            ->where(function ($query) use ($checkPlanForDigitalCard) {
                $query->whereIn('pm.cid', $checkPlanForDigitalCard)
                    ->orWhere('pm.brand','=','L713');
            })
            ->whereNotNull('member_id')
            ->exists();
    }

    public function checkNotDigitalCardCarrier($plan)
    {
        $checkPlanForDigitalCard = static::CHECK_CARRIER_FOR_DIGITAL_CARD;
        return DB::table('plans as p')
            ->where('pid',$plan->pid)
            ->whereNotIn('carrier',$checkPlanForDigitalCard)
            ->exists();
    }
}
