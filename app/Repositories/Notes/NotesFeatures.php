<?php

namespace App\Repositories\Notes;

use App\AgentInfo;
use App\AgentNote;
use App\AgentUser;
use App\GroupUpdate;
use App\Helpers\AgentUpdatesHelper;
use App\Helpers\CommonHelper;
use App\Helpers\PolicyUpdateHelper;
use App\Http\Resources\ErrorResource;
use App\PolicyNote;
use App\PolicyUpdate;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Exception;
use App\Http\Resources\SuccessResource;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class NotesFeatures extends Model
{

    protected function mimes(){
        return array('pdf','doc','docx','xls','xlsx','csv');
    }


    use ResponseMessage, Paginator;

    public function __construct()
    {
        $this->noteFilePath = 'uploads/note_files';
        $this->agentnoteFilePath = 'uploads/agent_note_files';
        $this->groupFilePath = 'uploads/group_File_Path';
    }

    public function addNotes($request)
    {
        try {
            $data = [
                'anuser' => $request->aid,
                'policy_id' => $request->policy_id,
                'annote' => $request->note,
                'status' => $request->status,
                'username' => $request->username,
                'agent_id' => $request->agent_id
            ];

            // Update to fetch agent_user's email id to add note if they're the one that do.
            if ($request->is_root == 0 && $request->agent_id) {
                $adminUser = AgentUser::where('agent_id', $request->agent_id)
                    ->where('username', $data['username'])
                    ->where('is_root', 0)
                    ->where('status', 'A')
                    ->first();

                if ($adminUser) {
                    $data['username'] = $adminUser->username;
                }
            }

            $status = false;
            if ($request->hasFile('file')) {
                if ($request->file('file')->getSize() > 4200000) {
                    return response()->json(
                        new ErrorResource([
                            'statusCode' => 422,
                            'message' => 'File too large, max allowed is 4 mb.',
                        ]),
                        422
                    );
                }
                $file = $request->file('file');
                $validationResponse = CommonHelper::fileValidation($file);
                if ($validationResponse !== true) {
                    return response()->json(
                        new ErrorResource([
                            'statusCode' => 422,
                            'message' => $validationResponse['error'],
                        ]),
                        422
                    );
                }
                $fileName = time() . '_' . $file->getClientOriginalName();
                if ($file->move($this->noteFilePath, $fileName)) {
                    $data['file'] = $fileName;
                }
            }
            if(isset($request->aid)){
                $notes = PolicyNote::where('anID', $request->aid)->first();
                if ($notes instanceof PolicyNote) {
                    $data['updated_at'] = date('Y-m-d H:i:s');
                    $message = "Note updated successfully.";
                    $status = PolicyNote::where('anID', $request->aid)->update($data);
                }
            } else {
                $data['created_at'] = date('Y-m-d H:i:s');
                $data['ants'] = time();
                $status = PolicyNote::insert($data);
                $message = "Note added successfully.";
            }
            if ($status) {
                PolicyUpdateHelper::updateEligibility([
                    'elgb_policyid' => $request->policy_id,
                    'elgb_act' => PolicyUpdate::POLICY_NOTE_UPDATE,
                    'elgb_act_date' => time(),
                    'elgb_comment' => $message,
                    'elgb_agent' => request()->header('id'),
                    'elgb_file_date' => date('Y-m-d')
                ]);

                return response()->json(
                    new SuccessResource([
                        'statusCode' => 200,
                        'message' => $message,
                    ]),
                    200
                );
            } else {
                throw new Exception();
            }
        } catch (Exception $e) {
            return response()->json(
                new ErrorResource([
                    'statusCode' => 422,
                    'message' => 'Failed adding notes',
                ]),
                422
            );
        }
    }

    public function addAgentNote($request)
    {
        try {
            $data = [
                'agent_id' => $request->agent_id,
                'message' => $request->message,
                'subject' => $request->subject ?? null,
                'agent_email' => $request->agent_email,
                'status' => $request->status,
                'added_by'=> $request->loginUserName ?? request()->header('name'),
            ];

            $data['date'] = (strtotime("now"));
            $status = false;
            if ($request->hasFile('attachment')) {
                if ($request->file('attachment')->getSize() > 4200000) {
                    return response()->json(
                        new ErrorResource([
                            'statusCode' => 400,
                            'message' => 'File too large, max allowed is 4 mb.',
                        ]),
                        400
                    );
                }
                $fileNameParts = explode('.', $request->file('attachment')->getClientOriginalName());
                $ext = strtolower(end($fileNameParts));
                if(!in_array($ext,self::mimes())){
                    return response()->json(
                        new ErrorResource([
                            'statusCode' => 400,
                            'message' => 'Attachment extension is invalid, it must be pdf, doc, docx, xls, xlsx, csv.',
                        ]),
                        400
                    );
                }
                $file = $request->file('attachment');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $data['Attachment'] = $fileName;
                $file->move($this->agentnoteFilePath, $fileName);
            }
            if($request->has('aid')){
                $aid_available = AgentNote::where('aid', $request->aid)->count();
                if($aid_available > 0){
                    $status = AgentNote::where('aid', $request->aid)->update($data);
                    $message = "Note updated successfully.";
                    $comm = 'Agent Note Updated';
                }
                else{
                    return ["error" => 'Note Id is not valid.'];
                }

            }
             else {
                $status = AgentNote::create($data);
                $comm = 'Agent Note created';

                $message = "Note added successfully.";
            }
            if ($status) {
                $data = [
                    "agent_id" => $request->agent_id,
                    "elgb_act" => \App\AgentUpdate::AGENT_NOTES_UPDATES,
                    "elgb_comment" => $comm,
                    "elgb_act_date" => time(),
                    "elgb_agent_id" => $request['loginUserId'] ?? request()->header('id'),
                    "elgb_agent_name" => $request['loginUserName'] ?? request()->header('name')
                ];
                AgentUpdatesHelper::createAgentUpdateLog($data);
                return response()->json(
                    new SuccessResource([
                        'statusCode' => 200,
                        'message' => $message,
                    ]),
                    200
                );
            } else {
                throw new Exception();
            }
        } catch (Exception $e) {
            return response()->json(
                new ErrorResource([
                    'statusCode' => 400,
                    'message' => 'Failed adding notes.',
                    'reason' => $e->getMessage(),
                ]),
                400
            );
        }
    }


    public function deleteNotes($request, $id)
    {
        try {
            $notes = PolicyNote::where('anID', $id)->first();
            if ($notes instanceof PolicyNote) {
                if (file_exists($this->noteFilePath . '/' . $notes->file) && $notes->file != '') {
                    unlink($this->noteFilePath . '/' . $notes->file);
                }
                PolicyNote::where('anID', $id)->delete();

                PolicyUpdateHelper::updateEligibility([
                    'elgb_policyid' => $request->policy_id,
                    'elgb_act' => PolicyUpdate::POLICY_NOTE_DELETE,
                    'elgb_act_date' => time(),
                    'elgb_comment' => $message = 'Note removed successfully.',
                    'elgb_agent' => request()->header('id'),
                    'elgb_file_date' => date('Y-m-d')
                ]);

                return ["success" => $message];
            } else {
                return ["error" => 'Notes not found.'];
            }
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }

    public function deleteAgentNotes($id)
    {
        try {
            $notes = AgentNote::find($id);
            if ($notes instanceof AgentNote) {
                if (file_exists($this->agentnoteFilePath . '/' . $notes->Attachment) && $notes->Attachment != '') {
                    unlink($this->agentnoteFilePath . '/' . $notes->Attachment);
                }
                AgentNote::where('aid', $id)->delete();
                $data = [
                    "agent_id" => $notes['agent_id'],
                    "elgb_act" => \App\AgentUpdate::AGENT_NOTES_DELETE,
                    "elgb_comment" => 'Agent notes deleted successfully',
                    "elgb_act_date" => time(),
                    "elgb_agent_id" => request()->header('id'),
                    "elgb_agent_name" => request()->header('name'),
                ];
                AgentUpdatesHelper::createAgentUpdateLog($data);
                return response()->json(
                    new SuccessResource([
                        'statusCode' => 200,
                        'message' => 'Agent Notes removed successfully.',
                    ]),
                    200
                );
            } else {
                return response()->json(
                    new ErrorResource([
                        'statusCode' => 400,
                        'message' => 'Agent Notes not found.',
                    ]),
                    400
                );
            }
        } catch (Exception $e) {
            return response()->json(
                new ErrorResource([
                    'statusCode' => 400,
                    'message' => 'Failed removing agent notes.',
                    'reason' => $e->getMessage(),
                ]),
                400
            );
        }
    }

    public function getLatestNote($request, $policyID)
    {
        try {
            $notes = PolicyNote::where('policy_id', $policyID)->orderBy('anID', 'DESC')->first();
            if (!$notes instanceof PolicyNote) {
                return ["error" => 'Notes not found.'];
            }
            $notes->added_by = '';
            $notes->annote = trim(preg_replace('/\s\s+/', ' ', strip_tags($notes->annote)));
            $notes->added_by = ($notes->username != '') ? $notes->username : $notes->anuser;
            $notes->created_at = ($notes->created_at != '') ? date('m/d/Y', strtotime($notes->created_at)) : '';
            $notes->file = ($notes->file != '' && file_exists($this->noteFilePath . '/' . $notes->file)) ? $request->root() . '/api/v1/download-attachment/' . $notes->file : '';
            $notes->status = CommonHelper::getNoteStatus($notes->status);
            return $notes;
        } catch (Exception $e) {
            return ["error" => 'Failed getting note.'];
        }
    }

    public function getAllNotes($request, $policyID)
    {
        try {
            $notes = PolicyNote::where('policy_id', $policyID)->whereNotNull('annote')->with('agent')->orderBy('anID', 'DESC')->get();
            $noteAll = [];
            foreach ($notes as $note) {
                $note->status_text = CommonHelper::getNoteStatus($note->status);
                $note->added_by = ($note->username != '') ? $note->username : $note->anuser;
                $note->created_at = ($note->created_at != '') ? date('m/d/Y', strtotime($note->created_at)) : '';
                $note->file = ($note->file != '' && file_exists($this->noteFilePath . '/' . $note->file)) ? $request->root() . '/api/v1/download-attachment/' . $note->file : '';
                $note->annote = trim(preg_replace('/\s\s+/', ' ', strip_tags($note->annote)));
                array_push($noteAll, $note);
            }
            return $noteAll;
        } catch (Exception $e) {
            return ["error" => 'Failed getting note.'];
        }
    }

    public function downloadNoteAttachment($filename)
    {
        if (file_exists( $this->noteFilePath . '/' . $filename)){
            $destinationPath = $this->noteFilePath . '/' . $filename;
        }
        else if(file_exists( $this->groupFilePath . '/' . $filename)){
            $destinationPath = $this->groupFilePath . '/' . $filename;
        }
        else{
            $destinationPath = $this->agentnoteFilePath . '/' . $filename;
        }

        return response()->download($destinationPath);
    }

    protected function groupNoteData($request)
    {
        return [
            'ants' => Carbon::now()->timestamp,
            'gid' => $request['group_id'],
            'annote' => $request['note'],
            'anuser' => isset($request['loginUserId']) ? $request['loginUserId'] : null,
            'username' => isset($request['loginUserName']) ? $request['loginUserName'] : null,
            'status' => $request['status'],
            'created_at' => Carbon::now()->format('Y-m-d H:i:s')
        ];
    }

    public function addGroupNotes(Request $request)
    {
        $groupNoteData = $this->groupNoteData($request);
        DB::beginTransaction();
        try {
            if ($request->hasFile('file')) {
                $uploadedFile = $this->uploadFile($request['file']);
                $groupNoteData['file'] = $uploadedFile;
            }

            $groupNote = PolicyNote::find($request->id);
            if ($groupNote instanceof PolicyNote && $groupNote->gid == $request->group_id)
            {
                $groupNoteData['updated_at'] = date('Y-m-d H:i:s');
                $status = $groupNote->update($groupNoteData);
                $message = "Group Note updated successfully.";
            } else {
                $status = PolicyNote::create($groupNoteData);
                $message = "Group Note added successfully.";
            }

            DB::commit();

            GroupUpdate::create([
                "group_id" => $request->group_id,
                "elgb_act" => GroupUpdate::GROUP_NOTE_UPDATE,
                "elgb_comment" => $message,
                "elgb_act_date" => time(),
                "elgb_agent" => $request->loginUserId ?? request()->header('id'),
                "elgb_agentname" => $request->loginUserName ?? request()->header('name'),
            ]);

            return $this->successResponse($message);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse('Failed to add note.');
        }
    }

    public function deleteGroupNote($request)
    {
        try {
            $groupNote = PolicyNote::find($request->noteId);
            $attachmentRemoveStatus = false;
            if ($groupNote instanceof PolicyNote) {
                $filePath = '/group-notes/' . $groupNote->file;
                if ( Storage::disk('s3')->exists($filePath) ) {
                    $attachmentRemoveStatus = Storage::disk('s3')->delete($filePath);
                }
                $groupNote->delete();

                $message = ($attachmentRemoveStatus)
                    ? 'Group Note and Attachment removed successfully.'
                    : 'Group Note removed successfully.';

                GroupUpdate::create([
                    "group_id" => $request->group_id,
                    "elgb_act" => GroupUpdate::GROUP_NOTE_DELETE,
                    "elgb_comment" => $message,
                    "elgb_act_date" => time(),
                    "elgb_agent" => request()->header('id'),
                    "elgb_agentname" => request()->header('name'),
                ]);

                return ["success" => $message];
            } else {
                return ["error" => 'Group Note not found.'];
            }
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }

    protected function uploadFile($file)
    {
        $fileName = null;
        if ($file instanceof UploadedFile) {
            $fileName = time() . '_' . $file->getClientOriginalName();
            Storage::disk('s3')->put('/group-notes/' . $fileName, file_get_contents($file));
        }
        return $fileName;
    }

    public function listGroupNotes($groupId)
    {
        $notes = PolicyNote::query()
            ->where('gid', '=', $groupId)
            ->orderBy('anID','desc')
            ->paginate(10);
        $data =  $this->formattedGroupNotes($notes);
        return $this->successResponse('Success',$data);
    }


    public function singleFormattedGroupNote($d)
    {
        return [
            'anID' => $d->anID,
            'note' => $d->annote,
            'file' => $d->file,
            'file_url' => $d->group_file_url,
            'status' => $d->status,
            'formatted_status' => array_search($d->status, PolicyNote::$statuses),
            'added_by' => $d->username,
            'added_by_id' => $d->anuser,
            'created_at' => $d->created_at,
            'added_on' => Carbon::createFromTimestamp($d->ants)->format('m/d/Y')
        ];
    }

    public function formattedGroupNotes($data = [])
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedGroupNote($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }
}
