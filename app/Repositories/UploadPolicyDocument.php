<?php

namespace App\Repositories;

use App\Mail\PolicyEmail;
use App\AgentAppsUpload;
use App\UserInfoPolicyAddress;
use App\PpoUpload;
use App\UpfileLocc;
use Illuminate\Database\Eloquent\Model;
use League\Flysystem\Filesystem;
use League\Flysystem\Sftp\SftpAdapter;
use Storage;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class UploadPolicyDocument extends Model
{
    public function addPolicyDocument($request)
    {
        $appName = $request->app_name;
        $caid = $request->agent_id;
        $fName =pathinfo($request->file_upload->getClientOriginalName(), PATHINFO_FILENAME);
        $ext = pathinfo($request->file_upload->getClientOriginalName(), PATHINFO_EXTENSION);
        $fileName = $fName.date('m-d-Y').'_app_'.$appName.'_'.$caid.'.'.$ext;
        $file_local = $request->file('file_upload');
        $extArray = array('pdf','jpg','jpeg');
        if(!in_array($ext,$extArray))
        {
            return array('type' => 'error', 'message' =>'Invalid document type. Please upload your document in .jpeg or .jpg or .pdf format.');
        }
        try{
            Storage::disk('nuerabenefits')->put('/admn/__files__/docs/'.$fileName,file_get_contents($file_local));
            if(Storage::disk('nuerabenefits')->exists('/admn/__files__/docs/'.$fileName))
            {
                $now = Carbon::now()->timestamp;
                $agentAppsUpload = new AgentAppsUpload;
                $agentAppsUpload->license_id = $caid;
                $agentAppsUpload->file_name = $fileName;
                $agentAppsUpload->date_added = $now;
                $agentAppsUpload->file_status = '1';
                $agentAppsUpload->app_name = $appName;
                $agentAppsUpload->app_desc = $request->app_desc;
                $agentAppsUpload->grp = 'idilus';
                $agentAppsUpload->pid = $request->policy_id;
                $agentAppsUpload->save();
                return array('type' => 'success', 'message' => 'Document Uploaded.');
            }
            else
            {
                return array('type' => 'error', 'message' => 'Unable to upload Document.');
            }
        } catch (Exception $e){
            $message = 'Failed To upload Document: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function viewPolicyDocument($request)
    {
    if($request->policy_id){
        $dataPolicyDocument = DB::select(DB::raw("(select doc_id, license_id, file_name, date_added, file_status, app_name, app_desc, grp, pid, 0 as policy_id, 0 as file_id, 0 as filename, 0 as date_upload from agent_apps_upload where pid='$request->policy_id' AND grp='idilus') union all (select 0 as docid, 0 as license_id, 0 as file_name, 0 as date_added, 0 as file_status, 0 as app_name, 0 as app_desc, 0 as grp, 0 as pid, policy_id, file_id, filename, date_upload from ppo_uploads where policy_id='$request->policy_id') order by date_added, date_upload desc"));
            $dataPolicy = DB::select(DB::raw("select * from userinfo_policy_address,plan_overview where userinfo_policy_address.policy_id=plan_overview.policy_id and userinfo_policy_address.policy_id=$request->policy_id"));
        }
        else
        {
            $dataPolicyDocument = DB::select(DB::raw("(select doc_id, license_id, file_name, date_added, file_status, app_name, app_desc, grp, pid, 0 as policy_id, 0 as file_id, 0 as filename, 0 as date_upload from agent_apps_upload) union all (select 0 as docid, 0 as license_id, 0 as file_name, 0 as date_added, 0 as file_status, 0 as app_name, 0 as app_desc, 0 as grp, 0 as pid, policy_id, file_id, filename, date_upload from ppo_uploads) order by date_added,date_upload desc"));
            $dataPolicy = [];
        }
        $data = [
            'dataPolicyDocument' => $dataPolicyDocument,
            'dataPolicy' => $dataPolicy,
        ];
        return $data;
    }
    public function deletePolicyDocument($request)
    {
        try{
            if($request->is_table=='0'){
                $delete = AgentAppsUpload::where('doc_id',$request->file_id)->delete();
            }else{
                $delete = PpoUpload::where('file_id',$request->file_id)->delete();
            }
            if($delete>0)
            {
                return array('type' => 'success', 'message' => 'Document Deleted.');
            }
            else
            {
                return array('type' => 'error', 'message' => 'Document Not Found.');
            }
        } catch (Exception $e){
            $message = 'Failed To Delete Document: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function addPolicyLetter($request)
    {
        $fName =pathinfo($request->file_upload->getClientOriginalName(), PATHINFO_FILENAME);
        $ext = pathinfo($request->file_upload->getClientOriginalName(), PATHINFO_EXTENSION);
        $fileName = $fName.'_'.date('m-d-Y').'.'.$ext;
        $file_local = $request->file('file_upload');
        $extArray = array('pdf','xls','xlsx');
        if(!in_array($ext,$extArray))
        {
            return array('type' => 'error', 'message' =>'Invalid document type. Please upload your document in .xls or .xlsx or .pdf format.');
        }
        try{
            Storage::disk('nuerabenefits')->put('/admn/__files__/_CC9_LOCC_FILE_STORAGE_/sdfhjgsdfhsdhjfgsdjfkjsdhfkjhsdf_sfldsfdsjfklj_10/'.$fileName,file_get_contents($file_local));
            if(Storage::disk('nuerabenefits')->exists('/admn/__files__/_CC9_LOCC_FILE_STORAGE_/sdfhjgsdfhsdhjfgsdjfkjsdhfkjhsdf_sfldsfdsjfklj_10/'.$fileName))
            {
                $now = Carbon::now()->timestamp;
                $upfileLocc = new UpfileLocc;
                $upfileLocc->title = $request->title;
                $upfileLocc->details = $request->details;
                $upfileLocc->ts = $now;
                $upfileLocc->status = '1';
                $upfileLocc->filename = $fileName;
                $upfileLocc->policy_id = $request->policy_id;
                $upfileLocc->save();
                return array('type' => 'success', 'message' => 'Document Uploaded.');
            }
            else
            {
                return array('type' => 'error', 'message' => 'Unable to upload Document.');
            }
        } catch (Exception $e){
            $message = 'Failed To upload Document: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function editPolicyLetter($request)
    {
        try{
            $upfileLocc = UpfileLocc::where('upid',$request->file_id)->first();
            if(!$upfileLocc->upid){
                return array('type' => 'error', 'message' => 'Document not found.');
            }
            if($request->file_upload)
            {
                $fName =pathinfo($request->file_upload->getClientOriginalName(), PATHINFO_FILENAME);
                $ext = pathinfo($request->file_upload->getClientOriginalName(), PATHINFO_EXTENSION);
                $fileName = $fName.'_'.date('m-d-Y').'.'.$ext;
                $file_local = $request->file('file_upload');
                $extArray = array('pdf','xls','xlsx');
                if(!in_array($ext,$extArray))
                {
                    return array('type' => 'error', 'message' =>'Invalid document type. Please upload your document in .xls or .xlsx or .pdf format.');
                }
                Storage::disk('nuerabenefits')->put('/admn/__files__/_CC9_LOCC_FILE_STORAGE_/sdfhjgsdfhsdhjfgsdjfkjsdhfkjhsdf_sfldsfdsjfklj_10/'.$fileName,file_get_contents($file_local));
                if(Storage::disk('nuerabenefits')->exists('/admn/__files__/_CC9_LOCC_FILE_STORAGE_/sdfhjgsdfhsdhjfgsdjfkjsdhfkjhsdf_sfldsfdsjfklj_10/'.$fileName))
                {
                    $upfileLocc->filename = $fileName;
                }
            }
            $upfileLocc->title = $request->title;
            $upfileLocc->details = $request->details;
            $upfileLocc->save();
            return array('type' => 'success', 'message' => 'Document Updated.');
        } catch (Exception $e){
            $message = 'Failed To upload Document: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function viewPolicyTaxDocument($request)
    {
        $data = PpoUpload::where('policy_id',$request->policy_id)->get();
        return $data;
    }
    public function addPolicyTaxDocument($request)
    {
        try
        {
            $policyID = $request->policy_id;
            if ($request->hasFile('file'))
            {
                foreach ($request->file('file') as $file)
                {
                    $fileName =  $policyID.'_'.$file->getClientOriginalName();
                    if (Storage::disk('corenroll')->put('/PPO_FILE_UPLOADS/' . $fileName, file_get_contents($file)))
                    {
                        PpoUpload::insert(['policy_id' => $policyID, 'filename' => $fileName, 'uptime' => time(), 'date_upload' => date('Y-m-d')]);
                    }
                }
            }
            return array('type' => 'success', 'message' => 'Document Uploaded.');
        }
        catch (Exception $e)
        {
            $message = 'Failed To upload Document: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function addPolicyTaxDocumentReminderEmail($request)
    {
        try {
            $policyID = $request->policy_id;
            $data = UserInfoPolicyAddress::where('policy_id', $policyID)->get()->first();
            $agentDetails = optional($data)->toArray();
            if (!$agentDetails)
            {
                return array('type' => 'error', 'message' => 'Policy not found.');
            }
            $agentName = $agentDetails['agent_fname'];
            $agentEmail = $agentDetails['agent_email'];
            $cEmail = $agentDetails['cemail'];
            $messageTop = " This is an automated notice to notify you to upload one of the following tax document.";
            $emailData = [
                'agentName' => $agentName,
                'messageTop' => $messageTop,
                'middleMessage' => '',
                'data' => [],
                'companyPhone' => '',
                'subject' => "Upload Tax Document",
                'templateName' => 'taxUploadReminder',
                'url' => 'https://corenroll.com/ppo-upload.php?policy_id=' . base64_encode($policyID)
            ];
            Mail::to($cEmail)->bcc($agentEmail)->send(new PolicyEmail($emailData));
            return array('type' => 'success', 'message' => 'Email sent successfully.');
        } catch (Exception $e) {
            $message = 'Failed To send upload reminder email: ';
            return ['error' => $e->getMessage()];
        }
    }
    public function addNewFiles($request)
    {
        try{
            $policy_id = $request->input('policy_id');
            $fName =pathinfo($request->file_upload->getClientOriginalName(), PATHINFO_FILENAME);
            $ext = pathinfo($request->file_upload->getClientOriginalName(), PATHINFO_EXTENSION);
            $fileName = $fName.date('m-d-Y').'.'.$ext;
            $file_local = $request->file('file_upload');
            //get details of policy
            Storage::disk('corenroll')->put('/CORENROLL/PPO_FILE_UPLOADS/'.$fileName,file_get_contents($file_local));
            if(Storage::disk('corenroll')->exists('/CORENROLL/PPO_FILE_UPLOADS/'.$fileName))
            {
                $taxDoc = [
                    'file_id' => '',
                    'policy_id' =>$policy_id,
                    'filename' => $fileName,
                    'date_upload' =>date('Y-m-d'),
                    'uptime' =>time()
                ];
                $insertPpoUploads=PpoUpload::insertGetId($taxDoc);
                if(empty($insertPpoUploads))
                    throw new Exception('No Insertion');
                return array('type'=>'success','response'=>$insertPpoUploads);
            }
        }catch(Exception $e){
            $message = 'Failed To add new files.';
            return ['error' => $e->getMessage()];
        }
    }

}
