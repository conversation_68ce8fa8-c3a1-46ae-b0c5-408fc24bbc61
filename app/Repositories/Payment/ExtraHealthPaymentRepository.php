<?php


namespace App\Repositories\Payment;

use App\ExtraHealthPaymentFileGeneration;
use App\Jobs\ExportTempPaymentExcel;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use GuzzleHttp\Client;
use Illuminate\Http\Request;


class ExtraHealthPaymentRepository
{
    use Paginator, ResponseMessage;

    public function __construct()
    {
    }

    public function generate_export(Request $request)
    {
        $data['added_by'] = request()->header('name');
        $data['added_by_id'] = request()->header('id');
        $tempPayments = $this->fetchTempDetails($request);
        if (count($tempPayments) > 0) {
            ExportTempPaymentExcel::dispatch($tempPayments, $data)->onQueue('processing');
        }
    }

    private function fetchTempDetails($request)
    {
        $params = [
            'perPage' => $request->input('perPage', 30),
            'page' => $request->input('page', 1),
            'policy_id' => $request->input('policy_id'),
            'transaction_date' => $request->input('transaction_date'),
            'firstname' => $request->input('firstname'),
            'lastname' => $request->input('lastname'),
            'transaction_status' => $request->input('transaction_status'),
            'email' => $request->input('email'),
            'card' => $request->input('card'),
            'card_type' => $request->input('card_type'),
            'phone_number' => $request->input('phone_number'),
            'rep_name' => $request->input('rep_name')
        ];

        $externalUrl = config('app.payment_system.url') . 'fetch-temp-payment-detalis';

        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];

        $client = new Client();

        $response = $client->get($externalUrl, [
            'headers' => $headers,
            'query' => $params,
        ]);

        $data = json_decode($response->getBody(), true);
        return $data['data']['data'] ?? [];
    }

    public function getPaymentReport($request): array
    {
        $paymentFileDetail = ExtraHealthPaymentFileGeneration::fetchPaymentReport($request['id']);
        $paymentFileList = $paymentFileDetail->map(function ($paymentFileList) {
            return collect($paymentFileList->toArray())
                ->only(['id', 'file_path', 'file_name', 'file_generation_started', 'file_generation_ended', 'no_of_record', 'added_by', 'added_by_id', 'created_at', 'updated_at', 'file_download_link'])
                ->all();
        });
        return [
            'data'=>$paymentFileList,
            'links' => $this->links($paymentFileDetail),
            'meta' => $this->meta($paymentFileDetail)
        ];
    }
}
