<?php


namespace App\Repositories\Payment;


use App\Address;
use App\Helpers\DecryptEcryptHelper;
use App\Helpers\EmailSendHelper;
use App\Helpers\GuzzleHelper;
use App\Helpers\PolicyUpdateHelper;
use App\PaymentCCMerchant;
use App\PaymentTypeCc;
use App\PlanOverview;
use App\Policy;
use App\Service\DependentInfoService;
use App\Traits\ResponseMessage;
use App\UserInfo;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use stdClass;

class CcPaymentRepository
{
    use ResponseMessage;
    /**
     * @var PaymentTypeCc
     */
    private $model;
    /**
     * @var PaymentCCMerchant
     */
    private $modelMerchant;

    /**
     * CcPaymentRepository constructor.
     * @param PaymentTypeCc $model
     * @param PaymentCCMerchant $modelMerchant
     */
    public function __construct(PaymentTypeCc $model, PaymentCCMerchant $modelMerchant)
    {
        $this->model = $model;
        $this->modelMerchant = $modelMerchant;
    }

    public function checkPayerByUserId($userId , $merchant = null)
    {
        return $this->model::query()
            ->where('cc_userid', $userId)
            ->whereNotNull('payer_id')
            ->whereNotNull('card_id')
            ->when($merchant === 'nuera', function ($query) use ($merchant) {
                $query->where(function($q) use ($merchant) {
                    $q->where('merchant', $merchant)
                    ->orWhereNull('merchant');
                });
            }, function ($query) use ($merchant) {
                $query->where('merchant', $merchant);
            })
            ->first();
    }

    protected function paymentCcData($request, $addressId, $cardInfoData): array
    {
        return [
            'cc_type' => $request['credit_type'],
            'cc_num' => DecryptEcryptHelper::encryptInfo($request['card_num']),
            'cc_num4' => substr($request['card_num'], -4, 4),
            'cc_expmm' => $request['expiryMonth'],
            'cc_expyyyy' => $request['expiryYear'],
            'cc_date' => strtotime("now"),
            'cc_status' => $request['credit_status'],
            'cc_userid' => $request['userId'],
            'cc_contactfname' => $request['fname'],
            'cc_contactlname' => $request['lname'],
            'cc_cvc' => $request['credit_cvc'],
            'cc_addressid' => $addressId,
            'merchant' => $cardInfoData['merchant'],
            'payer_id' => $cardInfoData['payer_id'] ?: null,
            'card_id' => $cardInfoData['card_id'] ?: null,
            'verification' => [
                'payer_id' => $cardInfoData['payer_id'] ?: null,
                'card_id' => $cardInfoData['card_id'] ?: null,
                'merchant' => $cardInfoData['merchant'],
                'verified_at' => Carbon::now()->format('Y-m-d H:i:s')
            ]
        ];
    }

    protected function paymentAddAddressData($request): array
    {
        return [
            'a_userid' => $request['userId'],
            'address1' => $request['address1'],
            'address2' => $request['address2'],
            'city' => $request['city'],
            'state' => $request['state'],
            'zip' => $request['zip'],
            'status' => 1,
            'type' => 'B',
        ];
    }

    protected function cardInfoData($request): array
    {
        return [
            'nameOnCard' => $request['fname'] . ' ' . $request['lname'],
            'cardNumber' => $request['card_num'],
            'expirationMonth' => (string)$request['expiryMonth'],
            'expirationYear' => (string)$request['expiryYear'],
            'securityCode' => $request['credit_cvc'],
            'billingAddress' => [
                'street1' => $request['address1'],
                'street2' => $request['address2'],
                'city' => $request['city'],
                'state' => $request['state'],
                'postalCode' => $request['zip'],
                'country' => 'USA'
            ]
        ];
    }

    protected function cardInfo($request): array
    {
        /**
         * @var $user UserInfo
         */
        $user = UserInfo::find($request['userId']);
        if (! isset($request['merchant'])) {
            $depService = new DependentInfoService();
            $isExtraHealth = $depService->checkIfPolicyHasExtraHealth($request['policyId']);
            if($isExtraHealth){
                $merchantData = new stdClass();
                $merchantData->merchant = 'extra_health';
            }
            else{
                $merchantResponse = $this->merchantApi($request['policyId'], 'policy');
                if (!$merchantResponse['status'])
                    return $this->failedValidation('Failed to fetch Merchant Account');
                $merchantData = $merchantResponse['data'];
            }

        }

        // old implementtions
        // $payer = $this->checkPayerByUserId($request['userId'], $merchantData->merchant);
        $payer = $this->checkPayerByUserId($request['userId'], isset($request['merchant']) ? $request['merchant'] : $merchantData->merchant);
        
        $cardInfoData = $this->cardInfoData($request);
        if (isset($payer)) {
            $requestData = [
                'merchant' => $request['merchant'] ?? $merchantData->merchant,
                'card' => $cardInfoData,
                'payerType' => 'existing',
                'payerId' => $payer->payer_id
            ];
        } else {
            $payerData = self::preparePayerInfoExisting($user);
            $requestData = [
                'merchant' => $request['merchant'] ?? $merchantData->merchant,
                'card' => $cardInfoData,
                'payerType' => 'new',
                'payer' => $payerData
            ];
        }

        return $requestData;
    }

    public static function preparePayerInfoExisting($user): array
    {
        $payer = [];
        if ($user) {
            $payer = [
                'name' => $user->cfname . ' ' . $user->cmname . ' ' . $user->clname,
                'email' => $user->cemail
            ];
            $userAddress = $user->customerAddress;
            if ($userAddress) {
                $payer['address'] = [
                    'street1' => $userAddress->address1,
                    'street2' => $userAddress->address2,
                    'city' => $userAddress->city,
                    'state' => $userAddress->state,
                    'postalCode' => $userAddress->zip,
                    'country' => 'USA'
                ];
            }
        }
        return $payer;
    }

    public function addCreditInfo($request)
    {
        //reformat to default year after validation
        $request['expiryYear'] = Carbon::parse($request['expiryYear'])->format('Y');
        $cardRequestData = $this->cardInfo($request);
        $getMerchant = $this->getMerchant($request['policyId']);
        $cardRequestData['merchant'] = $getMerchant;
        $cardInfo = $this->payStandApi($cardRequestData);
        if ($cardInfo['status']) {
            $cardInfoData = [
                'payer_id'  => $cardInfo['data']->payerId,
                'card_id'   => $cardInfo['data']->cardId,
                'merchant'  => $cardInfo['data']->merchant
            ];
            DB::beginTransaction();
            try {
                $homeAddress = $this->paymentAddAddressData($request);
                if (isset($request['maddress'])){
                    $addressID = $request['maddress'];
                }else{
                    $addressID = Address::insertGetId($homeAddress);
                }
                $data = $this->paymentCcData($request, $addressID, $cardInfoData);
                $cc = $this->model::create($data);
                $data['verification']['cc_id'] = $cc->cc_id;
                $this->modelMerchant::create($data['verification']);
                /**
                 * update policy
                 * @var $policy Policy
                 */
                $policy = Policy::where('policy_id', $request['policyId'])->first();
                if (isset($policy)) {
                    $policy->update([
                        'payment_id' => $cc->cc_id,
                        'payment_type' => 'cc',
                        'recurring_payment_id'=>$cc->cc_id,
                        'recurring_payment_type'=>'cc'
                    ]);
                }

                $this->preparePostEligibilityLog(
                    $request['policyId'], 'bic',
                    "Credit Card added. Changed Recurring and Default Payment Method to CC.",
                    date('Y-m-d')
                );

                if ($request['sendEmail'] == 1) {
                    EmailSendHelper::sendUserPolicyActionEmail($request['userId'], "Billing Information CC", "Billing Information CC has been updated", 'billinginformation', [], 'BIC');
                }
                DB::commit();
                return $this->successResponse('Successfully added credit card billing information.');
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse('Failed to add credit card billing information.');
            }
        } else {
            return $this->failedResponse('Failed to add credit card information.');
        }

    }

    public function payStandApi($requestData): array
    {
        $url = config('app.payment_system.url') . 'cards/create';
        $response = GuzzleHelper::curlPostApi($url, json_encode($requestData), 'default', true);
        $responseData = [];
        if (isset($response->status) && $response->status == 'success') {
            $responseData['status'] = true;
            $responseData['data'] = $response->data;
        } elseif (isset($response->status) && $response->status == 'error') {
            $responseData['status'] = false;
            $this->payStandErrorResponse($response);
        } else {
            $responseData['status'] = false;
            $responseData['data'] = $response->data;
        }
        $responseData['data']->merchant = $requestData['merchant'];

        return $responseData;
    }

    protected function payStandErrorResponse($response)
    {
        $data = $response->data;
        $failedResult = [];
        if ($data->code == 'paymentFailure') {
            $failedResult['card_num'][0] = $data->explanation;
        } elseif ($data->code == 'insufficientResourceAccess') {
            $data->reason = 'Invalid or Expired Payer Id, not found in Payment Database.';
            $failedResult = $data;
        } else {
            $failedResult = $data;
        }
        $this->failedValidation($failedResult);
    }

    public function merchantApi($id, $idType): array
    {
        $url = config('app.purenroll_system.url') . "merchant/$idType/$id";
        $response = json_decode(GuzzleHelper::getApi($url, []));
        if (isset($response->status) && $response->status == 'success') {
            $responseData['status'] = true;
            $responseData['data'] = $response->data;
        } else {
            $responseData['status'] = false;
            $responseData['data'] = $response->message ?? $response->error ?? 'Failed to fetch merchant.';
        }
        return $responseData;
    }

    public function verifyExistingCardWithMerchant($request)
    {
        $ccData = PaymentTypeCc::with('address')->find($request['id']);
        $cardInfoData = [
            'credit_type' => $ccData->cc_type,
            'card_num' => $ccData->cc_num ? DecryptEcryptHelper::decryptInfo($ccData->cc_num) : null,
            'credit_status' => $ccData->cc_status,
            'userId' => $ccData->cc_userid,
            'expiryMonth' => $ccData->cc_expmm ?? null,
            'expiryYear' => $ccData->cc_expyyyy ?? null,
            'fname' => $ccData->cc_contactfname ?? null,
            'lname' => $ccData->cc_contactlname ?? null,
            'credit_cvc' => $ccData->cc_cvc ?? null,
            'address1' => $ccData->address['address1'],
            'city' => $ccData->address['city'],
            'zip' => $ccData->address['zip'],
            'state' => $ccData->address['state'],
            'address2' => $ccData->address['address2'],
            'merchant' => $request['merchant']
        ];

        $cardRequestData = $this->cardInfo($cardInfoData);
        $cardInfo = $this->payStandApi($cardRequestData);

        return $this->successResponse('Successfully verified credit card billing information.',
            $cardInfo['data']);
    }

    private function preparePostEligibilityLog($policyId, $elgbActCode, $comment, $elgbDate, $loggedUserId = null)
    {
        $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
        $data['elgb_policyid'] = $policyId;
        $data['elgb_act'] = $elgbActCode;
        $data['elgb_act_date'] = time();
        $data['elgb_comment'] = $comment;
        $data['elgb_file_date'] = $elgbDate;
        $data['elgb_agent'] = $loggedUserId ?? request()->header('id') ?? null;
        $data['origin'] = $origin;

        PolicyUpdateHelper::updateEligibility($data);
    }

    public static function getMerchant(string $policy): string
    {
        $cutoffDate = Carbon::create('2025-04-11');
        $isExtraHealth = self::checkIfPolicyHasExtraHealth($policy);
        $policyEffectiveDate = Policy::where('policy_id', $policy)
        ->value('edate');
        $policyEffectiveDate = Carbon::createFromTimestamp($policyEffectiveDate);
        $policyDate = Carbon::parse($policyEffectiveDate);
        if ($policyDate->lt($cutoffDate)) {
            return 'nuera';
        }

        if ($policyDate->gt($cutoffDate) && $isExtraHealth) {
            return 'extra_health';
        }
        return 'nuera';
    }

    public static function checkIfPolicyHasExtraHealth($policy_id)
    {
        return PlanOverview::query()->where('policy_id', $policy_id)
            ->whereHas('hasExtraHelthPlans')
            ->exists();
    }
}
