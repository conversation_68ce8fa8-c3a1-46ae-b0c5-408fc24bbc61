<?php

namespace App\Repositories\Payment;

use App\Helpers\EmailSendHelper;
use App\Helpers\PolicyUpdateHelper;
use App\PaymentEft;
use App\CreditCardInfo;
use App\Policy;
use App\PlanOverview;
use App\PolicyUpdate;
use App\UserInfoPolicyAddress;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use League\Flysystem\Exception;

class DefaultAndRecurringFeatures extends Model
{
    public function updatePolicy($setData, $id)
    {
        try{
            Policy::where('policy_id',$id)->update($setData);
            $message = "Policy  Updated successfully";
        }
        catch(Exception $e){
            return ['error'=>$e->getMessage()];
        }
        return ['success'=>$message];
    }
    public function paymentEFTCount($payid)
    {
        try {
            return PaymentEft::where('bank_id', $payid)->get()->count();
        }
        catch(Exception $e){
            return ['error'=>$e->getMessage()];
        }
    }
    public function creditCardInfoCount($payid){
        try {
//            return CreditCardInfo::where('creditcard_id', $payid)->get()->count();
            return DB::table('payment_cc')
            ->where('cc_id', $payid)->get()->count();
        }
        catch(Exception $e){
            return ['error'=>$e->getMessage()];
        }
    }
    public function updatePaymentEFT($setData, $condition)
    {
        try{
            PaymentEft::where(function($q) use ($condition)
            {
                foreach($condition as $key => $value)
                {
                    $q->where($key, '=', $value);
                }
            })->update($setData);
            $message = "Updated Payment successfully";
        }
        catch(Exception $e){
            return ['error'=>$e->getMessage()];
        }
        return ['success'=>$message];
    }
    protected function updateCreditCardInfo($setData, $condition)
    {
        try{
//            CreditCardInfo::where(function($q) use ($condition)
            DB::table('payment_cc')->where(function($q) use ($condition)
            {
                foreach($condition as $key => $value)
                {
                    $q->where($key, '=', $value);
                }
            })->update($setData);
            $message = "Updated CreditCard Info successfully";
        }
        catch(Exception $e){
            return ['error'=>$e->getMessage()];
        }
        return ['success'=>$message];
    }
    public function postEligibility($policy_id, $action, $reason, $agent, $date_efile, $tdate)
    {
        $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
        $data['elgb_policyid'] = $policy_id;
        $data['elgb_act'] = $action;
        $data['elgb_act_date'] = time();
        $data['elgb_comment'] = $reason;
        $data['elgb_agent'] = $agent;
        $data['elgb_file_date'] = $date_efile;
        $data['elgb_term_date'] = $tdate;
        $data['origin'] = $origin;
        return PolicyUpdateHelper::updateEligibility($data);

    }
    public function defaultRecurringMethodSet($request)
    {
        $type = $request->type;
        $policyID = $request->policy_id;
        $payid = $request->payid;
        $userID = $request->user_id;
        $recurring = $request->recurring;
        $agent = $request->aid;
        $dateEfile = date('Y-m-d');
        $comment = "Payment method has changed to $type";
        if (!UserInfoPolicyAddress::where('policy_id', $request->policy_id)->count()) {
            return ['error' => 'Policy not found.'];
        }
        if($recurring){
            $comment = "Recurring Payment method has changed to $type";
            $data = array('recurring_payment_type'=>$type,'recurring_payment_id'=>$payid);
            $this->updatePolicy($data,$policyID);
            $this->postEligibility($policyID,"bic", $comment, $agent, $dateEfile, '');
            $data =  ['success'=>$comment];
        } else {
            $data =  $this->setOtherTypes($type, $payid, $userID, $policyID, $comment, $agent, $dateEfile);
        }
        $extraHealthExist = $this->findExtraHealthCarrierOnPolicy($policyID);
        if (!$extraHealthExist) {
            EmailSendHelper::sendPolicyActionEmail($request->policy_id, "Payment Method Updated", $comment, 'defaultrecurring','BIC');    
        }
//        EmailSendHelper::sendGenericPolicyEmailUserPolicyDetails($request->policy_id, "Payment Method Updated", $comment, 'defaultrecurring','BIC');
        
        return $data;
    }
    protected function setOtherTypes($type, $payid, $userID, $policyID, $comment, $agent, $dateEfile)
    {
        if ($type=='list' || $type=='elist'|| $type =='stmt' ) {
            return $this->setListElistSTMT($type, $policyID, $comment, $agent, $dateEfile);
        }
        $num =0;
        if ($type=='eft') {
            $num = $this->paymentEFTCount($payid);
            $this->updatePaymentEFT(array('bank_status'=>'N'),array('bank_userid'=>$userID));
            $this->updatePaymentEFT(array('bank_status'=>'A'),array('bank_userid'=>$userID,'bank_id'=>$payid));
        }
        if ($type=='cc') {
            $num = $this->creditCardInfoCount($payid);
//            $this->updateCreditCardInfo(array('credit_status'=>'N'),array('credit_userid'=>$userID));
//            $this->updateCreditCardInfo(array('credit_status'=>'A'),array('credit_userid'=>$userID,'creditcard_id'=>$payid));
            $this->updateCreditCardInfo(array('cc_status'=>'N'),array('cc_userid'=>$userID));
            $this->updateCreditCardInfo(array('cc_status'=>'A'),array('cc_userid'=>$userID,'cc_id'=>$payid));
        }
        if ($num>0) {
            $data = array("payment_type"=>$type,"payment_id"=>$payid);
            $this->updatePolicy($data,$policyID);;
            $this->postEligibility($policyID, "bic", $comment, $agent, $dateEfile, '');
            return ['success'=>$comment];
        }
        else {
            return ['error'=>'No User Found'];
        }
    }
    protected function setListElistSTMT($type, $policyID, $comment, $agent, $dateEfile)
    {
        $data = array('payment_type'=>$type,'payment_id'=>'');
        $this->updatePolicy($data, $policyID);
        $this->postEligibility($policyID, "bic", $comment, $agent, $dateEfile, '');
        return ['success'=>$comment];
    }

    public function findExtraHealthCarrierOnPolicy($policy_id)
    {
        $containsExtraHealthCarrier = PlanOverview::where('policy_id', $policy_id)
            ->where('cid', 92)
            ->exists();

        return $containsExtraHealthCarrier;
    }
}
