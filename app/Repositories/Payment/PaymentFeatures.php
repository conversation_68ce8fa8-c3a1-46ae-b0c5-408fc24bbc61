<?php

namespace App\Repositories\Payment;

use App\Helpers\DecryptEcryptHelper;
use App\Helpers\GuzzleHelper;
use App\PaymentEft;
use App\PaymentTypeCc;
use App\Policy;
use App\RepInfoRequest;
use App\RepInfoRequestsDetail;
use App\Traits\ResponseMessage;
use Illuminate\Database\Eloquent\Model;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PaymentFeatures extends Model
{
    use ResponseMessage;

    public function addUpdateBankAccount($request)
    {
        $encryptedBankAccount = DecryptEcryptHelper::encryptInfo($request->bank_account);
        $bankAccount4 = substr($request->bank_account, -4, 4);
        $data['bank_name'] = $request->bank_name;
        $data['bank_accountname'] = $request->bank_accountname;
        $data['bank_account'] = $encryptedBankAccount;
        $data['bank_routing'] = $request->bank_routing;
        $data['bank_date'] = time();
        $data['bank_account4'] = $bankAccount4;
        $data['bank_status'] = 'A';
        $data['bank_userid'] = $request->bank_userid;
        if ($request->bank_id != null) {
            $value = $this->updateBankInfo($data, $request->bank_id);
        } else {
            $value = $this->addBankInfo($data);
        }
        return $value;
    }

    public function fetchPaymentMethod($request)
    {
        $policyID = $request->pid;
        $user = $this->getUserID($policyID);
        if (!isset($user->userid)) {
            return ['error' => 'No Any User Found'];
        }
        $userID = $user->userid;
        $payCC = $this->getPayCC($userID);
        $paymentEFT = $this->getPaymentEFT($userID);
        return [
            'user' => $user,
            'payCC' => $payCC,
            'paymentEFT' => $paymentEFT
        ];
    }

    public function addBankInfo($data)
    {
        try {
            PaymentEft::insert($data);
            $message = "Bank Account Added Successfully.";
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
        return ['success' => $message];
    }

    public function updateBankInfo($data, $bankID)
    {
        try {
            PaymentEft::where('bank_id', $bankID)->update($data);
            $message = "Bank Details Updated Successfully.";
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
        return ['success' => $message];
    }

    public function getBankDetailsByID($id)
    {
        try {
            $data = PaymentEft::where('bank_id', $id)->get();
            if (!isset($data[0]['bank_account'])) {
                return ['error' => 'Bank Account Not Found'];
            }
            $data[0]['bank_account'] = DecryptEcryptHelper::decryptInfo($data[0]['bank_account']);
            return $data;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function getUserID($pid)
    {
        try {
            return UserInfoPolicyAddress::where('policy_id', $pid)->get()->first();
        } catch (Exception $e) {
            $message = $e->getMessage();
            return $message;
        }
    }

    public function getPayCC($userID)
    {
        try {
//             $creditCardIDs = CreditCardInfo::where('status','edited')->pluck('creditcard_id')->toArray();
//            return CreditCardInfo::where('credit_userid',$userID)->whereNOTIn('creditcard_id',$creditCardIDs)->get();
            $data = DB::table('payment_cc')
                ->where('cc_userid', '=', $userID)
                ->get();
            $result = [];
            foreach ($data as $d) {
                $collection = collect($d);
                $collection->put('decrypted_cc_num', DecryptEcryptHelper::decryptInfo($d->cc_num));
                $result[] = $collection->all();
            }
            return $result;
        } catch (\Throwable $e) {
            return $e->getMessage();
        }
    }

    public function getPaymentEFT($userID)
    {
        try {
            return PaymentEft::where('bank_userid', $userID)->get();
        } catch (Exception $e) {
            $message = $e->getMessage();
            return $message;
        }
    }

    public function togglePaymentStatus($request)
    {
        $type = $request->type;
        $moduleId = $request->moduleId;
        try {
            switch ($type) {
                case 'cc':
                    $data = DB::table('payment_cc')
                        ->where('cc_id', '=', $moduleId)
                        ->first();
                    $status = $data->cc_status == 'A' ? 'N' : 'A';
                    DB::table('payment_cc')
                        ->where('cc_id', '=', $moduleId)
                        ->update(['cc_status' => $status]);
                    $message = 'Status Changed';
                    break;
                case 'eft':
                    //todo eft bank status
                    $message = 'Eft Status Changed';
                default:
                    $message = '';
                    return null;
            }
            return $this->successResponse($message);
        } catch (\Throwable $th) {
            return $this->failedResponse($th->getMessage());
        }

    }

    public function processPaymentForCards(RepInfoRequest $modal)
    {
        $payment_amount = (float) 5; // Amount to be charged for each card
        $recurring_payment_id = Policy::where('policy_id', $modal->policy_id)->value('recurring_payment_id');
        $getPaymentData = $this->getPaymentInfo($modal->userid , $recurring_payment_id);
        if (!$getPaymentData) {
            return [
                'success' => false,
                'message' => 'No payment method found for this user.'
            ];
        }
    
        $requestData = [
            'card_id' => $getPaymentData->card_id,
            'payer_id' => $getPaymentData->payer_id,
            // 'amount' => (float) $modal->cards_requested,
            'amount' => $payment_amount,
        ];
    
        $url = config('app.payment_system.url') . 'process-client-payment';
    
        try {
            $response = GuzzleHelper::postApi($url, [], $requestData);
            $decodedResponse = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("Invalid JSON response from payment API");
            }

            if(isset($decodedResponse['success']) && $decodedResponse['success'] == false){
                RepInfoRequestsDetail::create([
                    'info_request_id' => $modal->id,
                    'card_id' => $getPaymentData->card_id,
                    'payer_id' => $getPaymentData->payer_id,
                    'amount' => $modal->cards_requested,
                    'status' =>'Failed',
                    'payment_description' => $decodedResponse['message'] ?? 'Payment processing failed.',
                ]);
                return [
                    'success' => false,
                    'message' => $decodedResponse['message'] ?? 'Payment processing failed.'
                ];
            }
            if (isset($decodedResponse['data'])) {
                RepInfoRequestsDetail::create([
                    'info_request_id' => $modal->id,
                    'card_id' => $getPaymentData->card_id,
                    'payer_id' => $getPaymentData->payer_id,
                    'amount' => $decodedResponse['data']['amount'] ?? 0,
                    'payment_id' => $decodedResponse['data']['payment_id'] ?? null,
                    'status' => $decodedResponse['data']['status'] ?? null,
                    'payment_description' => $decodedResponse['message'] ?? 'Payment processing failed.',
                    'clearing_date' => isset($decodedResponse['data']['clearing_date']) 
                        ? Carbon::parse($decodedResponse['data']['clearing_date']) 
                        : null,
                    'created_date' => isset($decodedResponse['data']['created_date']) 
                        ? Carbon::parse($decodedResponse['data']['created_date']) 
                        : null,
                    'invoice_id' => $decodedResponse['data']['invoice_id'] ?? null,
                ]);
            }
    
            return [
                'success' => true,
                'data' => $decodedResponse
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    

    public function getPaymentInfo($userId , $recurring_payment_id)
    {
        return PaymentTypeCc::query()
            ->where('cc_userid', $userId)
            ->where('cc_id', $recurring_payment_id)
            ->whereNotNull('payer_id')
            ->whereNotNull('card_id')
            ->first();
    }
}
