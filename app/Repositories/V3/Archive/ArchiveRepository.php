<?php
namespace App\Repositories\V3\Archive;

use App\AgentInfo;
use App\AgentUser;
use App\AgentUserArchive;
use App\GroupUser;
use App\GroupUserArchive;
use App\PushNotificationLog;
use App\SsoLoginArchive;
use App\UserLogin;
use App\UserLoginArchive;
use App\SsoUser;
use App\SsoUserType;
use App\UserDeviceID;
use App\UserInfo;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class ArchiveRepository
{
    public function deleteAgent($id)
    {
        Log::channel('archive')->info("Deleting agent with ID: $id");
        return DB::transaction(function () use ($id) {
            $agent = AgentInfo::find($id);
            $agent_user = AgentUser::where('agent_id',$id)->first();
            if ($agent_user) {
                $agentArchive = AgentUserArchive::withTrashed()->where('agent_id', $id)->first();
                if ($agentArchive) {
                    $agentArchive->fill($agent_user->toArray())->save();
                } else {
                    AgentUserArchive::create($agent_user->toArray());
                }
                $agent_user->forceDelete();
            }

            $ssoUser = SsoUser::where('email', $agent->agent_email)->where('user_type', 'A')->first();
            if ($ssoUser) {
                $ssoLoginArchive = SsoLoginArchive::withTrashed()->where('email', $ssoUser->email)->first();
                if ($ssoLoginArchive) {
                    $ssoLoginArchive->fill($ssoUser->toArray())->save();
                } else {
                    SsoLoginArchive::create($ssoUser->toArray());
                }
                $this->deleteSSOUser($ssoUser);
            }
            
            Log::channel('archive')->info("Agent with ID: $id deleted successfully");
            return true;
        });
    }
    
    public function restoreAgent($id)
    {
        Log::channel('archive')->info("Restoring agent with ID: $id");
        return DB::transaction(function () use ($id) {
            $agentInfo = AgentInfo::withTrashed()->where('agent_id', $id)->first();
            $agentArchive = AgentUserArchive::withTrashed()->where('agent_id', $id)->first();
            if ($agentArchive) {
                $agent = AgentUser::where('agent_id',$id)->first();
                if ($agent) {
                    throw new \Exception('Agent user already exist.', Response::HTTP_CONFLICT);
                } else {
                    $agentData = $agentArchive->toArray();
                    unset($agentData['id']);
                    $agent = AgentUser::create($agentData);
                }
                $agentArchive->delete();
            }
            $ssoLoginArchive = SsoLoginArchive::withTrashed()->where('email', $agentInfo->agent_email)->first();
            if ($ssoLoginArchive) {
                $ssoUser = SsoUser::where('email', $ssoLoginArchive->email)->where('user_type', 'A')->first();
                if ($ssoUser) {
                    $ssoUser->fill($ssoLoginArchive->toArray())->save();
                } else {
                    $ssoData = $ssoLoginArchive->toArray();
                    unset($ssoData['id']);
                    unset($ssoData['payment_verification_code']);
                    unset($ssoData['access_token']);
                    unset($ssoData['refresh_token']);
                    unset($ssoData['deleted_at']);
                    SsoUser::create($ssoData);
                }
                $ssoLoginArchive->delete();
            }
            
            Log::channel('archive')->info("Agent with ID: $id restored successfully");
            return true;
        });
    }
    

    public function deleteMember($id)
    {
        Log::channel('archive')->info("Deleting member with ID: $id");
        return DB::transaction(function () use ($id) {
            $user_info  = UserInfo::where('userid', $id)->first();
            $member = UserLogin::where('userid',$id)->first();
            if ($member) {
                $memberArchive = UserLoginArchive::withTrashed()->where('id', $id)->first();
                if ($memberArchive) {
                    $memberArchive->fill($member->toArray())->save();
                } else {
                    UserLoginArchive::create($member->toArray());
                }
                $member->forceDelete();
            }

            $ssoUser = SsoUser::where('email', $user_info->cemail)->where('user_type', 'M')->first();
            if ($ssoUser) {
                $ssoLoginArchive = SsoLoginArchive::withTrashed()->where('email', $ssoUser->email)->first();
                if ($ssoLoginArchive) {
                    $ssoLoginArchive->fill($ssoUser->toArray())->save();
                } else {
                    SsoLoginArchive::create($ssoUser->toArray());
                }
                $this->deleteSSOUser($ssoUser);
            }
            Log::channel('archive')->info("Member with ID: $id deleted successfully");
            return true;
        });
    }

    public function restoreMember($id)
    {
        Log::channel('archive')->info("Restoring member with ID: $id");
        return DB::transaction(function () use ($id) {
            $user_info  = UserInfo::where('userid', $id)->first();
            $memberArchive = UserLoginArchive::withTrashed()->where('userid',$id)->first();
            if ($memberArchive) {
                $member = UserLogin::where('userid',$id)->first();
                if ($member) {
                    throw new \Exception('Member login already exists.', Response::HTTP_CONFLICT);
                } else {
                    $memberData = $memberArchive->toArray();
                    unset($memberData['id']);
                    $member = UserLogin::create($memberData);
                }
                $memberArchive->delete();
            }
            $ssoLoginArchive = SsoLoginArchive::withTrashed()->where('email', $user_info->cemail)->first();
            if ($ssoLoginArchive) {
                $ssoUser = SsoUser::where('email', $ssoLoginArchive->username)->first();
                if ($ssoUser) {
                    throw new \Exception('Member login already exists in sso.', Response::HTTP_CONFLICT);
                }  else {
                    $ssoData = $ssoLoginArchive->toArray();
                    unset($ssoData['id']);
                    unset($ssoData['payment_verification_code']);
                    unset($ssoData['access_token']);
                    unset($ssoData['refresh_token']);
                    unset($ssoData['deleted_at']);
                    SsoUser::create($ssoData);
                }
                $ssoLoginArchive->delete();
            }
            Log::channel('archive')->info("Member with ID: $id restored successfully");
            return true;
        });
    }

    public function deleteGroup($id)
    {
        Log::channel('archive')->info("Deleting group with ID: $id");
        return DB::transaction(function () use ($id) {
            $groupUsers = GroupUser::where('gid', $id)->get();
            if ($groupUsers->isEmpty()) {
                foreach ($groupUsers as $groupUser) {
                    $groupArchive = GroupUserArchive::withTrashed()->where('gid', $groupUser->gid)->first();
                    if ($groupArchive) {
                        throw new \Exception('Group user already exist.', Response::HTTP_CONFLICT);
                    } else {
                        GroupUserArchive::create($groupUser->toArray());
                    }
    
                    $ssoUser = SsoUser::where('email', $groupUser->groupuser_email)->where('user_type', 'G')->first();
                    if ($ssoUser) {
                        $ssoLoginArchive = SsoLoginArchive::withTrashed()->where('email', $ssoUser->email)->first();
                        if ($ssoLoginArchive) {
                            throw new \Exception('Group user already exist in sso.', Response::HTTP_CONFLICT);
                        } else {
                            SsoLoginArchive::create($ssoUser->toArray());
                        }
                        $this->deleteSSOUser($ssoUser);
                    }
                    $groupUser->forceDelete();
                }
            }
            Log::channel('archive')->info("Group with ID: $id deleted successfully");
            return true;
        });
    }

    public function restoreGroup($id)
    {
        Log::channel('archive')->info("Restoring group with ID: $id");
        return DB::transaction(function () use ($id) {
            $groupArchives = GroupUserArchive::withTrashed()->where('gid', $id)->get();
            if ($groupArchives->isEmpty()) {
                throw new \Exception('Group does not exist in archive.', Response::HTTP_CONFLICT);
            }

            foreach ($groupArchives as $groupArchive) {
                $groupUser = GroupUser::withTrashed()->where('gid', $groupArchive->gid)->first();
                if ($groupUser) {
                    throw new \Exception('Group user already exists.', Response::HTTP_CONFLICT);
                } else {
                    $groupData = $groupArchive->toArray();
                    unset($groupData['id']);
                    $groupUser = GroupUser::create($groupData);
                }

                $ssoLoginArchive = SsoLoginArchive::withTrashed()->where('email', $groupUser->groupuser_email)->first();
                if ($ssoLoginArchive) {
                    $ssoUser = SsoUser::where('email', $ssoLoginArchive->email)->where('user_type', 'G')->first();
                    if ($ssoUser) {
                        throw new \Exception('Group user already exists in sso.', Response::HTTP_CONFLICT);
                    }  else {
                        $ssoData = $ssoLoginArchive->toArray();
                        unset($ssoData['id']);
                        unset($ssoData['payment_verification_code']);
                        unset($ssoData['access_token']);
                        unset($ssoData['refresh_token']);
                        unset($ssoData['deleted_at']);
                        SsoUser::create($ssoData);
                    }
                    $ssoLoginArchive->delete();
                }
                $groupArchive->delete();
            }

            Log::channel('archive')->info("Group with ID: $id restored successfully");
            return true;
        });
    }

    public function deleteSSOUser($ssoUser)
    {
        Log::channel('archive')->info("Deleting SSO user with ID: {$ssoUser->id}");
        UserDeviceID::where('user_id', $ssoUser->id)->delete();
        PushNotificationLog::where('user_id', $ssoUser->id)->forceDelete();
        SsoUserType::where('user_id', $ssoUser->id)->delete();
        $ssoUser->forceDelete();
        Log::channel('archive')->info("SSO user with ID: {$ssoUser->id} deleted successfully");
    }
}
