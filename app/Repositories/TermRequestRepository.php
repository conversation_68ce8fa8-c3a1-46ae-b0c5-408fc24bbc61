<?php

namespace App\Repositories;

use App\AgentInfo;
use App\AssocFee;
use App\Dependent;
use App\GidAssocFee;
use App\Helpers\CommonHelper;
use App\Helpers\DecryptEcryptHelper;
use App\Helpers\FamilyRelationFinderHelper;
use App\Helpers\GuzzleHelper;
use App\Helpers\InvoiceHelper;
use App\Helpers\PolicyUpdateHelper;
use App\Helpers\UplineAgentsEmail;
use App\InvoiceHistory;
use App\InvoiceItemHistory;
use App\PlanOverview;
use App\PlanPolicy;
use App\PlanPricingDisplay;
use App\Policy;
use App\Service\MessageService;
use App\TermRequest;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Plan;
use App\Repositories\Plans\PlanChangeOnAddress;
use App\Repositories\PolicyTerminateWithdraw\PolicyTerminateWithdrawFeatures;
use App\Repositories\V3\Archive\ArchiveRepository;
use DateTime;
use Illuminate\Support\Facades\Log;
use App\NbInvoice;
use App\NbInvoiceItem;
use App\PlanTermHistory;

class TermRequestRepository
{
    use Paginator, ResponseMessage;
    /**
     * @var TermRequest
     */
    //private $model;


    static $withRelations = [
        'policy',
        'plan'
    ];
    /**
     * @var MessageService
     */
    //private $messageService;

    /**
     * TermRequestRepository constructor.
     * @param TermRequest $model
     * @param MessageService $messageService
     */
    // public function __construct(
    //     TermRequest $model,
    //     MessageService $messageService
    // )
    // {
    //     $this->model = $model;

    private  $PlanChangeOnAddress;
    private  $archiveRepository;
    private  $policyTerminateWithdrawFeatures;

    public function __construct()
    {
        $this->policyTerminateWithdrawFeatures = new PolicyTerminateWithdrawFeatures();
        $this->archiveRepository = new ArchiveRepository();
        $this->PlanChangeOnAddress = new PlanChangeOnAddress();
    }

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return TermRequest::find($id);
    }

    public function getByField($key, $value)
    {
        return TermRequest::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->orderBy('term_id', 'DESC')
            ->paginate($limit);
    }


    protected function getQueries($filters)
    {
        $query = TermRequest::query()
            ->with(self::$withRelations);
        $this->filterContent($query, $filters);
        return $query;
    }


    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['term_id'])) {
            $data->where('term_id', '=', $filters['term_id']);
        }

        if (isset($filters['pid'])) {
            $data->where('pid', '=', $filters['pid']);
        }

        if (isset($filters['policy_id'])) {
            $data->where('policy_id', '=', $filters['policy_id']);
        }

        if (isset($filters['req_date'])) {
            $data->where('req_date', '=', $filters['req_date']);
        }

        if (isset($filters['term_status'])) {
            if($filters['term_status'] == 'P'){
                $data->whereNull('term_status');
            }else{
                $data->where('term_status', '=', $filters['term_status']);
            }
        }

        if (isset($filters['term_date'])) {
            $data->where('term_date', '=', $filters['term_date']);
        }

        if (isset($filters['who'])) {
            $data->where('who', 'LIKE', '%' . $filters['who'] . '%');
        }

        if (isset($filters['plan_name'])) {
            $data->whereHas('policy', function ($subQuery) use ($filters) {
                $subQuery->whereHas('planOverview', function ($q) use ($filters) {
                    $q->where('plan_name_system', 'LIKE', '%' . $filters['plan_name'] . '%');
                });
            });
            $data->orWhereHas('plan', function ($q) use ($filters) {
                $q->where('plan_name_system', 'LIKE', '%' . $filters['plan_name'] . '%');
            });
        }

        if (isset($filters['member_name'])) {
            $data->whereHas('policy', function ($subQuery) use ($filters) {
                $subQuery->whereHas('getMember', function ($q) use ($filters) {
                    $concat = $this->concatFullname('userinfo.cfname', 'userinfo.cmname', 'userinfo.clname');
                    $q->where(DB::raw($concat), 'LIKE', '%' . $filters['member_name'] . '%');
                });
            });

        }

        if (isset($filters['agent_name'])) {
            $data->whereHas('policy', function ($subQuery) use ($filters) {
                $subQuery->whereHas('agentInfo', function ($q) use ($filters) {
                    $concat = $this->concatFullname('agent_fname', 'agent_mname', 'agent_lname');
                    $q->where(DB::raw($concat), 'LIKE', '%' . $filters['agent_name'] . '%');
                });
            });

        }
    }


    public function paginatedFormattedList($limit, $filters = [])
    {
        $data = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($data);
    }

    protected function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }


    protected function policyFormattedItem($policy)
    {
        $userFullName = isset($policy->getMember) ? $policy->getMember->fullname : null;
        return [
            'policyId' => $policy->policy_id,
            'userName' => $userFullName,
            'effectiveDate' => $policy->effective_date,
        ];
    }

    protected function formattedPlan($plan)
    {
        return [
            'planId' => $plan->pid,
            'planName' => $plan->plan_name_system,
        ];
    }

    public function planOverview($pId, $policyId)
    {
        try {
            return PlanOverview::query()
                ->select(['pid', 'policy_id', 'p_ai', 'plan_name_system', 'effective_date', 'pterm_date'])
                ->where('pid', '=', $pId)
                ->where('policy_id', '=', $policyId)
                ->firstOrFail();
        } catch (\Throwable $th) {
            return null;
        }
    }

    public function planOverviewAll($policyId, $pIds = [])
    {
        try {
            $query = PlanOverview::query()
                ->select(['pid', 'policy_id', 'p_ai', 'plan_name_system', 'effective_date', 'pterm_date', 'pstatus'])
                ->where('policy_id', '=', $policyId);
            if (isset($pIds)) {
                $query->whereIn('pid', $pIds);
            }
            return $query->get();

        } catch (\Throwable $th) {
            return null;
        }
    }

    protected function formattedPlanPolicy($pId, $policyId)
    {
        $planOverview = $this->planOverview($pId, $policyId);
        if (isset($planOverview)) {
            $planPolicy = $planOverview->planPolicy;
            return [
                'planPolicyId' => $planPolicy->p_ai,
                'planPolicyStatus' => $planPolicy->is_approved,
            ];
        } else {
            return null;
        }
    }

    protected function singleFormattedItem($d)
    {
        $policy = isset($d->policy) ? $this->policyFormattedItem($d->policy) : null;
        $plans = [];
        $plan = isset($d->plan) ? $this->formattedPlan($d->plan) : null;
        if ($d->pid == 'ALL') {
            $pIds = explode(',', $d->all_term_pid);
            $planOverview = $this->planOverviewAll($d->policy_id, $pIds);
            foreach ($planOverview as $p) {
                $plans[] = [
                    'planName' => $p->plan_name_system,
                ];
            }
        }

        $userInfo = $agentInfo = [];
        if (isset($d->policy)) {
            $user = $d->policy->getMember;
            $agent = $d->policy->agentInfo;
            $userInfo = isset($user) ? $this->formattedUserInfo($user) : [];
            $agentInfo = isset($agent) ? $this->formattedAgentInfo($agent) : [];
        }

//        $planPolicy = $this->formattedPlanPolicy($d->pid, $d->policy_id);
        return [
            'termId' => $d->term_id,
            'policyId' => $d->policy_id,
            'pId' => $d->pid,
            'requestDate' => $d->req_date,
            'signature' => $d->signature,
            'signatureUrl' => $d->signature_url,
            'termStatus' => $d->term_status,
            'formattedTermStatus' => isset($d->term_status) ? array_search($d->term_status, TermRequest::$statuses) : null,
            'termDate' => $d->term_date,
            'reason' => $d->reason,
            'termReason' => $d->term_reason,
            'who' => $d->who,
            'termed_by' => $d->who === 'mem'? 'Member' :($d->who === 'rep' ?'Agent' : $d->who) ,
            'policy' => $policy,
            'plan' => $plan,
            'userInfo' => $userInfo,
            'agentInfo' => $agentInfo,
            'plans' => $plans,
            'requested_rep' => $d->requested_rep === null ? null : $this->getRepName($d->requested_rep),
//            'planPolicy' => $planPolicy
        ];
    }

    protected function concatFullname($firstName, $middleName, $lastName)
    {
        $sqlQuery = "CONCAT_WS(' ',";
        $sqlQuery .= "CASE $firstName WHEN '' THEN NULL ELSE $firstName END,";
        $sqlQuery .= "CASE $middleName WHEN '' THEN NULL ELSE $middleName END,";
        $sqlQuery .= "CASE $lastName  WHEN '' THEN NULL ELSE $lastName END)";
        return $sqlQuery;
    }

    public function policyUpdateLog($policyId,  $action, $comment, $termDate = null, $loginUserId = 'auto', $termed_plan_id = null)
    {
        $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
        Log::channel('planReplacedAlternative')->info("policy update log for policy id: $policyId");
        $data = [
            'elgb_policyid' => $policyId,
            'elgb_act' => $action,
            'elgb_act_date' => time(),
            'elgb_comment' => $comment,
            'elgb_agent' => $loginUserId,
            'termed_plan_id' => $termed_plan_id,
            'elgb_file_date' => date('Y-m-d'),
            'origin' => $origin
        ];

        if (isset($termDate)) {
            $data['elgb_term_date'] = $termDate;
        }
        return PolicyUpdateHelper::updateEligibility($data);
    }

    protected function getPlanPoliciesByPolicyId($policyId)
    {
        return PlanPolicy::query()
            ->where('policy_num', '=', $policyId)
            ->where('pstatus', '=', 1)
            ->get();
    }

    public function approveTermRequest($data)
    {
        $termRequest = $this->getByField('term_id', $data['term_id']);
        if (!$termRequest) return $this->failedResponse('No Term Request found.');
        if ($termRequest->term_status == 'A') return $this->failedResponse('This term request has been already approved.', 409);
        if ($termRequest->term_status == 'R') return $this->failedResponse('This term request has been already rejected.', 409);
        $policy = $termRequest->policy;
        $userInfoPolicyAddress = $policy->userInfoPolicyAddress;
        $policyId = $policy->policy_id;
        $termDate = $termRequest->term_date;
        DB::beginTransaction();
        try {
            $termRequest->update([
                'term_status' => 'A',
            ]);

            $comment = 'Term Request has been approved.';

            if ($termRequest->pid == 'ALL') {
                $policy->update([
                    'status' => Policy::STATUS_TERMED,
                    'term_date' => $termDate
                ]);
                $planPolicies = $this->getPlanPoliciesByPolicyId($termRequest->policy_id);
                foreach ($planPolicies as $pp) {
                    $pp->update([
                        'pstatus' => 2,
                        'pterm_date' => $termDate,
                    ]);
                }

            } else {
                $planOverview = $this->planOverview($termRequest->pid, $termRequest->policy_id);
                $planPolicy = $planOverview->planPolicy;
                $planPolicyId = $planPolicy->p_ai;
                $planPolicy->update([
                    'pstatus' => 2,
                    'pterm_date' => $termDate,
                ]);
                $website = $this->getExactWebUrl($userInfoPolicyAddress->weburl);
                $this->feeAdjustment($planPolicyId, $planOverview, $website, $termDate, $data['login_user_id']);

                //term whole policy if no active plan left
                $activePlansCollection = PlanOverview::where('policy_id', $termRequest->policy_id)
                                                    ->where('pstatus', '1')
                                                    ->get();

                if ($activePlansCollection->isNotEmpty()) {
                    $associatedActivePlans = $activePlansCollection->where('is_assoc', 1);

                    if ($activePlansCollection->count() == 1 && $associatedActivePlans->count() == 1) {
                        $singleAssociatedPlan = $associatedActivePlans->first();
                        $singleAssociatedPlan->update([
                            'pstatus' => 2,
                            'pterm_date' => $termDate,
                        ]);

                        $policy->update([
                            'status' => Policy::STATUS_TERMED,
                            'term_date' => $termDate
                        ]);
                    }
                }else{
                    $policy->update([
                        'status' => Policy::STATUS_TERMED,
                        'term_date' => $termDate
                    ]);
                }
            }
            $this->policyUpdateLog($policyId, 'tcr', $comment, null, $data['login_user_id']);
            //sending email
            $emailData = $this->emailContextBasedData($termRequest);
            $messageService = new MessageService();
            $messageService->sendEmailWithContentData($emailData);

            $data = [
                'termId' => $termRequest->term_id,
                'status' => 'approved'
            ];

            DB::commit();
            return $this->successResponse($comment, $data);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }


    public function rejectTermRequest($data)
    {
        $termRequest = $this->getByField('term_id', $data['term_id']);
        if (!$termRequest) return $this->failedResponse('No Term Request found.');
        if ($termRequest->term_status == 'R') return $this->failedResponse('This term request has been already rejected.', 409);
        if ($termRequest->term_status == 'A') return $this->failedResponse('This term request has been already approved.', 409);

        DB::beginTransaction();
        try {
            $termRequest->update([
                'term_status' => 'R',
                'reason' => $data['reason'],
            ]);

            $comment = 'Term Request has been rejected.';

            $emailData = $this->emailContextBasedData($termRequest, 'reject');
            $messageService = new MessageService();
            $messageService->sendEmailWithContentData($emailData);
            $data = [
                'termId' => $termRequest->term_id,
                'status' => 'rejected'
            ];

            DB::commit();
            return $this->successResponse($comment, $data);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse();
        }
    }

    protected function emailContextBasedData($data, $type = 'approve')
    {
        /**
         * @var  $data TermRequest
         */
        $typeText = $type == "approve" ? 'approved' : "rejected";
        $toAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $data->policy->getMember->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $data->policy->agentInfo->agent_email;
        $bccAddress = config("testemail.TEST_EMAIL") ? '' : '<EMAIL>';

        $dataName = isset($data->policy->getMember) ? $data->policy->getMember->fullname : '';
        $message = "This is an automated notice to notify that your termination request for following plan has been " . $typeText . " on " . date('m/d/Y') . ".";
        $planName = $this->getPlanName($data);

        $contentData = [
            'Confirmation #' => $data['policy_id'],
            'Primary Name' => $dataName,
            'Plan Name' => $planName,
            'Eff. Date' => isset($data->policy) ? $data->policy->effective_date : '',
            'Term Date' => $data['term_date'],
        ];
        if ($type != 'approve') unset($contentData['Term Date']);
        $reason = $type == 'approve' ? null : ($data['reason'] ?? 'Termination request could not be processed.');
        $subject = "Plan Termination Request";
        $emailConfigurationName = $type == "approve" ? "PLAN_TERM_APPROVED" : "POLICY_WITHDRAWN_EMAIL";

        return [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'bccAddress' => $bccAddress,
            'subject' => $subject,
            'message' => $message,
            'reason' => $reason,
            'data' => $contentData,
            'dataName' => $dataName
        ];

    }


    public function getExactWebUrl($weburl)
    {
        $domainOnly = explode("/", $weburl);
        $domains[] = $domainOnly[0];
        $priorityUrl = array_diff($domains, array('corenroll.com'));
        return $priorityUrl[0];
    }


    public function feeAdjustment($planPolicyId, $matchingRecord, $website, $termDate, $loginUserId = 'auto',$action='tcr')
    {
        $policyId = $matchingRecord->policy_id;
        $allOtherPlans = PlanOverview::query()
            ->where('policy_id', $policyId)
            ->where('p_ai', '!=', $planPolicyId)
            ->get();
        $planPolicy = $this->getPlanPolicy($planPolicyId);
        $planPolicyStatus = $planPolicy->peffective_date == $termDate ? 3 : 2;
        $associationPlans = [];
        $otherPlans = [];
        $assocCounter = 0;
        $otherCounter = 0;
        $planIds = [];
        $planPricingIds = [];
        foreach ($allOtherPlans as $plan) {
            if ($plan->is_assoc) {
                $associationPlans[$assocCounter]['plan_id'] = $plan->pid;
                $associationPlans[$assocCounter]['plan_pricing_id'] = $plan->plan_id;
                $associationPlans[$assocCounter]['exists'] = false;
                $assocCounter++;
            } else {
                if ($plan->pstatus == 3) {
                    continue;
                }
                $planIds[] = $plan->pid;
                $planPricingIds[] = $plan->plan_id;
                $otherPlans[$otherCounter]['plan_id'] = $plan->pid;
                $otherPlans[$otherCounter]['plan_pricing_id'] = $plan->plan_id;
                $otherCounter++;
            }
        }

        $associationPlans = array_unique($associationPlans, SORT_REGULAR);
        $uniqueAssocPlans = [];
        foreach ($associationPlans as $associationPlan) {
            $uniqueAssocPlans[]=$associationPlan;
        }
        if (count($otherPlans) == 0) {
            $this->policyWithdraw($policyId, $termDate, $loginUserId,$planPolicyStatus,$action);
        }

        $newAssociationPlans =$this->computeAssociationFees($matchingRecord->gid, $website, $otherPlans);
        $newAssociationPlans = array_unique($newAssociationPlans, SORT_REGULAR);
        $uniqueNewAssocPlans = [];
        foreach ($newAssociationPlans as $newAssociationPlan) {
            $uniqueNewAssocPlans[]=$newAssociationPlan;
        }
        foreach ($uniqueAssocPlans as $associationPlan) {
            if (in_array($associationPlan['plan_id'], array_column($uniqueNewAssocPlans, 'plan_id'))) {
                //valid existing association fee
                $matchedKey = array_search($associationPlan['plan_id'], array_column($uniqueNewAssocPlans, 'plan_id'));
                $uniqueNewAssocPlans[$matchedKey]['exists'] = true;
            } else {
                $this->removeAssociation($policyId, $associationPlan['plan_pricing_id'], $termDate, $loginUserId);
            }
        }
//        foreach ($uniqueNewAssocPlans as $newAssociationPlan) {
//            if (!$newAssociationPlan['exists']) {
//                $this->insertAssociation($policyId, $newAssociationPlan['plan_pricing_id'], $termDate, $loginUserId);
//            }
//        }
    }

    public function computeAssociationFees($groupId, $website, $plans)
    {
        $associationIds = [];
        $finalPlanIds = [];
        foreach ($plans as $key => $plan) {
            $groupAssociation = GidAssocFee::query()
                ->where('gid', $groupId)
                ->select(['a_pid', 'a_ppid'])
                ->first();
            if ($groupAssociation) {
                $associationIds['plan_id'] = $groupAssociation->a_pid;
                $associationIds['plan_pricing_id'] = $groupAssociation->a_ppid;
                $associationIds['exists'] = false;
                $finalPlanIds[] = $associationIds;
            }
            $planAssociation = AssocFee::query()
                ->where('website', $website)
                ->where('pid', $plan['plan_id'])
                ->select(['a_pid', 'a_ppid'])
                ->first();
            if ($planAssociation) {
                $planAssociationIds['plan_id'] = $planAssociation->a_pid;
                $planAssociationIds['plan_pricing_id'] = $planAssociation->a_ppid;
                $planAssociationIds['exists'] = false;
                $finalPlanIds[] = $planAssociationIds;
            }
        }

        $afterBexSpecific = count($plans) > 0 ? $this->bexSpecificCase($finalPlanIds, $groupId, $website) : $finalPlanIds;
        $afterGroupSpecific = count($plans) > 0 ? $this->groupSpecificCase($afterBexSpecific, $groupId) : $afterBexSpecific;
        //converting associative array object to array
        $encodeArrayObject = json_encode($afterGroupSpecific);
        return json_decode($encodeArrayObject,true);
    }



    public function bexSpecificCase($plans, $groupId, $website)
    {
        if ($website == 'brokerexchanges.com') {
            if (in_array(626, array_column($plans, 'plan_id')) || in_array(556, array_column($plans, 'plan_id')) || in_array(627, array_column($plans, 'plan_id'))) {

                if (!in_array(690, array_column($plans, 'plan_id')) && !in_array(8599, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 690;
                    $add->plan_pricing_id = 8599;;
                    array_push($plans, $add);
                }

                if (in_array(721, array_column($plans, 'plan_id'))) {
                    $index = array_search(721, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            } else {
                if (!in_array(721, array_column($plans, 'plan_id')) && !in_array(8941, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 721;
                    $add->plan_pricing_id = 8941;;
                    array_push($plans, $add);
                }

                if (in_array(690, array_column($plans, 'plan_id'))) {
                    $index = array_search(690, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            }
            if ($groupId == 2361) {
                if (in_array(734, array_column($plans, 'plan_id')) || in_array(617, array_column($plans, 'plan_id')) || in_array(729, array_column($plans, 'plan_id')) || in_array(809, array_column($plans, 'plan_id'))) {

                    if (!in_array(832, array_column($plans, 'plan_id')) && !in_array(10856, array_column($plans, 'plan_pricing_id'))) {
                        $add = new \stdClass();
                        $add->plan_id = 832;
                        $add->plan_pricing_id = 10856;;
                        array_push($plans, $add);
                    }
                    $index = array_search(721, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            }
        }
        return $plans;
    }

    public function groupSpecificCase($plans, $groupId)
    {
        if ($groupId == 2361) {
            $total = 0;
            foreach ($plans as $plan) {
                $price = PlanPricingDisplay::query()
                    ->where('plan_pricing_id', $plan->plan_pricing_id)
                    ->value('price_male_nons');
                $total += $price;
            }
            if ($total < 10 && $total > 0) {
                if (!in_array(832, array_column($plans, 'plan_id')) && !in_array(10856, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 832;
                    $add->plan_pricing_id = 10856;;
                    array_push($plans, $add);
                }
            }
        }
        return $plans;
    }

    public function insertAssociation($policyId, $planId, $termDate, $loginUserId = 'auto')
    {
        $effectiveDate = Carbon::createFromFormat('Y-m-d', $termDate)->addDay()->toDateString();;
        $associationData = [
            'policy_num' => $policyId,
            'plan_id' => $planId,
            'date' => time(),
            'peffective_date' => $effectiveDate,
            'ppptype' => 'price_male_nons',
            'pstatus' => '1',
            'pefees' => null
        ];
        PlanPolicy::create($associationData);
        $comment = 'New association fee added';
        $this->policyUpdateLog($policyId, 'new', $comment, null, $loginUserId);
    }

    public function removeAssociation($policyId, $planId, $termDate, $loginUserId = 'auto')
    {
        $withdrawData = [
            'pstatus' => 2,
            'pterm_date' => $termDate
        ];

        $withdrawnFee = PlanPolicy::query()
            ->where('policy_num', $policyId)
            ->where('plan_id', $planId)
            ->update($withdrawData);
        if ($withdrawnFee) {
            $comment = 'Fee no longer valid for remaining active products';
            $this->policyUpdateLog($policyId, 'tcr', $comment, $termDate, $loginUserId);
        }
    }

    public function policyWithdraw($policyId, $termDate, $loginUserId = 'auto',$planPolicyStatus = 2,$action='tcr')
    {
        //withdrawn or termed
        $withdrawn = Policy::query()->where('policy_id', $policyId)
            ->update([
                'status' => $planPolicyStatus == 3 ? Policy::STATUS_WITHDRAWN : Policy::STATUS_TERMED,
                'term_date' => $termDate
            ]);
        if ($withdrawn){
            $statusText = $planPolicyStatus == 3 ? 'withdrawn' : 'termination';
            $comment = "Policy level {$statusText} - only one product is there";
            $this->policyUpdateLog($policyId, $action, $comment, $termDate, $loginUserId);
        }
    }


    protected function getPlanName($term)
    {
        if ($term->pid == 'ALL') {
            $pIds = explode(',', $term->all_term_pid);
            $planOverview = $this->planOverviewAll($term->policy_id, $pIds);
            $data = [];
            foreach ($planOverview as $p) {
                $data[] = $p->plan_name_system;
            }
            $plan = implode('<br/>', $data);
        } else {
            $plan = isset($term->plan) ? $term->plan->plan_name_system : null;
        }
        return $plan;
    }


    public function getTermDetail($termId)
    {
        $term = $this->getByField('term_id', $termId);
        if (!$term) return $this->failedResponse('No Term Request found.');
        $plan = $this->getPlanName($term);
        $userInfo = $dependents = $groupInfo = [];
        if (isset($term->policy)) {
            /**
             * @var $policy Policy
             */
            $policy = $term->policy;
            $user = $policy->getMember;
            $group = $policy->getGroup;
            $groupInfo = isset($group) ? $this->formattedGroupInfo($group) : [];
            $userInfo = isset($user) ? $this->formattedUserInfo($user) : [];
            $dependents = isset($policy->dependentInPolicy) ? $this->formattedDependents($policy->dependentInPolicy) : [];
        }

        $data = [
            'termId' => $termId,
            'policyId' => $term->policy_id,
            'termDate' => date('m/d/Y', strtotime($term->term_date)),
            'requestDate' => date('m/d/Y', strtotime($term->req_date)),
            'pId' => $term->pid,
            'termReason' => $term->term_reason,
            'planName' => $plan,
            'signatureUrl' => $term->signature_url,
            'signature' => $term->signature,
            'base64SignatureImageUrl' => $term->base_64_encoded_signature_image_url,
            'userInfo' => $userInfo,
            'groupInfo' => $groupInfo,
            'dependents' => $dependents,
        ];

        return $this->successResponse('Term Request Detail', $data);
    }

    protected function formattedDependents($dependents)
    {
        $result = [];
        foreach ($dependents as $d) {
            $result[] = [
                'dependentId' => $d->dependent_id,
                'relation' => FamilyRelationFinderHelper::getFullRelation($d->d_relate),
//                'ssn' => DecryptEcryptHelper::decryptInfo($d->d_ssn),
                'formattedSsn' => 'XXX-XX-' . $d->d_ssn4,
                'firstName' => $d->d_fname,
                'lastName' => $d->d_lname,
                'middleName' => $d->d_mname,
                'gender' => array_search($d->d_gender, Dependent::$gender),
                'dob' => $d->d_dob,
                'policyId' => $d->policy_id,
            ];
        }
        return $result;
    }


    protected function formattedUserAddress($userAddress)
    {
        return [
            'address1' => $userAddress->address1,
            'address2' => $userAddress->adderss2,
            'state' => $userAddress->state,
            'city' => $userAddress->city,
            'zip' => $userAddress->zip,
        ];
    }

    protected function formattedUserInfo($user)
    {
        $address = isset($user->getHomeAddress) ? $this->formattedUserAddress($user->getHomeAddress) : [];
        return [
            'userId' => $user->userid,
            'fullname' => $user->fullname,
            'email' => $user->cemail,
            'phone1' => CommonHelper::format_phone($user->phone1),
            'phone2' => CommonHelper::format_phone($user->phone2),
            'formattedSsn' => 'XXX-XX-' . $user->cssn4,
            'dob' => $user->cdob,
            'address' => $address
        ];
    }

    protected function formattedAgentInfo($agent)
    {
        return [
            'agentId' => $agent->agent_id,
            'fullname' => $agent->fullname,
            'email' => $agent->agent_email,
            'phone1' => CommonHelper::format_phone($agent->agent_phone1),
            'phone2' => CommonHelper::format_phone($agent->agent_phone2),
            'address' => [
                'address1' => $agent->agent_address1,
                'address2' => $agent->agent_adderss2,
                'state' => $agent->agent_state,
                'city' => $agent->agent_city,
                'zip' => $agent->agent_zip,
            ]
        ];
    }

    protected function formattedGroupInfo($group)
    {
        return [
            'groupId' => $group->gid,
            'groupName' => $group->gname,
        ];
    }


    protected function getPlanPolicy($planPolicyId)
    {
        return PlanPolicy::query()
            ->where('p_ai', '=', $planPolicyId)
            ->first();
    }

    public function activePlanTermed($data)
    {
        Log::channel('planReplacedAlternative')->info("Active Plan term envoked");

        $emailData = '';
        $planPolicyId = $data['planPolicyId'];
        $planPolicy = $this->getPlanPolicy($planPolicyId);

        if (!$planPolicy) return $this->failedResponse('No Plan Policy Found.', 404);
        if ($planPolicy->pstatus == 2) return $this->failedResponse('This plan is already termed.', 409);
        $policy = $planPolicy->getPlanPolicyDetail;
        $policyId = $policy->policy_id;
        $userInfoPolicyAddress = $policy->userInfoPolicyAddress;
        $planOverview = PlanOverview::query()->where([
            'policy_id' => $policyId,
            'p_ai' => $planPolicyId
        ])->first();
        $isAssoc = $planOverview->is_assoc;
        $termDate = $data['termDate'];
        $pStatus = $planPolicy->peffective_date == $termDate ? 3 : 2;
        DB::beginTransaction();
        try {
            $planPolicy->update([
                'pstatus' => $pStatus,
                'pterm_date' => $termDate,
            ]);
            $commentStatus = $pStatus == 3 ? 'withdrawn' : 'termed.';
            $comment = 'Plan (' . $planOverview->plan_name_system . ') has been ' . $commentStatus;
            $website = $this->getExactWebUrl($userInfoPolicyAddress->weburl);

            //policy updates
            $this->policyUpdateLog($policyId , $data['reason'], $data['notes'], $termDate, $data['loginUserId'], $planOverview->pid);

            //fee adjustment
            if ($isAssoc !== 1){
                $this->feeAdjustment($planPolicyId, $planOverview, $website, $termDate, $data['loginUserId'],$data['reason']);
            }

            //term whole policy if no active plan left
            $this->termWholePolicy($policy,$termDate);

              //sending email based on alternative found or termination
            if (isset($data['newPlanId']) && $data['newPlanId'] !== '') { //checks if the new alternative plan is being set
                $oldPlanInfo = [
                    'old_name' => $planOverview->plan_name_system,
                    'old_price' => $planOverview->price_male_nons,
                    'active_period' => sprintf(
                        '%s - %s',
                        $planPolicy->peffective_date,
                        $planPolicy->pterm_date
                    ),
                ];

                $newPlanId = $data['newPlanId'];
                $newPlanPricingId = $data['newplanPricingId'];
                Log::channel('planReplacedAlternative')->info("Plan replacement  for policy {$policy->policy_id}");
                Log::channel('planReplacedAlternative')->info("Old plan details:", $oldPlanInfo);
                $newPlan = Plan::find($newPlanId);
                $newPlanName = $newPlan->plan_name_system;
                $newPlanPrice = PlanPricingDisplay::where('plan_pricing_id', $newPlanPricingId)->value('price_male_nons');
                $newPlanEffectiveDate =  $data['newEffDate'];

                $newPlanInfo = [
                    'new_name' => $newPlanName,
                    'new_price' => $newPlanPrice,
                    'new_effective_date' => $newPlanEffectiveDate,
                ];

                Log::channel('planReplacedAlternative')->info("New plan details:", $newPlanInfo);
                $emailData = $this->emailContextBasedTerminationReplacedPlanData($planPolicy, $policy, $oldPlanInfo, $newPlanInfo,$data['reason']);
                Log::channel('planReplacedAlternative')->info("Email data:", $emailData);
            }
            else{
                $emailData = $this->emailContextBasedActivePlanTerminationData($planPolicy, $policy, $planOverview->plan_name_system, $data['notes'], $data['reason']);
            }
            $messageService = new MessageService();
            $messageService->sendEmailWithContentData($emailData);

            $data = [
                'planPolicyId' => (int)$planPolicyId,
                'policyId' => (int)$policyId,
                'status' => 'termed'
            ];
            DB::commit();
            return $this->successResponse($comment, $data);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    protected function emailContextBasedTerminationReplacedPlanData($planPolicy, $policy, $oldPlanInfo,$newPlanInfo,$request_reason)
    {
         $toAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policy->getMember->cemail;
         $ccAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : [$policy->agentInfo->agent_email,$policy->getMember->cemail_alt];

        if (is_array($ccAddress)) {
            // Filter out null or empty values
            $ccAddress = array_filter($ccAddress);
        }
        $agent_id = Policy::where('policy_id', '=', $policy->policy_id)->value('p_agent_num');
        $ccAddress = UplineAgentsEmail::addUplineCcAddress($ccAddress, $agent_id);

        $dataName = isset($policy->getMember) ? $policy->getMember->fullname : '';
        $messageStatus = $planPolicy->pstatus == 3 ? 'withdrawn' : 'termed';

        $message = "This is an automated notice to notify that your following plan has been replaced on " . date('m/d/Y') . ".";
        $reason = "As a result of the address change, your insurance plan has been updated. Below are the details of your old and new plans:";
        $subject = "Important: Plan Update Due to Address Change";
        $contentData = array_merge($oldPlanInfo, $newPlanInfo);

        $middleMessage = '<tr style="border-collapse:collapse;">
        <td colspan="5" align="left" style="padding:0;Margin:0;padding-top:20px;padding-bottom:10px;">
            <h3 style="Margin:0;font-size:16px;font-weight:bold;margin-bottom:10px;">Old Plan:</h3>
            <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;">
                Plan Name: ' . $oldPlanInfo['old_name'] . '<br>
                Price: $' . number_format($oldPlanInfo['old_price'], 2) . '<br>
                Active Period: ' . $oldPlanInfo['active_period'] . '
            </p>
        </td>
        </tr>
        <tr style="border-collapse:collapse;">
        <td colspan="5" align="left" style="padding:0;Margin:0;padding-top:20px;padding-bottom:10px;">
            <h3 style="Margin:0;font-size:16px;font-weight:bold;margin-bottom:10px;">New Plan:</h3>
            <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;">
                Plan Name: ' . $newPlanInfo['new_name'] . '<br>
                Price: $' . number_format($newPlanInfo['new_price'], 2) . '<br>
                Effective Date: ' . $newPlanInfo['new_effective_date'] . '
            </p>
        </td>
        </tr>';

        $emailConfigurationName = "PLAN_REPLACEMENT_EMAIL";

        return [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'reason' => $reason,
            'middleMessage'=>$middleMessage,
            'data' => $contentData,
            'dataName' => $dataName,
            'type'=>'ADDRESS_CHANGE'
        ];
    }

    protected function emailContextBasedActivePlanTerminationData($planPolicy, $policy, $planName, $notes = "", $request_reason = null)
    {
        $toAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policy->getMember->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : [$policy->agentInfo->agent_email,$policy->getMember->cemail_alt];
        if (is_array($ccAddress)) {
            // Filter out null or empty values
            $ccAddress = array_filter($ccAddress);
        }

        $agent_id = Policy::where('policy_id', '=', $policy->policy_id)->value('p_agent_num');
        $ccAddress = UplineAgentsEmail::addUplineCcAddress($ccAddress, $agent_id);

        $dataName = isset($policy->getMember) ? $policy->getMember->fullname : '';
        $messageStatus = $planPolicy->pstatus == 3 ? 'withdrawn' : 'termed';

        $message = "This is an automated notice to notify that your following plan has been {$messageStatus} on " . date('m/d/Y') . ".";
        $contentData = [
            'Confirmation #' => $policy->policy_id,
            'Primary Name' => $dataName,
            'Plan Name' => $planName,
//            'Eff. Date' => isset($planPolicy) ? $planPolicy->peffective_date : '',
            'Term Date' => isset($planPolicy) ? $planPolicy->pterm_date : '',
        ];
        $reason = isset($notes) ? $notes : null;
        $subject =$planPolicy->pstatus == 3 ? "Plan Withdrawn" : "Plan Termination";
        $planOverview = PlanOverview::query()
            ->select('cid')
            ->where([
            'policy_id' => $policy->policy_id,
            'p_ai' => $planPolicy->p_ai
        ])->first();

        $emailConfigurationName = $planOverview->cid === 92 ? "POLICY_WITHDRAWN_EMAIL_EXTRA_HEALTH" : "POLICY_WITHDRAWN_EMAIL";

        return [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'reason' => $reason,
            'data' => $contentData,
            'dataName' => $dataName
        ];

    }

    protected function termWholePolicy(Policy $policy,$termDate){
        //term whole policy if no active plan left
        $countActPlans = PlanOverview::where('policy_id', $policy->policy_id)
            ->where('pstatus', '1')
            ->get();
        $countActPlans = $countActPlans->count();

        if ($countActPlans > 0) {
            # do nothing
        }else{
            $policy->update([
                'status' => Policy::STATUS_TERMED,
                'term_date' => $termDate
            ]);
        }
        // $checkOtherPolicies = $this->policyTerminateWithdrawFeatures->checkOtherActivePolicies($policy->policy_userid, $policy->policy_id);
        // if($checkOtherPolicies) {
        //     $this->archiveRepository->deleteMember($policy->policy_userid);
        // }
    }

    public function getRepName($agent_id){
        $agent = AgentInfo::where('agent_id', $agent_id)->first();
        return $agent->fullname;

    }


    public function updateTermDate($data)
    {
        $planPolicyId = $data['plan_id'];
        $planPolicy = $this->getPlanPolicy($planPolicyId);

        if (!$planPolicy) {
            return $this->failedResponse('No Plan Policy Found.', 404);
        }

        $policy = $planPolicy->getPlanPolicyDetail;

        if ($policy->status === 'TERMED') {
            return $this->failedResponse('The entire policy is already termed. Updating the plan term date is not allowed.', 409);
        }

        $policyId = $policy->policy_id;
        $userInfoPolicyAddress = $policy->userInfoPolicyAddress;
        $planOverview = PlanOverview::query()->where([
            'policy_id' => $policyId,
            'p_ai' => $planPolicyId,
        ])->first();

        if (!$planOverview) {
            return $this->failedResponse('Plan overview not found.', 404);
        }
        $newTermDate = $data['tdate'];
        $currentTermDate = $planPolicy->pterm_date;
        $plan_term_history = PlanTermHistory::where('p_ai', $planPolicy->p_ai)
            ->orderBy('plan_term_history_id', 'desc')
            ->first();
        if ($plan_term_history && !is_null($plan_term_history->pterm_date) && $plan_term_history->pterm_date == $currentTermDate) {
            $present_term_date = $plan_term_history->pterm_date;
        } else {
            $present_term_date = !is_null($currentTermDate) ? $currentTermDate : null;
        }
        DB::beginTransaction();
        try {
            Log::info("Starting term date update for plan policy ID: {$planPolicyId}", [
                'currentTermDate' => $present_term_date,
                'newTermDate' => $newTermDate,
            ]);

            if ($newTermDate > $present_term_date) {

                $invoices = NbInvoice::whereBetween('invoice_start_date', [$present_term_date, $newTermDate])
                ->where('invoice_policy_id', $policyId)
                ->get();
                self::insertInvoiceData($invoices);

                // $months = $this->generateMonthsBetween($currentTermDate, $newTermDate);
                // $invoiceGenerated = $this->generateInvoices($policyId, $months);

                // if (!$invoiceGenerated) {
                //     throw new \Exception('Failed to generate invoices. Term date update aborted.');
                // }

                // Update the term date and status after successful invoice generation
                $planPolicy->update([
                    'pterm_date' => $newTermDate,
                ]);
                DB::commit();
                InvoiceHelper::updatePaidNBInvoice($present_term_date, $policyId);
                Log::info('Term date  updated successfully.', [
                    'planPolicyId' => $planPolicyId,
                    'pterm_date' => $newTermDate,
                ]);
            } else {
                $invoices = NbInvoice::where('invoice_start_date', '>=', $newTermDate)
                ->where('invoice_policy_id', $policyId)
                ->get();

                self::insertInvoiceData($invoices);
                $planPolicy->update([
                    'pterm_date' => $newTermDate,
                ]);
                DB::commit();

                InvoiceHelper::updatePaidNBInvoice($newTermDate, $policyId);
            }

            self::InsertPlanTermHistoryFromPolicy($planPolicy);

            // Log the update
            $comment = 'The term date for Plan (' . $planOverview->plan_name_system . ') has been updated to ' . $newTermDate . '.';
            $this->policyUpdateLog($policyId, $data['action'] , $data['reason'], $newTermDate, $data['loginUserId'],$planOverview->pid );

            if ($data['sendEmail'] == 1) {
                $this->sendEmailNotification($planPolicy, $policy, $planOverview, $data);
            }

            Log::info("Term date update successful for plan policy ID: {$planPolicyId}", [
                'policyId' => $policyId,
                'newTermDate' => $newTermDate,
            ]);

            self::regenerateAndFixUnProcessedInvoices($policyId);
            InvoiceHelper::fixCarryForwards($policyId);

            return $this->successResponse($comment, [
                'planPolicyId' => (int)$planPolicyId,
                'policyId' => (int)$policyId,
                'status' => 'Term Date Updated',
            ]);
        } catch (\Throwable $th) {
            Log::error("Error during term date update for plan policy ID: {$planPolicyId}", [
                'error' => $th->getMessage(),
            ]);
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    function InsertPlanTermHistoryFromPolicy($planPolicy)
    {
        try {
            $fillableColumns = (new PlanTermHistory())->getFillable();
            $data = [];

            foreach ($fillableColumns as $column) {
                if (isset($planPolicy->$column)) {
                    $data[$column] = $planPolicy->$column;
                }
            }

            if (!empty($data)) {
                // Check for duplicate record
                $query = PlanTermHistory::query();
                foreach ($data as $column => $value) {
                    if (is_null($value)) {
                        $query->whereNull($column);
                    } else {
                        $query->where($column, $value);
                    }
                }
                // Insert only if no duplicate exists
                if (!$query->exists()) {
                    PlanTermHistory::create($data);
                }
            }
        } catch (\Exception $e) {
            return "Error inserting data: " . $e->getMessage();
        }
    }

    public static function insertInvoiceData($invoices)
    {
        try {
            $invoices = collect($invoices);
            $invoiceIds = $invoices->pluck('invoice_id')->toArray();
            $invoiceItemsById = NbInvoiceItem::whereIn('invoice_id', $invoiceIds)->get()->groupBy('invoice_id');

            foreach ($invoices as $invoice) {
                $invoiceHistoryData = [];
                $invoiceHistoryFillable = (new InvoiceHistory())->getFillable();

                foreach ($invoiceHistoryFillable as $column) {
                    if (isset($invoice->$column)) {
                        $invoiceHistoryData[$column] = $invoice->$column;
                    }
                }

                if (!empty($invoiceHistoryData)) {
                    $InvoiceHistoryQuery = InvoiceHistory::query();
                    foreach ($invoiceHistoryData as $column => $value) {
                        if (is_null($value)) {
                            $InvoiceHistoryQuery->whereNull($column);
                        } else {
                            $InvoiceHistoryQuery->where($column, $value);
                        }
                    }

                    if (!$InvoiceHistoryQuery->exists()) {
                        InvoiceHistory::create($invoiceHistoryData);
                    }
                }

                $invoiceItemHistoryFillable = (new InvoiceItemHistory())->getFillable();
                $invoiceItems = $invoiceItemsById[$invoice->invoice_id] ?? collect();

                foreach ($invoiceItems as $item) {
                    $itemData = [];
                    foreach ($invoiceItemHistoryFillable as $column) {
                        if (isset($item->$column)) {
                            $itemData[$column] = $item->$column;
                        } elseif (isset($invoice->$column)) {
                            $itemData[$column] = $invoice->$column;
                        }
                    }

                    if (!empty($itemData)) {
                        $InvoiceItemHistory = InvoiceItemHistory::query();
                        foreach ($itemData as $column => $value) {
                            if (is_null($value)) {
                                $InvoiceItemHistory->whereNull($column);
                            } else {
                                $InvoiceItemHistory->where($column, $value);
                            }
                        }

                        if (!$InvoiceItemHistory->exists()) {
                            InvoiceItemHistory::create($itemData);
                            Log::info('Inserted data into nb_invoice_item_history', ['data' => $itemData]);
                        } else {
                            Log::info('Data already exists in nb_invoice_item_history, no insert performed', ['data' => $itemData]);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error inserting data into InvoiceItemHistory', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);

            return "Error inserting data: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
        }
    }



    /**
     * Generate a list of months between two dates.
     */
    // private function generateMonthsBetween($startDate, $endDate)
    // {
    //     $start = new DateTime($startDate);
    //     $end = (new DateTime($endDate))->modify('first day of next month');
    //     $months = [];

    //     while ($start < $end) {
    //         $months[] = $start->format('Y-m-01'); // First day of each month
    //         $start->modify('first day of next month');
    //     }

    //     Log::info('Generated months for invoice generation', ['months' => $months]);
    //     return $months;
    // }


    private  function regenerateAndFixUnProcessedInvoices($policy_id){
        Log::info("Starting invoice regeneration process for policy ID: {$policy_id}");

        try {
            // Fetch invoice IDs to be regenerated
            $invoices = NbInvoice::where('invoice_policy_id', $policy_id)
            ->where('invoice_status', 'ACTIVE')
            ->where(function ($query) {
                $query->whereNull('payment_party_status')
                    ->orWhereNotIn('payment_party_status', ['PAID', 'PROCESSING', 'PROCESSED']);
            })
            ->get();

            self::insertInvoiceData($invoices);

            if (empty($invoices)) {
                Log::info("No eligible invoices found for policy ID: {$policy_id}.");
                return;
            }

            // Log the invoice IDs ready for regeneration
            Log::info("Eligible invoices for regeneration for policy ID: {$policy_id}. Invoice IDs: " . implode(', ', $invoices->pluck('invoice_id')->toArray()));

            // Iterate over invoice IDs to regenerate
            foreach ($invoices->pluck('invoice_id')->toArray() as $invoiceId) {
                try {
                    $url = config('app.purenroll_system.url') . "regenerateInvoice/{$invoiceId}";
                    $responseJson = GuzzleHelper::getApi($url, []);
                    $response = json_decode($responseJson, true);
                    if (isset($response['status']) && $response['status'] === 'success') {
                        Log::info("Invoice {$invoiceId} regenerated successfully.", $response);
                    } else {
                        Log::warning("Failed to regenerate invoice {$invoiceId}. Response: " . $responseJson);
                    }
                } catch (\Exception $e) {
                    Log::error("Error regenerating invoice {$invoiceId}: " . $e->getMessage());
                }
            }
            Log::info("Invoice regeneration process completed for policy ID: {$policy_id}");
        } catch (\Exception $e) {
            Log::error("Error during invoice regeneration process for policy ID: {$policy_id}: " . $e->getMessage());
            throw $e;
        }

    }
    /**
     * Generate invoices via API.
     */
    // private function generateInvoices($policyId, $months)
    // {
    //     Log::info("Generating invoices for policy ID: {$policyId}", ['effective_dates' => $months]);

    //     $url = config('app.purenroll_system.url') . "generate-single-invoice";
    //     $payload = [
    //         'policy_id' => $policyId,
    //         'effective_date' => $months,
    //     ];

    //     $responseJson = GuzzleHelper::postApi($url, [], $payload);

    //     if (preg_match('/\{.*?\}/', $responseJson, $matches)) {
    //         $response = json_decode($matches[0], true);
    //         if ($response && $response['status'] === 'success') {
    //             Log::info('Invoices generated successfully for policy ID: ' . $policyId);
    //             return true;
    //         } else {
    //             Log::warning('Invoice generation failed', ['response' => $response]);
    //             return false;
    //         }
    //     } else {
    //         Log::error('Invalid response from invoice API', ['response' => $responseJson]);
    //         return false;
    //     }
    // }


    /**
     * Send email notification for term date update.
     */
    private function sendEmailNotification($planPolicy, $policy, $planOverview, $data)
    {
        $emailData = $this->emailContextBasedActivePlanTerminationData(
            $planPolicy,
            $policy,
            $planOverview->plan_name_system,
            $data['reason'],
            $data['reason']
        );

        $messageService = new MessageService();
        $messageService->sendEmailWithContentData($emailData);

        Log::info('Email notification sent for term date update', ['planPolicyId' => $planPolicy->plan_id]);
    }






}
