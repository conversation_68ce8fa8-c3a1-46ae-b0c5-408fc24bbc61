<?php

namespace App\Repositories;

use App\Helpers\CheckPlanTypeHelper;
use App\Helpers\EmailSendHelper;
use App\Helpers\SendEmailMemberHelper;
use App\Helpers\SendNotificationHelper;
use App\Helpers\PolicyUpdateHelper;
use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use App\PlanPolicy;
use App\Repositories\PolicyTerminateWithdraw\PolicyTerminateWithdrawFeatures;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use App\Policy;
use App\PpoUpload;
use App\Signature;
use App\PolicyUpdate;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\PlanOverview;
use App\Repositories\Plans\PrudentialPlanRepository;
use Symfony\Component\HttpFoundation\Response;

class MemberApproval extends Model
{
    use ResponseMessage;
    
    public function getApprovalList($request)
    {
        $perPage = $request->input('per_page', 20);
    
        // Query Builder with Filters
        $members = UserInfoPolicyAddress::with(['plans' => function ($query) {
            $query->select([
                'tier', 'plan_name_system', 'peffective_date', 'pstatus', 'pterm_date', 
                'ppptype', 'plan_cat', 'policy_id', 'price_male_nons', 'esig', 'status'
            ]);
        }])
        ->leftJoin('plan_overview as p', 'userinfo_policy_address.policy_id', '=', 'p.policy_id')
        ->leftJoin('group_info as g', 'userinfo_policy_address.eid', '=', 'g.gid')
        ->select([
            'p.policy_id', 'p.cid', 'p.is_assoc', 
            'userinfo_policy_address.effective_date', 'userinfo_policy_address.Approval',
            'userinfo_policy_address.cfname', 'userinfo_policy_address.cmname', 'userinfo_policy_address.clname',
            'userinfo_policy_address.edate', 'userinfo_policy_address.status',
            'userinfo_policy_address.agent_id', 'userinfo_policy_address.agent_code',
            'userinfo_policy_address.agent_lname', 'userinfo_policy_address.agent_fname',
            'g.gname'
        ])
        ->selectRaw('
            CASE WHEN p.eprocess = "rep" THEN "Rep" ELSE "Member" END AS enrolledBy,
            CASE WHEN userinfo_policy_address.is_benefit_store THEN "enroll.purenroll.com" ELSE userinfo_policy_address.weburl END AS platform_name,
            CASE WHEN userinfo_policy_address.weburl REGEXP \'^https?://\' THEN userinfo_policy_address.weburl ELSE CONCAT("https://", userinfo_policy_address.weburl) END AS platform_link,
            (
                SELECT COUNT(DISTINCT po.userid) AS active_members_count
                FROM plan_overview po
                JOIN group_info gi ON po.gid = gi.gid
                WHERE po.gid = userinfo_policy_address.eid
                AND gi.gtype = "employer"
                AND (po.status = "ACTIVE" OR po.status = "WITHDRAWN")
            ) AS active_members_count,
            (SELECT COUNT(*) FROM plan_overview WHERE policy_id = userinfo_policy_address.policy_id AND cid = 77) AS is_iha,
            (SELECT COUNT(*) FROM plan_overview WHERE policy_id = userinfo_policy_address.policy_id AND cid = ' . PrudentialPlanRepository::PLAN_CARRIER_ID . ') AS is_prudential
            
        ')
        ->selectRaw('
            CASE 
                WHEN (
                    SELECT COUNT(*) 
                    FROM plan_overview AS po
                    WHERE po.policy_id = p.policy_id
                    AND po.status = "ACTIVE" 
                    AND po.esig = 1
                ) = 0 THEN NULL
                ELSE (
                    SELECT EXISTS (
                        SELECT 1 
                        FROM signatures AS s
                        WHERE s.pid = p.policy_id
                    )
                )
            END AS sign
        ')
        ->where('userinfo_policy_address.status', 'ACTIVE')
        ->whereRaw('(SELECT COUNT(*) FROM plan_overview WHERE policy_id = userinfo_policy_address.policy_id AND cid = 77) = 0')
        ->when(!empty($request->approved_status) && $request->approved_status !== 'ALL', function ($query) use ($request) {
            if ($request->approved_status === 'NULL') {
                $query->whereNull('userinfo_policy_address.Approval');
            } else {
                $query->where('userinfo_policy_address.Approval', $request->approved_status);
            }
        })
        ->orderBy('userinfo_policy_address.edate', 'DESC')
        ->paginate($perPage);
    
        // Check if data exists
        if ($members->isEmpty()) {
            return response()->json(
                new ErrorResource([
                    'statusCode' => Response::HTTP_CONFLICT,
                    'message' => 'No Agent found.'
                ]), Response::HTTP_CONFLICT
            );
        }
    
        // Return paginated response
        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Member approved list fetched.',
                'data' => [
                    'current_page' => $members->currentPage(),
                    'total' => $members->total(),
                    'per_page' => $members->perPage(),
                    'last_page' => $members->lastPage(),
                    'members' => $members->items()
                ]
            ]), Response::HTTP_OK
        );
    }
    

    //function to approve or reject member according to request
    public function changeMemberStatus($request)
    {
        try {

            $type = $request['type'];
            $policy_id = $request['policy_id'];
            //check the data type and set accordingly
//            $policyDetails = UserInfoPolicyAddress::where('policy_id', $policy_id)->get()->first();
            $policyDetails = Policy::find($policy_id);
            if (empty($policyDetails)) {
                return ['error' => 'Policy not found'];
            }
            if ($type == 'approve') {
                $approve = '1';
                list($message, $subject) = $this->approvePolicyUpdates($policyDetails);
            } else {
                $approve = '0';
                $this->rejectMember($policyDetails);
            }
            $updateMember = Policy::where('policy_id', $policy_id)
                ->update(['Approval' => $approve]);
            if ($updateMember == false)
                throw new Exception('No Updates');

            if ($type == 'approve') {
                //updating plan policy
                PlanPolicy::where('policy_num', $request['policy_id'])->update([
                    'is_approved' => 1
                ]);
                $this->memberApproveSendEmail($policyDetails, $subject, $message);

            }
            return array('type' => 'success', 'response' => $updateMember);

        } catch (Exception $e) {
            $response = false;
            $message = $e->getMessage();
            return array('response' => $response, 'message' => $message);
        }
    }

    //function to set signature received
    public function setSignatureReceived($request)
    {
        try {
            $policy_id = $request->input('policy_id');
            //get details of policy
            $policyDetail = UserInfoPolicyAddress::where('policy_id', $policy_id)->first();
            $signature = [
                'signator' => $policyDetail->cfname,
                'signature' => 'sig_recieved',
                'created' => date('Y-m-d'),
                'pid' => $policy_id,
                'userid' => $policyDetail->userid,
            ];
            $insertSignature = Signature::insertGetId($signature);
            if (empty($insertSignature))
                throw new Exception('No Insertion');
            return array('type' => 'success', 'response' => $insertSignature);

        } catch (Exception $e) {
            $response = false;
            $message = $e->getMessage();
            return array('response' => $response, 'message' => $message);
        }
    }

    //function to set signature received
    public function setTaxDocumentReceived($request)
    {
        try {
            $policy_id = $request->input('policy_id');
            //get details of policy
            $taxDoc = [
                'file_id' => '',
                'policy_id' => $policy_id,
                'filename' => 'file_received',
                'date_upload' => date('Y-m-d')
            ];
            $insertPpoUploads = PpoUpload::insertGetId($taxDoc);
            if (empty($insertPpoUploads))
                throw new Exception('No Insertion');
            return array('type' => 'success', 'response' => $insertPpoUploads);

        } catch (Exception $e) {
            $response = false;
            $message = $e->getMessage();
            return array('response' => $response, 'message' => $message);
        }
    }

    public function approveRejectMember($request)
    {
        $policyId = $request['policy_id'];
        $type = $request['type'];
        $approve = $type == 'approve' ? 1 : 0;
        $policy = Policy::find($policyId);
        $successMessageText = $type == 'approve' ? 'approved' : 'rejected';
        if ($policy->Approval === 1) return $this->failedResponse('Member has been already approved.', 409);
        if ($policy->Approval === 0) return $this->failedResponse('Member has been already rejected.', 409);
        DB::beginTransaction();
        try {
            $policy->update([
                'Approval' => $approve,
            ]);
            if ($type == 'approve') {
                list($message, $subject) = $this->approvePolicyUpdates($policy);
                //updating plan policy
                PlanPolicy::where('policy_num', $request['policy_id'])->update([
                    'is_approved' => 1
                ]);
                $this->memberApproveSendEmail($policy, $subject, $message);
            } else {
                $this->rejectMember($policy);
            }
            DB::commit();
            return $this->successResponse("Member ${successMessageText} successfully.", []);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse("Failed To {$type} member");
        }
    }


    protected function rejectMember($policy)
    {
        $params = [
            "tdate" => $policy->effective_date,
            "reason" => "Withdrawn can not be processed",
            "policy_id" => $policy->policy_id,
            "aid" => '0',
            "action" => "wcp",
            "sendEmail" => 1,
        ];
        $policyFeature = new PolicyTerminateWithdrawFeatures();
        $policyFeature->setWithdrawPolicy((object)$params);
    }

    /**
     * @param $policy
     * @return array
     */
    protected function approvePolicyUpdates($policy): array
    {
        $message = "Member has been approved";
        $subject = "Member Approval Notice";
        $data['elgb_policyid'] = $policy->policy_id;
        $data['elgb_act'] = 'APPROVE';
        $data['elgb_act_date'] = time();
        $data['elgb_comment'] = "POLICY APPROVED";
        $data['elgb_agent'] = 'web';
        $data['elgb_file_date'] = date('Y-m-d');
        PolicyUpdateHelper::updateEligibility($data);
        return array($message, $subject);
    }

    /**
     * @param $policy
     * @param string $subject
     * @param string $message
     */
    public function memberApproveSendEmail($policy, $subject = "Member Approval Notice", $message = "Member has been approved"): void
    {
        $policyId = $policy->policy_id;
        $planType = CheckPlanTypeHelper::checkPlanType($policyId);
        $checkSmmr = CheckPlanTypeHelper::checkSmmrPlan($policyId);
        $bodyConfigs = [
            'policy_id' => $policy->policy_id,
        ];
        //Send Member Benefit Email
        // SendEmailMemberHelper::sendBenefitEmail($bodyConfigs);
        if ($planType == '1') {
            // without rider
            $subject = "Member Approval Email";
            SendEmailMemberHelper::sendEmailMember($policyId, 'MEMBER_MEDICAL_PLAN_WITHOUT_RIDER_EMAIL_NOTIFICATION', $subject);
        } elseif($planType == '0') {
            // Ancillary Email
            $subject = "Member Approval Email";
            SendEmailMemberHelper::sendEmailMember($policyId, 'ELEVATE_MEMBER_WELCOME_NOTIFICATION', $subject);
        } else {
            // medical plans with add ons and rider
            $subject = 'Member Approval Email';
            $emailMessageConfiguration = 'MEMBER_PLAN_WITH_RIDER_APPROVED_EMAIL_NOTIFICATION';
            $result = SendEmailMemberHelper::sendEmailMember($policyId,$emailMessageConfiguration,$subject);
        }
        if ($checkSmmr) {
            SendEmailMemberHelper::sendSmmrEmail($policy);
        }
    }
}
