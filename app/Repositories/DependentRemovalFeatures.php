<?php

namespace App\Repositories;

use App\Dependent;
use App\Helpers\FamilyRelationFinderHelper;
use App\Helpers\PolicyUpdateHelper;
use App\UserInfoPolicyAddress;
use Exception;
use App\DependentPolicy;
use App\Helpers\EmailSendHelper;
use Illuminate\Database\Eloquent\Model;

class DependentRemovalFeatures extends Model
{
    public function removeDependent($request)
    {
        try {
            $dependentID = $request->dependent_id;
            $policyID = $request->policy_id;
            $agentID = $request->aid;
            $reason = $request->reason;
            $dependentPolicyCount = DependentPolicy::where('dependent_id', $dependentID)->where('policy_id', $policyID)->count();
            if (!$dependentPolicyCount) {
                return ['error' => 'Dependent data not found.'];
            }
            $status = DependentPolicy::where('dependent_id', $dependentID)
                ->where('policy_id', $policyID)
                ->delete();
            if ($status) {
                $data['elgb_policyid'] = $policyID;
                $data['elgb_act'] = 'DEP';
                $data['elgb_comment'] = $reason ? $reason : 'Dependent Removed';
                $data['elgb_agent'] = $agentID;
                $data['elgb_act_date'] = time();
                $data['elgb_file_date'] = date('Y-m-d');
                PolicyUpdateHelper::updateEligibility($data);

                if ($request->sendEmail) {
                    //$userID = UserInfoPolicyAddress::where('policy_id',$policyID)->pluck('userid')->first();
                    $dependentData = Dependent::where('did', $dependentID)
                        ->get(['userid', 'd_fname', 'd_lname', 'd_relate', 'd_dob', 'd_ssn4', 'd_gender'])
                        ->first()->toArray();
                    $data = [
                        "Dependent Name" => $dependentData['d_fname'] . " " . $dependentData['d_lname'],
                        "Relation" => FamilyRelationFinderHelper::getFullRelation($dependentData['d_relate']),
                        "DOB" => ($dependentData['d_dob'] !='') ? 'XX/XX/'.date('Y',strtotime($dependentData['d_dob'])) : '',
                        "SSN" => "XXX-XX-" . $dependentData['d_ssn4'],
                        "Gender" => $dependentData['d_gender'] == 0 ? 'Male' : 'Female'
                    ];
                    if (UserInfoPolicyAddress::where('userid', $dependentData['userid'])->count()) {
                        EmailSendHelper::sendGenericPolicyEmailUser($dependentData['userid'], "Dependent Removed from Policy", "A dependent has been removed from Policy", "dependentremoval", $data, 'PDR');
                    }
                }
                return ['success' => 'Dependent data removed successfully.'];
            } else {
                return ['error' => 'Failed removing dependent data.'];
            }
        } catch (Exception $e) {
            return ['error' => 'Failed removing dependent data.'];
        }
    }
}
