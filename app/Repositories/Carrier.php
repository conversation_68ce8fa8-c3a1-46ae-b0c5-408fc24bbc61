<?php

namespace App\Repositories;

use App\CarrierInfo;
use App\CarrierState;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Traits\ResponseMessage;

class Carrier extends Model
{
    use ResponseMessage;
    /**
     * Get carriers
     *
     * @param $cid
     * @return mixed
     */
    public function carrierInfo($cid)
    {
        if($cid != 0){
            $result = CarrierInfo::where('carrier_company_id','1')
            ->where('cid', $cid)
            ->with('carrierState')
            ->first();
        }else{
            $result = CarrierInfo::where('carrier_company_id','1')
            ->with('carrierState')
            ->get();
        }
        return $result;
    }

    /**
     * Get carriers
     *
     * @param $cid
     * @return mixed
     */
    public function carrierData()
    {
        $result = CarrierInfo::where('carrier_company_id','1')
        ->orderBy('carrier_name', 'asc')
        ->get();
        return $this->successResponse('Carrier List', $result);
    }

    /**
     * Create & update carrier
     *
     * @param $request
     * @return array|string[]
     */
    public function newCarrier($request)
    {
        $state = $request->state;
        $data['carrier_name'] = $request->carrier_name;
        $data['carrier_code'] = $request->carrier_code;
        $data['email'] = $request->email;
        if ($request->hasFile('carrier_logo')) {
            $data['carrier_logo'] = $this->uploadLogo($request->file('carrier_logo'));
        }
        $data['added'] = time();
        $data['carrier_company_id'] = '1';
        if ($request->cid) {
            $carrier = $this->updateCarrier($request->cid, $data, $state);
        } else {
            $carrier = $this->addCarrier($data, $state);
        }
        return $carrier;
    }

    /**
     * Store Carrier
     *
     * @param $data
     * @param $state
     * @return array|string[]
     */
    public function addCarrier($data, $state)
    {
        try {
            $carrierId = CarrierInfo::insertGetId($data);
            $this->addState($carrierId, $state);
            $message = 'Carrier successfully added.';
        }catch (Exception $e) {
            $message = 'Failed to add carrier.';
            return ['error' => $e->getMessage()];
        }
        return ['success' => $message];
    }

    /** Add state
     *
     * @param $cid
     * @param $state
     */
    public function addState($cid, $state)
    {
        for ($i=0; $i < count($state); $i++) {
            $carrierState = new CarrierState;
            $carrierState->carrier_id = $cid;
            $carrierState->state = $state[$i];
            $carrierState->save();
        }
    }

    /** Update Carrier
     *
     * @param $cid
     * @param $data
     * @param $state
     * @return array|string[]
     */
    public function updateCarrier($cid, $data, $state)
    {
        try {
            CarrierInfo::where('cid', $cid)->update($data);
            $this->updateState($cid, $state);
            $message = 'Carrier successfully updated.';
        }catch (Exception $e) {
            $message = 'Failed to update.';
            return ['error' => $e->getMessage()];
        }
        return ['success' => $message];
    }

    /**
     * Update State
     *
     * @param $cid
     * @param $state
     */
    protected function updateState($cid, $state)
    {
        // Remove old states
        CarrierState::where('carrier_id', $cid)->delete();
        // Attach new states
        $this->addState($cid, $state);
    }

    protected function uploadLogo($logo)
    {
        $fileName = pathinfo($logo->getClientOriginalName(), PATHINFO_FILENAME);
        $fileExt = pathinfo($logo->getClientOriginalName(), PATHINFO_EXTENSION);
        $fileName = $fileName . '-' . time() . '.' . $fileExt;
        // Store logo
        Storage::disk('s3')->put($fileName, file_get_contents($logo), 'public');
        return Storage::disk('s3')->url($fileName);
    }

    public function carrierList()
    {
        return CarrierInfo::where('carrier_company_id','1')
        ->orderBy('carrier_name', 'asc')
        ->get()->map(function ($each) {
            return [
                'carrier_name' => $each->carrier_name,
                'carrier_code' => $each->carrier_code,
                'cid' => $each->cid,
            ];
        });
    }
}
