<?php

namespace App\Repositories;

use App\BankSign;
use App\Helpers\EmailSendHelper;
use App\Helpers\SendNotificationHelper;
use App\UserInfoPolicyAddress;
use Exception;
use Illuminate\Database\Eloquent\Model;

class BankInfo extends Model
{
    public function getBankInfo()
    {
        $bankrequest = BankSign::where('status', '0')
                    ->get();
        return $bankrequest;
    }

    public function updateBankInfo($bankid, $update,$reason = '')
    {
        try {
            $updateBank=BankSign::where('id', $bankid)->first();
            $updateBank->status=$update;
            $updateBank->save();
            $data = [
                "Member Name" => $updateBank->user_name,
                "Bank Name" => $updateBank->bank_name,
                "Account Num" => "XXXXXX-".$updateBank->acc_last4
            ];
            if($reason !='') { $data['Reason'] = $reason; }
            if($updateBank->status == 1) {
                $subject = 'Bank Approval Notice';
                $message = 'Bank info has been approved';
            } else {
                $subject = 'Bank Rejection Notice';
                $message = 'Bank info has been rejected';
            }
            $userData = UserInfoPolicyAddress::where('userid',$updateBank->user_id)->first();
            if(!empty($userData)) {
                SendNotificationHelper::sendBankApprovalNotification($userData,$data,$subject);
                EmailSendHelper::sendGenericPolicyEmailUser($updateBank->user_id, $subject, $message, 'bankapproval', $data);
            }
            $message = "Bank Update Success.";
        } catch (Exception $e) {
            return ['error'=>"Bank information not found."];
        }
        return ['success'=>$message];
    }
}
