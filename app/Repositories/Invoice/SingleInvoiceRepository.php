<?php

namespace App\Repositories\Invoice;

use App\Helpers\GuzzleHelper;
use App\Helpers\SendNotificationHelper;
use App\NbInvoice;
use App\NbInvoiceItem;
use App\Plan;
use App\Policy;
use App\NbPayment;
use Illuminate\Support\Facades\DB;

class SingleInvoiceRepository
{
    public static function payInvoicesAlert($user_id)
    {
        $today = date('Y-m-d');
        $alert = false;

        try {
            $policies = Policy::where('policy_userid', $user_id)
                ->where('Approval', 1)
                ->where('status', '!=', 'WITHDRAWN')
                ->pluck('policy_id')
                ->toArray();

            $invoices = NbInvoice::whereIn('invoice_policy_id', $policies)
                ->where(function ($query) {
                    $query->where('invoice_payment_status', '!=', 'paid')
                        ->Where(function ($q) {
                            $q->where('payment_party_status', '!=', 'processing')
                                ->orWhereNull('payment_party_status');
                        });
                })
                ->where('invoice_status', 'ACTIVE')
                ->where('invoice_due_date', '<', $today)
                ->select('invoice_id', 'invoice_policy_id', 'invoice_start_date')
                ->get();

            foreach ($invoices as $invoice) {
                $policy = Policy::where('policy_id', $invoice->invoice_policy_id)
                    ->select('policy_id', 'term_date', 'status')
                    ->first();

                if (!empty($policy) && strtolower($policy->status) == 'termed') {
                    if ($invoice->invoice_start_date < $policy->term_date) {
                        $alert = true;
                        break;
                    }
                } else {
                    $alert = true;
                    break;
                }
            }

            $alert? $message = 'Please pay your unpaid invoices.' : $message = '';
            $res = ['status' => 'success', 'alert' => $alert, 'message' => $message, 'code' => 200];
        } catch (\Throwable $e) {
            $res = ['status' => 'error', 'alert' => false, 'message' => $e->getMessage(), 'code' => 500];
        }
        return $res;
    }

    public function formatAmount($amount)
    {
        $pos = strpos($amount, '.');
        if (!empty($pos)) {
            $pos += 3;
            $sub_amount = substr($amount, 0, $pos);
        } else {
            $sub_amount = $amount;
        }
        return floatval($sub_amount);
    }

    public function getActualInvoiceTotal($invoice)
    {
        $total = $invoice->invoice_total + $invoice->invoice_late_fee + $invoice->invoice_bounce_fee + $invoice->invoice_stmt_fee + $invoice->invoice_efee + $invoice->ccfees;
        return $this->formatAmount($total);
    }

    private function getInvoicePlanDetails($plans, $invoice)
    {
        $plans_and_fees = [];
        foreach ($plans as $plan) {
            $plan_data = [
                'name' => $plan['name'],
                'amount' => floatval($plan['amount'])
            ];
            $plans_and_fees[] = $plan_data;
        }
        $fees = [
            ['name' => 'EFee', 'amount' => floatval($invoice->invoice_efee)],
            ['name' => 'Late Fee', 'amount' => floatval($invoice->invoice_late_fee)],
            ['name' => 'Bounce Fee', 'amount' => floatval($invoice->invoice_bounce_fee)],
            ['name' => 'Statement Fee', 'amount' => floatval($invoice->invoice_stmt_fee)],
            ['name' => 'Payment Service Fee', 'amount' => floatval($invoice->ccfees)]
        ];

        $plans_and_fees = array_merge($plans_and_fees, $fees);
        return $plans_and_fees;
    }

    private function formatInvoiceDetails($invoice, $plans, $payment)
    {
        $invoice_details = [
            'invoice_id' => $invoice->invoice_id,
            'invoice_policy_id' => $invoice->invoice_policy_id,
            'invoice_status' => $invoice->invoice_payment_status,
            'overall_due_amount' => floatval($invoice->invoice_due_amount - $invoice->invoice_due_amount_paid),
            'invoice_due_date' => !empty($invoice->invoice_due_date)? date('m/d/Y', strtotime($invoice->invoice_due_date)) : null,
            'invoice_start_date' => !empty($invoice->invoice_start_date)? date('m/d/Y', strtotime($invoice->invoice_start_date)) : null,
            'invoice_end_date' => !empty($invoice->invoice_end_date)? date('m/d/Y', strtotime($invoice->invoice_end_date)) : null,
            'plans_and_fees' => $this->getInvoicePlanDetails($plans, $invoice),
            'paid_amount' => floatval($invoice->invoice_due_amount_paid),
            'policy_amount' => floatval($invoice->invoice_total),
            'previous_due' => floatval($invoice->invoice_prev_due),
            'overall_amount' => floatval($invoice->invoice_due_amount),
            'actual_invoice_total' => $this->getActualInvoiceTotal($invoice),
            'file_link' => config('app.purenroll_system.url') . "downloadInvMergedFile/" . $invoice->invoice_id . '.pdf'
        ];

        if (strtolower($invoice->payment_party_status) != 'processing' && strtolower($invoice->invoice_payment_status) != 'paid')
        {
            $invoice_details['payment_summary'] = $payment ?? false;
            $invoice_details['pay_now_link'] = config('app.payment_system.base_url') . "pay-now?invoice_id=" . base64_encode($invoice->invoice_id);
        }

        return $invoice_details;
    }

    private function getInvoiceDetail($invoice)
    {
        $plans = array();
        $invoice_items = NbInvoiceItem::where('invoice_id', $invoice->invoice_id)
            ->select('plan_id', 'amount')
            ->get();

        foreach ($invoice_items as $item) {
            $plan_name = Plan::where('pid', $item->plan_id)
                ->pluck('web_display_name')
                ->first();

            $plans[] = ['name' => $plan_name, 'amount' => $item->amount];
        }

        $payment = NbPayment::where('invoice_id', $invoice->invoice_id)
            ->orderBy('updated_at', 'DESC')
            ->select('payment_id', 'payment_status')
            ->first();

        $formattedData = $this->formatInvoiceDetails($invoice, $plans, $payment);

        return $formattedData;
    }

    public function getInvoiceInfo($policy_id)
    {
        try {
            $data = array();
            $invoices = NbInvoice::where('invoice_policy_id', $policy_id)
                ->where('invoice_status', 'ACTIVE')
                ->whereIn('invoice_payment_status', ['UNPAID', 'PARTIAL'])
                ->where(function ($query) {
                    $query->whereNull('payment_party_status')
                        ->orWhere('payment_party_status', '!=', 'PROCESSING');
                })
                ->select('invoice_id', 'invoice_policy_id', 'invoice_start_date', 'invoice_end_date', 'invoice_due_date', 'invoice_payment_status', 'invoice_total', 'invoice_efee', 'invoice_late_fee', 'invoice_bounce_fee', 'invoice_stmt_fee', 'ccfees', 'invoice_due_amount', 'invoice_due_amount_paid', 'payment_party_status', 'invoice_prev_due', 'payment_party')
                ->orderBy('invoice_start_date', 'DESC')
                ->get();

            foreach ($invoices as $invoice) {
                $data[] = $this->getInvoiceDetail($invoice);
            }
            $resp = [
                'status' => 'success',
                'message' => 'Successfully fetched invoice detail.',
                'data' => $data,
                'code' => 200
            ];
        } catch (\Throwable $e) {
            $resp = [
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => [],
                'code' => 500
            ];
        }

        return $resp;
    }

    private function formatPaymentDetails($payment)
    {
        $paymentDetail = [
            'payment_id' => $payment->payment_id,
            'invoice_id' => $payment->invoice_id,
            'payment_amount' => floatval($payment->payment_amount),
            'payment_date' => !empty($payment->payment_date)? date('m/d/Y', strtotime($payment->payment_date)) : null,
            'payment_status' => $payment->payment_status,
            'payment_method' => $payment->payment_method,
            'payment_notes' => $payment->payment_notes,
            'processing_date' => !empty($payment->processing_date)? date('m/d/Y', strtotime($payment->processing_date)) : null,
            'payment_party' => $payment->payment_party
        ];

        !empty($payment->paystand_refund_id)? $paymentDetail['paystand_refund_id'] = $payment->paystand_refund_id : null;

        if (!empty($payment->paystand_payment_id)) {
            $paymentDetail['paystand_payment_id'] = $payment->paystand_payment_id;

            if (strtolower($payment->payment_method) == 'eft') {
                $paymentDetail['bank_detail'] = [
                    'account_name' => $payment->account_name,
                    'bank_name' => $payment->bank_name,
                    'routing_num' => $payment->routing_num,
                    'account_num' => $payment->account_num
                ];
            } else if (strtolower($payment->payment_method) == 'cc') {
                $paymentDetail['cc_detail'] = [
                    'account_name' => $payment->account_name,
                    'card_num' => $payment->card_num,
                    'brand' => $payment->brand
                ];
            }
        }

        return $paymentDetail;
    }

    private function getManagedBankCcDetail($payment_method, $payment, $remote_data = [])
    {
        if (count($remote_data) > 0) {
            if (strtolower($payment_method) == 'cc') {
                $payment->account_name = isset($remote_data['card'])? $remote_data['card']['account_name'] : null;
                $payment->card_num = isset($remote_data['card'])? $remote_data['card']['card_number'] : null;
                $payment->brand = isset($remote_data['card'])? $remote_data['card']['brand'] : null;
            } else {
                $payment->account_name = isset($remote_data['bank'])? $remote_data['bank']['account_name'] : null;
                $payment->bank_name = isset($remote_data['bank'])? $remote_data['bank']['bank_name'] : null;
                $payment->routing_num = isset($remote_data['bank'])? $remote_data['bank']['routing_number'] : null;
                $payment->account_num = isset($remote_data['bank'])? $remote_data['bank']['account_number'] : null;
            }
        } else {
            $payment->account_name = null;
            if (strtolower($payment_method) == 'cc') {
                $payment->card_num = null;
                $payment->brand = null;
            } else {
                $payment->bank_name = null;
                $payment->routing_num = null;
                $payment->account_num = null;
            }
        }
        return $payment;
    }

    public function getInvoicePaymentDetails($invoice_id)
    {
        $paystand_pay_ids = array();
        $match_pay_ids = array();
        $newPaymentDetails = array();
        $formattedPaymentDetails = array();

        try {
            $payments = NbPayment::join('nb_invoices as n', 'nb_payments.invoice_id', 'n.invoice_id')
                ->where('nb_payments.invoice_id', $invoice_id)
                ->select('nb_payments.payment_id', 'nb_payments.payment_amount', 'nb_payments.payment_date', 'nb_payments.payment_status', 'nb_payments.payment_method', 'nb_payments.payment_notes', 'nb_payments.paystand_refund_id', 'nb_payments.paystand_payment_id', 'n.invoice_id', 'n.processing_date', 'n.payment_party')
                ->orderBy('nb_payments.updated_at', 'DESC')
                ->get();

            if (!empty($payments)) {
                foreach ($payments as $payment) {
                    if (!in_array($payment->paystand_payment_id, $paystand_pay_ids) || in_array($payment->paystand_payment_id, $match_pay_ids)) {
                        strtolower($payment->payment_party) == 'paystand' && !empty($payment->paystand_payment_id)? array_push($paystand_pay_ids, $payment->paystand_payment_id) : false;
                        $newPayment = $payment;

                        if (strpos(strtolower($payment->payment_status), 'refunded') === false) {
                            unset($newPayment->{'paystand_refund_id'});
                        } else {
                            strtolower($payment->payment_party) == 'paystand' && !empty($payment->paystand_payment_id)? array_push($match_pay_ids, $payment->paystand_payment_id) : false;      // store refund paystand_payment_ids
                        }
                        if (!empty($payment->paystand_payment_id) && in_array(strtolower($payment->payment_method), ['eft', 'cc'])) {
                            $url = config('app.payment_system.url').'get-bank-info/'.$payment->paystand_payment_id;
                            $resp = GuzzleHelper::getApi($url, []);
                            $remote_data = json_decode($resp, true);
                            if (isset($remote_data['status']) && $remote_data['status'] == 'success') {
                                $newPayment = $this->getManagedBankCcDetail($payment->payment_method, $payment, $remote_data['data']);
                            } else {
                                $newPayment = $this->getManagedBankCcDetail($payment->payment_method, $payment);
                            }
                        }
                        $newPaymentDetails[] = $this->formatPaymentDetails($newPayment);
                    }
                }
            }
            $resp = [
                'status' => 'success',
                'message' => 'Successfully fetched payment summary detail.',
                'data' => $newPaymentDetails,
                'code' => 200
            ];
        } catch (\Throwable $e) {
            $resp = [
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => [],
                'code' => 500
            ];
        }
        return $resp;
    }

    private function formatInvoiceHistory($invoice)
    {
        return [
            'invoice_id' => $invoice->invoice_id,
            'invoice_policy_id' => $invoice->invoice_policy_id,
            'status' => strtolower($invoice->payment_party_status) == 'processing'? $invoice->payment_party_status : $invoice->invoice_payment_status,
            'amount' => $this->getActualInvoiceTotal($invoice),
            'date' => date('M Y', strtotime($invoice->invoice_start_date))
        ];
    }

    public function getInvoiceHistory($datas)
    {
        $formattedData = array();
        $policy_id = $datas->policy_id;

        try {
            $invoices = NbInvoice::where('invoice_policy_id', $policy_id)
                ->where('invoice_status', 'ACTIVE');

            if (!empty($datas->from_date) && !empty($datas->to_date)) {
                $from_date = date('Y-m-01', strtotime($datas->from_date));
                $to_date = date('Y-m-01', strtotime($datas->to_date));
                $invoices = $invoices->whereBetween('invoice_start_date', [$from_date, $to_date]);
            } else if (!empty($datas->from_date)) {
                $from_date = date('Y-m-01', strtotime($datas->from_date));
                $invoices = $invoices->where('invoice_start_date', '>=', $from_date);
            } else if (!empty($datas->to_date)) {
                $to_date = date('Y-m-01', strtotime($datas->to_date));
                $invoices = $invoices->where('invoice_start_date', '<=', $to_date);
            }

            // Total transactions   (place before status filter)
            $total_transaction = clone $invoices;         // clone original invoice instance so that original instance is not effected
            $total_transaction = $total_transaction->where('invoice_payment_status', 'PAID')->sum('invoice_due_amount_paid');         // shows date filter amount

            // status filter
            if (!empty($datas->status)) {
                if (strtolower($datas->status) == 'paid') {
                    $invoices = $invoices->where('invoice_payment_status', 'PAID');
                } else if (strtolower($datas->status) == 'processing') {
                    $invoices = $invoices->where('payment_party_status', 'PROCESSING');
                }
            } else {
                $invoices = $invoices->where(function ($query) {
                    $query->where('invoice_payment_status', 'PAID')
                        ->orWhere('payment_party_status', 'PROCESSING');
                });
            }
            $invoices = $invoices->select('invoice_id', 'invoice_due_amount_paid', 'invoice_payment_status', 'payment_party_status', 'invoice_start_date', 'invoice_policy_id', 'processing_amount', 'invoice_total', 'invoice_efee', 'invoice_late_fee', 'invoice_bounce_fee', 'invoice_stmt_fee', 'ccfees')
                ->orderBy('invoice_date', 'DESC')
                ->get();

            foreach ($invoices as $invoice) {
                $formattedData[] = $this->formatInvoiceHistory($invoice);
            }

            $resp = [
                'status' => 'success',
                'message' => 'Successfully fetched invoice history.',
                'data' => ['total_transaction' => $total_transaction, 'invoice_history' => $formattedData],
                'code' => 200
            ];
        } catch (\Throwable $e) {
            $resp = [
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => [],
                'code' => 500
            ];
        }
        return $resp;
    }

    public function getInvoiceHistoryDetail($invoice_id)
    {
        try {
            $invoice = NbInvoice::where('invoice_id', $invoice_id)
                ->select('invoice_id', 'invoice_policy_id', 'invoice_start_date', 'invoice_end_date', 'invoice_due_date', 'invoice_payment_status', 'invoice_total', 'invoice_efee', 'invoice_late_fee', 'invoice_bounce_fee', 'invoice_stmt_fee', 'ccfees', 'invoice_due_amount', 'invoice_due_amount_paid', 'payment_party_status', 'invoice_prev_due', 'payment_party')
                ->first();

            $invoice_detail = $this->getInvoiceDetail($invoice);
            $payment_response = $this->getInvoicePaymentDetails($invoice_id);
            if ($payment_response['status'] == 'error') {
                return $payment_response;
            }
            $payment_detail = $payment_response['data'];

            $resp = [
                'status' => 'success',
                'message' => 'Successfully fetched invoice details.',
                'data' => ['invoice_details' => $invoice_detail, 'payment_details' => $payment_detail],
                'code' => 200
            ];
        } catch (\Throwable $e) {
            $resp = [
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => [],
                'code' => 500
            ];
        }

        return $resp;
    }

    public function sendPushNotification($invoice_id, $status, $message)
    {
        try {
            $invoice = NbInvoice::where('invoice_id', $invoice_id)
                ->select('invoice_id', 'invoice_policy_id', 'invoice_start_date', 'invoice_end_date', 'invoice_due_date', 'invoice_payment_status', 'invoice_total', 'invoice_efee', 'invoice_late_fee', 'invoice_bounce_fee', 'invoice_stmt_fee', 'ccfees', 'invoice_due_amount', 'invoice_due_amount_paid', 'payment_party_status', 'invoice_prev_due', 'payment_party', 'payment_method','invoice_user_id')
                ->first();
            if(isset($invoice)) {
                $userinfo = DB::table('userinfo')->where('userid', '=', $invoice->invoice_user_id)->select('cfname',
                'clname', 'cfname', 'home_address', 'cemail')->first();

            if($userinfo){
                $subject = 'Invoice Status Updated Sucessfully.';
                $message = $message ?? "This is a notification to notify that the ".$invoice->invoice_policy_id." status has been updated to".$status."";
                $invoice_date = explode('-',$invoice->invoice_end_date);
                $notificationData = [
                    'data' => [
                        'invoice_id' => $invoice_id,
                        'policy_id' => $invoice->invoice_policy_id,
                        'invoice_year' => $invoice_date[0],
                        'invoice_month' => $invoice_date[1]
                    ],
                    'message_title' => $subject,
                    'message_body' => $message,
                    'user_id' => $invoice->invoice_user_id,
                    'type' => 'INVOICE'
                ];
                SendNotificationHelper::sendPushNotification($notificationData);
            }

                $resp = [
                    'status' => 'success',
                    'message' => 'Push Notification has been queued successfully for sending.',
                    'data' => ['invoice' => $invoice],
                    'code' => 200
                ];
            } else {
                $resp = [
                    'status' => 'error',
                    'message' => 'Invoice not found',
                    'data' => [],
                    'code' => 404
                ];
            }

        } catch (\Throwable $e) {
            $resp = [
                'status' => 'error',
                'message' => $e->getMessage(),
                'data' => [],
                'code' => 500
            ];
        }

        return $resp;


    }
}
