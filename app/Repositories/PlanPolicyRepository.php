<?php


namespace App\Repositories;


use App\AgentInfo;
use App\AssocFee;
use App\GidAssocFee;
use App\Helpers\SendEmailMemberHelper;
use App\Helpers\GuzzleHelper;
use App\Helpers\SendNotificationHelper;
use App\Http\Resources\DataResponse;
use App\MemberReferral;
use App\PlanOverview;
use App\PlanPolicy;
use App\PlanPricingDisplay;
use App\Policy;
use App\RepIncentive;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\UserInfoPolicyAddress;

class PlanPolicyRepository
{
    use ResponseMessage, Paginator;
    /**
     * @var PlanPolicy
     */
    private $model;


    /**
     * PlanPolicyService constructor.
     * @param PlanPolicy $model
     */
    public function __construct(PlanPolicy $model)
    {
        $this->model = $model;
    }

    static $withRelations = [
        'getPlanPricing',
        'getPlanPolicyDetail',
    ];

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }


    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->with(self::$withRelations);
        $this->filterContent($query, $filters);
        return $query;
    }


    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['id'])) {
            $data->where('p_ai', '=', $filters['id']);
        }

        if (isset($filters['plan_id'])) {
            $data->where('plan_id', '=', $filters['plan_id']);
        }

        if (isset($filters['policy_num'])) {
            $data->where('policy_num', '=', $filters['policy_num']);
        }
    }

    public function toggleIsApproved($planPolicyId)
    {
        $planPolicy = $this->getByField('p_ai', $planPolicyId);
        if ($planPolicy) {
            $isApproved = $planPolicy->is_approved;
            $isApproved = !$isApproved;
            $message = $isApproved ? 'Approved' : 'Decline';
            try {
                $planPolicy->update([
                    'is_approved' => $isApproved
                ]);
                return $this->successResponse('Policy Plan ' . $message);
            } catch (\Throwable $th) {
                return $this->failedResponse('Failed To Change Health Plan');
            }
        } else {
            return $this->failedResponse();
        }

    }

    public function paginatedFormattedList($limit, $filters = [])
    {
        $data = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($data);
    }

    public function formattedPlan($plan)
    {
        if (isset($plan)) {
            return [
                'name' => $plan->plan_name_system,
                'code' => $plan->plan_code_system
            ];
        } else {
            return null;
        }
    }

    public function singleFormattedItem($d)
    {

        return [
            'id' => $d->p_ai,
            'policyId' => $d->policy_num,
            'planId' => $d->plan_id,
            'plan' => $this->formattedPlan($d->plan),
//            'effectiveDate' => $d->peffective_date,
//            'termDate' => $d->pterm_date,
//            'pFees' => $d->pfees,
//            'idilusSurchage' => $d->idilus_surcharge,
            'status' => array_search($d->pstatus, PlanPolicy::$statuses),
            'isApproved' => $d->is_approved,
        ];

    }

    public function formattedItems($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return $result;
    }

    public function formattedData($data)
    {

        return [
            'data' => $this->formattedItems($data),
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    public function approvePlanPolicy($requestData)
    {
        $planPolicyId = (int)$requestData['plan_policy_id'];;
        $planPolicy = $this->getByField('p_ai', $planPolicyId);
        // dd($planPolicy);
        if (!$planPolicy) return $this->failedResponse('No Plan Policy Found', 404);
        if ($planPolicy->is_approved === 1) return $this->failedResponse('This plan has been already approved.', 409);
        if ($planPolicy->is_approved === 0) return $this->failedResponse('This plan has been already rejected.', 409);

        $policy = $planPolicy->getPlanPolicyDetail;
        $policyId = $policy->policy_id;

        $planOverview = PlanOverview::query()->where([
            'policy_id' => $policyId,
            'p_ai' => $planPolicyId
        ])->first();

        $agentId = $policy->p_agent_num;
        if ($planPolicy) {
            DB::beginTransaction();
            try {
                $planPolicy->update([
                    'is_approved' => true,
                ]);

                //adding into rep incentive if agent doesnt have previous plans
                if ($this->checkIfAgentHasPlans($agentId, $policyId) == false) {
                    $this->insertRepIncentive($planOverview);
                }
                $comment = 'Plan (' . $planOverview->plan_name_system . ') has been approved.';
                // Update to policy_updates
                $this->writePolicyLog($policy->id, 'APPROVE', $comment, $requestData['login_user_id']);
                $data = [
                    'planPolicyId' => $planPolicyId,
                    'policyId' => $policyId,
                    'status' => 'approved'
                ];
                //check and set member referral
                if ($this->checkMemberReferralExist($policyId)) {
                    $this->updateMemberReferralStatus($policyId);
                }

                //sending approval email
                $memberApproval = new MemberApproval();
                $memberData = [
                    'policy_id' => $policyId,
                    'type' => 'approve'
                ];
                $memberApproval->changeMemberStatus($memberData);

                DB::commit();
                return $this->successResponse($comment, $data);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse('Failed To Change Plan Policy');
            }
        } else {
            return $this->failedResponse();
        }
    }

    public function declinePlanPolicy($requestData)
    {
        $planPolicyId = (int)$requestData['plan_policy_id'];;
        $planPolicy = $this->getByField('p_ai', $planPolicyId);
        if (!$planPolicy) return $this->failedResponse('No Plan Policy Found', 404);
        if ($planPolicy->is_approved === 1) return $this->failedResponse('This plan has been already approved.', 409);
        if ($planPolicy->is_approved === 0) return $this->failedResponse('This plan has been already rejected.', 409);
        $policy = $planPolicy->getPlanPolicyDetail;
        $policyId = $policy->policy_id;
        $userInfoPolicyAddress = $policy->userInfoPolicyAddress;
        $planOverview = PlanOverview::query()->where([
            'policy_id' => $policyId,
            'p_ai' => $planPolicyId
        ])->first();
        if ($planPolicy) {
            DB::beginTransaction();
            try {
                $planPolicy->update([
                    'is_approved' => false,
                    'pstatus' => 3,
                    'pterm_date' => $planOverview->effective_date,
                ]);
                //set Approval to 0
                Policy::where('policy_id', $policyId)->update(['Approval' => 0]);
                $comment = 'Plan (' . $planOverview->plan_name_system . ') has been rejected.';
                $website = $this->getExactWebUrl($userInfoPolicyAddress->weburl);
                //policy updates
                $this->writePolicyLog($policyId, 'tcr', $comment, $requestData['login_user_id']);
                //fee adjustment
                $this->feeAdjustment($planPolicyId, $planOverview, $website, $requestData['login_user_id']);
                //sending mail
                $this->sendWithdrawnEmail($planOverview);
                $data = [
                    'planPolicyId' => $planPolicyId,
                    'policyId' => $policyId,
                    'status' => 'rejected'
                ];
                //check and set member referral
                if ($this->checkMemberReferralExist($policyId)) {
                    $this->updateMemberReferralStatus($policyId, MemberReferral::IS_COMPLETE_NO);
                }
                DB::commit();
                return $this->successResponse($comment, $data);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse('Failed To Change Plan Policy');
            }
        } else {
            return $this->failedResponse();
        }
    }

    public function feeAdjustment($planPolicyId, $matchingRecord, $website, $loginUserId = 'auto')
    {
        $policyId = $matchingRecord->policy_id;
        $allOtherPlans = PlanOverview::query()
            ->where('policy_id', $policyId)
            ->where('p_ai', '!=', $planPolicyId)
            ->get();
        $effectiveDate = $matchingRecord->effective_date;

        $associationPlans = [];
        $otherPlans = [];
        $assocCounter = 0;
        $otherCounter = 0;
        $planIds = [];
        $planPricingIds = [];
        foreach ($allOtherPlans as $plan) {
            if ($plan->is_assoc) {
                $associationPlans[$assocCounter]['plan_id'] = $plan->pid;
                $associationPlans[$assocCounter]['plan_pricing_id'] = $plan->plan_id;
                $associationPlans[$assocCounter]['exists'] = false;
                $assocCounter++;
            } else {
                if ($plan->pstatus == 3) {
                    continue;
                }
                $planIds[] = $plan->pid;
                $planPricingIds[] = $plan->plan_id;
                $otherPlans[$otherCounter]['plan_id'] = $plan->pid;
                $otherPlans[$otherCounter]['plan_pricing_id'] = $plan->plan_id;
                $otherCounter++;
            }
        }
        if (count($otherPlans) == 0) {
            $this->policyWithdraw($policyId, $matchingRecord->peffective_date, $loginUserId);
        }

        $newAssociationPlans = $this->computeAssociationFees($matchingRecord->gid, $website, $otherPlans);
        foreach ($associationPlans as $associationPlan) {
            if (in_array($associationPlan['plan_id'], array_column($newAssociationPlans, 'plan_id'))) {
                //valid existing association fee
                $matchedKey = array_search($associationPlan['plan_id'], array_column($newAssociationPlans, 'plan_id'));
                $newAssociationPlans[$matchedKey]['exists'] = true;
            } else {
                // need to term
                $this->removeAssociation($policyId, $associationPlan['plan_pricing_id'], $matchingRecord->peffective_date, $loginUserId);
            }
        }
        foreach ($newAssociationPlans as $newAssociationPlan) {
            if (!$newAssociationPlan['exists']) {
                $this->insertAssociation($policyId, $newAssociationPlan['plan_pricing_id'], $effectiveDate, $loginUserId);
            }
        }
    }

    public function computeAssociationFees($groupId, $website, $plans)
    {
        $associationIds = [];
        $finalPlanIds = [];
        foreach ($plans as $key => $plan) {
            $groupAssociation = GidAssocFee::query()
                ->where('gid', $groupId)
                ->select(['a_pid', 'a_ppid'])
                ->first();
            if ($groupAssociation) {
                $associationIds['plan_id'] = $groupAssociation->a_pid;
                $associationIds['plan_pricing_id'] = $groupAssociation->a_ppid;
                $associationIds['exists'] = false;
                $finalPlanIds[] = $associationIds;
            }
            $planAssociation = AssocFee::query()
                ->where('website', $website)
                ->where('pid', $plan['plan_id'])
                ->select(['a_pid', 'a_ppid'])
                ->first();
            if ($planAssociation) {
                $planAssociationIds['plan_id'] = $planAssociation->a_pid;
                $planAssociationIds['plan_pricing_id'] = $planAssociation->a_ppid;
                $planAssociationIds['exists'] = false;
                $finalPlanIds[] = $planAssociationIds;
            }
        }
        $afterBexSpecific = count($plans) > 0 ? $this->bexSpecificCase($finalPlanIds, $groupId, $website) : $finalPlanIds;
        $afterGroupSpecific = count($plans) > 0 ? $this->groupSpecificCase($afterBexSpecific, $groupId) : $afterBexSpecific;
        return $afterGroupSpecific;
    }

    public function insertAssociation($policyId, $planId, $effectiveDate, $loginUserId = 'auto')
    {
        $associationData = [
            'policy_num' => $policyId,
            'plan_id' => $planId,
            'date' => time(),
            'peffective_date' => $effectiveDate,
            'ppptype' => 'price_male_nons',
            'pstatus' => '1',
            'pefees' => null
        ];
        $this->model->insert($associationData);
        $comment = 'New association fee added';
        $this->writePolicyLog($policyId, 'new', $comment, $loginUserId);
    }

    public function removeAssociation($policyId, $planId, $effectiveDate, $loginUserId = 'auto')
    {
        $withdrawData = [
            'pstatus' => 3,
            'pterm_date' => $effectiveDate
        ];
        $withdrawnFee = $this->model::query()
            ->where('policy_num', $policyId)
            ->where('plan_id', $planId)
            ->update($withdrawData);
        if ($withdrawnFee) {
            $comment = 'Fee no longer valid for remaining active products';
            $this->writePolicyLog($policyId, 'tcr', $comment, $loginUserId);
        }
    }

    public function policyWithdraw($policyId, $termDate, $loginUserId = 'auto')
    {
        $withdrawn = Policy::query()->where('policy_id', $policyId)
            ->update([
                'status' => Policy::STATUS_WITHDRAWN,
                'term_date' => $termDate
            ]);
        if ($withdrawn) {
            $comment = 'Policy level withdrawn - only one product is there';
            $this->writePolicyLog($policyId, 'tcr', $comment, $loginUserId);
        }
    }


    public function bexSpecificCase($plans, $groupId, $website)
    {
        if ($website == 'brokerexchanges.com') {
            if (in_array(626, array_column($plans, 'plan_id')) || in_array(556, array_column($plans, 'plan_id')) || in_array(627, array_column($plans, 'plan_id'))) {

                if (!in_array(690, array_column($plans, 'plan_id')) && !in_array(8599, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 690;
                    $add->plan_pricing_id = 8599;;
                    array_push($plans, $add);
                }

                if (in_array(721, array_column($plans, 'plan_id'))) {
                    $index = array_search(721, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            } else {
                if (!in_array(721, array_column($plans, 'plan_id')) && !in_array(8941, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 721;
                    $add->plan_pricing_id = 8941;;
                    array_push($plans, $add);
                }

                if (in_array(690, array_column($plans, 'plan_id'))) {
                    $index = array_search(690, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            }
            if ($groupId == 2361) {
                if (in_array(734, array_column($plans, 'plan_id')) || in_array(617, array_column($plans, 'plan_id')) || in_array(729, array_column($plans, 'plan_id')) || in_array(809, array_column($plans, 'plan_id'))) {

                    if (!in_array(832, array_column($plans, 'plan_id')) && !in_array(10856, array_column($plans, 'plan_pricing_id'))) {
                        $add = new \stdClass();
                        $add->plan_id = 832;
                        $add->plan_pricing_id = 10856;;
                        array_push($plans, $add);
                    }
                    $index = array_search(721, array_column($plans, 'plan_id'));
                    unset($plans[$index]);
                }
            }
        }
        return $plans;
    }

    public function groupSpecificCase($plans, $groupId)
    {
        if ($groupId == 2361) {
            $total = 0;
            foreach ($plans as $plan) {
                $price = PlanPricingDisplay::query()
                    ->where('plan_pricing_id', $plan->plan_pricing_id)
                    ->value('price_male_nons');
                $total += $price;
            }
            if ($total < 10 && $total > 0) {
                if (!in_array(832, array_column($plans, 'plan_id')) && !in_array(10856, array_column($plans, 'plan_pricing_id'))) {
                    $add = new \stdClass();
                    $add->plan_id = 832;
                    $add->plan_pricing_id = 10856;;
                    array_push($plans, $add);
                }
            }
        }
        return $plans;
    }

    public function writePolicyLog($policyId, $action, $comment, $loginUserId = 'auto')
    {
        $update = [
            'elgb_policyid' => $policyId,
            'elgb_act' => $action,
            'elgb_act_date' => time(),
            'elgb_comment' => $comment,
            'elgb_agent' => $loginUserId,
            'elgb_file_date' => date('Y-m-d')
        ];
        $policyUpdateLog = new PolicyUpdateLog();
        $policyUpdateLog->create($update);
        return $policyUpdateLog;
    }

    public function getAgentInfo($agentId)
    {
        return AgentInfo::query()->where('agent_id', $agentId)->first();
    }

    public function sendWithdrawnEmail($record)
    {
        $user = $record->userInfo;
        $agent = $this->getAgentInfo($record->p_agent_num);
        $userEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $user->cemail;
        $agentEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $agent->agent_email;
        $message = "This is an automated notice to notify that the following plan has been withdrawn on " . date('m/d/Y') . ".";
        $data = [
            'email' => $userEmail,
            'agent_email' => $agentEmail,
            'name' => $user->fullname,
            'policy_id' => $record->policy_id,
            'plan_name' => $record->plan_name_system,
            'effective_date' => $record->effective_date,
            'term_date' => $record->effective_date,
            'message' => $message,
            'reason' => 'Plan Cannot Be Approved',
            'agent_id' => $record->p_agent_num

        ];
        $this->sendEmailWithMessageCenter($data);
    }

    public function sendEmailWithMessageCenter($data)
    {
        $messageCenterBaseUrl = config('app.messagecenter.key') ?? 'https://qa-api-msg.purenroll.com/';
        $url = $messageCenterBaseUrl . "api/v1/send-email-with-content-data";
        $requestData = [
            'email_message_configuration_name' => 'POLICY_WITHDRAWN_EMAIL',
            'toAddress' => [$data['email']],
            'ccAddress' => [$data['agent_email']],
            'bccAddress' => [],
            'subject' => 'Plan Withdrawn Email',
            'attachedFiles' => [],
            'generalTemplateData' => [],
            'contentData' => [
                'countData' => 1,
                'name' => $data['name'],
                'content' => [
                    'messageTop' => $data['message'],
                    'reason' => $data['reason'],
                    'data' => [
                        'Confirmation #' => $data['policy_id'],
                        'Primary Name' => $data['name'],
                        'Plan Name' => $data['plan_name'],
                        'Eff. Date' => $data['effective_date'],
                        'Withdrawn Date' => $data['term_date']
                    ]
                ]
            ]
        ];

        $responseJson = GuzzleHelper::postApi($url, [], $requestData);
        $response = json_decode($responseJson, true);
        if (isset($response['status_code']) && $response['status_code'] == '200' && $response['status'] == 'success') {
            return true;
        } else {
            return false;
        }
    }


    public function getExactWebUrl($weburl)
    {
        $domainOnly = explode("/", $weburl);
        $domains[] = $domainOnly[0];
        $priorityUrl = array_diff($domains, array('corenroll.com'));
        return $priorityUrl[0];
    }

    protected function checkIfAgentHasPlans($agentId, $policyId)
    {
        return PlanOverview::query()
            ->where('pl_type', '=', 'MM')
            ->where('p_agent_num', '=', $agentId)
            ->where('policy_id', '<', $policyId)
            ->whereIn('pstatus', [1, 2])
            ->exists();
    }

    protected function repIncentiveData($planOverview)
    {
        return [
            'agent_id' => (int)$planOverview->p_agent_num,
            'policy_id' => (int)$planOverview->policy_id,
            'pid' => (int)$planOverview->pid,
            'ppid' => (int)$planOverview->plan_pricing_id
        ];
    }

    protected function insertRepIncentive($planOverview)
    {
        $data = $this->repIncentiveData($planOverview);
        $model = RepIncentive::create($data);
        if ($model) {
            //sending notification to agent
            $notificationMessage = $this->notificationToAgentData((int)$planOverview->p_agent_num);
            SendNotificationHelper::sendNotification($notificationMessage);
        }
    }

    protected function notificationToAgentData($agentId)
    {
        return [
            "agent_id" => $agentId,
            "message_title" => "Reward Notification",
            "message_body" => "Congratulations: By enrolling your first major medical plan and logging into the Mobile APP you are now eligible for the $50 bonus.",
            "type" => "REP_INCENTIVE"
        ];
    }

    protected function checkMemberReferralExist($policyId)
    {
        return MemberReferral::query()
            ->where('policy_id', '=', $policyId)
            ->exists();
    }

    protected function updateMemberReferralStatus($policyId, $status = MemberReferral::IS_COMPLETE_YES)
    {
        if ($status == MemberReferral::IS_COMPLETE_NO) {
            return (bool)MemberReferral::query()
                ->where('policy_id', '=', $policyId)
                ->update([
                    'is_complete' => MemberReferral::IS_COMPLETE_NO,
                    'policy_id' => null,
                    'userid' => null
                ]);
        } else {
            return (bool)MemberReferral::query()
                ->where('policy_id', '=', $policyId)
                ->update([
                    'is_complete' => $status
                ]);
        }

    }

    public function updateAIStatus($data)
    {
            \Log::info('Update AI Status Request: ' . json_encode($data));

            if (!isset($data['policy_id']) || !isset($data['approved'])) {
                return $this->failedResponse('Policy ID and Approved Status are required', 400);
            }
            
            $url = config('app.ai_stats.url') . "/api/status-update/" . $data['policy_id'];
            $token = config('app.ai_stats.token');
            $headers = ['Content-Type' => 'application/json'];
            \Log::info('Sending Request to AI-Stats Service: ' . $url);

            $responseJson = GuzzleHelper::putApiWithToken($url, $headers, ['approved'=>$data['approved']], $token);
            $response = json_decode($responseJson, true);

            \Log::info('AI-Stats Service Response: ' . json_encode($response));

            if (isset($response["error"])) {
                return $this->failedResponse($response["error"], 404);
            }
            return $this->successResponse("AI Status Updated Successfully", 200);
    }


}
