<?php

namespace App\Repositories\MedicalTypes;

use App\AgentInfo;
use App\AgentMedicalTypes;
use App\Helpers\CommonHelper;
use App\MedicalTypes;
use App\Plan;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use Exception;
use Illuminate\Support\Facades\DB;

class MedicalTypesRepository
{
    use ResponseMessage, Paginator;

    static $withRelations = [
        'plans',
        'agents',
    ];

    public function store($data)
    {
        try {
            $isMedicalCode = MedicalTypes::where('medical_code', strtoupper($data->medical_code))->exists();
            if ($isMedicalCode) {
                return $this->failedResponse('Medical code name already exists.Please try another!');
            }
            $payload = [
                'medical_code' => strtoupper($data->medical_code),
                'description' => $data->description ?: '',
                'created_by' => $data->created_by,
            ];
            $isCreated =  MedicalTypes::create($payload);
            if ($isCreated) {
                return $this->successResponse('Medical Type Created Successfully!');
            }
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function update($id, $data)
    {
        try {
            $isUpdated =  MedicalTypes::where('id', $id)->update($data);
            if ($isUpdated) {
                return $this->successResponse('Medical Type Updated Successfully!');
            }
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            AgentMedicalTypes::where('medical_type_id', $id)->delete();
            Plan::where('medical_type_id', $id)->update([
                'medical_type_id' => null
            ]);
            MedicalTypes::where('id', $id)->delete();
            DB::commit();
            return $this->successResponse('Medical Type Deleted Successfully!');
        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();
            return $this->failedResponse($e->getMessage());
        }
    }

    public function show($code)
    {
        try {
            $data =  MedicalTypes::where('id', $code)->get();
            return $this->successResponse('Data Feteched Successfully!', $data);
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }


    public function index($filters = [])
    {
        try {
            $data = $this->paginatedList(10, $filters)
                ->appends($filters);
            return $this->formattedData($data);
        } catch (Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }

    protected function getQueries($filters)
    {
        $query = MedicalTypes::query();
        // ->with(self::$withRelations);
        $this->filterContent($query, $filters);
        return $query;
    }

    protected function filterContent($data, $filters = [])
    {
        if (isset($filters['medical_code'])) {
            $data->where('medical_code', '=', strtoupper($filters['medical_code']));
        }
    }

    public function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result[] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    protected function singleFormattedItem($d)
    {
        return [
            'id' => $d->id,
            'medical_code' => strtoupper($d->medical_code),
            'description' => $d->description ?: '',
            'created_by' => $d->created_by,
            'updated_by' => $d->updated_by ?: '',
            'created_at' => $d->created_at,
            'updated_at' => $d->updated_at ?: '',
            'plans' => Plan::where('medical_type_id', $d->id)->get()->toArray(),
            'agents' => AgentMedicalTypes::where('medical_type_id', $d->id)->get()->toArray()
        ];
    }

    public function addMedicalTypesToAgents($data)
    {
        DB::beginTransaction();
        try {
            $medical_id = $data['medical_type_id'];
            $agents = AgentMedicalTypes::where('medical_type_id', $medical_id)->pluck('agent_id')->toArray();
            foreach ($agents as $ag) {
                if (!in_array($ag, array_column($data['agents'], 'agent_id'))) {
                    AgentMedicalTypes::where('agent_id', $ag)->where('medical_type_id', $medical_id)->delete();
                }
            }
            foreach ($data['agents'] as $a) {
                $payload = [
                    'agent_id' => $a['agent_id'],
                    'medical_type_id' => $medical_id,
                ];
                $dataToUpdate = [
                    'include_downline' => $a['includeDownline'],
                ];
                AgentMedicalTypes::updateOrCreate($payload, $dataToUpdate);
            }
            DB::commit();
            return $this->successResponse('Medical type successfully updated!');
        } catch (Exception $e) {
            DB::rollBack();
            return $this->failedResponse($e->getMessage());
        }
    }

    public function addMedicalTypeToPlan($data)
    {
        DB::beginTransaction();
        try {
            $medical_id = $data['medical_type_id'];
            $plans = Plan::where('medical_type_id', $medical_id)->pluck('pid')->toArray();
            foreach ($plans as $pl) {
                if (!in_array($pl, $data['plan_id'])) {
                    Plan::where('pid', $pl)->update([
                        'medical_type_id' => null
                    ]);
                }
            }
            foreach ($data['plan_id'] as $p) {
                $isMedicalCode = Plan::where('pid', $p)->whereNotNull('medical_type_id')->where('medical_type_id', '!=', $medical_id)->exists();
                if ($isMedicalCode) {
                    return $this->failedResponse('Medical code already exists for this plan !');
                }
                Plan::where('pid', $p)->update([
                    'medical_type_id' => $medical_id
                ]);
            }
            DB::commit();
            return $this->successResponse('Medical type successfully updated!');
        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getAgentList()
    {
        try {
            $data = AgentInfo::where('agent_status', 'A')->get(['agent_id', 'agent_fname', 'agent_mname', 'agent_lname']);
            return $this->successResponse('Success', $data);
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getPlanList()
    {
        try {
            $data = Plan::where([['forsale', '=', 1], ['is_assoc', '!=', 1]])->whereNotNull('web_display_name')->groupBy('web_display_name')->get(['pid', 'plan_name_system', 'carrier']);
            $planLists = [];
            foreach ($data as $d) {
                if ($d->carrier == 77 && !CommonHelper::isWordInString($d->plan_name_system, 'L3')) {
                    continue;
                }
                array_push($planLists, $d);
            }
            return $this->successResponse('Success', $planLists);
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getAgentListFromMedicalType($medical_id)
    {
        try {
            $data = AgentMedicalTypes::where('medical_type_id', $medical_id)->get(['agent_id','include_downline']);
            return $this->successResponse('Success', $data);
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function getPlanListFromMedicalType($medical_id)
    {
        try {
            $data = Plan::where('medical_type_id', $medical_id)->pluck('pid');
            return $this->successResponse('Success', $data);
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->failedResponse($e->getMessage());
        }
    }
}
