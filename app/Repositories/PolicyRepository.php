<?php


namespace App\Repositories;


use App\CreditCardInfo;
use App\Dependent;
use App\DependentInPolicy;
use App\DependentPolicy;
use App\GroupIndustry;
use App\Helpers\CheckPlanTypeHelper;
use App\Helpers\CommonHelper;
use App\Helpers\DecryptEcryptHelper;
use App\Helpers\EmailSendHelper;
use App\Helpers\FamilyRelationFinderHelper;
use App\Helpers\SendEmailMemberHelper;
use App\Http\Resources\DataResponse;
use App\Helpers\PolicyUpdateHelper;
use App\MedCondition;
use App\MedMedication;
use App\Payment;
use App\PaymentCC;
use App\PaymentEft;
use App\PaymentTypeCc;
use App\PlanAgeRestrictionDependent;
use App\PlanOverview;
use App\PlanPolicy;
use App\Policy;
use App\PolicyUpdate;
use App\Service\CustomValidationService;
use App\DependentUpdate;
use App\GroupInfo;
use App\Service\DependentInfoService;
use App\Service\PlanAgeService;
use App\Service\Policy\PolicyMessageService;
use App\Traits\DateTrait;
use App\Traits\Paginator;
use App\Traits\ResponseMessage;
use App\Traits\SyncMember;
use App\UserEmployer;
use App\UserInfo;
use App\UserInfoDepMedBex;
use App\UserInfoPolicyAddress;
use App\UserLogin;
use App\NbInvoice;
use App\NbInvoiceItem;
use App\NbPayment;
use Carbon\Carbon;
use DateTime;
use App\Helpers\GuzzleHelper;
use Carbon\Traits\Date;
use Exception;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Repositories\Groups\GroupRepository;

class PolicyRepository
{
    use Paginator, ResponseMessage, SyncMember, DateTrait;
    /**
     * @var Policy
     */
    private $model;
    /**
     * @var PlanAgeService
     */
    private $planAgeService;
    /**
     * @var DependentInfoService
     */
    private $depService;
    /**
     * @var PolicyMessageService
     */
    private $messageService;
    /**
     * @var GroupRepository
     */
    private $groupRepo;


    /**
     * PolicyService constructor.
     * @param Policy $model
     * @param PlanAgeService $planAgeService
     * @param DependentInfoService $depService
     * @param PolicyMessageService $messageService
     */
    public function __construct(
        Policy $model,
        PlanAgeService $planAgeService,
        DependentInfoService $depService,
        PolicyMessageService $messageService,
        GroupRepository $groupRepo
    )
    {
        $this->model = $model;
        $this->planAgeService = $planAgeService;
        $this->depService = $depService;
        $this->messageService = $messageService;
        $this->groupRepo = $groupRepo;
    }

    static $withRelations = [
        'planOverview',
        'getMember',
        'userInfoPolicyAddress',
        'agentInfo',
        'userInfoDepMedBex',
        'dependentInPolicy',
        'policyClientBrowserInfo',
        'healthPlanOverview',
        'getGroup'
    ];

    public function listAll($filters = [])
    {
        return $this->getQueries($filters)->get();
    }

    public function listOne($filters = [])
    {
        return $this->getQueries($filters);
    }

    public function getById($id)
    {
        return $this->model::find($id);
    }

    public function getByField($key, $value)
    {
        return $this->model::query()
            ->where($key, '=', $value)
            ->first();
    }

    public function paginatedList($limit, $filters = [])
    {
        return $this->getQueries($filters)
            ->paginate($limit);
    }


    protected function getQueries($filters)
    {
        $query = $this->model::query()
            ->with(self::$withRelations)
            ->orderBy('policy_id', 'DESC');
        $this->filterContent($query, $filters);
        return $query;
    }


    protected function filterContent($data, $filters = [])
    {

        if (isset($filters['policy_userid'])) {
            $data->where('policy_userid', '=', $filters['policy_userid']);
        }

        if (isset($filters['policy_id'])) {
            $data->where('policy_id', '=', $filters['policy_id']);
        }


        if (isset($filters['effective_date'])) {
            $data->where('effective_date', '=', $filters['effective_date']);
        }

        if (isset($filters['edate'])) {
            $unixDateTime = strtotime($filters["edate"]);
//            $formattedToDate ='FROM_UNIXTIME('.$unixDateTime.',"%Y-%m-%d")';
//            $sqlQuery ='UNIX_TIMESTAMP('.$formattedToDate.')';

            $nextDate = Carbon::parse($filters['edate'])->addDay()->unix();
            $data->whereBetween('edate', [$unixDateTime, $nextDate]);
            //            $data->where('edate', '=', strtotime($filters['edate']));

        }

        if (isset($filters['term_date'])) {
            $data->where('term_date', '=', $filters['term_date']);
        }

        if (isset($filters['status'])) {
            $data->where('status', '=', $filters['status']);
        }

        if (isset($filters['plan_status'])) {
            $planStatus = $filters['plan_status'] === "null" ? null : (bool) $filters['plan_status'];
            $data->whereHas('healthPlanOverview.planPolicy', function ($q) use ($planStatus) {
                if ($planStatus === null) {
                    $q->whereNull('is_approved');
                } else {
                    $q->where('is_approved', $planStatus);
                }
            });
        }

        if (isset($filters['approval'])) {
            $data->where('Approval', '=', $filters['approval']);
        }

        if (isset($filters['fromDate']) and isset($filters['toDate'])) {
            $fromDate = strtotime($filters['fromDate']);
            $toDate = strtotime($filters['toDate']);
            $nextDate = Carbon::parse($toDate)->addDay()->unix();
            $data->whereBetween('edate', [$fromDate, $nextDate]);
        }

        if (isset($filters['health_plan_name'])) {
            $data->whereHas('healthPlanOverview', function ($q) use ($filters) {
                $q->where('plan_name_system', 'LIKE', '%' . $filters['health_plan_name'] . '%');
            });
        }

        if (isset($filters['plan_name'])) {
            $data->whereHas('planOverview', function ($q) use ($filters) {
                $q->where('plan_name_system', 'LIKE', '%' . $filters['plan_name'] . '%');
            });
        }

        if (isset($filters['member_name'])) {
            $data->whereHas('getMember', function ($q) use ($filters) {
                $concat = $this->concatFullname('userinfo.cfname', 'userinfo.cmname', 'userinfo.clname');
                $q->where(DB::raw($concat), 'LIKE', '%' . $filters['member_name'] . '%');
            });
        }

        if (isset($filters['agent_name'])) {
            $data->whereHas('agentInfo', function ($q) use ($filters) {
                $concat = $this->concatFullname('agent_info.agent_fname', 'agent_info.agent_mname', 'agent_info.agent_lname');
                $q->where(DB::raw($concat), 'LIKE', '%' . $filters['agent_name'] . '%');
            });
        }

        if (isset($filters['from_edate']) || isset($filters['to_edate'])) {
            $fromEnrollDate = isset($filters['from_edate']) ? strtotime($filters['from_edate']) : $this->getFirstEnrollmentDate();
            $toEnrollDate = isset($filters['to_edate']) ? strtotime($filters['to_edate']) : Carbon::now()->unix();
            $nextDate = Carbon::parse($toEnrollDate)->addDay()->unix();
            $data->whereBetween('edate', [$fromEnrollDate, $nextDate]);
        }


        if (isset($filters['withoutStatus'])) {
            $data->where('status', '!=', $filters['withoutStatus']);
        }


    }

    protected function getFirstEnrollmentDate()
    {
        return (int)$this->model::query()
                ->whereNotNull('edate')
                ->orderBy('edate', 'ASC')->first()->edate
            ?? strtotime(Carbon::create('1990')->format('Y-m-d'));
    }

    public function getDistinctBillDate()
    {

        $orderDate = DB::select("SELECT CASE WHEN bill_date > 31 THEN 'other' ELSE bill_date END AS unique_bill_date FROM policies GROUP BY unique_bill_date ORDER BY unique_bill_date ASC;");
        $date = [];
        foreach($orderDate as $key => $order) {
            if($order->unique_bill_date != null) {
                $date[$key]['name'] = $order->unique_bill_date;
                $date[$key]['value'] =$order->unique_bill_date;
            }

        }
        usort($date, function($a, $b) {
          return $a['value'] <=> $b['value'];
        });

        return $date;
    }

    protected function concatFullname($firstName, $middleName, $lastName)
    {
        $sqlQuery = "CONCAT_WS(' ',";
        $sqlQuery .= "CASE $firstName WHEN '' THEN NULL ELSE $firstName END,";
        $sqlQuery .= "CASE $middleName WHEN '' THEN NULL ELSE $middleName END,";
        $sqlQuery .= "CASE $lastName  WHEN '' THEN NULL ELSE $lastName END)";
        return $sqlQuery;
    }

    public function paginatedPolicyFormattedList($limit, $filters = [])
    {
        $data = $this->paginatedList($limit, $filters)
            ->appends($filters);
        return $this->formattedData($data);
    }

    public function getHealthPlanPoliciesQuery($filters = [])
    {
        $query = $this->model::query()
            ->with(self::$withRelations)
            ->whereHas('healthPlanOverview', function ($q) {
                $q->where('cid', '!=', 77);
            });

        $query->with(['getGroup' => function ($q) {
            $q->where('gtype', GroupInfo::GROUP_TYPE_EMPLOYER);
        }]);

        $query->orderBy('policy_id', 'DESC');
        $this->filterContent($query, $filters);

        return $query;
    }

    public function paginatedHealthPlanPolicies($limit, $filters = [])
    {
        $data = $this->getHealthPlanPoliciesQuery($filters)
            ->paginate($limit)
            ->appends($filters);

        return $this->healthPlanFormattedData($data);
    }

    protected function singleFormattedItem($d)
    {
        $userFullName = isset($d->getMember) ? $d->getMember->fullname : null;
        $planOverviewRepo = new PlanOverviewRepository(new PlanOverview());
        $plans = isset($d->planOverview) ?
            $planOverviewRepo->formattedItems($d->planOverview)
            : null;
        $agentName = isset($d->agentInfo) ? $d->agentInfo->fullname : null;
        return [
            'policyId' => $d->policy_id,
            'userName' => $userFullName,
            'enrollmentDate' => $d->enrollment_date,
            'effectiveDate' => $d->effective_date,
            'approval' => $d->Approval,
            'status' => $d->status,
            'plans' => $plans,
            'agentInfo' => $agentName
        ];
    }

    protected function healthPlanSingleFormattedItem($d)
    {
        $userFullName = isset($d->getMember) ? $d->getMember->fullname : null;
        $planOverviewRepo = new PlanOverviewRepository(new PlanOverview());
        $healthPlan = isset($d->healthPlanOverview) ?
            $planOverviewRepo->singleFormattedItem($d->healthPlanOverview)
            : null;
        $groupDetails = isset($d->getGroup) ?
            $this->groupRepo->formattedGroup($d->getGroup)
            : null;
        $agentName = isset($d->agentInfo) ? $d->agentInfo->fullname : null;
        return [
            'policyId' => $d->policy_id,
            'userName' => $userFullName,
            'enrollmentDate' => $d->enrollment_date,
            'effectiveDate' => $d->effective_date,
            'status' => $d->status,
            'groupInfo' => $groupDetails,
            'healthPlan' => $healthPlan,
            'agentName' => $agentName,
        ];
    }

    protected function formattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->singleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    protected function healthPlanFormattedData($data)
    {
        $result = [];
        foreach ($data as $d) {
            $result [] = $this->healthPlanSingleFormattedItem($d);
        }
        return [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];
    }

    public function getPolicyDetail($policyId)
    {
        $policy = $this->model::query()
            ->with(self::$withRelations)
            ->where('policy_id', '=', $policyId)
            ->first();

        if (!$policy) return false;

        $userInfoPolicyAddress = isset($policy->userInfoPolicyAddress)
            ? $policy->userInfoPolicyAddress
            : null;
        $paymentInfo = $this->formattedPaymentInfo($policy->payment_type, 'payment', $policy);
        $recurringPaymentInfo = $this->formattedPaymentInfo($policy->recurring_payment_type, 'recurringPayment', $policy);
        $enrollmentDate = $policy->enrollment_date;
        $priorCarrierEffectiveDate = isset($policy->p_carrier_edate) ? Carbon::parse($policy->p_carrier_edate)->format('Y-m-d') : null;
        $priorCarrierTermDate = isset($policy->p_carrier_tdate) ? Carbon::parse($policy->p_carrier_tdate)->format('Y-m-d') : null;
        return [
            'policyId' => $policy->policy_id,
            'plans' => $this->formattedPlanOverview($policy->planOverview),
            'healthPlan' => $this->formattedHealthPlanOverview($policy->healthPlanOverview),
            'enrollmentDate' => isset($userInfoPolicyAddress->enrollment_date) ? $userInfoPolicyAddress->enrollment_date : null,
            'effectiveDate' => isset($userInfoPolicyAddress->effective_date) ? $userInfoPolicyAddress->effective_date : null,
            'priorCarrierName' => $policy->p_carrier_name,
            'priorCarrierEffectiveDate' => $priorCarrierEffectiveDate,
            'priorCarrierTermDate' => $priorCarrierTermDate,
            'primaryInfo' => $this->formattedUserPrimaryInfo($policy->getMember, $enrollmentDate),
            'contactInfo' => $this->formattedUserContactInfo($userInfoPolicyAddress),
            'agentInfo' => $this->formattedAgentInfo($policy->agentInfo),
            'paymentType' => isset($policy->payment_type) ? $policy->payment_type : null,
            'recurringPaymentType' => isset($policy->recurring_payment_type) ? $policy->recurring_payment_type : null,
            'paymentInfo' => $paymentInfo,
            'recurringPaymentInfo' => $recurringPaymentInfo,
            'userInfoDepMedBex' => $this->formattedUserInfoDepBex($policy->userInfoDepMedBex),
            'dependents' => $this->formattedDependents($policy->dependentInPolicy),
            'clientBrowser' => $this->formattedClientBrowser($policy->policyClientBrowserInfo),
        ];
    }

    protected function formattedDependents($dependents)
    {
        if (isset($dependents)) {
            $result = [];
            foreach ($dependents as $q) {
                // Adding Policy ID check
                $healthAnswersV2 = $this->healthAnswersV2($q);
                $healthAnswers = isset($healthAnswersV2) ? $this->formattedUserInfoDepBex($healthAnswersV2, 'dependent'): null;
                //$healthAnswers = isset($q->healthAnswers) ? $this->formattedUserInfoDepBex($q->healthAnswers, 'dependent') : null;
                $result[] = [
                    'fullname' => $q->fullname,
                    'relation' => FamilyRelationFinderHelper::getFullRelation($q->d_relate),
                    'gender' => array_search($q->d_gender, DependentInPolicy::$gender),
                    'dob' => $q->d_dob,
                    'formattedDob' => substr($q->d_dob, 0, 4) . '-XX-XX',
                    'ssn' => 'XXX-XX-' . $q->d_ssn4 . '(' . DecryptEcryptHelper::decryptInfo($q->d_ssn) . ')',
                    'formattedSsn' => 'XXX-XX-' . $q->d_ssn4,
                    'healthAnswers' => $healthAnswers
                ];
            }
            return $result;
        } else {
            return null;
        }
    }
    protected function healthAnswersV2($data){
        $result = [];
        foreach($data->healthAnswers as $value){
            if($data->policy_id == $value->policyId){
                $result[] = $value;
            }
        }
        return $result;
    }

    protected function formattedUserInfoDepBex($userInfoDepMedBex, $type = 'userInfoDepMedBex')
    {
        if (isset($userInfoDepMedBex)) {
            $result = [];
            $total = count($userInfoDepMedBex);
            foreach ($userInfoDepMedBex as $q) {
                $medMedicationsData = $medConditionsData = [];

                if ($q->selected_answer == "1" || $q->selected_answer == true) {
                    if ($type == 'userInfoDepMedBex') {
                        $medMedications = $q->getMedication;
                    } else {
                        $medMedications = $q->getDepMedication;
                    }
                    foreach ($medMedications as $m) {
                        $medMedicationsData[] = [
                            'medication' => $m->medication,
                            'dosage' => $m->dosage,
                            'medical_condition' => $m->medical_condition,

                        ];
                    }
                    $medConditions = $q->getMedCondition;
                    foreach ($medConditions as $mc) {
                        $medConditionsData[] = [
                            'health_condition' => $mc->health_condition,
                            'date_of_onset' => $mc->date_of_onset,
                            'date_of_recovery' => $mc->date_of_recovery,
                            'is_treatment' => $mc->is_treatment,
                            'is_medicate' => $mc->is_medicate,
                            'd_last_seen' => $mc->d_last_seen,
                            'symptoms' => $mc->symptoms,
                        ];
                    }
                }

                $result[] = [
                    'med_id' => $q->med_id,
                    'question' => isset($q->getQuestion->question) ? $q->getQuestion->question : null,
                    'additionalNotes' => $q->additional_notes,
                    'dependentId' => $q->dependent_id,
                    'status' => isset($q->getQuestion->status) ? $q->getQuestion->status : null,
                    'total' => $total,
                    'selectedAnswer' => $q->selected_answer,
                    'medMedications' => $medMedicationsData,
                    'medConditions' => $medConditionsData
                ];
            }
            return $result;
        } else {
            return null;
        }
    }

    protected function formattedClientBrowser($clientBrowser)
    {
        if (isset($clientBrowser)) {
            return [
                'browser' => $clientBrowser->browser,
                'version' => $clientBrowser->browser_version,
                'platform' => $clientBrowser->platform,
                'deviceName' => $clientBrowser->device_name,
                'ipAddress' => $clientBrowser->ip_address,
                'deviceType' => $clientBrowser->device_type,
            ];
        } else {
            return null;
        }

    }

    protected function formattedPaymentEft($payment)
    {
        if (isset($payment)) {
            return [
                'bankId' => $payment->bank_id,
                'bankName' => $payment->bank_name,
                'bankAccountName' => $payment->accountname,
                'bankAccount' => DecryptEcryptHelper::decryptInfo($payment->bank_account),
                'bankAccount4' => $payment->bank_account4,
                'formattedBankAccount' => ' XXXXX-' . $payment->bank_account4,
                'bankRouting' => $payment->bank_routing
            ];
        } else {
            return null;
        }
    }

    protected function formattedPaymentCc($payment)
    {
        if (isset($payment)) {
            return [
                'creditCardId' => $payment->cc_id,
                'creditCardType' => $payment->cc_type,
                'creditCardNum' => DecryptEcryptHelper::decryptInfo($payment->cc_num),
                'creditCardNum4' => $payment->cc_num4,
                'formattedCreditCardNum' => 'XXXXXX-' . $payment->cc_num4,
                'creditCardExpiryMonth' => $payment->cc_expmm,
                'creditCardExpiryYear' => $payment->cc_expyyyy,
                'creditCardStatus' => $payment->cc_status,
                'firstName' => $payment->cc_contactfname,
                'lastName' => $payment->cc_contactlname
            ];
        } else {
            return null;
        }
    }

    protected function formattedPaymentInfo($paymentType, $relation, $model)
    {
        switch ($paymentType) {
            case 'eft':
                $paymentInfo = isset($model->{$relation})
                    ? $this->formattedPaymentEft($model->{$relation}) : null;
                break;
            case 'cc':
                $paymentInfo = isset($model->{$relation})
                    ? $this->formattedPaymentCc($model->{$relation}) : null;
                break;
            default:
                $paymentInfo = $paymentType;
        }
        return $paymentInfo;

    }

    protected function formattedAgentInfo($agentInfo)
    {
        if (isset($agentInfo)) {
            return [
                'id' => $agentInfo->agent_id,
                'code' => $agentInfo->agent_code,
                'fullname' => $agentInfo->fullname,
                'firstName' => $agentInfo->agent_fname,
                'middleName' => $agentInfo->agent_mname,
                'lastName' => $agentInfo->agent_lname,
                'ssn' => $agentInfo->agent_ssn,
                'phone1' => CommonHelper::format_phone($agentInfo->agent_phone1),
                'address1' => $agentInfo->agent_address1,
                'address2' => $agentInfo->agent_address2,
                'city' => $agentInfo->agent_city,
                'state' => $agentInfo->agent_state,
                'zip' => $agentInfo->agent_zip,
            ];
        } else {
            return null;
        }
    }

    protected function formattedUserContactInfo($userContactInfo)
    {
        if (isset($userContactInfo)) {
            return [
                'cEmail' => $userContactInfo->cemail,
                'phone1' => CommonHelper::format_phone($userContactInfo->phone1),
                'address1' => $userContactInfo->address1,
                'address2' => $userContactInfo->address2,
                'city' => $userContactInfo->city,
                'state' => $userContactInfo->state,
                'zip' => $userContactInfo->zip,
            ];
        } else {
            return null;
        }
    }

    protected function formattedUserPrimaryInfo($userPrimaryInfo, $enrollmentDate)
    {
        if (isset($userPrimaryInfo)) {
            return [
                'id' => $userPrimaryInfo->userid,
                'fullname' => $userPrimaryInfo->fullname,
                'firstName' => $userPrimaryInfo->cfname,
                'middleName' => $userPrimaryInfo->cmname,
                'lastName' => $userPrimaryInfo->clname,
                'email' => $userPrimaryInfo->cemail,
                'dob' => $userPrimaryInfo->cdob,
                'formattedDob' => substr($userPrimaryInfo->cdob, 0, 4) . '-XX-XX',
                'gender' => (int)$userPrimaryInfo->cgender == 0 ? 'Male' : 'Female',
                'heightFeet' => $userPrimaryInfo->height_feet,
                'heightInches' => $userPrimaryInfo->height_inches,
                'weightLbs' => $userPrimaryInfo->weight_lbs,
                'ssn' => DecryptEcryptHelper::decryptInfo($userPrimaryInfo->cssn),
                'formattedSsn' => 'XXX-XX-' . $userPrimaryInfo->cssn4,
                'signUpDate' => $enrollmentDate
            ];
        } else {
            return null;
        }
    }

    protected function formattedPlanOverview($planOverview)
    {
        if (isset($planOverview)) {
            $result = [];
            foreach ($planOverview as $p) {
                $result[] = [
                    'name' => $p->plan_name_system,
                    'tier' => $p->tier
                ];
            }
            return $result;
        } else {
            return null;
        }
    }

    public function formattedHealthPlanOverview($healthPlanOverview)
    {
        if (isset($healthPlanOverview)) {
            return [
                'name' => $healthPlanOverview->plan_name_system,
                'tier' => $healthPlanOverview->tier
            ];
        } else {
            return null;
        }
    }

    public function getEffectiveDates($filters = [])
    {
        $query = $this->model::query()
            ->select('effective_date');
        if (isset($filters['healthPlanExists'])) {
            $query->whereHas('healthPlanOverview');
        }
        $dates = $query
            ->distinct()
            ->orderBy('effective_date', 'DESC')
            ->get();
        $data = [];
        foreach ($dates as $date) {
            if (!empty($date->effective_date)) {
                $data[] = $date->effective_date;
            }
        }
        return $data;
    }


    public function changePolicyEffectiveDate($data)
    {
        $policy = $this->model::query()
            ->where('policy_id', '=', $data->policyID)
            ->first();

        if (!$policy) return $this->failedResponse("Policy not found.");
        $pastThirdMonth = date('Y-m-01', strtotime('-3 month'));
        $oldEffectiveDate = $policy->effective_date;
        $newEffectiveDate = date('Y-m-01', strtotime($data->new_effective_date));
        if (strtotime($pastThirdMonth) > strtotime($newEffectiveDate)) return $this->failedResponse("Change allowed only for last three months.");
        $policyDateChanged = $this->model::query()->where('policy_id', $data->policyID)->update(['effective_date' => $newEffectiveDate]);
        $planIDs = [];
        $planOldDate = [];
        $message = "";
        if ($policyDateChanged) {
            $updateMessage = [];
            if (strtotime($newEffectiveDate) != strtotime($oldEffectiveDate)) {
                array_push($updateMessage, "Policy effective date change from {$this->formatDate($oldEffectiveDate)} to {$this->formatDate($newEffectiveDate)}.");
                $message = "Policy Effective date Changed from {$this->formatDate($oldEffectiveDate)} to {$this->formatDate($newEffectiveDate)}";
            }

            if (count($data->plans) == 1) {
                if (strtotime($data->plans[0]['effective_date']) > strtotime($newEffectiveDate)) return $this->failedResponse("Policy effective date and Plan effective date should be equal in single plan.");
                $planOldData = PlanOverview::where('p_ai', $data->plans[0]['primaryID'])->select('peffective_date', 'web_display_name')->first();
                PlanPolicy::where('p_ai', $data->plans[0]['primaryID'])->update(['peffective_date' => $data->plans[0]['effective_date']]);
                $updateNBInvoice = self::updateNBInvoice($data, $newEffectiveDate);
                if (strtotime($data->plans[0]['effective_date']) != strtotime($planOldData->peffective_date)) {
                    $planIDs[] = $data->plans[0]['primaryID'];
                    $planOldDate[] = $planOldData->peffective_date;
                    array_push($updateMessage, "{$planOldData->web_display_name} plan effective date change from {$this->formatDate($planOldData->peffective_date)} to {$this->formatDate($data->plans[0]['effective_date'])}.");
                    if (!$message) {
                        $message = "Plan Effective date has changed";
                    }
                }
            } else {
                foreach ($data->plans as $plan) {
                    $planOldData = PlanOverview::where('p_ai', $plan['primaryID'])->select('peffective_date', 'web_display_name')->first();
                    PlanPolicy::where('p_ai', $plan['primaryID'])->update(['peffective_date' => $plan['effective_date']]);
                    $updateNBInvoice = self::updateNBInvoice($data, $newEffectiveDate);
                    if (strtotime($plan['effective_date']) != strtotime($planOldData->peffective_date)) {
                        $planIDs[] = $plan['primaryID'];
                        $planOldDate[] = $planOldData->peffective_date;
                        array_push($updateMessage, "{$planOldData->web_display_name} plan effective date change from {$this->formatDate($planOldData->peffective_date)} to {$this->formatDate($plan['effective_date'])}.");
                        if (!$message) {
                            $message = "Plan Effective date has changed";
                        }
                    }

                }
            }

            if ($data->sendEmail == 1 && $message) {
                EmailSendHelper::sendChangeDateEmail($data->policyID, 'Effective date Changed', $message, $planIDs, $planOldDate);
            }
            $updateMessage = implode("<br/>", $updateMessage);

            if ($updateMessage) {
                $this->updatePolicyLog($policy, "EDU", $updateMessage, $data->loginUserId);
            }
            return $this->successResponse("Successfully Changed Effective date of Policy.");
        }
        return $this->failedResponse("Error while changing policy effective date");
    }


    /**
     * @param $query
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * Fetching userdepmedbex info conditionally
     * Parameter are dep_id and policy_id
     * Fetching pricing policies ids and checking if those ids exist in plan questions view
     * if pricing policies ids exist health questions are fetch else return error response
     */
    public function getHealthQuestions($query)
    {
        $pricingPolicyIds = $this->depService->getPlanPricingIdsFromPlanOverview($query['policy_id']);
        $checkRequiredHealthQuestionnaire = $this->depService->checkHealthQuestionsRequired($pricingPolicyIds);
        if (! empty($query['dep_id']) && $checkRequiredHealthQuestionnaire) {
            $depId = $query['dep_id'];
            $policyId = $query['policy_id'];
            $userDepMedBex = UserInfoDepMedBex::query()
                ->where('dependent_id', '=', $depId)
                ->where('policyId', $policyId)
                ->get();
            $data = $this->formattedHealthQuestionnaire($userDepMedBex, 'dependent');
        } elseif ( empty($query['dep_id']) && $checkRequiredHealthQuestionnaire) {
            $policyId = $query['policy_id'];
            $policy = Policy::find($policyId);
            if (!$policy) return $this->failedResponse('No Policy Found.');
            $userDepMedBex = $policy->userInfoDepMedBex;
            $data = $this->formattedHealthQuestionnaire($userDepMedBex, 'policy');
        } else {
            return $this->failedResponse('Failed to fetch questionnaires');
        }
        return $this->successResponse('Data', $data);
    }

    /**
     * @param $userInfoDepMedBex
     * @param string $type
     * @return array|null
     * formatting userinfo_dep_meds_bex data
     * here assuming med condition is one to one and medication is multiple
     * if question_id is 113 then its medication else conditions
     */
    protected function formattedHealthQuestionnaire($userInfoDepMedBex, $type)
    {
        if (isset($userInfoDepMedBex)) {
            $result = [];
            $healthQuestions = [];
            foreach ($userInfoDepMedBex as $q) {
                $medMedicationsData = [];
                $medConditionsData = [
                    'health_condition' => "",
                    'date_of_onset' => "",
                    'date_of_recovery' => "",
                    'is_treatment' => "",
                    'is_medicate' => "",
                    'd_last_seen' => "",
                    'symptoms' => "",
                ];
                $selectedAnswer = $type == 'policy' ? 0 : (int)$q->selected_answer;
                if ($selectedAnswer == "1" || $selectedAnswer == true) {
                    if ($q->question_id == 113) {
                        if ($type == 'policy') {
                            $medMedications = $q->getMedication;
                        } else {
                            $medMedications = $q->getDepMedication;
                        }
                        foreach ($medMedications as $m) {
                            $medMedicationsData[] = [
                                'id' => $m->id,
                                'medication' => $m->medication ?: "",
                                'dosage' => $m->dosage ?: "",
                                'medical_condition' => $m->medical_condition ?: "",
                            ];
                        }
                    }
                    $mc = $q->medCondition;
                    if (isset($mc)) {
                        $medConditionsData = [
                            'id' => $mc->id ?: "",
                            'health_condition' => $mc->health_condition ?: "",
                            'date_of_onset' => $mc->date_of_onset ?: "",
                            'date_of_recovery' => $mc->date_of_recovery ?: "",
                            'is_treatment' => $mc->is_treatment ?: "",
                            'is_medicate' => $mc->is_medicate ?: "",
                            'd_last_seen' => $mc->d_last_seen ?: "",
                            'symptoms' => $mc->symptoms ?: "",
                        ];
                    }
                }
                $questionType = $q->question_id == 113 ? 'medication' : 'condition';
                $additionalNotes = ($type == 'policy') ? '' : ($q->additional_notes ?? "");
                $healthQuestions[] = [
                    'med_id' => $q->med_id,
                    'question_id' => $q->question_id,
                    'question' => isset($q->getQuestion) ? $q->getQuestion->question : null,
                    'status' => isset($q->getQuestion) ? $q->getQuestion->status : null,
                    'selectedAnswer' => $type == 'policy' ? 0 : (int)$q->selected_answer,
                    'medMedications' => $medMedicationsData,
                    'medConditions' => $questionType == 'condition' ? $medConditionsData : null,
                    'type' => $questionType,
                ];
            }
            return $result = [
                'healthQuestionnaires' => $healthQuestions,
                'health_notes' => $additionalNotes ?? ""
            ];
        } else {
            return null;
        }
    }

    protected function dependentData($request)
    {
        $request['d_ssn'] = preg_replace('/[^0-9]/', '', $request['d_ssn']);
        $ssn = DecryptEcryptHelper::encryptInfo($request['d_ssn']);
        $ssn4 = substr($request['d_ssn'], -4);
        return [
            "userid" => $request['userid'],
            "d_fname" => $request['d_fname'],
            "d_mname" => isset($request['d_mname']) ? $request['d_mname'] : "",
            "d_lname" => $request['d_lname'],
            "d_relate" => $request['d_relate'],
            "d_gender" => $request['d_gender'],
            "d_dob" => $request['d_dob'] ? Carbon::parse($request['d_dob'])->format('Y-m-d') : null,
            "d_ssn" => $ssn,
            "d_ssn4" => $ssn4,
            "dhrq" => $request['dhrq'],
            "dhrq2" => $request['dhrq2'],
            "dwrq" => $request['dwrq'],
            "additional_notes" => $request['d_notes'],
            "is_legally_disabled" => $request['is_legally_disabled'] ?? 0,
            "disability_condition" => $request['disability_condition'] ?? null
        ];
    }

    /**
     * @param $policy
     * @param $action
     * @param $comment
     * @param $loginUserId
     * @return PolicyUpdate
     * updating policy logs
     */
    public function updatePolicyLog($policy, $action, $comment, $loginUserId)
    {
        $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
        $update = [
            'elgb_policyid' => $policy->policy_id,
            'elgb_act' => $action,
            'elgb_act_date' => time(),
            'elgb_comment' => $comment,
            'elgb_agent' => $loginUserId,
            'elgb_file_date' => date('Y-m-d'),
            'origin' => $origin
        ];
        $policyUpdateLog = new PolicyUpdate();
        $policyUpdateLog->create($update);
        return $policyUpdateLog;
    }

    public function addToUserInfoDepMedBex($data, $depId)
    {
        $policyId = $data['policy_id'];
        $healthQuestionnaires = $data['healthQuestionnaires'];
        foreach ($healthQuestionnaires as $h) {

            $healthData = [
                'question_id' => $h['question_id'],
                'dependent_id' => $depId,
                'policyId' => $policyId,
                'selected_answer' => $h['selectedAnswer'],
                'additional_notes' => $data['health_notes']
            ];
            $userDepMedBex = UserInfoDepMedBex::create($healthData);
            //add to med_conditions
            if ($h['selectedAnswer'] == '1' && $h['type'] == 'condition') {
                $mc = $h['medConditions'];
                $medConditionsData = [
                    'med_id' => $userDepMedBex->med_id,
                    'health_condition' => $mc['health_condition'] ?: "",
                    'date_of_onset' => $mc['date_of_onset'] ?: "",
                    'date_of_recovery' => $mc['date_of_recovery'] ?: "",
                    'is_treatment' => $mc['is_treatment'] ?: "",
                    'is_medicate' => $mc['is_medicate'] ?: "",
                    'd_last_seen' => $mc['d_last_seen'] ?: "",
                    'symptoms' => $mc['symptoms'] ?: "",
                ];
                $medCondition = MedCondition::create($medConditionsData);
            }
            //add to med_medications
            if ($h['selectedAnswer'] == '1' && $h['type'] == 'medication') {
                foreach ($h['medMedications'] as $m) {
                    $medMedicationData = [
                        'med_id' => $userDepMedBex->med_id,
                        'dependent_id' => $depId,
                        'medication' => $m['medication'] ?: "",
                        'dosage' => $m['dosage'] ?: "",
                        'medical_condition' => $m['medical_condition'] ?: "",
                    ];
                    $medMedications = MedMedication::create($medMedicationData);
                }
            }
        }
    }

    public function createDependent($data)
    {
        $formatted_ssn = preg_replace('/[^0-9]/', '', $data['d_ssn']);
        $depService = new DependentInfoService();
        $isExtraHealth = $depService->checkIfPolicyHasExtraHealth($data['policy_id']);
        $planPricingIds = $depService->getPlanPricingIdsFromPlanOverview($data['policy_id']);
        $checkHealthQ = $depService->checkHealthQuestionsRequired($planPricingIds) && !$depService->checkIfPolicyHasExtraHealth($data['policy_id']);
        if (!$checkHealthQ && isset($data['healthQuestionnaires'])) return $this->failedResponse('HealthQuestionnaires are not required for this plan.');
        $shouldSkip = ($data['is_legally_disabled'] == 1) && $this->shouldSkipAgeCheck($data['policy_id']);
        if (!$shouldSkip) {
            $checkAge = $this->checkPlanAge($data);
            if ($checkAge['success'] == false) {
                return $this->failedResponse($checkAge['message']);
            }
        }
        $policy = Policy::find($data['policy_id']);
        $userInfoPolicyAddress = isset($policy->userInfoPolicyAddress)
            ? $policy->userInfoPolicyAddress
            : null;
        if(!is_null($userInfoPolicyAddress) && !$isExtraHealth) {
            $client_ssn = DecryptEcryptHelper::decryptInfo($userInfoPolicyAddress->cssn);
            if ($client_ssn == $formatted_ssn) return $this->failedResponse("SSN must be unique. The requested SSN matches a client's SSN.");
        }
        $data['userid'] = $policy->getMember->userid;
        $depData = $this->dependentData($data);
        $tier = PlanOverview::where('policy_id',$data['policy_id'])->get('tier');
        $dependentInPolicy = $policy->dependentInPolicy;
        $isL713 = CheckPlanTypeHelper::checkL713ProductByPolicy($data['policy_id']);
        $isIHAPlans = CheckPlanTypeHelper::checkIHAPlan($data['policy_id']);
        if($data['d_option'] == "instant") {
            if( $depData['d_relate'] == 'S' && $dependentInPolicy->contains('d_relate', 'S'))
            return $this->failedResponse('Spouse is already added for this policy.');

            switch ( $tier[0]->tier ) {
                case 'IC':
                    if ( $depData['d_relate'] != 'C')
                        return $this->failedResponse('Only Child is allowed for this tier.');
                    if ( $isL713 && $dependentInPolicy->where('d_relate', 'C')->count() >= 1) {
                        return $this->failedResponse('The IC tier for L713 plan allows only 1 child.');
                    }
                    if ( $isIHAPlans && $dependentInPolicy->where('d_relate', 'C')->count() >= 5) {
                        return $this->failedResponse('Dependent max limit has been crossed. No more than 5 dependents can be added in this coverage.');
                    }
                    if ( $isExtraHealth && $dependentInPolicy->where('d_relate', 'C')->count() >= 9) {
                        return $this->failedResponse('No more than 9 children can be added as dependents in this coverage.');
                    }
                    break;

                case 'IS':
                    if ( $depData['d_relate'] != 'S')
                        return $this->failedResponse('Only Spouse is allowed for this tier.');
                    break;

                case 'IF':
                    if ( $isIHAPlans && $dependentInPolicy->where('d_relate', 'C')->count() >= 9) {
                        return $this->failedResponse('No more than 9 children can be added as dependents in this coverage.');
                    }
                    if ( $isExtraHealth && $dependentInPolicy->where('d_relate', 'C')->count() >= 8) {
                        return $this->failedResponse('No more than 8 children can be added as dependents in this coverage.');
                    }
                    break;

                case 'IO':
                    return $this->failedResponse('Dependent info is not required for this tier.');
            }
        } else {
            if( $depData['d_relate'] == 'S' && $dependentInPolicy->contains('d_relate', 'S'))
            return $this->failedResponse('Spouse is already added for this policy.');

            $dependentUpdate = DependentUpdate::where('policy_id', $data['policy_id'])->where('relation', 'Spouse')->first();
            if( $depData['d_relate'] == 'S' && isset($dependentUpdate))
            return $this->failedResponse('Spouse is already added in dependent log for this policy.');
        }


        DB::beginTransaction();
        try {
            //create dependent
            $dep = Dependent::create($depData);
            if($data['d_option'] == "instant") {
                //create in dependent in policy
                DependentPolicy::create([
                    'dependent_id' => $dep->did,
                    'policy_id' => $policy->policy_id
                ]);

                //update in policy updates
                $this->updatePolicyLog($policy, 'DEP', 'Dependent Created.', $data['login_user_id']);
            } else {
                $data['new_effective_date'] = Carbon::parse($data['new_effective_date'])->format('Y-m-d');
                //create in dependent in policy
                $full_name = ucfirst($data['d_fname']) . ($data['d_mname'] ? ' ' . $data['d_mname'] : '') . ' ' . $data['d_lname'];
                if ($data['d_relate'] == "0" || $data['d_relate'] == "S") {
                    $relation = "Spouse";
                } else if($data['d_relate'] == "1" || $data['d_relate'] == "C") {
                    $relation = "Child";
                } else if ($data['d_relate'] == "O") {
                    $relation = "Other";
                }
                DependentUpdate::create([
                    'dependent_id' => $dep->did,
                    'full_name' => $full_name,
                    'policy_id' => $policy->policy_id,
                    'relation' => $relation,
                    'created_by' => $data['login_user_id'],
                    'elgb_effdate' => $data['new_effective_date'],
                    'comments' => $data['comments'],
                ]);
            }
            //create in userinfo_dep_meds_bex
            if($checkHealthQ){
                $this->addToUserInfoDepMedBex($data, $dep->did);
            }

            DB::commit();
            return $this->successResponse('Dependent Created.');
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    public function getDependentList($policy_id)
    {
        try{
            $policy = Policy::find($policy_id);
            $userId = $policy->getMember->userid;
            $schedule_dependent = DependentUpdate::pluck('dependent_id')->all();
            $data = Dependent::where('userid',$userId)->whereDoesntHave('getDependentPolicy', function ($query) use ($policy_id) {
                $query->where('policy_id', $policy_id);
            })
            ->whereNotIn('did', $schedule_dependent)
            ->get()->toArray();
            $data['min_effective_date'] = date('Y-m-d', strtotime('+1 day'));
            return $this->successResponse('Success',$data);
        }catch(Exception $e){
            return $this->failedResponse($e->getMessage());
        }
    }

    public function addDependentToPolicy($request)
    {
        try{
            $policy = Policy::find($request['policy_id']);
            $dependentInPolicy = $policy->dependentInPolicy;

            $tier = PlanOverview::where('policy_id',$request['policy_id'])->get('tier');
            $isL713 = CheckPlanTypeHelper::checkL713ProductByPolicy($request['policy_id']);
            $isIHAPlans = CheckPlanTypeHelper::checkIHAPlan($request['policy_id']);

            switch ( $tier[0]->tier ) {
                case 'IC':
                    if($isL713 && $dependentInPolicy->where('d_relate', '==', 'C')->count() >= 1) {
                        return $this->failedResponse('The IC tier for L713 plan allows only 1 child.');
                    }
                    if ( $isIHAPlans && $dependentInPolicy->where('d_relate', '==', 'C')->count() >= 5) {
                        return $this->failedResponse('Dependent max limit has been crossed. No more than 5 dependents can be added in this coverage.');
                    }
                    break;

                case 'IF':
                    if ( $isIHAPlans && $dependentInPolicy->where('d_relate', '==', 'C')->count() >= 9) {
                        return $this->failedResponse('Dependent max limit has been crossed. No more than 9 dependents can be added in this coverage.');
                    }
                    break;

                case 'IO':
                    return $this->failedResponse('Dependent info is not required for this tier.');
            }

            //spouse validation
            $dependents = DependentPolicy::where(['policy_id' => $request['policy_id']])->whereNotNull('dependent_id')->pluck('dependent_id')->toArray();
            if(!empty($dependents)){
                $spouseAlreadyExists = Dependent::whereIn('did',$dependents)->where('d_relate','S')->exists();
                if($spouseAlreadyExists &&  Dependent::where('did',$request['did'])->where('d_relate','S')->exists()){
                    return $this->failedResponse("Spouse already exists! Cannot add more than one spouse.");
                }
            }
            //create in dependent in policy
            DependentPolicy::create([
                'dependent_id' => $request['did'],
                'policy_id' => $request['policy_id']
            ]);
            return $this->successResponse('Dependent Created Successfully!',Dependent::find($request['did']));
        }catch(Exception $e){
            return $this->failedResponse($e->getMessage());
        }
    }

    public function updateToUserInfoDepMedBex($data, $depId)
    {
        $healthQuestionnaires = $data['healthQuestionnaires'];
        $currentMedMedicationIds = [];
        foreach ($healthQuestionnaires as $h) {
            $healthData = [
                'question_id' => $h['question_id'],
                'dependent_id' => $depId,
                'selected_answer' => $h['selectedAnswer'],
                'additional_notes' => $data['health_notes']
            ];

            //add to med_conditions
            $userDepMedBex = UserInfoDepMedBex::find($h['med_id']);
            $userDepMedBex->update($healthData);
            if ($h['selectedAnswer'] == '1') {
                if ($h['type'] == 'condition') {
                    $mc = $h['medConditions'];
                    $medConditionsData = [
                        'med_id' => $userDepMedBex->med_id,
                        'health_condition' => $mc['health_condition'] ?: "",
                        'date_of_onset' => $mc['date_of_onset'] ?: "",
                        'date_of_recovery' => $mc['date_of_recovery'] ?: "",
                        'is_treatment' => $mc['is_treatment'] ?: "",
                        'is_medicate' => $mc['is_medicate'] ?: "",
                        'd_last_seen' => $mc['d_last_seen'] ?: "",
                        'symptoms' => $mc['symptoms'] ?: "",
                    ];
                    /***
                     * updating if data found else creating new one
                     */
                    if (isset($mc['id'])) {
                        $medCondition = MedCondition::find($mc['id']);
                        $medCondition->update($medConditionsData);
                    } else {
                        MedCondition::create($medConditionsData);
                    }
                }
                //add to med_medications
                if ($h['type'] == 'medication') {
                    foreach ($h['medMedications'] as $m) {
                        $medMedicationData = [
                            'med_id' => $userDepMedBex->med_id,
                            'dependent_id' => $depId,
                            'medication' => $m['medication'] ?: "",
                            'dosage' => $m['dosage'] ?: "",
                            'medical_condition' => $m['medical_condition'] ?: "",
                            'additional_notes' => $data['health_notes']
                        ];
                        if (isset($m['id'])) {
                            $currentMedMedicationIds[] = $m['id'];
                            $medMedication = MedMedication::find($m['id']);
                            $medMedication->update($medMedicationData);
                        } else {
                            $currentMedMedicationIds[] = MedMedication::create($medMedicationData)->id;
                        }
                    }
                }
            }
        }
        MedMedication::where('dependent_id', '=', $data['did'])->whereNotIn('id', $currentMedMedicationIds)->delete();
    }

    public function updateDependent($data)
    {
        $plan_tier = PolicyUpdateHelper::getPolicyPlanTier($data['policy_id']);
        $dependents = PolicyUpdateHelper::getPolicyDependents($data['policy_id']);
        $depService = new DependentInfoService();
        $isL713 = CheckPlanTypeHelper::checkL713ProductByPolicy($data['policy_id']);
        $otherDependents = $dependents->where('dependent_id', '!=', $data['did']);
        $isExtraHealth = $depService->checkIfPolicyHasExtraHealth($data['policy_id']);
        $policy = Policy::find($data['policy_id']);
        $isIHAPlans = CheckPlanTypeHelper::checkIHAPlan($data['policy_id']);
        $userInfoPolicyAddress = isset($policy->userInfoPolicyAddress)
            ? $policy->userInfoPolicyAddress
            : null;
        $formatted_ssn = preg_replace('/[^0-9]/', '', $data['d_ssn']);
        // if(!is_null($userInfoPolicyAddress) && !$this->depService->checkIfPolicyHasExtraHealth($data['policy_id'])) {
        //     $client_ssn = DecryptEcryptHelper::decryptInfo($userInfoPolicyAddress->cssn);
        //     if ($client_ssn == $formatted_ssn) return $this->failedResponse("SSN must be unique. The requested SSN matches a client's SSN.");
        // }
        switch ( $plan_tier ) {
            case 'IC':
                if ( $data['d_relate'] != 'C')
                    return $this->failedResponse('Only Child is allowed for this tier.');
                if ( $isL713 && $otherDependents->where('d_relate', 'C')->count() >= 1) {
                    return $this->failedResponse('The IC tier for L713 plan allows only 1 child.');
                }
                if ( $isIHAPlans && $otherDependents->where('d_relate', 'C')->count() >= 5) {
                    return $this->failedResponse('Dependent max limit has been crossed. No more than 5 dependents can be added in this coverage.');
                }
                if ( $isExtraHealth && $otherDependents->where('d_relate', 'C')->count() >= 9) {
                    return $this->failedResponse('Dependent max limit has been crossed. No more than 9 dependents can be added in this coverage.');
                }
                break;

            case 'IS':
                if ( $data['d_relate'] != 'S')
                    return $this->failedResponse('Only Spouse is allowed for this tier.');
                break;

            case 'IF':
                if (strtoupper($data['d_relate']) == 'S' && $otherDependents->contains('d_relate', 'S')) {
                    return $this->failedResponse('Spouse already exists. Only single dependent of type Spouse is allowed in Tier IF.');
                }
                if ( $isIHAPlans && $otherDependents->where('d_relate', 'C')->count() >= 9) {
                    return $this->failedResponse('Dependent max limit has been crossed. No more than 9 dependents can be added in this coverage.');
                }
                if ( $isExtraHealth && $otherDependents->where('d_relate', 'C')->count() >= 8) {
                    return $this->failedResponse('Dependent max limit has been crossed. No more than 8 dependents can be added in this coverage.');
                }
                break;

            case 'IO':
                return $this->failedResponse('Dependent info is not required for this tier.');
        }

        $depService = new DependentInfoService();
        $planPricingIds = $depService->getPlanPricingIdsFromPlanOverview($data['policy_id']);
        $checkHealthQ = $depService->checkHealthQuestionsRequired($planPricingIds) && !$depService->checkIfPolicyHasExtraHealth($data['policy_id']);
        $shouldSkip = ($data['is_legally_disabled'] == 1) && $this->shouldSkipAgeCheck($data['policy_id']);
        if (!$shouldSkip) {
            $checkAge = $this->checkPlanAge($data);
            if ($checkAge['success'] == false) {
                return $this->failedResponse($checkAge['message']);
            }
        }

        $dependent = Dependent::find($data['did']);
        $policy = Policy::find($data['policy_id']);
        $data['userid'] = $policy->getMember->userid;
        $depData = $this->dependentData($data);

        $changes = [];

        // Get old and new full names
        $oldFullName = trim(implode(' ', array_filter([
            $dependent->getOriginal('d_fname'),
            $dependent->getOriginal('d_mname'),
            $dependent->getOriginal('d_lname')
        ])));

        $newFullName = trim(implode(' ', array_filter([
            $depData['d_fname'] ?? '',
            $depData['d_mname'] ?? '',
            $depData['d_lname'] ?? ''
        ])));

        $nameChanged = false; // Flag to track if any name field changed

        foreach ($depData as $key => $newValue) {
            if ($key === 'd_ssn') continue; // Ignore SSN since it has encrypted value

            $oldValue = $dependent->getOriginal($key);

            // Check if the value actually changed
            if ($oldValue != $newValue) {
                // Detect if name field changed
                if (in_array($key, ['d_fname', 'd_mname', 'd_lname'])) {
                    $nameChanged = true;
                    continue; // Skip logging individual name changes
                }

                // Format key name
                $formattedKey = ($key === 'd_ssn4') ? 'Ssn' : ucfirst(str_replace('d_', '', $key));

                // Handle gender transformation
                if ($key === 'd_gender') {
                    $oldValue = ($oldValue == '0') ? 'Male' : 'Female';
                    $newValue = ($newValue == '0') ? 'Male' : 'Female';
                }

                $changes[] = "$formattedKey was updated from '$oldValue' to '$newValue'";
            }
        }

        // Log full name change if any name field was updated
        if ($nameChanged) {
            $changes[] = "Name was updated from '$oldFullName' to '$newFullName'";
        }

        $logMessage = !empty($changes) ? implode(', ', $changes) : 'No changes were made to the dependent details.';

        DB::beginTransaction();
        try {
            //update dependent
            $dependent->update($depData);

            //create in userinfo_dep_meds_bex
            if ($checkHealthQ && DependentInfoService::checkHealthQuestionConfiguredForPolicyDep($dependent->did, 'dependent')) {
                $this->updateToUserInfoDepMedBex($data, $dependent->did);
            }
            //update in policy updates
            $this->updatePolicyLog($policy, 'DEP', $logMessage, $data['login_user_id']);

            DB::commit();
            return $this->successResponse('Dependent Updated.');
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    protected function deleteCollection($collection)
    {
        if (isset($collection)) {
            foreach ($collection as $c) {
                $c->delete();
            }
        }
    }

    public function deleteDependent($data)
    {
        $depInPolicy = DependentPolicy::where('dependent_id', $data['did'])
            ->where('policy_id', $data['policy_id'])
            ->first();
        $policy = Policy::find($data['policy_id']);

        $plan_tier = PolicyUpdateHelper::getPolicyPlanTier($data['policy_id']);
        $dependents = PolicyUpdateHelper::getPolicyDependents($data['policy_id']);
        $isL713 = CheckPlanTypeHelper::checkL713ProductByPolicy($data['policy_id']);
        $otherDependents = $dependents->where('dependent_id', '!=', $data['did']);

        switch ($plan_tier) {
            case 'IF':
                if (! $isL713) {
                    if (! $otherDependents->contains('d_relate', 'S')
                        || ($otherDependents->contains('d_relate', 'S') && $otherDependents->where('d_relate', '=', 'C')->count() == 0) ) {
                        return $this->failedResponse('Tier IF must have at least 1 spouse with 1 child.');
                    }
                }
                if ($isL713) {
                    if ( $otherDependents->contains('d_relate', 'S') && $otherDependents->where('d_relate', '=', 'C')->count() == 0 ) {
                        return $this->failedResponse('Tier IF of L713 plan must have at least 1 spouse with 1 child.');
                    }
                    if (! $otherDependents->contains('d_relate', 'S') && $otherDependents->where('d_relate', '=', 'C')->count() <= 1 ) {
                        return $this->failedResponse('Tier IF of L713 plan must have at least 2 children without a spouse.');
                    }
                }
                break;

            case 'IS':
                if (! $otherDependents->contains('d_relate', 'S') ) {
                    return $this->failedResponse('Tier IS must have 1 spouse.');
                }
                break;

            case 'IC':
                if (! $otherDependents->contains('d_relate', 'C') ) {
                    return $this->failedResponse('Tier IC must have at least 1 child.');
                }
                break;
        }

        DB::beginTransaction();
        try {

            //remove userinfo_dep_meds_bex with its relations
            $userInfoDepMedBox = UserInfoDepMedBex::query()->where('dependent_id', '=', $data['did'])->get();
            foreach ($userInfoDepMedBox as $u) {
                if (isset($u->medCondition)) {
                    $u->medCondition->delete();
                }
                $this->deleteCollection($u->getDepMedication);
                $u->delete();
            }

            $depInPolicy->delete();

            //update in policy updates
            $this->updatePolicyLog($policy, 'DEP', $data['reason'], $data['login_user_id']);

            DB::commit();
            return $this->successResponse('Dependent removed from policy.');
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    /**
     * @param $data
     * @return array
     */
    protected function checkPlanAge($data): array
    {
        $checkAgeData = [
            'd_dob' => $data['d_dob'],
            'policy_id' => $data['policy_id'],
            'd_relate' => $data['d_relate']
        ];
        return $this->planAgeService->validateSingleDependent($checkAgeData);
    }

    /**
     * @param $policyId
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * Fetching user employer info through policy id
     * Fetching plan policy ids and checking rq_employer from plan pricing display table
     * If check column true returning user employer data
     */
    public function getEmployerInfo($policyId)
    {
        $policy = Policy::find($policyId);
        $failedMessage = 'Employer data not found.';
        $userId = $policy->getMember->userid;
        if (!$policy) return $this->failedResponse('Policy not found.');
        $planPricingIds = $this->planAgeService->getPlanPricingIdsFromPlanOverview($policyId);
        $check = $this->planAgeService->checkColumn('rq_employer', $planPricingIds);
        if (!$check) return $this->failedResponse($failedMessage);
        $data = UserEmployer::query()->where('userid', $userId)->first();
        if (!$data) return $this->failedResponse($failedMessage);
        $formattedData = $this->employerFormattedData($data, $policyId, $planPricingIds);

        return $this->successResponse('Employer data fetched.', $formattedData);
    }

    public function employerFormattedData($data, $policyId, $planPricingIds)
    {
        /**
         * @var $userInfo UserInfo
         */
        $userInfo = $data->userinfo ?: null;
        $empStatus = $this->planAgeService->checkColumn('emp_status', $planPricingIds) ? $data->status : null;
        $empStatusName = $empStatus ? array_search($empStatus, UserEmployer::$statuses) : null;
        $occupation = $this->planAgeService->checkColumn('rq_occupation', $planPricingIds) ? $data->occupation : null;
        $startDate = $this->planAgeService->checkColumn('rq_employ_start_date', $planPricingIds) ? Carbon::parse($data->start_date)->format('m/d/Y') : null;
        $empHours = $this->planAgeService->checkColumn('emp_hours', $planPricingIds) ? $data->hours : null;
        $empHoursName = $empHours ? array_search($empHours, UserEmployer::$workHours) : null;
        $compensation = $data->compensation ? array_search($data->compensation, UserEmployer::$compensationTypes) : null;
        $checkNAWUPlan = $this->planAgeService->checkNAWUPlan($policyId);

        return [
            "id" => $data->id,
            "name" => $data->name,
            "phone" => $data->phone,
            "address1" => $data->address1,
            "address2" => $data->address2,
            "occupation" => $occupation,
            "city" => $data->city,
            "state" => $data->state,
            "zip" => $data->zip,
            "compensation" => $data->compensation,
            "status" => $empStatus,
            "userid" => $data->userid,
            "isprimary" => $data->ispriamary,
            "status_name" => $empStatusName,
            "start_date" => $startDate,
            "hours" => $empHours,
            "hours_name" => $empHoursName,
            "compensation_name" => $compensation,
            "emp_industry" => $checkNAWUPlan && $userInfo ? $userInfo->emp_industry : null,
            "emp_num_employed" => $checkNAWUPlan && $userInfo ? $userInfo->emp_num_employed : null,
            "emp_vac_policy" => $checkNAWUPlan && $userInfo ? $userInfo->emp_vac_policy : null,
            "emp_barg_unit" => $checkNAWUPlan && $userInfo ? $userInfo->emp_barg_unit : null,
            "emp_vac_policy_other" => $checkNAWUPlan && $userInfo ? $this->getHolidayPolicies($userInfo->emp_vac_policy_other) : null,
        ];
    }

    public function getEmployerInfoOptions()
    {
        $statuses = UserEmployer::$statuses;
        $workHours = UserEmployer::$workHours;
        $compensation = UserEmployer::$compensationTypes;
        $groupIndustries = GroupIndustry::query()
            ->where('status', true)
            ->orderBy('title')
            ->pluck('title')
            ->toArray();
        $data = [
            'statuses' => $this->keyValueOptions($statuses),
            'workHours' => $this->valueOptions($workHours),
            'compensationTypes' => $this->keyValueOptions($compensation),
            'bargUnits' => $this->keyValueOptions(UserInfo::$bargUnits),
            'vacPolicies' => $this->valueOptions(UserInfo::$vacPolicies),
            'empIndustries' => $this->valueOptions($groupIndustries)
        ];
        return $this->successResponse('Options Fetched.', $data);
    }

    protected function valueOptions($options)
    {
        $data = [];
        foreach ($options as $v) {
            $data[] = [
                'name' => $v,
                'value' => $v
            ];
        }
        return $data;
    }

    protected function keyValueOptions($options)
    {
        $data = [];
        foreach ($options as $k => $v) {
            $data[] = [
                'name' => $k,
                'value' => $v
            ];
        }
        return $data;
    }

    /**
     * @param $val
     * @return array
     * Returning holiday policy with value 1,0
     * If in emp_vac_policy have json like text decoding and converting to array else val policy other will be null
     * Here checking if array value of vac policy matches to constant holiday policies returning its value else returning 0
     */
    protected function getHolidayPolicies($val)
    {
        $vacPolicyOther = json_decode($val, true);
        $holidayPolicies = UserInfo::$holidayPolicies;
        $result = [];
        foreach ($holidayPolicies as $h) {
            $result[$h] = isset($vacPolicyOther) && in_array($h, $vacPolicyOther) ? $vacPolicyOther[$h] : 0;
        }
        return $this->keyValueOptions($result);
    }

    /**
     * @param $request
     * @param $userId
     * @return array
     * Employer data to store in user_employers
     */
    protected function setUserEmployerdata($request, $userId)
    {
        $planPricingIds = $this->planAgeService->getPlanPricingIdsFromPlanOverview($request['policy_id']);
        $checkEmpStatus = $this->planAgeService->checkColumn('emp_status', $planPricingIds);
        $checkOccupation = $this->planAgeService->checkColumn('rq_occupation', $planPricingIds);
        $checkStartDate = $this->planAgeService->checkColumn('rq_employ_start_date', $planPricingIds);
        $checkAverageWorkHours = $this->planAgeService->checkColumn('emp_hours', $planPricingIds);
        return [
            'name' => $request['name'],
            'phone' => $request['phone'],
            'address1' => $request['address1'],
            'address2' => $request['address2'] ?: null,
            'city' => $request['city'],
            'zip' => $request['zip'],
            'state' => $request['state'],
            'compensation' => $request['compensation'] ?: null,
            'status' => $checkEmpStatus && $request['status'] ? $request['status'] : null,
            'occupation' => $checkOccupation && $request['occupation'] ? $request['occupation'] : null,
            'start_date' => $checkStartDate && $request['start_date'] ? Carbon::parse($request['start_date'])->format('Y-m-d') : null,
            "hours" => $checkAverageWorkHours && $request['hours'] ? $request['hours'] : null,
            "isprimary" => null,
            "userid" => $userId,
        ];
    }

    /**
     * @param $request
     * @return array
     * user data to store in userinfo
     */
    protected function setUserInfoData($request)
    {
        $holidayPolicies = json_encode($request['emp_vac_policy_other']);
        return [
            "emp_industry" => $request['emp_industry'] ?: null,
            "emp_num_employed" => $request['emp_num_employed'] ?: null,
            "emp_vac_policy" => $request['emp_vac_policy'] ?: null,
            "emp_barg_unit" => $request['emp_barg_unit'] ?: null,
            "emp_vac_policy_other" => $holidayPolicies ?: null,
        ];
    }

    /**
     * @param $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * Update user employer
     * checking address validation
     * update user employer
     * if nawuplan type updating in userinfo
     * adding logs in policies_updates
     * sending email
     */
    public function updateEmployerInfo($request)
    {
        if ($request['with_validation'] == 1) {
            $validationService = new CustomValidationService();
            $validationService->validateAddressUSPS(
                $request['address1'],
                $request['address2'],
                $request['city'],
                $request['state'],
                $request['zip']
            );
        }
        $policy = Policy::find($request['policy_id']);
        $userId = $policy->getMember->userid;
        $data = $this->setUserEmployerdata($request, $userId);
        $userInfoData = $this->setUserInfoData($request);
        $checkNAWUPlan = $this->planAgeService->checkNAWUPlan($request['policy_id']);

        /**
         * @var $userEmployer UserEmployer
         */
        $userEmployer = UserEmployer::find($request['id']);
        /**
         * @var $userInfo UserInfo
         */
        $userInfo = $userEmployer->userInfo;

        DB::beginTransaction();
        try {
            //update user employer
            $userEmployer->update($data);

            //update userInfo
            if ($checkNAWUPlan) {
                $userInfo->update($userInfoData);
            }

            $successMessage = "Employer information updated.";

            //update in policy updates
            $this->updatePolicyLog($policy, 'PIC', $successMessage, $request['login_user_id']);

            //send email
            SendEmailMemberHelper::sendUserEmployerEmail($policy);

            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->failedResponse($th->getMessage());
        }
    }

    /**
     * @param $request
     * @param $userId
     * @return array
     * validated data to store in userinfo
     * checking height,weight form plan age service if true setting data else setting to empty
     */
    protected function updatePersonalInfoData($request, $userId)
    {
        $encryptedSSN = trim(DecryptEcryptHelper::encryptInfo($request['cssn']));
        $ssnCheck = $this->customSsnValidation($encryptedSSN, $userId);
        if ($ssnCheck) {
            $errors['cssn'] = ['SSN already exists.Same SSN is associated with another user.'];
            $this->failedValidation($errors);
        }
        $planService = $this->planAgeService;
        $planPricingIds = $planService->getPlanPricingIdsFromPlanOverview($request['policy_id']);
        $checkHeight = $planService->checkColumn('heightrq', $planPricingIds);
        $checkHeight2 = $planService->checkColumn('heightrq2', $planPricingIds);
        $checkWeight = $planService->checkColumn('weightrq', $planPricingIds);
        return [
            'cfname' => $request['cfname'] ?: "",
            'clname' => $request['clname'] ?: "",
            'cmname' => $request['cmname'] ?: "",
            'cgender' => isset($request['cgender']) ? (int)$request['cgender'] : null,
            'cssn' => $encryptedSSN,
            'cssn4' => substr($request['cssn'], -4, 4),
            'cdob' => $request['cdob'] ?: "",
            "height_feet" => $checkHeight || $checkHeight2 ? $request['height_feet'] : "",
            "height_inches" => $checkHeight2 || $checkHeight ? $request['height_inches'] : "",
            "weight_lbs" => $checkWeight ? $request['weight_lbs'] : "",
        ];
    }

    public function failedValidation($errors)
    {
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }

    public function customSsnValidation($encryptedSSN, $userId)
    {
        return UserInfoPolicyAddress::query()
            ->where([
                ['cssn', '=', $encryptedSSN],
                ['userid', '!=', $userId],
                ['status', '=', "status"],
            ])
            ->where('cfname', 'NOT LIKE', "%test%")
            ->exists();
    }

    /**
     * @param $request
     * @return array|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * update client information
     * update request data in user info
     * adding logs in policies_updates
     * sending email
     * @todo check and update height weight
     */
    public function updatePersonalInfo($request)
    {
        $policy = $this->model::find($request['policy_id']);
        /**
         * @var $userInfo UserInfo
         */
        $userInfo = $policy->getMember;
        if (!$userInfo) return $this->failedResponse('Policy User not found.');
        DB::beginTransaction();
        try {
            $updateData = $this->updatePersonalInfoData($request, $userInfo->userid);
            $userInfo->update($updateData);

            $successMessage = "User information updated.";
            $picReason = isset($request['pic_reason']) ? $request['pic_reason'] : $successMessage;

            //update in policy updates
            $this->updatePolicyLog($policy, 'PIC', $picReason, $request['login_user_id']);

            //send email
            if ($request['send_email'] == 1) {
                $this->messageService->sendUserUpdateEmail($userInfo);
            }
            DB::commit();
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            $failedMessage = "Failed to update";
            return $this->failedResponse($failedMessage);
        }
    }

    /**
     * @param $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * update email data in user info
     * update email in sso
     * adding logs in policies_updates
     * sending email
     * currently using previous version of code
     * @todo refactor to custom user email validation
     * adding valid value in is_email_valid field since its already validated through neverbounce validation
     * update in userlogin
     */
    public function updateEmailInfo($request)
    {
        $policy = $this->model::find($request['policy_id']);
        /**
         * @var $userInfo UserInfo
         */
        $userInfo = $policy->getMember;
        if (!$userInfo) return $this->failedResponse('Policy User not found.');

        if ($request['cemail'] == $userInfo->cemail) return $this->failedResponse("Cannot update same email", 409);
        $userId = $userInfo->userid;
        $emailCheck = UserLogin::query()->whereIn('userid', UserInfoPolicyAddress::query()->where('status', 'ACTIVE')->pluck('userid')->toArray())
            ->where('userid', '!=', $userId)
            ->where('username', $request['cemail'])
            ->exists();
        if ($emailCheck) {
            $errors['cemail'] = ['Email already exists.Same Email is associated with another user.'];
            $this->failedValidation($errors);
        }
        $userLogin = UserLogin::query()
            ->where('userid', $userId)
            ->first();
        $oldEmail = isset($userLogin->username) ? $userLogin->username : $userInfo->cemail;
        $oldUserInfo = UserLogin::find($userId);

        DB::beginTransaction();
        try {
            $userInfo->update([
                'cemail' => $request['cemail'],
                'is_email_valid' => UserInfo::VALID
            ]);

            $successMessage = "User email information updated.";
            //update in policy updates
            $this->updatePolicyLog($policy, 'PIC', $successMessage, $request['login_user_id']);

            if (isset($userLogin)) {
                $userLogin->update([
                    'username' => $request['cemail'],
                    'is_email_valid' => UserInfo::VALID
                ]);
            }

            //send email
            $this->messageService->sendUserInfoEmailEmail($userInfo, $oldEmail);
            DB::commit();
            if ($userLogin instanceof UserLogin) {
                $syncMemberResponse = $this->syncMemberSSO($oldUserInfo, $request['cemail']);
                if (strpos($syncMemberResponse, "Error") !== false) {
                    return $this->failedResponse($syncMemberResponse);
                }
            }
            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            $failedMessage = "Failed to update";
            return $this->failedResponse($failedMessage);
        }

    }

    // update member alternative email
    public function updateAltEmailInfo($request)
    {
        $policy = $this->model::find($request['policy_id']);
        /**
         * @var $userInfo UserInfo
         */
        $userInfo = $policy->getMember;
        if (!$userInfo) return $this->failedResponse('Policy User not found.');

        if ($request['cemail_alt'] != null && $request['cemail_alt'] == $userInfo->cemail_alt) return $this->failedResponse("Cannot update same email", 409);
        try {
            $cemail_alt_check = $userInfo->cemail_alt;
            $userInfo->update([
                'cemail_alt' => $request['cemail_alt'],
                'is_email_valid' => UserInfo::VALID
            ]);
            if ($cemail_alt_check != '') {
                $successMessage = "User alternative email information Updated.";
            } else {
                $successMessage = "User alternative email information Added.";
            }

            //update in policy updates
            $this->updatePolicyLog($policy, 'PIC', $successMessage, $request['login_user_id']);

            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            $failedMessage = "Failed to update";
            return $this->failedResponse($failedMessage);
        }
    }

    /**
     * @param $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * update phone info  data in user info
     * adding logs in policies_updates
     * sending email
     * currently using previous version of code
     * @todo refactor to custom validation for phone
     * adding valid value related phone valid field since its already validated through num verify validation
     * sync phones to sso
     */
    public function updatePhoneInfo($request)
    {
        $policy = $this->model::find($request['policy_id']);
        /**
         * @var $userInfo UserInfo
         */
        $userInfo = $policy->getMember;
        if (!$userInfo) return $this->failedResponse('Policy User not found.');

        $userId = $userInfo->userid;
        if (UserInfoPolicyAddress::query()->where('phone1', $request['phone1'])
            ->where('status', 'ACTIVE')
            ->where('userid', '!=', $userId)->exists()) {
            $errors['phone1'] = ['Phone number already exists.Same Phone Number is associated with another user.'];
            $this->failedValidation($errors);
        }
        $oldPhones = [
            'phone1' => $userInfo->phone1,
            'phone2' => $userInfo->phone2
        ];
        $newPhones = [
            'phone1' => $request['phone1'],
            'phone2' => $request['phone2'] ?: "N/A"
        ];
        $oldUserInfo = UserLogin::find($userId);

        DB::beginTransaction();
        try {

            $userInfo->update([
                'phone1' => $request['phone1'],
                'phone2' => isset($request['phone2']) ? $request['phone2'] : null,
                'is_phone1_valid' => UserInfo::VALID,
                'is_phone2_valid' => isset($request['phone2']) ? UserInfo::VALID : null
            ]);

            $successMessage = "User contact information updated.";
            //update in policy updates
            $this->updatePolicyLog($policy, 'PIC', $successMessage, $request['login_user_id']);

            //send email
            $this->messageService->sendUserInfoPhoneEmail($userInfo, $oldPhones, $newPhones);
            DB::commit();
            //sync sso
            if ($oldUserInfo instanceof UserLogin) {
                $syncMemberResponse = $this->syncMemberSSO($oldUserInfo, $userInfo->cemail);
                if (strpos($syncMemberResponse, "Error") !== false) {
                    return $this->failedResponse($syncMemberResponse);
                }
            }

            return $this->successResponse($successMessage);
        } catch (\Throwable $th) {
            DB::rollBack();
            $failedMessage = "Failed to update";
            return $this->failedResponse($failedMessage);
        }
    }

    public function getEftPaymentDetail($paymentId)
    {
        $data = PaymentEft::find($paymentId);
        if (!$data) return $this->failedResponse("Payment Detail not found.");
        /**
         * @todo use response trait - PolicyResponse Trait
         */
        $result = [
            'id' => $data->bank_id,
            'name' => $data->bank_name,
            'account_name' => $data->bank_accountname,
            'routing' => $data->bank_routing,
            'account' => $data->bank_account,
            'unmasked_account' => $data->bank_account ? DecryptEcryptHelper::decryptInfo($data->bank_account) : null,
            'date' => $data->bank_date ? $this->formatTimestampUnix($data->bank_date) : null,
            'user_id' => $data->bank_userid,
            'account_type' => $data->account_type,
            'account_holder_type' => $data->account_holder_type,
            'status' => $data->bank_status ?: null,
            'branch_loc' => $data->bankbranchloc ?: null,
            'is_approved' => $data->is_approved ?: null,
            'is_action_taken' => $data->is_action_taken ?: null,
            'reason_to_reject' => $data->reason_to_reject ?: null,
            'request_type' => $data->request_type ?: null,
            'reason_to_accept' => $data->reason_to_accept ?: null,
            'rejected_date' => $data->rejected_date ? $this->formatDate($data->rejected_date) : null,
            'sign' => $data->sign ?: null,
            'is_primary' => $data->is_primary ?: null,
            'bank_sign' => $data->bank_sign ?: null,
            'funding_source_id' => $data->funding_source_id ?: null,
            'customer_id' => $data->customer_id ?: null,
            'is_verified' => $data->is_verified ?: null,
            'unique_identification_number' => $data->unique_identification_number ?: null,
            'created_at' => $data->created_at ? $this->formatDate($data->created_at) : null,
            'updated_at' => $data->updated_at ? $this->formatDate($data->updated_at) : null,
        ];
        return $this->successResponse('Payment Detail Fetched.', $result);
    }

    public function getCCPaymentDetail($paymentId)
    {
        $data = PaymentTypeCc::find($paymentId);
        if (!$data) return $this->failedResponse("Credit Card Detail not found.");
        /**
         * @todo use response trait - PolicyResponse Trait
         */
        $result = [
            'id' => $data->cc_id,
            'type' => $data->cc_type,
            'number' => $data->cc_num,
            'unmasked_number' => $data->cc_num ? DecryptEcryptHelper::decryptInfo($data->cc_num) : null,
            'number4' => $data->cc_num4,
            'expiry_month' => $data->cc_expmm ?: null,
            'expiry_year' => $data->cc_expyyyy ?: null,
            'date' => $data->cc_date ? $this->formatTimestampUnix($data->cc_date) : null,
            'status' => $data->cc_status ?: null,
            'user_id' => $data->cc_userid,
            'address_id' => $data->cc_addressid,
            'contact_first_name' => $data->cc_contactfname ?: null,
            'contact_last_name' => $data->cc_contactlname ?: null,
            'cvc' => $data->cc_cvc ?: null,
            'payer_id' => $data->payer_id ?: null,
            'card_id' => $data->card_id ?: null,
            'created_at' => $data->created_at ? $this->formatDate($data->created_at) : null,
            'updated_at' => $data->updated_at ? $this->formatDate($data->updated_at) : null,
        ];
        return $this->successResponse('Credit Card Detail Fetched.', $result);
    }

    public function getMonthStartingDates($startDate, $endDate)
    {
        $start = Carbon::createFromFormat('Y-m-d', $startDate)->startOfMonth();
        $end = Carbon::createFromFormat('Y-m-d', $endDate)->startOfMonth();

        $monthStartingDates = [];

        while ($start->lte($end)) {
            $monthStartingDates[] = $start->toDateString();
            $start->addMonth();
        }

        $monthStartingDates = array_slice($monthStartingDates, 0, -1);

        return $monthStartingDates;
    }

    public function updateNBInvoice($data, $newEffectiveDate) {
        $nbInvoices = NbInvoice::where('invoice_policy_id', $data->policyID)->get();

        $repeated_nbInvoices =NbInvoice::select('invoice_date')
                            ->where('invoice_policy_id', $data->policyID)
                            ->groupBy('invoice_date')
                            ->havingRaw('COUNT(*) > 1')
                            ->pluck('invoice_date');
        $chubb_nbInvoices = NbInvoice::whereIn('invoice_date', $repeated_nbInvoices)
                        ->where('invoice_policy_id', $data->policyID)
                        ->where('payment_type','reccuring')
                        ->pluck('invoice_id');
        $n = 1;
        $y = 0;
        if($nbInvoices) {
            foreach($nbInvoices as $key => $nb_invoice) {
                if($nb_invoice->invoice_payment_status != "UNPAID" || ($nb_invoice->invoice_payment_status == "UNPAID" && $nb_invoice->payment_party_status == "PROCESSING") || ($nb_invoice->invoice_payment_status == "UNPAID" && self::checkIfPaymentExists($nb_invoice->invoice_id))) {
                    if($nb_invoice->payment_type == "first") {
                        // Nb Invoice Update
                        Log::info("Update of Nb Invoice of invoice_id \t" . $nb_invoice->invoice_id . "\t Started.");
                        $newEffectiveTimestamp = strtotime($newEffectiveDate);
                        $modifiedInvoiceDate = date("Y-m-01", $newEffectiveTimestamp);
                        $modifiedInvoiceDateForDueDate = Carbon::parse($modifiedInvoiceDate);
                        $nbInvoiceDueDate = Carbon::parse($nb_invoice->invoice_due_date);
                        $nbInvoiceProcessingDate = Carbon::parse($nb_invoice->processing_date);
                        $newInvoiceDueDate = self::formatDateForInvoice($nbInvoiceDueDate, $modifiedInvoiceDateForDueDate);
                        $newProcessingDate = self::formatDateForInvoice($nbInvoiceProcessingDate, $modifiedInvoiceDateForDueDate);

                        $lastDayOfMonth =Carbon::parse($modifiedInvoiceDate)->endOfMonth()->toDateString();
                        $nb_invoice->invoice_date = date("Y-m-d", strtotime("-1 month", strtotime($modifiedInvoiceDate)));
                        $nb_invoice->invoice_due_date = date("Y-m-d", strtotime("-1 month", strtotime($newInvoiceDueDate)));
                        $nb_invoice->processing_date = date("Y-m-d", strtotime("-1 month", strtotime($newProcessingDate)));

                        $nb_invoice->invoice_start_date = $modifiedInvoiceDate;
                        if($nb_invoice->pay_type == "annual") {
                            $lastDayOfMonth = Carbon::parse($lastDayOfMonth)->addMonths(12);
                            $lastDayOfMonth =  date("Y-m-t", strtotime("-31 days", strtotime($lastDayOfMonth)));
                        }
                        $nb_invoice->invoice_end_date = $lastDayOfMonth;
                        $nb_invoice->save();
                        Log::info("Update of Invoice \t" . $nb_invoice->invoice_id . "\t PDF Started.");
                        $url = config('app.purenroll_system.url') . "generateInvoicePDF";
                        $payload = [
                            'invoice_id' => $nb_invoice->invoice_id,
                        ];
                        $responseJson = GuzzleHelper::postApi($url, [], $payload);
                        $response = json_decode($responseJson, true);
                        if($response['status'] == "success") {
                            Log::info("Updated Invoice for \t" . $nb_invoice->invoice_id . "\t PDF generated successfully.");
                        } else {
                            Log::info("There was error while generating updated Invoice PDF for \t" . $nb_invoice->invoice_id . "\t.");
                        }
                        Log::info("Update of Nb Invoice of invoice_id \t" . $nb_invoice->invoice_id . "\t Completed.");


                        // Nb Invoice Item Update
                        Log::info("Update of Nb Invoice Item of invoice_id \t" . $nb_invoice->invoice_id . "\t and policy_id \t" . $data->policyID. "\t Started.");
                        $nb_invoice_items = NbInvoiceItem::where('policy_id',$data->policyID)->where('invoice_id', $nb_invoice->invoice_id)->update(array('start_date' => $nb_invoice->invoice_start_date,'end_date' => $nb_invoice->invoice_end_date,'eff_date' => $newEffectiveDate));
                        Log::info("Update of Nb Invoice Item of invoice_id \t" . $nb_invoice->invoice_id . "\t and policy_id \t" . $data->policyID. "\t Completed.");

                        // Nb Payment update
                        $nb_payment = NbPayment::where('payment_policyid',$data->policyID)->where('invoice_id', $nb_invoice->invoice_id)->first();
                        if($nb_payment) {
                            Log::info("Update of Nb Payment of payment_id \t" . $nb_payment->payment_id . "\t Started.");
                            $invoice_end_date = new DateTime($nb_invoice->invoice_end_date);
                            $nb_payment->payment_year = $invoice_end_date->format("Y");
                            $nb_payment->payment_month = $invoice_end_date->format("m");
                            $nb_payment->payment_paid_through_date = $nb_invoice->invoice_end_date;
                            $nb_payment->save();
                            Log::info("Update of Nb Payment of payment_id \t" . $nb_payment->payment_id . "\t Completed.");
                        }
                    } else {

                        // Nb Invoice Update(Recurring)
                        $nb_invoice_invoice_id = $nb_invoice->invoice_id;
                        if($chubb_nbInvoices->contains($nb_invoice_invoice_id)) {
                            $index = $chubb_nbInvoices->search($nb_invoice_invoice_id);
                            if ($index !== false && $index != 0) {
                                    if ($index % 2 == 0) {
                                    $n = $n;
                                    $y = $y;
                                    } else {
                                    $n = $n - 1;
                                    $y = $y - 1;
                                    }
                            }
                        }
                        Log::info("Update of Nb Invoice of invoice_id \t" . $nb_invoice->invoice_id . "\t Started.");
                        $firstInvoice = NbInvoice::where('invoice_policy_id', $data->policyID)->where('payment_type','first')->first();
                        if($key > 1) {
                            $firstInvoice = NbInvoice::where('invoice_policy_id', $data->policyID)->where('payment_type','reccuring')->where('invoice_id', '<', $nb_invoice->invoice_id)->orderBy('invoice_id', 'DESC')->first();
                        }
                        $newEffectiveTimestamp = strtotime($firstInvoice->invoice_end_date);
                        $modifiedInvoiceDate = date("Y-m-01", $newEffectiveTimestamp);
                        $modifiedInvoiceDateForDueDate = Carbon::parse($modifiedInvoiceDate);
                        $nbInvoiceDueDate = Carbon::parse($nb_invoice->invoice_due_date);
                        $nbInvoiceProcessingDate = Carbon::parse($nb_invoice->processing_date);
                        $newInvoiceDueDate = self::formatDateForInvoice($nbInvoiceDueDate, $modifiedInvoiceDateForDueDate);
                        $newProcessingDate = self::formatDateForInvoice($nbInvoiceProcessingDate, $modifiedInvoiceDateForDueDate);
                        $nb_invoice->invoice_date = date("Y-m-01", strtotime("0 month", strtotime($newInvoiceDueDate)));
                        $nb_invoice->invoice_due_date = date("Y-m-d", strtotime("0 month", strtotime($newInvoiceDueDate)));
                        $nb_invoice->processing_date = date("Y-m-d", strtotime("0 month", strtotime($newProcessingDate)));
                        $nb_invoice->invoice_start_date = date("Y-m-01", strtotime("+1 month", strtotime($newInvoiceDueDate)));
                        if($nb_invoice->pay_type == "annual"){
                            $invoice_end_date = Carbon::create($firstInvoice->invoice_end_date);
                            $nb_invoice->invoice_end_date = $invoice_end_date->copy()->addMonthsNoOverflow(12);
                        } else {
                            $lastDayOfMonth =Carbon::parse($nb_invoice->invoice_start_date)->endOfMonth()->toDateString();
                            $nb_invoice->invoice_end_date = $lastDayOfMonth;
                        }
                        $nb_invoice->save();
                        Log::info("Update of Invoice \t" . $nb_invoice->invoice_id . "\t PDF Started.");
                        $url = config('app.purenroll_system.url') . "generateInvoicePDF";
                        $payload = [
                            'invoice_id' => $nb_invoice->invoice_id,
                        ];
                        $responseJson = GuzzleHelper::postApi($url, [], $payload);
                        $response = json_decode($responseJson, true);
                        if($response['status'] == "success") {
                            Log::info("Updated Invoice for \t" . $nb_invoice->invoice_id . "\t PDF generated successfully.");
                        } else {
                            Log::info("There was error while generating updated Invoice PDF for \t" . $nb_invoice->invoice_id . "\t.");
                        }
                        Log::info("Update of Nb Invoice of invoice_id \t" . $nb_invoice->invoice_id . "\t Completed.");

                        // Nb Invoice Item Update
                        Log::info("Update of Nb Invoice Item of invoice_id \t" . $nb_invoice->invoice_id . "\t and policy_id \t" . $data->policyID. "\t Started.");
                        $nb_invoice_items = NbInvoiceItem::where('policy_id',$data->policyID)->where('invoice_id', $nb_invoice->invoice_id)->update(array('start_date' => $nb_invoice->invoice_start_date,'end_date' => $nb_invoice->invoice_end_date,'eff_date' => $newEffectiveDate));
                        Log::info("Update of Nb Invoice Item of invoice_id \t" . $nb_invoice->invoice_id . "\t and policy_id \t" . $data->policyID. "\t Completed.");


                        // Nb Payment update
                        $nb_payment = NbPayment::where('payment_policyid',$data->policyID)->where('invoice_id', $nb_invoice->invoice_id)->first();
                        if($nb_payment) {
                            Log::info("Update of Nb Payment of payment_id \t" . $nb_payment->payment_id . "\t Started.");
                            $invoice_end_date = new DateTime($nb_invoice->invoice_end_date);
                            $nb_payment->payment_year = $invoice_end_date->format("Y");
                            $nb_payment->payment_month = $invoice_end_date->format("m");
                            $nb_payment->payment_paid_through_date = $nb_invoice->invoice_end_date;
                            $nb_payment->save();
                            Log::info("Update of Nb Payment of payment_id \t" . $nb_payment->payment_id . "\t Completed.");

                        }
                    }
                }
                else {
                    $nb_invoice_item = NbInvoiceItem::where('policy_id',$data->policyID)->where('invoice_id', $nb_invoice->invoice_id)->delete();
                    $nb_invoice->delete();
                    Log::channel('invoiceDeletion')
                        ->info(PHP_EOL ."Unpaid Nb Invoices {$nb_invoice->invoice_id} deleted successfully.");
                    Log::info("Unpaid Nb Invoices and Nb Invoices item deleted successfully.");
                }

            }
        }

        $first_Effective_Date = NbInvoice::where('invoice_policy_id', $data->policyID)->where('payment_type','first')->pluck('invoice_start_date')->first();
        $last_Effective_Date = Carbon::now()->addMonth()->format('Y-m-01');
        $monthStartingDates = null;
        if(!$first_Effective_Date) {
            $first_Effective_Date = $newEffectiveDate;
        }
        // get all effective date from first invoice effective date to today's month
        if(strtotime($first_Effective_Date) != strtotime($last_Effective_Date)) {
            $monthStartingDates = self::getMonthStartingDates($first_Effective_Date, $last_Effective_Date);
        }
        // if there is no invoice or only one invoice of todays month then monthStartingDates = first_Effective_Date
        if(strtotime($first_Effective_Date) == strtotime($last_Effective_Date)) {
            $monthStartingDates[] = $first_Effective_Date;
        }
        $invoiceStartDates = NbInvoice::where('invoice_policy_id', $data->policyID)->pluck('invoice_start_date')->toArray();
        $monthStartingDates = array_filter($monthStartingDates, function ($date) use ($invoiceStartDates) {
            return !in_array($date, $invoiceStartDates);
        });
        $remaining_dates = null;
        if($monthStartingDates) {
            foreach($monthStartingDates as $dates) {
                $exists = NbInvoice::where('invoice_policy_id', $data->policyID)->whereDate('invoice_start_date', '<=', $dates)
                        ->whereDate('invoice_end_date', '>=', $dates)
                        ->first();
                if (!$exists) {
                    $remaining_dates[] = $dates;
                } else {
                    return false;
                }
            }
        }
        if($remaining_dates != null) {
            $client = new Client();
            Log::info("Commission api called for generating single invoice.");

            $url = config('app.purenroll_system.url') . "generate-single-invoice";
            $payload = [
                'policy_id' => $data->policyID,
                'effective_date' => $remaining_dates,
            ];
            $responseJson = GuzzleHelper::postApi($url, [], $payload);
            // $response = json_decode($responseJson, true);
            $pattern = '/\{.*?\}/';
            if (preg_match($pattern, $responseJson, $matches)) {
                // $matches[0] contains the matched string inside the curly braces
                $result_string = $matches[0];
                if($result_string) {
                    $response = json_decode($result_string, true);
                }

            }
            if($response && $response['status'] == "success") {
                Log::info("Invoice genereated Successfully.");
            } else {
                Log::info("There was error while generating invoice.");
            }
            return true;
        } else {
            return false;
        }

    }

    public function formatDateForInvoice($nbInvoiceDate, $modifiedNewDate)
    {
        $newInvoiceDate = $nbInvoiceDate->copy()
                            ->setYear($modifiedNewDate->year)
                            ->setMonth($modifiedNewDate->month);
        return $newInvoiceDate;
    }

    public static function checkIfPaymentExists($invoice_id)
    {
        return NbPayment::where('invoice_id', $invoice_id)
            ->count() > 0;
    }

   public function getDependentNames($limit, $name){
        $names = DependentInPolicy::where(function ($query) use ($name) {
            CommonHelper::filterDependentsByName($query, $name);
        })
        ->selectRaw("CONCAT(d_fname, ' ', d_lname) as name")
        ->orderBy('name', 'asc')
        ->take($limit)
        ->distinct()
        ->pluck('name');
        return $names;
    }

    private function shouldSkipAgeCheck($policyId): bool
    {
        return PlanOverview::where('policy_id', $policyId)
            ->where(function($query) {
                $query->whereIn('pl_type', ['MM', 'DENT', 'VISION'])
                    ->orWhere('cid', 80);
            })
            ->exists();
    }
}
