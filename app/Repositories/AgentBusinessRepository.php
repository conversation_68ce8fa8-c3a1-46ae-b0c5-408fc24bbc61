<?php

namespace App\Repositories;

use App\AgentBusiness;
use App\AgentInfo;
use App\Helpers\AgentUpdatesHelper;
use App\Traits\ResponseMessage;
use Carbon\Carbon;
use App\Service\MessageService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;

class AgentBusinessRepository
{
    use ResponseMessage;
    private $messageService;
    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }
    public function add(Request $request){
        try {
            if(AgentInfo::find($request->business_agent_id)) {
                $agentBusiness = new AgentBusiness();
                $agentBusiness->business_agent_id = $request->business_agent_id;
                $agentBusiness->business_name = $request->business_name;
                $agentBusiness->business_tax = $request->business_tax;
//                $agentBusiness->business_address1 = $request->business_address1;
//                $agentBusiness->business_address2 = $request->business_address2;
//                $agentBusiness->business_city = $request->business_city;
//                $agentBusiness->business_state = $request->business_state;
//                $agentBusiness->business_zip = $request->business_zip;
//                $agentBusiness->biz_logo = $request->biz_logo;
//                $agentBusiness->cover_img = $request->cover_img;
                $agentBusiness->save();

                //send email
                $this->sendEmail($request->business_agent_id,$agentBusiness->business_name,$agentBusiness->business_tax,"Agent business info Added");

                return $this->successResponse(" Agent Business successfully added!");
            }
            return $this->successResponse(" Agent not found");
        }catch (\Exception $e) {
            return $this->failedResponse($e);
        }
    }
    public function update(Request $request){
        DB::beginTransaction();
        try {
            $agentBusiness = AgentBusiness::where('business_agent_id',$request->business_agent_id)->first();
            if(count($request->file())){
                foreach ($request->file() as $key => $value) {
                    $img = $this->editBusinessImages($request->file()[$key], $agentBusiness[$key]);
                    if($img){
                        AgentBusiness::where('business_agent_id',$request->business_agent_id)->update([
                            $key => $img
                        ]);
                    }
                }
            }
            $agentBusiness->business_name = $request->business_name;
            $agentBusiness->business_tax = $request->business_tax;
//            $agentBusiness->business_address1 = $request->business_address1;
//            $agentBusiness->business_address2 = $request->business_address2;
//            $agentBusiness->business_city = $request->business_city;
//            $agentBusiness->business_state = $request->business_state;
//            $agentBusiness->business_zip = $request->business_zip;
//            $agentBusiness->biz_logo = $request->biz_logo;
//            $agentBusiness->cover_img = $request->cover_img;

            if($agentBusiness->save()){
                $data = [
                    "agent_id" => $request->business_agent_id,
                    "elgb_act" => \App\AgentUpdate::ACT_BUSINESS_INFO,
                    "elgb_comment" => "Agent business info changed",
                    "elgb_act_date" => time(),
                    "elgb_agent_id" => isset($request['loginUserId']) ? $request['loginUserId'] : null,
                    "elgb_agent_name" => isset($request['loginUserName']) ? $request['loginUserName'] : null
                ];
                AgentUpdatesHelper::createAgentUpdateLog($data);
                $this->sendEmail($request->business_agent_id,$agentBusiness->business_name,$agentBusiness->business_tax,"Agent business info Updated");
                DB::commit();
                return $this->successResponse( " Agent Business successfully Updated!");
            }


        }catch (\Exception $e) {
            DB::rollBack();
            return $this->failedResponse($e);
        }
    }

    public function editBusinessImages($image, $previousFileName)
    {
        if ($image instanceof UploadedFile) {
            $fileName = pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME);
            $ext = $image->getClientOriginalExtension();
            $time = time();
            $imageName = "{$fileName}-{$time}.{$ext}";
            $imageName = str_replace(" ","_",$imageName);

            //checking and deleting previous file.
            if ($previousFileName) {
                $this->removeAgentImage($previousFileName);
            }
            //uploading new file
            $s3FilePath = "/agent/logo/{$imageName}";
            Storage::disk('corenroll')->put('/img/agents/' . $imageName, file_get_contents($image));
            Storage::disk('s3-third')->put($s3FilePath, file_get_contents($image));
        }
        return $imageName;

    }

    protected function removeAgentImage($imageName)
    {
        try{
            $previousFileNamePath = "/img/agents/{$imageName}";
            if (Storage::disk('corenroll')->exists($previousFileNamePath)) {
                Storage::disk('corenroll')->delete($previousFileNamePath);
            }
            $s3PreviousFileNamePath = "/agent/logo/{$imageName}";
            if (Storage::disk('s3-third')->exists($s3PreviousFileNamePath)) {
                Storage::disk('s3-third')->delete($s3PreviousFileNamePath);
            }

    }catch (\Exception $e) {
        return $this->failedResponse($e->getMessage());
    }

    }
    public function list() {
        try {
            $data = AgentBusiness::get(['business_agent_id', 'business_name','business_tax']);
            return $this->successResponse($data);
        }catch (\Exception $e) {
            return $this->failedResponse($e);
        }
    }

    public function agentDetail($id)
    {
        try {
            $data = AgentBusiness::where('business_agent_id', $id)->first();
            $data['biz_logo'] = isset($data['biz_logo']) ? self::addImageUrl($data['biz_logo']) : null;
            $data['cover_img'] = isset($data['cover_img']) ? self::addImageUrl($data['cover_img']) : null;

            return $this->successResponse("Success", $data);
        } catch (\Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }

    public function addImageUrl($imageName){
        $s3PreviousFileNamePath = "/agent/logo/{$imageName}";
        if (Storage::disk('s3-third')->exists($s3PreviousFileNamePath)) {
            return config('filesystems.disks.s3-third.url').$s3PreviousFileNamePath;
        }
    }

    protected function sendEmail($agent_id,$business_name, $business_taxid, $subject)
    {
        $agentInfo = AgentInfo::find($agent_id);
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
//        $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your business info has been updated on " . $date . ".";
        $contentData = [
            'Agent Id' => $agentInfo->agent_id,
            'Business Name' =>  $business_name,
            'Business Tax ID' => 'XXXXX-'. substr($business_taxid,-4),
        ];
        $emailConfigurationName = "AGENT_INFO_CHANGE";

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
        ];
        return $this->messageService->sendEmailWithContentData($emailData);
    }

}
