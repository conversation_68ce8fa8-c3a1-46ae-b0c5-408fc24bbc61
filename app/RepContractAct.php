<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class RepContractAct extends Model
{
    protected $table = 'rep_contract_act';
    protected $primaryKey = 'rcid';
    protected $guarded = [];

    const ancillary_contract_levels = [1,2,3,4,5,6,7];
    const premier_contract_levels = [1,2,3,4,5,6,7];
    const bex_contract_levels = [1,2,3,4,5];
    const fegli_contract_levels = [1,2,3,4,5,6,7];
    const patriot_contract_levels = [1,2,3,4,5,6,7];
    const category_contract_levels = [1,2,3,4,5,6,7,8,9];
}
