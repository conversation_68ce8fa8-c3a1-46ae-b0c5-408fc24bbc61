<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AssocFee extends Model
{
    protected $table = 'new_assoc_fee';
    protected $primaryKey = 'a_id';

    protected $guarded = ['a_id'];

    public function plan()
    {
        return $this->belongsTo(Plan::class, 'pid');
    }

    public function assocPlan()
    {
        return $this->belongsTo(Plan::class, 'a_pid');
    }

    public function getPlanPriceAttribute()
    {
        return PlanPricingDisplay::query()
            ->where([
                'pid' => $this->attributes['a_pid'],
                'plan_pricing_id' => $this->attributes['a_ppid']
            ])
            ->pluck('price_male_nons')
            ->first();
    }
}
