<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GroupUpdate extends Model
{
    protected $table = 'group_updates';
    protected $primaryKey = 'elgb_id';
    protected $guarded = ['elgb_id'];

    const ACT_IMAGE = "PICHG";
    const ACT_WAIVEFEE = "WAIVEFEE";
    const ACT_ADDRESS_INFO = "ADCHG";
    const ACT_GROUP_AGENT = "GACHG";
    const ACT_GROUP_FILE = "GFCHG";
    const ACT_GROUP_FILE_DELETE = "GFDCHG";
    const ACT_WEB_DISPLAY = "WEBHG";
    const ACT_INFO= "INFOHG";
    const BILLING_ACT_INFO= "BILCHG";
    CONST GROUP_NOTE_UPDATE = 'GNCHG';
    CONST GROUP_NOTE_DELETE = 'GNDLT';
    CONST SUB_GROUP_DELETE = 'SGDLT';
    CONST SUB_GROUP_ADD = 'SGADD';
    CONST SUB_GROUP_EDIT = 'SGEDT';
    CONST EMAIL_UPDATE = 'EMCHG';
}
