<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentLicense extends Model
{
    use SoftDeletes;

    protected $table = 'agent_license';
    protected $primaryKey = 'license_id';
    protected $guarded = ['license_id'];
    protected $dates = ['deleted_at'];
    protected $casts = [
        'license_exp_date' => 'date',
        // casting license_added_date to datetime, converts unix timestamp to normal format on save
        // 'license_added_date' => 'datetime'
    ];

    public const STATUS_APPROVED = 'A';
    public const STATUS_DISABLED = 'D';
    public const STATUS_PENDING = 'P';
    public const STATUS_SUSPENDED = 'S';

    static $statuses = [
        'Approved' => self::STATUS_APPROVED,
        'Disabled' => self::STATUS_DISABLED,
        'Pending' => self::STATUS_PENDING,
        'Suspended' => self::STATUS_SUSPENDED
    ];

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($agentLicense) {
            $agentLicense->license_status = self::$statuses['Disabled'];
            $agentLicense->license_deleted = 1;
            $agentLicense->save();
        });
    }


    public function restore()
    {
        $this->license_status = 'P';
        $this->license_deleted = 0;
        parent::restore(); // Call the parent restore method to perform the actual restore
    }

    public function agentLicense()
    {
        $this->hasMany('App\AgentLicenseDocument','license_id','license_id');
    }
    public  function state() {

        return  $this->hasOne(State::class,'abbrev','license_state');
    }
    public  function licenseDoc() {

        return  $this->hasOne(AgentLicenseDocument::class,'license_id','license_id');
    }

    public function agent(){
        return $this->belongsTo(AgentInfo::class,'license_agent_id');
    }

    public function addedBy(){
        return $this->belongsTo(AgentInfo::class,'added_by');
    }

    public function getStatusTextAttribute() {

        switch ($this->license_status) {
            case 'D':
                return 'Disabled';
            case 'A':
                return 'Active';
            case 'P':
                return 'Pending';
            default:
                return 'Terminated';
        }
    }
}
