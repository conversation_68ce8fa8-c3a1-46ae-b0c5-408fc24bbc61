<?php

namespace App;

use App\EmailDomain;
use Illuminate\Database\Eloquent\Model;

class EmailCCLogs extends Model
{

    protected $primaryKey = 'id';
    protected $connection = 'mysql';
    protected $table = 'email_cc_logs';
    // Disable default timestamp handling
    public $timestamps = false;

    // Using custom timestamp for updates
    const UPDATED_AT = 'status_updated_at';

    protected $fillable = [
        'rep_id',
        'rep_admin_email',
        'cc_status',
        'email_domain_id',
        'status_updated_at',
        'updated_by',
    ];

    public function emailDomain()
    {
        return $this->belongsTo(EmailDomain::class, 'email_domain_id');
    }
}
