<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PaymentTypeCc extends Model
{
    protected $table = 'payment_cc';
    protected $primaryKey = 'cc_id';
    protected $guarded = ['cc_id'];
    protected $with = ['merchants:cc_id,merchant,payer_id,card_id'];
    protected $casts = [
        'created_at' => 'datetime:m/d/Y H:i:s',
        'updated_at' => 'datetime:m/d/Y H:i:s'
    ];

    const PAYMENT_TYPE_VISA = 'Visa';
    const PAYMENT_TYPE_MASTER_CARD = 'Mastercard';
    const PAYMENT_TYPE_DISCOVER = 'Discover';

    static $paymentTypes = [
        self::PAYMENT_TYPE_VISA,
        self::PAYMENT_TYPE_MASTER_CARD,
        self::PAYMENT_TYPE_DISCOVER
    ];

    // get policy information from policies table for primary payment info
    public function policy()
    {
        return $this->hasOne('App\Policy', 'payment_id', 'cc_id');
    }

    public function merchants()
    {
        return $this->hasMany(PaymentCCMerchant::class, 'cc_id', 'cc_id');
    }

    public function scopeMerchant($query, $merchant)
    {
        return $query->with([
            'merchants' => function ($query) use ($merchant) {
                $query->where('merchant', $merchant);
            }
        ]);
    }

    public function address()
    {
        return $this->hasOne(Address::class, 'address_id', 'cc_addressid');
    }
}
