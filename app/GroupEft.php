<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GroupEft extends Model
{
    protected $table = 'group_eft';
    protected $primaryKey = 'bank_id';
    protected $guarded = ['bank_id'];
    const ACCOUNT_TYPE_SAVING = 'savings';
    const ACCOUNT_TYPE_CHECKING = 'checking';
    const ACCOUNT_HOLDER_TYPE_INDIVIDUAL = 'individual';
    const ACCOUNT_HOLDER_TYPE_COMPANY = 'company';

    public function groupInfo()
    {
        return $this->belongsTo(GroupInfo::class, 'bank_gid','gid');
    }
}
