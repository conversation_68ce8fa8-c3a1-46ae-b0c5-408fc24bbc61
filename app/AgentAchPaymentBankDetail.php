<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentAchPaymentBankDetail extends Model
{
    use SoftDeletes;

    protected $table = 'agent_achpayment_bank_details';
    protected $primaryKey = 'id';
    protected $appends = ['masked_account'];
    protected $guarded = ['id'];
    protected $dates = ['deleted_at'];

    const AGENT_ACH_ACCOUNT_TYPE_SAVING = 'savings';
    const AGENT_ACH_ACCOUNT_TYPE_CHECKING= 'checking';
    const AGENT_ACH_ACCOUNT_HOLDER_TYPE_INDIVIDUAL = 'individual';
    const AGENT_ACH_ACCOUNT_HOLDER_TYPE_COMPANY = 'company';


    public function setBankNameAttribute($value){
        $this->attributes['bank_name'] = strtoupper($value);
    }

    public function getMaskedAccountAttribute()
    {
        $account = (int)$this->account;
        return "XXXXX" . substr($account, -4);
    }

    public function restore()
    {
        parent::restore();
    }
}
