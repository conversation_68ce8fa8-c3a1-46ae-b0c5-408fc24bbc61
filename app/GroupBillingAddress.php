<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GroupBillingAddress extends Model
{
    protected $table = 'group_billing_addresses';
    protected $primaryKey = 'id';
    protected $appends = ['type'];
    protected $guarded = ['id'];

    const GROUP_ADDRESS_TYPE_BILLING = 'billing';

    public function getTypeAttribute()
    {
        return self::GROUP_ADDRESS_TYPE_BILLING;
    }
}

