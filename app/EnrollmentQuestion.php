<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class EnrollmentQuestion extends Model
{
    protected $table = 'enrollment_questions';
    protected $primaryKey = 'qid';
    protected $guarded = [];

    public const STATUS_ACTIVE = 'Active';
    public const STATUS_INACTIVE = 'Inactive';

    public const TRUE_CONDITION_TRUE = 'Yes';
    public const TRUE_CONDITION_FALSE = 'No';

    static $statuses = [
        self::STATUS_ACTIVE => 1,
        self::STATUS_INACTIVE => 0,
    ];
    
    static $trueConditions = [
        self::TRUE_CONDITION_TRUE => 1,
        self::TRUE_CONDITION_FALSE => 0,
    ];

}
