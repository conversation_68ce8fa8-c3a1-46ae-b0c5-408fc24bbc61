<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentInGroup extends Model
{
    use SoftDeletes;

    protected $table = 'agents_ingroup';
    protected $primaryKey = null;
    public $incrementing = false;
    public $timestamps = false;

    public function agentDetail()
    {
        return $this->hasOne('App\AgentInfo', 'agent_id', 'agent_id');
    }
}
