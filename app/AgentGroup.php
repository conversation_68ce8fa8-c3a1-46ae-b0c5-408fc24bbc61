<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentGroup extends Model
{
    use SoftDeletes;

    protected $table = 'agent_group';
    protected $primaryKey = 'aai';
    protected $guarded = ['aai'];
    protected $dates = ['deleted_at'];

    public const STATUS_APPROVED = 'A';
    public const STATUS_DISABLED = 'D';
    public const STATUS_PENDING = 'P';
    public const STATUS_SUSPENDED = 'S';

    static $statuses = [
        'Approved' => self::STATUS_APPROVED,
        'Disabled' => self::STATUS_DISABLED,
        'Pending' => self::STATUS_PENDING,
        'Suspended' => self::STATUS_SUSPENDED
    ];

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($agentGroup) {
            $agentGroup->ag_status = self::$statuses['Disabled'];
            $agentGroup->save();
        });
    }

    public function restore()
    {
        // Additional logic before restoring the record
        $this->update(['ag_status' => 'P']);
        parent::restore(); // Call the parent restore method to perform the actual restore
    }
}
