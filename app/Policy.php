<?php

namespace App;

use App\Helpers\CheckPlanTypeHelper;
use App\PlanOverview;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Policy extends Model
{
    protected $table = 'policies';
    protected $primaryKey = 'policy_id';

    protected $guarded = [];

    public const STATUS_ACTIVE = 'ACTIVE';
    public const STATUS_TERMED = 'TERMED';
    public const STATUS_WITHDRAWN = 'WITHDRAWN';

    public const PRUDENTIAL_LIFE_PLANS_IDS = [1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531];

    static $statuses = [
        self::STATUS_ACTIVE,
        self::STATUS_TERMED,
        self::STATUS_WITHDRAWN
    ];

    protected $attributes = ['enrollment_date'];


    // get userdetails from policy
    public function getMember()
    {
        return $this->hasOne('App\UserInfo', 'userid', 'policy_userid');
    }

    // get dependents for a policy
    public function getPolicyDependents()
    {
        return $this->hasMany('App\DependentPolicy', 'policy_id', 'policy_id');
    }

    //get groupinfo for a policy
    public function getGroup()
    {
        return $this->hasOne('App\GroupInfo', 'gid', 'eid');
    }

    // get plans for a policy
    public function getPolicyPlans()
    {
        return $this->hasMany('App\PlanPolicy', 'policy_num', 'policy_id');
    }

    // get plans from plan_overview for a policy
    public function getPlanOverviewFromPolicy($policy_id)
    {
        return PlanOverview::where('policy_id', $policy_id)->get();
    }

    public function planOverview()
    {
        return $this->hasMany(PlanOverview::class, 'policy_id');
    }

    public function healthPlanOverview()
    {
        return $this->hasOne(PlanOverview::class, 'policy_id')
            ->where('pl_type','=','MM');
    }

    public function userInfoPolicyAddress()
    {
        return $this->hasOne(UserInfoPolicyAddress::class, 'userid', 'policy_userid');
    }

    public function agentInfo()
    {
        return $this->hasOne(AgentInfo::class, 'agent_id', 'p_agent_num');
    }

    public function payment()
    {
        $paymentType = $this->payment_type;
        switch ($paymentType) {
            case 'eft':
                return $this->belongsTo(PaymentEft::class, 'payment_id','bank_id');
            case 'cc':
                return $this->belongsTo(PaymentTypeCc::class, 'payment_id','cc_id');
            default:
                return null;
        }
    }

    public function recurringPayment(){
        $paymentType = $this->recurring_payment_type;
        switch ($paymentType) {
            case 'eft':
                return $this->belongsTo(PaymentEft::class, 'recurring_payment_id','bank_id');
            case 'cc':
                return $this->belongsTo(PaymentTypeCc::class, 'recurring_payment_id','cc_id');
            default:
                return null;
        }
    }

    public function userInfoDepMedBex(){
        return $this->hasMany(UserInfoDepMedBex::class,'user_id', 'policy_userid')->whereNotNull('policyId')
            ->orderBy('question_id','ASC');
    }

    public function dependentInPolicy(){
        return $this->hasMany(DependentInPolicy::class,'policy_id','policy_id');
    }

    public function policyClientBrowserInfo(){
        return $this->hasOne(PolicyClientBrowserInfo::class,'policy_id','policy_id');
    }

    public function getEnrollmentDateAttribute()
    {
        return date('Y-m-d', $this->edate);
    }

    public function getPolicyPlan()
    {
        return $this->hasMany('App\PlanOverview', 'policy_id', 'policy_id');
    }

    public function signature(): HasOne
    {
        return $this->hasOne(Signature::class, 'pid', 'policy_id');
    }

    public function getHasL713PlanAttribute(): bool
    {
        return CheckPlanTypeHelper::checkL713ProductByPolicy($this->attributes['policy_id']);
    }

    public function getHasIHAPlanAttribute(): bool
    {
        return CheckPlanTypeHelper::checkIHAPlan($this->attributes['policy_id']);
    }

    public function getHasSignatureAttribute(): bool
    {
        return $this->signature()->exists();
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(NbInvoice::class, 'invoice_policy_id');
    }
}
