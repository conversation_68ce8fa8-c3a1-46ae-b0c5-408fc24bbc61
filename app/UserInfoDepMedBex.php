<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserInfoDepMedBex extends Model
{
    protected $table = 'userinfo_dep_meds_bex';
    protected $primaryKey = 'med_id';
    protected $guarded  = ['med_id'];

//    protected $with = ['userInfo','getQuestion'];

    public function getQuestion()
    {
        return $this->hasOne('App\PlanQuestionView', 'qid', 'question_id');
    }
    public function getMedication()
    {
        return $this->hasMany('App\MedMedication','user_id','user_id');
    }
    public function getMedCondition()
    {
        return $this->hasMany('App\MedCondition','med_id','med_id');
    }
    public function getDepMedication()
    {
        return $this->hasMany('App\MedMedication','dependent_id','dependent_id');
    }

    public function userInfo(){
        return $this->belongsTo(UserInfo::class,'user_id');
    }

    public function medCondition()
    {
        return $this->hasOne('App\MedCondition','med_id','med_id');
    }
}
