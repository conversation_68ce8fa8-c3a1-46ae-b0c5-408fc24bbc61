<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class RepInfoRequestsDetail extends Model
{
    protected $table = 'rep_info_requests_details';
    protected $fillable = [
        'info_request_id',
        'card_id',
        'payer_id',
        'amount',
        'payment_id',
        'status',
        'payment_description',
        'clearing_date',
        'created_date',
        'invoice_id',
    ];

    protected $casts = [
        'clearing_date' => 'datetime',
        'created_date' => 'datetime',
        'amount' => 'decimal:2',
    ];
}


