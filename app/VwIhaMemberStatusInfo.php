<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class VwIhaMemberStatusInfo extends Model
{
    protected $table = 'vw_iha_member_status_info';

    // for new enrollment
    public const PENDING_UNDERWRITING = 'PENDING-UNDERWRITING';
    public const PENDING_SIGNATURE = 'PENDING-SIGNATURE';
    public const COMPLETED_READY_TO_ENROLL = 'SIGNATURE-RECEIVED-READY-TO-ACTIVATE';

    // for renewals
    public const PENDING_RENEWAL_SIGNATURE = 'PENDING-RENEWAL-SIGNATURE';
    public const RENEWAL_COMPLETED_READY_TO_ENROLL = 'RENEWAL-SIGNATURE-RECEIVED-READY-TO-ACTIVATE';
    public const PENDING_RENEWAL_UNDERWRITING = 'PENDING-RENEWAL-UNDERWRITING';

    // for active and termed
    public const TERMED_STATUS = 'TERMED';
    public const ACTIVE_STATUS = 'ACTIVE';

    // plan switch new requirement from IHA TO IHA
    public const PLAN_SWITCH_UNDERWRITING = 'PLAN-SWITCH-PENDING-UNDERWRITING';
    public const PLAN_SWITCH_SIGNATURE = 'PLAN-SWITCH-PENDING-SIGNATURE';
    public const PLAN_SWITCH_COMPLETED_READY_TO_ENROLL = 'PLAN-SWITCH-SIGNATURE-RECEIVED-READY-TO-ACTIVATE';

    // SUB CATEGORY
    public const UNDERWRITING_REJECTION_STATUS = 'UNDERWRITING-REJECTION';
    public const PENDING_SIGNATURE_REJECTION_STATUS = 'SIGNATURE-REJECTION';
    public const RENEWAL_UNDERWRITING_REJECTION_STATUS = 'RENEWAL-UNDERWRITING-REJECTION';
    public const PENDING_RENEWAL_SIGNATURE_REJECTION_STATUS = 'RENEWAL-SIGNATURE-REJECTION';

    public const IHA_MEMBER_CONTRACT_ELIGIBLE_STATUS = [
        self::COMPLETED_READY_TO_ENROLL, self::ACTIVE_STATUS, self::TERMED_STATUS, self::RENEWAL_COMPLETED_READY_TO_ENROLL, self::PLAN_SWITCH_COMPLETED_READY_TO_ENROLL, self::PENDING_RENEWAL_UNDERWRITING, self::PENDING_RENEWAL_SIGNATURE, self::PLAN_SWITCH_SIGNATURE
    ];

    public function showMemberContract()
    {
       return in_array($this->attributes['iha_dashboard_status'], self::IHA_MEMBER_CONTRACT_ELIGIBLE_STATUS) && $this->attributes['pstatus'] == 1;
    }
}
