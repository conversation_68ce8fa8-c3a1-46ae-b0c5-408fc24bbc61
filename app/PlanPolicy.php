<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PlanPolicy extends Model
{
    protected $table = 'plan_policies';
    protected $primaryKey = 'p_ai';

    protected $guarded = [];

    public const STATUS_ACTIVE = 'ACTIVE';
    public const STATUS_TERMED = 'TERMED';
    public const STATUS_WITHDRAWN = 'WITHDRAWN';

    const STATUS_ACTIVE_VALUE = 1;
    const STATUS_TERMED_VALUE = 2;
    const STATUS_WITHDRAWN_VALUE = 3;
    static $statuses = [
        self::STATUS_ACTIVE => self::STATUS_ACTIVE_VALUE,
        self::STATUS_TERMED => self::STATUS_TERMED_VALUE,
        self::STATUS_WITHDRAWN => self::STATUS_WITHDRAWN_VALUE
    ];


    // get plan_pricing details from plan_policies
    public function getPlanPricing()
    {
        return $this->hasOne('App\PlanPricing', 'plan_pricing_id', 'plan_id');
    }

    // get policy details from plan_policies
    public function getPlanPolicyDetail()
    {
        return $this->hasOne('App\Policy', 'policy_id', 'policy_num');
    }

//    public function plan(){
//        return $this->belongsTo(Plan::class,'plan_id','pid');
//    }

    //get plan from plans table from plan_policies
    public function getPidFromPlanPolicy()
    {
        $planPricing = $this->getPlanPricing()->first();
        if (!$planPricing) {
            return null;
        }
        $planTier = $planPricing->getPlanTier()->first();
        if (!$planTier) {
            return null;
        }
        $plan = $planTier->getPlanDetail()->first();
        if (!$plan) {
            return null;
        }
        return $plan;
    }
}
