<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class DependentInPolicy extends Model
{
    protected $table = 'dependents_in_policy';
    protected $primaryKey = null;
    public $incrementing = false;
    public $timestamps = false;

    public const GENDER_MALE = 0;
    public const GENDER_FEMALE = 1;

    static $gender = [
        'Female' => self::GENDER_FEMALE,
        'Male' => self::GENDER_MALE
    ];

//    protected $with = ['healthAnswers'];

    public function healthAnswers()
    {
        return $this->hasMany('App\UserInfoDepMedBex', 'dependent_id', 'dependent_id')
            ->orderBy('question_id', 'ASC');

    }

    public function getDepMedication()
    {
        return $this->hasMany('App\MedMedication','dependent_id','dependent_id');
    }


    public function getFullNameAttribute()
    {
        return isset($this->d_mname) || !empty($this->d_mname)
            ? ucwords($this->d_fname . ' ' . $this->d_mname . ' ' . $this->d_lname)
            : ucwords($this->d_fname . ' ' . $this->d_lname);
    }

    public function getAgeAttribute()
    {
        $dob = $this->attributes['d_dob'];
        return Carbon::parse($dob)->age;
    }

    public function getFormattedHeightAttribute()
    {
        $formattedHeightInches = '';
        $heightFeet = $this->attributes['dhrq'];
        $heightInches = $this->attributes['dhrq2'];
        if ($heightInches !== null) {
            $formattedHeightInches = $heightInches . '"';
        } else {
            $formattedHeightInches = '';
        }
        if ($heightFeet !== null) {
            return $heightFeet . "'" . $formattedHeightInches;
        } else {
            return '';
        }
    }

    public function getFormattedWeightAttribute()
    {
        $weightLbs = $this->attributes['dwrq'];
        if ($weightLbs != null) {
            return $weightLbs . " lbs";
        } else {
            return '';
        }
    }

    // get user detail
    public function getMember(){
        return $this->hasOne('App\UserInfo','userid','userid');
    }
}
