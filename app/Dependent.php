<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Dependent extends Model
{
    protected $table = 'dependents';
    protected $primaryKey = 'did';
    protected $guarded =['did'];

    public const GENDER_MALE = 0;
    public const GENDER_FEMALE = 1;

    static $gender = [
        'Female' => self::GENDER_FEMALE,
        'Male' => self::GENDER_MALE
    ];

    // get user detail
    public function getMember(){
        return $this->hasOne('App\UserInfo','userid','userid');
    }
    // get info from dependent_policies table
    public function getDependentPolicy(){
        return $this->hasOne('App\DependentPolicy','dependent_id','did');
    }

}
