<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\CustomeHomepage;
use App\AgentUpdate;


class AgentInfo extends Model
{
    use SoftDeletes;

    protected $table = 'agent_info';
    protected $primaryKey = 'agent_id';
    protected $guarded = ['agent_id'];
    protected $dates = ['deleted_at'];
    protected $casts = [
        'agent_ga_effective_date' => 'datetime:Y-m-d'
    ];

    public const STATUS_APPROVED = 'A';
    public const STATUS_DISABLED = 'D';
    public const STATUS_PENDING = 'P';
    public const STATUS_SUSPENDED = 'S';
    public const STATUS_REMOVED = 'R';

    const AGENT_PAYMENT_METHOD_ACH = 'ach';
    const AGENT_PAYMENT_METHOD_CHECK = 'check';

    const MOBILE_APP_ANDROID = 'android';
    const MOBILE_APP_IOS = 'iOS';

    const CONTRACT_SIGNED = 1;
    const CONTRACT_NOT_SIGNED = 0;

    static $mobileAppDevices = [self::MOBILE_APP_ANDROID,self::MOBILE_APP_IOS];

    static $statuses = [
        'Approved' => self::STATUS_APPROVED,
        'Disabled' => self::STATUS_DISABLED,
        'Removed' => self::STATUS_REMOVED,
        'Pending' => self::STATUS_PENDING,
        'Suspended' => self::STATUS_SUSPENDED
    ];

    const LICENSE_DIR = "/agent/license/";

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($agentInfo) {
            $agentInfo->agent_status = self::$statuses['Removed'];
            $agentInfo->save();
        });

        static::updated(function ($agentInfo) {
            if ($agentInfo->isDirty('agent_email')) {
                \Log::channel('email_activity_log')->info('Agent Info: agent_email updated' );
                $id = request()->header('id');
                $name = request()->header('name');
                \Log::channel('email_activity_log')->info('Request', [
                    'data' => request()->all(),
                    'headers' => request()->headers->all()
                ]);
                \Log::channel('email_activity_log')->info('Header User Data', [
                    'id' => $id,
                    'name' => $name,
                ]);
                $data = [
                    'agent_id' => $agentInfo->agent_id,
                    'elgb_act' => AgentUpdate::EMAIL_UPDATE,
                    'elgb_comment' => 'Agent Email updated from ' . $agentInfo->getOriginal('agent_email') . ' to ' . $agentInfo->agent_email,
                    'elgb_act_date' => time(),
                    'elgb_agent_id' => $id,
                    'elgb_agent_name' => $name,
                    'changed_from' => $agentInfo->getOriginal('agent_email'),
                    'changed_to' => $agentInfo->agent_email
                ];

                AgentUpdate::create($data);
                \Log::channel('email_activity')->info('Agent email updated', $data);
            }
        });
    }

    public function restore()
    {
        $this->deleted_at = null; // This restores the soft-deleted record
        $this->agent_status = 'P';
        $this->save();
    }

    // get group detail
    public function getGroup()
    {
        return $this->hasMany('App\AgentInGroup', 'agent_id', 'agent_id')->where('ag_status','=','A');
    }

    // get agents license info
    public function getLicense()
    {
        return $this->hasMany(AgentLicense::class, 'license_agent_id', 'agent_id')
            ->where('license_deleted', '=', '0')
            ->where('license_resident', '=', '1');
    }

    //get agents total client
    public function getTotals()
    {
        return $this->hasMany('App\AgentTotals', 'agent_id', 'agent_id');
    }

    //get new agents total client
    public function getTotalsNew()
    {
        return $this->hasMany('App\AgentTotalsNew', 'agent_id', 'agent_id');
    }

    public function getAgentGa()
    {
        return $this->hasOne(AgentInfo::class, 'agent_id', 'agent_ga');
    }

    public function userActivity()
    {
        return $this->hasOne(UserActivityDetail::class, 'user_id', 'agent_id')
            ->where([
                ['action', 'login'],
                ['user_type', 'broker']
            ]);
    }

    public function achPayment()
    {
        return $this->hasOne(AgentAchpayment::class, 'achagent_id', 'agent_id');
    }


    public function downlineAgents()
    {
        return $this->hasMany(self::class, 'agent_ga', 'agent_id');
    }

    public function personalAddresses()
    {
        return $this->hasMany(AgentPersonalAddress::class, 'agent_id', 'agent_id');
    }

    public function businessAddresses()
    {
        return $this->hasMany(AgentBusinessAddress::class, 'agent_id', 'agent_id');
    }

    public function agentBusiness()
    {
        return $this->hasOne(AgentBusiness::class, 'business_agent_id', 'agent_id');
    }

    public function agentCheckPayment()
    {
        return $this->hasOne(AgentCheckpayment::class, 'checkpaymentagent_id', 'agent_id');
    }

    public function chequeAddresses()
    {
        return $this->hasMany(AgentChequeAddress::class, 'agent_id', 'agent_id');
    }

    public function achPaymentBankDetails()
    {
        return $this->hasMany(AgentAchPaymentBankDetail::class, 'agent_id', 'agent_id');
    }

    public function agentGroups()
    {
        return $this->hasMany(AgentGroup::class, 'agent_id', 'agent_id');
    }

    public function policies()
    {
        return $this->hasMany(Policy::class,'p_agent_num','agent_id');
    }

    public function getFullNameAttribute()
    {
        return $this->agent_mname != '' || $this->agent_name != null
            ? ucwords($this->agent_fname . ' ' . $this->agent_mname . ' ' . $this->agent_lname)
            : ucwords($this->agent_fname . ' ' . $this->agent_lname);
    }

    /**
     * @return string|null
     * currently fetching image from corenroll server
     * s3 only used to store image
     */
    public function getFullImageAttribute()
    {
        return $this->agent_img != "" ? "https://corenroll.com/agent_image.php?file=" . $this->agent_img : null;
    }

    public function agentUser()
    {
        return $this->hasOne(AgentUser::class, 'agent_id', 'agent_id')->where('is_root',1);
    }

    public function agentCustomeHomepage()
    {
        return $this->hasOne(CustomeHomepage::class, 'agent_id', 'agent_id');
    }

}
