<?php

namespace App;

use App\UserLogin;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\UserUpdate;
use App\AdminUser;

class UserInfo extends Model
{
    protected $table = 'userinfo';
    protected $primaryKey = 'userid';
    protected $guarded = [];

    protected $attributes = ['fullname'];

    public const GENDER_MALE = 0;
    public const GENDER_FEMALE = 1;

    const VALID = "VALID";
    const INVALID = "INVALID";

    static $gender = [
        'Female' => self::GENDER_FEMALE,
        'Male' => self::GENDER_MALE
    ];

    const EMP_VAL_POLICY_WL1 = '1 week or less';
    const EMP_VAL_POLICY_WL2 = 'Greater than 1 week but no more than 2 weeks';
    const EMP_VAL_POLICY_OTHER = 'Other';

    static $vacPolicies = [
        self::EMP_VAL_POLICY_WL1,
        self::EMP_VAL_POLICY_WL2,
        self::EMP_VAL_POLICY_OTHER,
    ];

    const EMP_BARG_UNIT_EMPLOYEES = 'employees';
    const EMP_BARG_UNIT_SALES = 'sales';
    const EMP_BARG_UNIT_CLERICAL = 'clerical';
    const EMP_BARG_UNIT_NON_CERICAL = 'non-clerical';
    const EMP_BARG_UNIT_OTHER = 'other';

    static $bargUnits = [
        'All Employees' => self::EMP_BARG_UNIT_EMPLOYEES,
        'Sales' => self::EMP_BARG_UNIT_SALES,
        'Clerical' => self::EMP_BARG_UNIT_CLERICAL,
        'Non-clerical' => self::EMP_BARG_UNIT_NON_CERICAL,
        'Other' => self::EMP_BARG_UNIT_OTHER,
    ];

    const HOLIDAY_POLICY_STANDARD = 'Standard_6';
    const HOLIDAY_PRESIDENT_DAY = 'Presidents_Day';
    const HOLIDAY_COLUMBUS_DAY = 'Columbus_Day';
    const HOLIDAY_RELIGIOUS_DAY = 'Religious_Days';
    const HOLIDAY_BIRTHDAY = 'Birthday';
    const HOLIDAY_FLOATING_DAY = 'Floating_Day';

    static $holidayPolicies = [
        self::HOLIDAY_POLICY_STANDARD,
        self::HOLIDAY_PRESIDENT_DAY,
        self::HOLIDAY_COLUMBUS_DAY,
        self::HOLIDAY_RELIGIOUS_DAY,
        self::HOLIDAY_BIRTHDAY,
        self::HOLIDAY_FLOATING_DAY
    ];


// get home address from userinfo table
    public function getHomeAddress()
    {
        return $this->hasOne('App\Address', 'address_id', 'home_address');
    }

    // get all dependents of a user
    public function getDependents()
    {
        return $this->hasMany('App\Dependent', 'userid', 'userid');
    }


    // get all registered users
    public function getRegisteredUsers($userid)
    {

        $loggedinUsers = UserLogin::where('status', 1)->where('userid', '>', $userid)->groupBy('userid')->get('userid');
        $registeredUsers = UserInfo::whereIn('userid', $loggedinUsers)->orderBy('userid', 'DESC')->get();
        return $registeredUsers;
    }

    public function getFullNameAttribute()
    {
        return $this->cmname
            ? ucwords($this->cfname . ' ' . $this->cmname . ' ' . $this->clname)
            : ucwords($this->cfname . ' ' . $this->clname);
    }

    public function getAgeAttribute(): int
    {
        return Carbon::parse($this->attributes['cdob'])->age;
    }

    public function getPolicy()
    {
        return $this->hasOne('App\Policy', 'policy_userid');
    }

    public function getPolicyList()
    {
        return $this->hasMany('App\Policy', 'policy_userid');
    }

    public function customerAddress()
    {
        return $this->hasOne('App\Address', 'a_userid');
    }

    public function getMedication()
    {
        return $this->hasMany('App\MedMedication', 'user_id', 'userid');
    }

    public function getFormattedHeightAttribute()
    {
        $formattedHeightInches = '';
        $heightFeet = $this->height_feet;
        $heightInches = $this->height_inches;
        if ($heightInches !== null) {
            $formattedHeightInches = $heightInches . '"';
        } else {
            $formattedHeightInches = '';
        }
        if ($heightFeet !== null) {
            return $heightFeet . "'" . $formattedHeightInches;
        } else {
            return '';
        }
    }

    public function getFormattedWeightAttribute()
    {
        $weightLbs = $this->weight_lbs;
        if ($weightLbs != null) {
            return $weightLbs . " lbs";
        } else {
            return '';
        }
    }

    public static function boot(){
        parent::boot();
        static::updated(function ($model) {
            if (array_key_exists('cemail', $model->getDirty())) {
                \Log::channel('email_activity_log')->info('User Info: cemail Updated' );
                $id = request()->header('id');
                $name = request()->header('name');
                \Log::channel('email_activity_log')->info('Request', [
                    'data' => request()->all(),
                    'headers' => request()->headers->all()
                ]);
                \Log::channel('email_activity_log')->info('Header User Data', [
                    'id' => $id,
                    'name' => $name,
                ]);
                $admin = AdminUser::where('id', $id)->first();
                $data = [
                    'user_id' => $model->userid,
                    'action_date' => time(),
                    'comment' => 'User Email updated from ' . $model->getOriginal('cemail') . ' to ' . $model->cemail,
                    'changed_by_id' => ($id && $id !== 'undefined') ? $id : null,
                    'changed_by_type' => $admin->userrole??NULL,
                    'changed_from' => $model->getOriginal('cemail'),
                    'changed_to' => $model->cemail,
                    'change_type' => UserUpdate::EMAIL_UPDATE,
                ];
                UserUpdate::create($data);
                \Log::channel('email_activity_log')->info('Email updated: User Info', $data);
            }
        });
    }
}
