<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserUpdate extends Model
{
    use SoftDeletes;
    
    protected $table = 'user_updates';
    
    protected $fillable = [
        'user_id',
        'action_date',
        'comment',
        'changed_by_id',
        'changed_by_type',
        'changed_from',
        'changed_to',
        'change_type',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    CONST EMAIL_UPDATE = 'EMCHG';

}
