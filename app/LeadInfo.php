<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use phpDocumentor\Reflection\Types\Boolean;

class LeadInfo extends Model
{
    protected $connection = 'mysql';
    protected $table = 'leads_information';
    protected $guarded = [
        'id', 'created_at', 'updated_at'
    ];
    protected $casts = [
        'is_tablet'  => 'boolean',
        'is_mobile'  => 'boolean',
        'is_desktop' => 'boolean',
        'is_robot'   => 'boolean',
        'created_at' => 'datetime:m-d-Y H:i:s',
        'updated_at' => 'datetime:m-d-Y H:i:s'
    ];

    public function agent_lead()
    {
        return $this->hasMany(AgentLead::class, 'device_id', 'device_id');
    }

    public function agent()
    {
        return $this->belongsTo(AgentInfo::class, 'agent_id', 'agent_id');
    }

    public function group()
    {
        return $this->belongsTo(GroupInfo::class, 'gid', 'gid');
    }
}
