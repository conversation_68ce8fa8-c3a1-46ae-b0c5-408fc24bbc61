<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;

class PolicyNote extends Model
{
    protected $table = 'policy_notes';
    protected $primaryKey = 'anID';
    public $timestamps = false;
    public $guarded = ['anID'];

    CONST STATUS_URGENT = 1;
    CONST STATUS_IMPORTANT = 2;
    CONST STATUS_HIGH = 3;
    CONST STATUS_MEDIUM = 4;
    CONST STATUS_LOW = 5;
    CONST STATUS_INFORMATION = 6;
    CONST STATUS_SPECIAL = 8;

    CONST COLOR_URGENT = '#FF0000';
    CONST COLOR_IMPORTANT = '#FF8000';
    CONST COLOR_HIGH = '#FFFF00';
    CONST COLOR_MEDIUM = '#800000';
    CONST COLOR_LOW = '#0000FF';
    CONST COLOR_INFORMATION = '#00FF00';
    CONST COLOR_SPECIAL = '#FFC0CB';

    static $statuses = [
        'Urgent' => self::STATUS_URGENT,
        'Important' => self::STATUS_IMPORTANT,
        'High' => self::STATUS_HIGH,
        'Medium' => self::STATUS_MEDIUM,
        'Low' => self::STATUS_LOW,
        'Information' => self::STATUS_INFORMATION,
        'Special' => self::STATUS_SPECIAL,
    ];

    static $colors = [
        'Urgent' => self::COLOR_URGENT,
        'Important' => self::COLOR_IMPORTANT,
        'High' => self::COLOR_HIGH,
        'Medium' => self::COLOR_MEDIUM,
        'Low' => self::COLOR_LOW,
        'Information' => self::COLOR_INFORMATION,
        'Special' => self::COLOR_SPECIAL,
    ];

    public function agent()
    {
        return $this->hasOne('App\AgentInfo', 'agent_id', 'agent_id');
    }

    public function getGroupFileUrlAttribute()
    {
        if (isset($this->file) and $this->file != '') {
            return config('filesystems.disks.s3.url') . "/group-notes/{$this->file}";
        } else {
            return null;
        }
    }

}
