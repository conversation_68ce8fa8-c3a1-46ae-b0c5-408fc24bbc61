<?php

namespace App\Rules;

use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Rule;

class RoutingNumberRule implements Rule
{

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $validationService = new CustomValidationService();
        return $validationService->validateRoutingNumber($value);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Routing number is invalid.';
    }
}
