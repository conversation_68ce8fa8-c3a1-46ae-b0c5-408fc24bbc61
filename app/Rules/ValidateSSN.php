<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Log;

class ValidateSSN implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public $messages =[];
    public $valid_flag = true;
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $segments = explode('-', $value);
        foreach ($segments as $segment) {
            if (!ctype_digit($segment)) {
                $this->messages[] = 'Invalid format. Please enter the identification number in the format XXX-XX-XXXX.';
                return false;
            }
        }
        if (strpos($value, '-') !== false) {
            if (!preg_match('/^\d{3}-\d{2}-\d{4}$/', $value)) {
                $this->messages[] = 'Invalid format. Please enter the identification number in the format XXX-XX-XXXX.';
                return false;
            }
        } else {
            if (!preg_match('/^\d{9}$/', $value)) {
                $this->messages[] = 'Invalid format. Please enter the identification number in the format XXX-XX-XXXX.';
                return false;
            }
        }
        $ssn = preg_replace('/[^0-9]/', '', $value);
        if (strlen($ssn) !== 9) {
            $this->messages[] = 'Invalid format. Please enter the identification number in the format XXX-XX-XXXX.';
            return false;
        }

        $area = (int) substr($ssn, 0, 3);
        $group = (int) substr($ssn, 3, 2);
        $serial = (int) substr($ssn, 5, 4);

        // Determine the type of number based on the area code
        if ($area >= 1 && $area <= 665 || $area >= 667 && $area <= 763 || $area >= 764 && $area <= 899) {
            $type = 'SSN';
        } elseif ($area >= 900 && $area <= 999) {
            if ($group === 93) {
                $type = 'ATIN';
            } else {
                $type = 'ITIN';
            }
        } else {
            $this->messages[] = 'Invalid identification number. Please check the number and try again.';
            return false;
        }

        // Area validation
        if ($type === 'SSN' && !(
            ($area >= 1 && $area <= 665) ||
            ($area >= 667 && $area <= 763) ||
            ($area >= 764 && $area <= 899)
        )) {
            $this->messages[] = 'Invalid identification number. Please check the number and try again.';
            $this->valid_flag= false;
        }

        if ($type === 'ITIN' && !($area >= 900 && $area <= 999)) {
            $this->messages[] = 'Invalid identification number. Please check the number and try again.';
            $this->valid_flag= false;
        }

        if ($type === 'ATIN' && !($area >= 900 && $area <= 999)) {
            $this->messages[] = 'Invalid identification number. Please check the number and try again.';
            $this->valid_flag= false;
        }

        // Group validation
        if ($type === 'SSN' && $group === 0) {
            $this->messages[] = 'Invalid identification number. The group or serial portion contains invalid values';
            $this->valid_flag= false;
        }

        if ($type === 'ITIN' && !(($group >= 70 && $group <= 88) || ($group >= 90 && $group <= 92) || ($group >= 94 && $group <= 99))) {
            $this->messages[] = 'Invalid identification number. The group or serial portion contains invalid values';
            $this->valid_flag= false;
        }

        if ($type === 'ATIN' && $group !== 93) {
            $this->messages[] = 'Invalid identification number. The group or serial portion contains invalid values';
            $this->valid_flag= false;
        }

        // Serial validation
        if ($type === 'SSN' && $serial === 0) {
            $this->messages[] = 'Invalid identification number. The group or serial portion contains invalid values';
            $this->valid_flag= false;
        }

        if (($type === 'ITIN' || $type === 'ATIN') && !($serial >= 0 && $serial <= 9999)) {
            $this->messages[] = 'Invalid identification number. The group or serial portion contains invalid values';
            $this->valid_flag= false;
        }


        return $this->valid_flag;

        
        /*
        * It handles the ssn with hyphens
        *  It should have 9 digits.
        *  The first part should have 3 digits and should not be 000 and 666.
        *  The second part should have 2 digits, and it should be from 01 to 99.
        *  The third part should have 4 digits, and it should be from 0001 to 9999.

        */

     
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
   
        return $this->messages;
    }
}