<?php

namespace App\Rules;

use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Rule;

class RoutingNumberBankNameRule implements Rule
{
    private $routingNumber;

    /**
     * Create a new rule instance.
     *
     * @param $routingNumber
     */
    public function __construct($routingNumber)
    {
        $this->routingNumber = $routingNumber;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $validationService = new CustomValidationService();
        $validateRouting = $validationService->validateRoutingNumberWithResponse($this->routingNumber);
        $bankName = strtoupper($validateRouting->original['data']['bank_name']);
        $value = strtoupper($value);
        return ($bankName == $value) ? true : false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Bank name is invalid.';
    }
}
