<?php

namespace App\Rules;

use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Rule;

class NumVerifyRule implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $validationService = new CustomValidationService();
        return $validationService->validatePhoneNumVerify($value);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Phone is invalid - Validated using ClearoutPhone';
    }
}
