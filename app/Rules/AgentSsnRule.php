<?php

namespace App\Rules;

use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Rule;

class AgentSsnRule implements Rule
{
    private $agentId;

    /**
     * Create a new rule instance.
     *
     * @param $agentId
     */
    public function __construct($agentId)
    {
        $this->agentId = $agentId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $validationService = new CustomValidationService();
        return $validationService->validateAgentSsn($value,$this->agentId);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Social Security Number already exists.';
    }
}
