<?php

namespace App\Rules;

use App\Helpers\CommonHelper;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Http\UploadedFile;

class GroupNoteValidateFile implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return self::groupNoteValidation($value) === true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Invalid file. Only pdf, doc, docx, xls, xlsx, csv, txt files are allowed.';
    }


    public static function allowedGroupNoteFileType()
    {
        return [
            "application/doc",
            "application/ms-doc",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",  // .doc & .docx
            "application/excel",
            "application/x-msexcel",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  // .xls & .xlsx
            "application/pdf",
            "text/csv",
            "text/plain",
        ];
    }

    public static function allowedGroupNoteExtensionForFiles()
    {
        return ["csv", "xls", "xlsx", "txt", "doc", "docx", "pdf"];
    }

    public static function groupNoteValidation($file)
    {
        /**
         * @var $file UploadedFile
         */
        $mimeType = $file->getMimeType();
        $extension = $file->getClientOriginalExtension();
        if (!in_array($mimeType, self::allowedGroupNoteFileType()) || !in_array(strtolower($extension), self::allowedGroupNoteExtensionForFiles())) {
            return false;
        }
        return true;
    }
}
