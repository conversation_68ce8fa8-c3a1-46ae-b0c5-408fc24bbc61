<?php

namespace App\Rules;

use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Rule;

class AgentEmailRule implements Rule
{

    private $agentId;

    /**
     * Create a new rule instance.
     *
     * @param $agentId
     */
    public function __construct($agentId)
    {
        $this->agentId = $agentId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $validationService = new CustomValidationService();
        return $validationService->validateAgentEmail($value,$this->agentId);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Email already exists.';
    }
}
