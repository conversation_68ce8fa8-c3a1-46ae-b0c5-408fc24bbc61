<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class TimeZone implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
       $timezone =  [
           "Samoa (UTC-11)",
           "Hawaii-Aleutian (UTC-10)",
           "Alaska (UTC-9)",
           "Pacific (UTC-8)",
           "Mountain (UTC-7)",
           "Central (UTC-6)",
           "Eastern (UTC-5)",
           "Atlantic (UTC-4)",
           "Chamorro (UTC+10)"
       ];
       return in_array($value,$timezone);

    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Invalid time zone.';
    }
}