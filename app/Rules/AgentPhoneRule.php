<?php

namespace App\Rules;

use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Rule;

class AgentPhoneRule implements Rule
{
    private $agentId;
    private $phoneField;

    /**
     * Create a new rule instance.
     *
     * @param $agentId
     */
    public function __construct($agentId, $phoneField = 'agent_phone1')
    {
        $this->agentId = $agentId;
        $this->phoneField = $phoneField;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $validationService = new CustomValidationService();
        return $validationService->validateAgentPhone($value,$this->agentId, $this->phoneField);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Phone already exists.';
    }
}
