<?php
namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SsoLoginArchive extends Model
{
    use  SoftDeletes;

    protected $connection = 'sso';
    protected $table = 'sso_login_archive';

    protected $fillable = [
        'name',
        'email',
        'phone',
        'user_type',
        'email_verified_at',
        'password',
        'verification_code',
        'payment_verification_code',
        'remember_token',
        'deviceid',
        'macid',
        'device_verified',
        'device_verified_at',
        'device_token',
        'access_token',
        'verification_code_expiry_date',
        'refresh_token'
    ];

    protected $dates = [
        'email_verified_at',
        'device_verified_at',
        'verification_code_expiry_date',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
}
