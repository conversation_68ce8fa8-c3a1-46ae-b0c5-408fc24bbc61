<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserLogin extends Model
{
    protected $table = 'user_login';
    protected $primaryKey = 'userid';
    public $incrementing = false;
    protected $guarded = [];
    
    protected $fillable = [
        'userid',
        'username',
        'password',
        'q1',
        'q2',
        'a1',
        'a2',
        'udate',
        'temp_pass',
        'status',
        'ip',
        'blocked',
        'password_validity',
        'subscribe',
        'last_attempt',
        'verification_code',
        'cookie_token',
        'phone',
        'ipAddress',
        'last_login_datetime',
        'password_verification_code',
        'weburl',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_email_valid',
    ];
    

}
