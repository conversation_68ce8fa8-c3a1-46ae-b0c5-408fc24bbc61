<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentAchpayment extends Model
{
    use SoftDeletes;

    protected $table = 'agent_achpayment';
    protected $primaryKey = 'achpayment_id';
    protected $appends = ['masked_account'];
    protected $guarded = ['achpayment_id'];
    protected $dates = ['deleted_at'];

    public function getMaskedAccountAttribute()
    {
        $account = (int)$this->achpayment_account;
        return "XXXXX-" . substr($account, -4);
    }

    public function restore()
    {
       parent::restore();
    }
}
