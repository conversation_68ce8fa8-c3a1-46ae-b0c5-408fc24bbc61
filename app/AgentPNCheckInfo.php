<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AgentPNCheckInfo extends Model
{
    protected $connection = 'mysql2';
    protected $table = 'agent_pn_check_info';
    protected $primaryKey = 'id';
    protected $fillable = ['agent_id', 'pt_date', 'paid_commission', 'source_id', 'check_id', 'link_id',
    'number', 'amount', 'fee', 'description', 'status', 'identifier', 'sndr_name', 'sndr_email', 'sndr_bname',
    'sndr_lbacc', 'rec_name', 'rec_email', 'rec_bname', 'rec_lbacc', 'direction', 'debit_date', 'credit_date',
    'printed_date', 'recurring','check_through','comm_type'];
}