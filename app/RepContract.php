<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class RepContract extends Model
{
    //
    protected $table = 'rep_contract';
    protected $primaryKey = 'contract_id';
    protected $guarded = [];


    public function saveRepContract($data){
        return $this->create($data);
    }

    public function repContractDetail(){
        return $this->hasMany('App\RepContractsDetail', 'contract_id', 'contract_id');
    }
}
