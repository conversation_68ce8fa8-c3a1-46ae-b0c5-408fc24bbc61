<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Plan extends Model
{
    protected $table = 'plans';
    protected $primaryKey = 'pid';
    protected $guarded = [];

    const PRUDENTIAL_SALARY_BELOW_100k = [1517, 1518, 1519, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529, 1530, 1531];


    // get plan_tier details from plan
    public function planTier()
    {
        return $this->hasMany('App\PlanTier', 'pid_tier', 'pid');
    }

    public function planStates()
    {
        return $this->hasMany('App\PlanState', 'pid', 'pid');
    }

    public function planAgeRestriction()
    {
        return $this->hasMany('App\PlanAgeRestriction', 'plan_id', 'pid')->where('status', 1);
    }

    public function planAgeRestrictionDependent()
    {
        return $this->hasMany('App\PlanAgeRestrictionDependent', 'plan_id', 'pid');
    }

    public function getDocs()
    {
        return $this->hasMany('App\PlanDoc', 'pid', 'pid');
    }

    public function getPlanPricingMaleNonsPriceAttribute(){
        return $this->hasMany(PlanOverview::class, 'pid', 'pid')
            ->where('pstatus','=',1)
            ->sum('price_male_nons');
    }

    public function planPricings () {

    	return $this->hasMany('App\PlanPricingDisplay', 'pid', 'pid');
    }

    public function getPlanCatFullFormAttribute()
    {
        return DB::table('acronym_abbreviation')
            ->whereCode($this->plan_cat)->pluck('fullform')
            ->first();
    }

    public function getPlanTypeFullFormAttribute()
    {
        return DB::table('acronym_abbreviation')
            ->whereCode($this->pl_type)->pluck('fullform')
            ->first();
    }

    public function homepagePlan() {
        return $this->hasMany(HomePageConfiguration::class, 'plan_id', 'pid');
    }
}
