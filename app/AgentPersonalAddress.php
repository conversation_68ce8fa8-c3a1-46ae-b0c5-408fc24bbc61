<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentPersonalAddress extends Model
{
    use SoftDeletes;

    protected $table = 'personal_addresses';
    protected $primaryKey = 'id';
    protected $appends = ['type'];
    protected $guarded = ['id'];
    protected $dates = ['deleted_at'];

    const AGENT_ADDRESS_TYPE_PERSONAL = 'personal';

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($agentPersonalAddress) {
            $agentPersonalAddress->active = 0;
            $agentPersonalAddress->save();
        });
    }

    public function restore()
    {
        $this->active = 1;
        parent::restore(); // Call the parent restore method to perform the actual restore
    }

    public function getTypeAttribute()
    {
        return self::AGENT_ADDRESS_TYPE_PERSONAL;
    }
}
