<?php

namespace App\Helpers;

use App\Helper\CommonHelper;
use App\Helper\PolicyDetailsHelper;
use App\PlanOverview;
use App\Policy;
use App\PolicyBeneficiary;
use App\PolicyUpdate;
use App\UserInfoPolicyAddress;
use App\VariablePricing;
use App\Helpers\DecryptEcryptHelper;
use App\PlanPolicyMember;
use Illuminate\Support\Facades\Log;


class PolicyUpdateHelper
{
    public static function updateEligibility($data)
    {
        $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
        $data['origin'] = $origin;
        try {
            PolicyUpdate::insert($data);
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public static function validateTermDate($policyID, $termDate)
    {
        $effectiveDate = Policy::where('policy_id', $policyID)->pluck('effective_date')->first();
        if ($effectiveDate > $termDate) {
            return false;
        }
        return true;
    }

    public static function getMonthlyPreium($userID)
    {
        $activePolicyIds = UserInfoPolicyAddress::where('userid', $userID)->get();
        $monthlyPremium = 0;
        foreach ($activePolicyIds as $policy) {
           $monthlyPremium+= self::getPolicyAmount($policy->policy_id);
        }
        return $monthlyPremium;
    }

    public static function getPolicyAmount($policyID)
    {
        $total = 0;
        $getPolicy = PlanOverview::where('policy_id', $policyID)->get();
        foreach ($getPolicy as $policyDetails) {
            if ($policyDetails->pstatus != 1) {
                continue;
            }

            if (in_array($policyDetails->pid, [661, 660, 659, 658, 657, 676, 678, 679, 680, 681, 682, 683, 684, 685])) {
                $price = VariablePricing::where('policy_id', $policyID)->where('plan', 'TL')->pluck('price')->first();
                $total = $total + $price + $policyDetails->ccfees;
            } elseif ($policyDetails->pid == 664 || $policyDetails->pid == 663) { // FOR VOLUNTARY CRITICAL ILLNESS INSURANCE PLAN
                $price = VariablePricing::where('policy_id', $policyID)->where('plan', 'CI')->pluck('price')->first();
                $total = $total + $price + $policyDetails->ccfees;
            } else {
                $total = $total + $policyDetails->price_male_nons;
            }
        }
        return $total;
    }

    // returns tier of plan
    public static function getPolicyPlanTier($policyID)
    {
        $policy = Policy::where('policy_id', $policyID)->first();
        $plans = $policy->getPolicyPlan;

        if (! empty($plans)) {
            $plan_tier = ($plans->where('pstatus', '1')->isNotEmpty()) ?
                $plans->where('pstatus', '1')->where('is_assoc', '0')->pluck('tier')->first() :
                $plans->pluck('tier')->first();
        } else {
            $plan_tier = null;
        }

        return $plan_tier;
    }

    // get dependents in a policy
    public static function getPolicyDependents($policyID)
    {
        $policy = Policy::where('policy_id', $policyID)->first();
        $dependents = $policy->dependentInPolicy;
        return $dependents;
    }

    public static function getPolicyBeneficiary($policyID)
    {
        $ben_data = PolicyBeneficiary::where('bpolicy_id', $policyID)
            ->select(['ben_id', 'bfname', 'blname', 'brelate', 'bdob', 'bssn', 'ben_percentage'])->get();
        foreach ($ben_data as $each_ben)
        {
            $each_ben['b_ssn'] = $each_ben->b_ssn;
            $each_ben['full_name'] = $each_ben->full_name;
        }
        return $ben_data;
    }

    public static function addPlanPoliciesMember ($policyId, $planId) {
        $planOverview = PlanOverview::where('policy_id', $policyId)->where('pid',$planId)->select('brand','cid','p_ai','pid','policy_id','policy_userid')->first();
        $client_ssn = DecryptEcryptHelper::decryptInfo($planOverview->userInfoPolicyAddress->cssn);
        $data = [
            "brand" => $planOverview->brand,
            "cid" => $planOverview->cid,
            "p_ai" => $planOverview->p_ai,
            "pid" => $planOverview->pid,
            "userid" => $planOverview->policy_userid,
            "policy_id" => $planOverview->policy_id,
            "ssn" => $client_ssn,
            "email_sent" => null,
            "email_sent_at" => null,
            "is_updateMemberCard" => 1,
        ];
        $plan_policy_member = PlanPolicyMember::create($data);
        if($plan_policy_member) {
            Log::info("Successfully inserted date in plan_policies_member table for policy Id \t" . $policyId . "\t and PlanId \t" . $planId . "\t");
            return true;
        } else {
            Log::info("There was error while inserted date in plan_policies_member table for policy Id \t" . $policyId . "\t and PlanId \t" . $planId . "\t");
            return false;
        }
    }

}
