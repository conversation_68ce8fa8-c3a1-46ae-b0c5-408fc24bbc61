<?php

namespace App\Helpers;

use App\BundelPlan;
use App\Dependent;
use App\MemberReferral;
use App\Plan;
use App\PlanPricingDisplay;
use App\PolicyNote;
use App\Signature;
use App\Policy;
use App\AgentInfo;
use App\PlanOverview;
use App\PlanPolicyMember;
use App\PrudentialClaim;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ManageClientsHelper
{

    public function mapData(UserInfoPolicyAddress $userInfoPolicyAddress, array $emailsAndPhones): array
    {
        $planData = $this->formatPlanData($userInfoPolicyAddress->plans , $userInfoPolicyAddress->payment_type , $userInfoPolicyAddress->policy_id);

        $recurringMonthly = $planData['recurring_monthly'];
        $annual_amount = isset($planData['annual_amount']) ? $planData['annual_amount'] : null;
        $quarterly_amount = isset($planData['quarterly_amount']) ? $planData['quarterly_amount'] : null;

        unset($planData['recurring_monthly'], $planData['annual_amount'], $planData['quarterly_amount']);
        if (count($userInfoPolicyAddress->plans)) {
            return [
                'policy' => [
                    'id' => $userInfoPolicyAddress->policy_id,
                    'status' => $userInfoPolicyAddress->status,
                ],
                'memberIds' => $this->getMemberIdByPolicyId($userInfoPolicyAddress->userid),
                'member' => $this->mapMemberInfo($userInfoPolicyAddress, $emailsAndPhones),
                'dates' => $this->mapDates($userInfoPolicyAddress),
                'agent_group' => $this->mapAgentAndGroupInfo($userInfoPolicyAddress),
                'plans' => $planData,
                'annual_amount' => $annual_amount,
                'quarterly_amount' => $quarterly_amount,
                'pay_type' => $annual_amount ? 'Annual' : ($quarterly_amount ? 'Quarterly' : 'Monthly'),
                'recurring_monthly' => $recurringMonthly,
                'nbInvoice' => $userInfoPolicyAddress -> nbInvoice,
                'merchant' => $userInfoPolicyAddress -> merchant,
                'plan_cat' => $userInfoPolicyAddress->plans->whereIn('plan_cat', ['ENANAWU', 'ENAELITE'])->count() > 0,
                'signature' => $userInfoPolicyAddress->plans
                    ->where('status', 'ACTIVE')
                    ->where('esig', '1')
                    ->count() == 0
                    ? null
//                    : $this->checkSignature($userInfoPolicyAddress->policy_id),
                    : $userInfoPolicyAddress->signature()->exists() ?? false,
                'benefit_store' => $userInfoPolicyAddress->is_benefit_store,
//                'user_registered' => $userInfoPolicyAddress->activeUserLogin->exists ?? false,
                'user_registered' => $this->isUserRegistered($userInfoPolicyAddress),
//                'tax_recieved' => $this->checkTax($userInfoPolicyAddress->policy_id),
                'tax_recieved' => $userInfoPolicyAddress->plans->where('plan_cat', 'ENANAWU')->count() > 0,
                'test_user' => in_array($userInfoPolicyAddress->cfname, ['test', 'TEST', 'Test']),
                'enroll_type' => $userInfoPolicyAddress->idilus_type == 'ee' ? 'EMPLOYEE' : null,
                'boffice' => $userInfoPolicyAddress->boffice,
                'approval' => $userInfoPolicyAddress->Approval,
                'is_auto_approved' => (bool) $userInfoPolicyAddress->policy->is_auto_approved,
                'bank' => $this->mapBankInfo($userInfoPolicyAddress),
                'note_icon_color' => $this->getLatestNoteColor($userInfoPolicyAddress->policy_id),
                'ihaStatus' => $this->getIhaPlanStatus($userInfoPolicyAddress),
                'isExtraHealth' => $userInfoPolicyAddress->hasExtraHelthPlans()->exists(),
                'is_prudential' => $this->checkIsPrudentialOr7kEnabled($userInfoPolicyAddress->policy_id),
                'dependents'=> $this->depInfo($userInfoPolicyAddress->policy_id),
                'has_member_referral_list' => $this->hasMemberReferralList($userInfoPolicyAddress->userid)
            ];
        }
        return [];
    }

    public static function prudPrice($forPrudPriceCheck, $getDepInfo){
        if($getDepInfo){
            $pruChildTotalPrice = 0;
            $pruSpoPrice = 0;
            foreach ($getDepInfo as $key => $value) {
                if(strtoupper($value['d_relate']) == 'S'){
                    $sdob = date('Y-m-d',strtotime($value['d_dob']));
                    $sage = self::calculateAge($sdob);
                    $gsp = PlanPricingDisplay::where('pid', $forPrudPriceCheck['pid'])->where('tier','IS')->where('pricing_status', 1)->where('age1','<=',$sage)->where('age2', '>=', $sage)->pluck('price_male_nons')->toArray();
                    $pruSpoPrice = count($gsp)?(float)$gsp[0]:0;
                }
                if(strtoupper($value['d_relate']) == 'C'){
                    $gcp = PlanPricingDisplay::where('pid', $forPrudPriceCheck['pid'])->where('tier','IC')->where('pricing_status', 1)->where('age1','<=', 24)->where('age2', '>=', 18)->pluck('price_male_nons')->toArray();
                    $pruChildPrice = count($gcp)?(float)$gcp[0]:0;
                    $pruChildTotalPrice += $pruChildPrice;
                }
            }

        }
        $page = self::calculateAge($forPrudPriceCheck['user_dob']);
        $gp = PlanPricingDisplay::where('pid', $forPrudPriceCheck['pid'])->where('tier','IO')->where('pricing_status', 1)->where('age1','<=',$page)->where('age2','>=',$page)->pluck('price_male_nons')->toArray();
        $pruMemPrice = count($gp)?(float)$gp[0]:0;

        if ($forPrudPriceCheck['tier'] == 'IO') {
            return number_format($pruMemPrice, 2);
        }
        if ($forPrudPriceCheck['tier'] == 'IS') {
            $pruSPrice = (float)$pruMemPrice+(float)$pruSpoPrice;
            return number_format($pruSPrice, 2);
        }

        if ($forPrudPriceCheck['tier'] == 'IC') {
            $pruChildPrice =(float)$pruChildTotalPrice +(float)$pruMemPrice;
            return number_format($pruChildPrice, 2);
        }
        if ($forPrudPriceCheck['tier'] == 'IF') {
            $pruFamPrice = (float)$pruChildTotalPrice + (float)$pruSpoPrice + (float)$pruMemPrice;
            return number_format($pruFamPrice, 2);
        }

    }

    public static function calculateAge($dob)
    {
        try{
            $birthDate = explode("-", $dob);
            $age = (date("md", date("U", mktime(0, 0, 0, $birthDate[1], $birthDate[2], $birthDate[0]))) > date("md")
                ? ((date("Y") - $birthDate[0]) - 1)
                : (date("Y") - $birthDate[0]));
            return $age;
        }
        catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function formatPlanData($plans , $paymenttype = null, $policy_id = null , $fetchType=''): array
    {
        $totalAmount = 0;
        $isMMPlan = false;
        $formattedPlans = [];
        $noCCFee = false;
        foreach ($plans as $key => $plan) {
            $PlanPrice = 0;
            $storePrice = 0;
            if ($plan['cid'] == 92) {
                $noCCFee = true;
            }
            if (isset($plan['pl_type']) && $plan['pl_type'] == "MM" && !($plan['cid'] == 77)) {
                $isMMPlan = true;
            }
            if(BundelPlan::where('bundle_pid',$plan['pid'])->count()){
                $bundelPlanId = BundelPlan::where('bundle_pid',$plan['pid'])->pluck('pid')->toArray();
                if(Plan::whereIn('pid',$bundelPlanId)->where('carrier',80)->exists()){
                    foreach ($bundelPlanId as $bID) {
                        $bundelPlan = Plan::where('pid',$bID)->where('forsale',1)->first();
                        if(($bundelPlan['plan_pricing_type'] == 2 && $bundelPlan['carrier'] == 80)){
                            $getDepInfo = self::depInfo($plan['policy_id']);
                            $forPrudPriceCheck['policy_id'] = $plan['policy_id'];
                            $forPrudPriceCheck['pid'] = $bID;
                            $forPrudPriceCheck['user_dob'] = $plan['user_dob'];
                            $forPrudPriceCheck['tier'] = $plan['tier'];
                            $forPrudPriceCheck['dep_count'] = count($getDepInfo);
                            $PlanPrice = self::prudPrice($forPrudPriceCheck , $getDepInfo);
                            $storePrice += (float)$PlanPrice;
                        }
                        else{
                            $planDetail['pid'] =$bID;
                            $planDetail['tier'] =$plan['tier'];
                            $planDetail['dob'] =$plan['user_dob'];
                            $planDetail['policy_id'] = $plan['policy_id'];
                            $planDetail['plan_pricing_type'] =$bundelPlan->plan_pricing_type;
                            $storePrice += (float)self::getPlanPricing($planDetail);
                        }
                    }
                }
                else{
                    if($plan['plan_pricing_type'] == 2 && $plan['cid'] == 80){
                        $getDepInfo = self::depInfo($plan['policy_id']);
                        $forPrudPriceCheck = $this->formatPrudentialParam($plan);
                        $forPrudPriceCheck['dep_count'] = count($getDepInfo);
                        $PlanPrice = self::prudPrice($forPrudPriceCheck , $getDepInfo);
                    }
                }
            }
            else{
                if($plan['plan_pricing_type'] == 2 && $plan['cid'] == 80){
                    $getDepInfo = self::depInfo($plan['policy_id']);
                    $forPrudPriceCheck = $this->formatPrudentialParam($plan);
                    $forPrudPriceCheck['dep_count'] = count($getDepInfo);
                    $PlanPrice = self::prudPrice($forPrudPriceCheck , $getDepInfo);
                }
            }
            if($storePrice != 0){
                $formattedPlans[$key]['price'] = round($storePrice,2);
            }
            else if($PlanPrice!=0){
                $formattedPlans[$key]['price'] = round($PlanPrice,2);
            }
            if(!isset($formattedPlans[$key]['price'])){
                $formattedPlans[$key]['price'] = round($plan['price_male_nons'],2);
            }
            $formattedPlans[$key]['web_display_name'] = $plan['web_display_name'];
            $formattedPlans[$key]['pid'] = $plan['pid'];
            $formattedPlans[$key]['tier'] = $plan['tier'];
            $formattedPlans[$key]['cid'] = $plan['cid'];
            $formattedPlans[$key]['plan_cat'] = isset($plan['plan_cat']) ? $plan['plan_cat'] :null;
            $formattedPlans[$key]['pl_type'] = isset($plan['pl_type']) ? $plan['pl_type'] :null;
            $formattedPlans[$key]['pstatus'] = isset($plan['pstatus']) ? $plan['pstatus'] :null;
            $formattedPlans[$key]['peffective_date'] = isset($plan['peffective_date']) ? $plan['peffective_date'] :null;
            if ($plan['pstatus'] == 1) {
                $totalAmount += $formattedPlans[$key]['price'];
            }
            if ($plan['pstatus'] == 2) {
                $formattedPlans[$key]['term_date'] = $plan['pterm_date'];
            }
            if ($plan['pstatus'] == 3) {
                $formattedPlans[$key]['withdrawn_date'] = $plan['pterm_date'];
            }
            if ($plan['pid'] == 924) {
                $disabilityData = $this->getDisabilityInfo($plan['plan_id']);
                $formattedPlans[$key]['BEP'] = $disabilityData->display_name;
                $formattedPlans[$key]['class'] = $disabilityData->class_id;
                $formattedPlans[$key]['amount'] = '$' . number_format((float)$disabilityData->benefit_amount, 2, '.', '');
            }
        }
        if($fetchType == 'external'){
            return $formattedPlans;
        }
        return $this->calculationPlansOther($formattedPlans , $paymenttype , $totalAmount , $isMMPlan , $policy_id, $noCCFee);

    }

    public function calculationPlansOther($formattedPlans , $paymenttype , $totalAmount , $isMMPlan , $policy_id, $noCCFee = false){
        if ($paymenttype == 'cc' && !$noCCFee) {
            $ccfees = 0.035 * $totalAmount;
            $formattedPlans['ccfees'] = "$" . number_format((float)$ccfees, 2, '.', '') ;
            $totalAmount += $ccfees;
        }
        $formattedPlans['recurring_monthly'] = "$" . number_format((float)$totalAmount, 2, '.', '');
        $formattedPlans['isMMPlan'] = $isMMPlan;

        $targetPids = explode(',', config('app.TARGET_PIDS_FOR_RENEWAL'));
        $targetDeltaDentalPids = explode(',', config('app.TARGET_PIDS_FOR_DELTA_DENTAL_RENEWAL'));
        $isRenewalTarget = false;
        $isRenewalDeltaDentalTarget = false;
        $isSolisticTarget = false;
        foreach ($formattedPlans as $plan) {
            if (isset($plan['pid']) && isset($plan['pstatus']) &&
                in_array((string)$plan['pid'], $targetPids) &&
                $plan['pstatus'] == "1") {
                $isRenewalTarget = true;
            }

            if (isset($plan['pid']) && isset($plan['pstatus']) &&
                in_array((string)$plan['pid'], $targetDeltaDentalPids) &&
                $plan['pstatus'] == "1") {
                $isRenewalDeltaDentalTarget = true;
            }

            if (isset($plan['cid'], $plan['pid']) &&
                $plan['cid'] == 11 &&
                $plan['pid'] > 1896
            ) {
                $isSolisticTarget = true;
            }
            if ($isRenewalTarget && $isSolisticTarget && $isRenewalDeltaDentalTarget) {
                break;
            }

        }
        $formattedPlans['is_renewal_target'] = $isRenewalTarget;
        $formattedPlans['is_solistic_target'] = $isSolisticTarget;
        $formattedPlans['is_delta_dental_target'] = $isRenewalDeltaDentalTarget;
        $recurringAmount = number_format((float)$totalAmount, 2, '.', '');
        $policy = Policy::where('policy_id', $policy_id)->select('pay_type')->first();

        if ($policy) {
            switch ($policy->pay_type) {
                case 'annual':
                    $formattedPlans['annual_amount'] = "$" . number_format($recurringAmount * 12, 2, '.', '');
                    break;
                case 'quarterly':
                    $formattedPlans['quarterly_amount'] = "$" . number_format($recurringAmount * 3, 2, '.', '');
                    break;
            }
        }

        $formattedPlans['recurring_monthly'] = "$" . number_format($recurringAmount, 2, '.', '');
        return $formattedPlans;
    }

    public function formatPrudentialParam($plan){
        return [
            'policy_id' => $plan['policy_id'],
            'pid' => $plan['pid'],
            'user_dob' => $plan['user_dob'],
            'tier' => $plan['tier'],
        ];
    }

    public static function getPlanPricing($planDetail){
        try{
            if ($planDetail['plan_pricing_type'] == '2') {
                $fetchPlan = PlanPricingDisplay::where('pid', $planDetail['pid'])
                    ->where('tier', $planDetail['tier'])
                    ->where('pricing_status', '1')
                    ->where('age1', '<=', self::calculateAge($planDetail['dob']))
                    ->where('age2', '>=', self::calculateAge($planDetail['dob']))
                    ->select('price_male_nons')
                    ->first();
            } else {
                $fetchPlan = PlanPricingDisplay::where('pid', $planDetail['pid'])
                    ->where('tier', $planDetail['tier'])
                    ->where('pricing_status', '1')
                    ->select('price_male_nons')
                    ->first();
            }
            return $fetchPlan['price_male_nons'];
        }
        catch(Exception $e) {
            return ['error' => $e->getMessage()];
        }

    }

    public function depInfo($policyId){
        return Dependent::
        join('dependents_in_policy', 'dependents_in_policy.dependent_id', '=', 'dependents.did')
        ->where('dependents_in_policy.policy_id', '=', $policyId)
        ->select('dependents.d_dob', 'dependents.d_relate', 'dependents.did','dependents.d_fname','dependents.d_lname')
        ->get();
    }

    private function getDisabilityInfo($planPricingId)
    {
        return DB::connection('mysql')->table('plan_pricing as pp')
            ->join('plan_benefit_mapping as pbm', 'pp.mapping_id', '=', 'pbm.id')
            ->join('benefit_elimination_period as bep', 'pbm.bep_id', '=', 'bep.id')
            ->where('pp.plan_pricing_id', $planPricingId)
            ->select('pbm.class_id', 'pbm.benefit_amount', 'bep.display_name')
            ->first();
    }

    private function mapMemberInfo(UserInfoPolicyAddress $userInfoPolicyAddress, array $emailsAndPhones): array
    {
        return [
            'user_id' => $userInfoPolicyAddress->userid,
            'name' => $userInfoPolicyAddress->full_name,
            'age' => $userInfoPolicyAddress->age,
            'email' => $userInfoPolicyAddress->cemail,
            'phone' => $userInfoPolicyAddress->phone1,
            'street_address1' => $userInfoPolicyAddress->address1,
            'street_address2' => $userInfoPolicyAddress->address2,
            'city' => $userInfoPolicyAddress->city,
            'state' => $userInfoPolicyAddress->state,
            'zip' => $userInfoPolicyAddress->zip,
            'cssn4' => $userInfoPolicyAddress->cssn4,
            'is_email_valid' => in_array($userInfoPolicyAddress->cemail, $emailsAndPhones['validatedEmails']),
            'is_phone1_valid' => in_array($userInfoPolicyAddress->phone1, $emailsAndPhones['validatedPhones']),
//            'is_usps_valid' => $this->checkAddress(
//                $userInfoPolicyAddress->address1,
//                $userInfoPolicyAddress->address2,
//                $userInfoPolicyAddress->city,
//                $userInfoPolicyAddress->state,
//                $userInfoPolicyAddress->zip
//            ),
            'is_usps_valid' => true,
        ];
    }

    private function mapDates(UserInfoPolicyAddress $userInfoPolicyAddress): array
    {
        $withDrawnDate = null;
        $termedDate = null;

        if ($userInfoPolicyAddress->plans->count() > 1) {
            $lastItem = $userInfoPolicyAddress->plans->sortByDesc('pterm_date')->first();

            if ($lastItem->status === "WITHDRAWN") {
                $withDrawnDate = $lastItem->pterm_date ? Carbon::parse($lastItem->pterm_date)->format('m/d/Y') : null;
            } else {
                $termedDate = $lastItem->pterm_date ? Carbon::parse($lastItem->pterm_date)->format('m/d/Y') : null;
            }
        } else {
            $firstItem = $userInfoPolicyAddress->plans->first();

            if ($firstItem->status === "WITHDRAWN") {
                $withDrawnDate = $firstItem->pterm_date ? Carbon::parse($firstItem->pterm_date)->format('m/d/Y') : null;
            } else {
                $termedDate = $firstItem->pterm_date ? Carbon::parse($firstItem->pterm_date)->format('m/d/Y') : null;
            }
        }
//        $nbInvoice = $this->getLatestNbInvoice($userInfoPolicyAddress->policy_id);

            return [
                'enrollment_date' => date('m/d/Y h:i:s A', $userInfoPolicyAddress->edate),
                'effective_date' => date('m/d/Y', strtotime($userInfoPolicyAddress->effective_date)),
                'eligibility' => [
                    'date' => $userInfoPolicyAddress->policyUpdate ? date('m/d/Y', $userInfoPolicyAddress->policyUpdate->elgb_act_date) : 0,
                    'act' => $userInfoPolicyAddress->policyUpdate ? $userInfoPolicyAddress->policyUpdate->elgb_act : 'N/A'
                ],
                'paid_through' => [
                    'date' => $this->getPaidThroughDate($userInfoPolicyAddress->paidThrough, $userInfoPolicyAddress->nbInvoice),
                    'processing_status' => $userInfoPolicyAddress->nbInvoice && $userInfoPolicyAddress->nbInvoice->payment_party_status == 'PROCESSING'
                        ? $userInfoPolicyAddress->nbInvoice->invoice_end_date . ' / $' . $userInfoPolicyAddress->nbInvoice->processing_amount
                        : ''
                ],
                'withdrawn_date' => $withDrawnDate,
                'termed_date' => $termedDate
            ];
        }

    private function getPaidThroughDate($paidThrough, $nbInvoice): string
    {
        if (!$paidThrough && !$nbInvoice) {
            return 'N/A';
        } elseif (!$paidThrough && $nbInvoice) {
            return Carbon::parse($nbInvoice->invoice_end_date)->format('m/d/Y');
        } elseif ($paidThrough && !$nbInvoice) {
            return Carbon::parse($paidThrough->payment_paid_through_date)->format('m/d/Y');
        } else {
            if (date($paidThrough->payment_paid_through_date) > date($nbInvoice->invoice_end_date)) {
                return Carbon::parse($paidThrough->payment_paid_through_date)->format('m/d/Y');
            } else {
                return Carbon::parse($nbInvoice->invoice_end_date)->format('m/d/Y');
            }
        }
    }

    private function mapAgentAndGroupInfo(UserInfoPolicyAddress $userInfoPolicyAddress): array
    {
        if ($userInfoPolicyAddress->is_benefit_store == 1) {
            $userInfoPolicyAddress->weburl = "enroll.purenroll.com";
        }
        $agentInfo = AgentInfo::where('agent_id', $userInfoPolicyAddress->agent_id)->select('agent_phone1')->first();
        return [
            'agent_id' => $userInfoPolicyAddress->agent_id,
            'name' => $userInfoPolicyAddress->agent_fname . ' ' . $userInfoPolicyAddress->agent_lname,
            'email' => $userInfoPolicyAddress->agent_email,
            'phone' => isset($agentInfo) ? $agentInfo->agent_phone1:"",
            'group_name' => $userInfoPolicyAddress->getGroup->gname ?? '',
            'eft_ach' => $userInfoPolicyAddress->payment_type == 'cc' ? '' : 'EFT/ACH INFO',
            'platform_name' => $userInfoPolicyAddress->is_benefit_store ? "enroll.purenroll.com" : $userInfoPolicyAddress->weburl,
            'platform_link' => filter_var($userInfoPolicyAddress->weburl, FILTER_VALIDATE_URL)
                ? $userInfoPolicyAddress->weburl
                : 'https://' . $userInfoPolicyAddress->weburl,
            'enrolledBy' => $userInfoPolicyAddress->plans->first()->eprocess == 'rep' ? 'Rep' : 'Member'
        ];
    }

    public function isUserRegistered(UserInfoPolicyAddress $userInfoPolicyAddress): bool
    {
        return
            ($userInfoPolicyAddress->activeUserLogin->exists ?? false) &&
            ($userInfoPolicyAddress->userActivities->count() > 0 ?? false);

    }

    private function mapBankInfo(UserInfoPolicyAddress $userInfoPolicyAddress): array
    {
        return [
            'initial_month' => $this->getBankInfo(
                $userInfoPolicyAddress->payment_type,
                $userInfoPolicyAddress->eftInfo,
                $userInfoPolicyAddress->ccInfo,
                $userInfoPolicyAddress->bill_date
            ),
            'recurring' => $this->getBankInfo(
                $userInfoPolicyAddress->recurring_payment_type,
                $userInfoPolicyAddress->recurringEftInfo,
                $userInfoPolicyAddress->recurringCCInfo,
                $userInfoPolicyAddress->bill_date
            )
        ];
    }

    private function getBankInfo($paymentType, $eftInfo, $creditCardInfo, $billDate)
    {
        $info = [];
        $info['payment_type'] = $paymentType;
        if ($paymentType == 'same') {
            return 'Same as Initial';
        }
        if ($paymentType == 'eft' && $eftInfo) {
            $info['bank_name'] = $eftInfo->bank_name;
            $info['account_number'] = 'xx' . $eftInfo->bank_account4;
        }
        if ($paymentType == 'cc' && $creditCardInfo) {
            $info['cc_num'] = ("XXXX-" . $creditCardInfo->cc_num4) ?? "";
            $info['exp_month'] = $creditCardInfo->cc_expmm ?? "";
            $info['exp_year'] = $creditCardInfo->cc_expyyyy ?? "";
        }
        $info['processing_date'] = $billDate;
        return $info;
    }

    public function checkTax($policyId)
    {
        return DB::connection('mysql')
                ->table('plan_overview')
                ->where('policy_id', $policyId)
                ->where('plan_cat', 'ENANAWU')
                ->exists()
            && DB::connection('mysql')
                ->table('ppo_uploads')
                ->where('policy_id', $policyId)
                ->doesntExist();
    }

    private function checkSignature($policyId): ?bool
    {
//        if (DB::connection('mysql')
//                ->table('plan_overview as p')
//                ->where('p.status', 'ACTIVE')
//                ->where('p.policy_id', $policyId)
//                ->where('p.esig', '1')
//                ->count() == 0) return null;

//        return DB::connection('mysql')
//            ->table('plan_overview as p')
//            ->join('signatures as s', 's.pid', 'p.policy_id')
//            ->where('p.status', 'ACTIVE')
//            ->where('pid', $policyId)
//            ->where('p.esig', '1');

        return Signature::query()
                ->where('pid', $policyId)
                ->count() > 0;
    }

    private function checkAddress($address1, $address2, $city, $state, $zip)
    {
        $user = config('validate.usps.key');
        $data = "XML=<AddressValidateRequest USERID='$user'>" .
            "<Address>" .
            "<Address1>$address1</Address1>" .
            "<Address2>$address2</Address2>" .
            "<City>$city</City>" .
            "<State>$state</State>" .
            "<Zip5>$zip</Zip5>" .
            "<Zip4></Zip4>" .
            "</Address>" .
            "</AddressValidateRequest>";
        $url = config('validate.usps.url');

        $response = GuzzleHelper::curlPostApi($url, $data, 'xml');
        if (isset($response['Address'])) {
            if (array_key_exists('Error', $response['Address'])) {
                $result['address1'] = ['Address1 is not valid'];
                return false;
            } else {
                $failedResult = [];
                if (strtolower($response['Address']['City']) != strtolower($city)) {
                    $failedResult['city'] = ['City is not valid'];
                }
                if ($response['Address']['Zip5'] != $zip) {
                    $failedResult['zip'] = ['Zip is not valid'];
                }
                if (strtolower($response['Address']['State']) != strtolower($state)) {
                    $failedResult['state'] = ['State is not valid'];
                }
                if ($failedResult == []) {
                    return true;
                }
                return false;
            }
        }
        return false;
    }

    private function getLatestNoteColor($policyId)
    {
        $latestNote = PolicyNote::where('policy_id', '=', $policyId)->latest()->first();
        try {
            return isset($latestNote)
                ? PolicyNote::$colors[ array_search($latestNote['status'], PolicyNote::$statuses, false) ]
                : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function checkIsPrudentialOr7kEnabled($policy_id)
    {
            $planTypes = ['ACCIDENT', 'CRITICAL', 'HOSPITAL', 'LIFE'];

            $prudentialPlans = PlanOverview::where('policy_id', $policy_id)
            ->whereIn('pl_type', $planTypes)
            ->where('cid', PrudentialClaim::PRUDENTIAL_CARRIER_ID);

            $sevenKPlans = PlanOverview::where('policy_id', $policy_id)
            ->where('7k_enable', 1);

        $prudentialOr7k = $prudentialPlans->union($sevenKPlans)
            ->exists();
            if($prudentialOr7k){
                return $prudentialOr7k;
            }
            else{
                $planIds = PlanOverview::where('policy_id',$policy_id)->pluck('pid')->toArray();
                $bundlePlanIds =  BundelPlan::whereIn('bundle_pid',  $planIds)->pluck('pid')->toArray();

                $prudentialPlansBundled = Plan::whereIn('pid', $bundlePlanIds)
                ->whereIn('pl_type', $planTypes)
                ->where('carrier', PrudentialClaim::PRUDENTIAL_CARRIER_ID);

                $sevenKPlansBundled = Plan::whereIn('pid', $bundlePlanIds)
                ->where('7k_enable', 1);

                return $prudentialPlansBundled->union($sevenKPlansBundled)
                ->exists();
            }
    }


    public function getIhaPlanStatus(UserInfoPolicyAddress $userInfoPolicyAddress){
        $ihaStatus = DB::table('vw_iha_member_status_info')
        ->where('policy_id', $userInfoPolicyAddress->policy_id)
        ->where('pstatus', 1)
        ->value('iha_dashboard_status');
        return $ihaStatus;
    }

    public function hasMemberReferralList(int $userId): bool
    {
        return MemberReferral::query()
            ->where('ref_by', '=', $userId)
            ->exists();
    }

    public function getMemberIdByPolicyId($userId)
    {
        $data = PlanPolicyMember::query()
            ->select('policy_id', 'member_id', 'pp.pterm_date', 'plan_policies_member.created_at')
            ->join('plan_policies as pp', 'policy_id', '=', 'pp.policy_num')
            ->where('plan_policies_member.userid', $userId)
            ->where(function ($query) {
                $query->where('pp.pterm_date', '>=', Carbon::today())
                    ->orWhereNull('pp.pterm_date');
            })
            ->distinct()
            ->get();

        $grouped = $data->groupBy('policy_id');

        // Apply filtering rules
        $filtered = $grouped->flatMap(function ($items) {
            if ($items->count() === 1) {
                return $items;
            }

            $uniquePolicyIds = $items->pluck('policy_id')->unique()->count();

            if ($uniquePolicyIds > 1) {
                // Case 1: return all members as-is (different policy_ids)
                return $items;
            }

            // Case 2: same policy_id, return only latest member based on created_at
            return $items->sortByDesc('created_at')->take(1);
        });

        return $filtered;
    }
}
