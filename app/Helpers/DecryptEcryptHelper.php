<?php

namespace App\Helpers;

class DecryptEcryptHelper
{
    public static function decryptInfo($info) {
        $target_url = 'https://corenroll.com/decrypt_ssn.php';
        $post = array('cssn'=>$info);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$target_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST,1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        return trim(curl_exec($ch));
    }
    public static function encryptInfo($info) {
        $target_url = 'https://corenroll.com/encrypt_ssn.php';
        $post = array('cssn'=>$info);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$target_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST,1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        return trim(curl_exec($ch));
    }
}
