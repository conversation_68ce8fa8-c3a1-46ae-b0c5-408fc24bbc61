<?php

namespace App\Helpers;

use App\AgentInfo;
use App\SsoUsers;
use App\UserDeviceID;
use App\PushNotificationLog;
use App\GroupUser;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class CommonHelper
{
    private static $agentVip = array();
    public static function getNoteStatus($statusID)
    {
        switch ($statusID) {
            case "1":
                return "Urgent";
                break;

            case "2":
                return "Important";
                break;

            case  "3":
                return "High";
                break;

            case  "4":
                return "Medium";
                break;

            case "5":
                return "Low";
                break;

            case "6":
                return "Informational";
                break;

            case "8":
                return "Special";
                break;
            default :
                return "Informational";
        }
    }
    public static function allowedExtensionForFiles()
    {
        return ["pdf", "doc", "docx", "xls", "xlsx", "csv"];
    }

    public static function fileValidation($file)
    {
        $memeType = $file->getMimeType();
        $extension = $file->getClientOriginalExtension();
        if (!in_array($extension, self::allowedExtensionForFiles())) {
            return ['error' =>'Invalid file selected.'];
        }
        return true;
    }
    public static function getAgentListForVIP($agentID)
    {
        if (!is_array($agentID)) $agentID = [$agentID];
        $agentList = AgentInfo::whereIn('agent_ga', $agentID)->pluck('agent_id')->toArray();
        if (!empty($agentList)) {
            self::$agentVip = array_merge(self::$agentVip, $agentList);
            self::getAgentListForVIP($agentList);
        }
        return self::$agentVip;
    }

    public static function format_phone($phone)
    {
        $phone = preg_replace("/[^0-9]/", "", $phone);

        if(strlen($phone) == 7)
            return preg_replace("/([0-9]{3})([0-9]{4})/", "$1-$2", $phone);
        elseif(strlen($phone) == 10)
            return preg_replace("/([0-9]{3})([0-9]{3})([0-9]{4})/", "($1) $2-$3", $phone);
        else
            return $phone;
    }

    public static function encryptInfo($info)
    {
        $target_url = 'https://corenroll.com/encrypt_pass_mobile_app.php';
        $post = array('password' => $info);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $target_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        $data = json_decode(curl_exec($ch));
        return isset($data->password) ? $data->password : '';
    }

    public static function maskSsn($number){
        return  str_repeat("X", strlen($number)-4) . "-".substr($number, -4);
    }

    public static function maskDob($dob){
        //masking of date format YYYY-MM-DD
        $year = Carbon::parse($dob)->format('Y');
        return  "XX/XX/{$year}";
    }

    public static function formattedDataWithComma($data)
    {
        return !($data == null || $data === '') ? $data . ',' : '';
    }

    public static function formattedAddress($address1, $address2, $city, $state, $zip)
    {
        $address1 = self::formattedDataWithComma($address1);
        $address2 = self::formattedDataWithComma($address2);
        $city = self::formattedDataWithComma($city);
        $state = self::formattedDataWithComma($state);
        $zip = $zip ? $zip : '';
        return "{$address1} {$address2} {$city} {$state} {$zip}";
    }

    public static function formattedAddress2($address2, $city, $state, $zip)
    {
        $address2 = self::formattedDataWithComma($address2);
        $city = self::formattedDataWithComma($city);
        $state = self::formattedDataWithComma($state);
        $zip = $zip ? $zip : '';
        return "{$address2} {$city} {$state} {$zip}";
    }

    public static function mapDropdownNameValue(array $values, bool $sameAsValue = true, array $names = []): array
    {
        return array_map(function ($value, $name) use ($sameAsValue) {
                return [
                    'name' => $sameAsValue ? $value : $name,
                    'value' => $value,
                ];
            }, $values, $names);
    }

    public static function formatDecimalWithoutRounding($number, $decimals): float
    {
        $power = pow(10, $decimals);
        if($number > 0){
            return (float) floor($number * $power) / $power;
        } else {
            return (float) ceil($number * $power) / $power;
        }
    }

    public static function isWordInString($string, $wordToCheck)
    {
        $pattern = "/\b" . preg_quote($wordToCheck, '/') . "\b/";
        return (bool) preg_match($pattern, $string);
    }

    public static function getAgentDownLine($id)
    {
        try{
            $target_url_bdv = config('corenroll.corenroll_api'). 'getdownlines.php';
            $post = array('agent_id'=>$id);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL,$target_url_bdv);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST,1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
            $responseData = curl_exec($ch);
            curl_close($ch);
            return explode("','", $responseData);
        }catch(Exception $e){
            Log::error('ERROR from getAgentDownLine  '. $e->getMessage());
            return [];
        }
    }

    public static function deleteSSOUser($email, $userType)
    {
        $ssoUsers = SsoUsers::where(['email' => $email, 'user_type' => $userType])->first();
        if ($ssoUsers instanceof SsoUsers) {
            UserDeviceID::where('user_id', $ssoUsers->id)->delete();
            PushNotificationLog::where('user_id', $ssoUsers->id)->forceDelete();
            $ssoUsers->delete();
            return true;
        }
        return false;
    }

    public static function removeGroupSSO($groupID)
    {
        $status = false;
        $groupUser = GroupUser::where('gid', $groupID)->get();
        foreach ($groupUser as $user) {
            $status = self::deleteSSOUser($user->groupuser_username, "G");
        }
        return ($status) ? true : false;
    }

    public static function addMemberSSO($userid)
    {
        try {
            $requestData = [
                "memberID" => $userid,
            ];
            $client = new Client();
            $request = $client->request('POST', config('notification.PUSH_NOTIFICATION_URL')."api.member/v1/sync-user", [
            'json' => $requestData,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ]
            ]);
            $body = $request->getBody()->getContents();
            return (object)json_decode(($body), true);
        } catch (\Exception $e) {
            $response = $e->getMessage();
            return (object)json_decode(($response), true);
        }
    }

    public static function getDownlineAgentList($agentID)
    {
        if (!is_array($agentID)) { $agentID = [$agentID]; self::$agentVip = array();  }
        $agentList = AgentInfo::whereIn('agent_ga', $agentID)->pluck('agent_id')->toArray();
        if (!empty($agentList)) {
            self::$agentVip = array_merge(self::$agentVip, $agentList);
            self::getDownlineAgentList($agentList);
        }
        return self::$agentVip;
    }

    public static function filterDependentsByName($query, $name)
    {
        $query->where(function ($match) use ($name) {
            $match->where('d_fname', 'LIKE', "%{$name}%")
                ->orWhere('d_lname', 'LIKE', "%{$name}%")
                ->orWhere('d_fname', '=', $name)
                ->orWhere('d_lname', '=', $name);
        })
        ->orWhereRaw("CONCAT(d_fname, ' ', d_lname) LIKE ?", ["%{$name}%"]);
    }
}
