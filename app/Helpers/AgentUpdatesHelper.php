<?php


namespace App\Helpers;


use App\AgentUpdate;

class AgentUpdatesHelper
{
    public function logAgentUpdates($data)
    {
        try {
            AgentUpdate::insert($data);
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public static function createAgentUpdateLog($data)
    {
        try {
            AgentUpdate::create($data);
        } catch (\Throwable $th) {
            return false;
        }
    }
}
