<?php

namespace App\Helpers;

use \GuzzleHttp\Exception\ClientException;

class GuzzleHelper
{
    public static function getApi($url, $header)
    {
        $client = new \GuzzleHttp\Client(['headers' => $header]);
        try {
            $request = $client->get($url);
        } catch (ClientException $e) {
            $body = $e->getResponse()->getBody()->getContents();
            return $body;
        }
        $body = $request->getBody()->getContents();
        return $body;
    }

    public static function getApiwithToken($url, $header, $token = null)
    {
        // Add the Bearer token to headers if provided
        if ($token) {
            $header['Authorization'] = 'Bearer ' . $token;
        }

        $client = new \GuzzleHttp\Client(['headers' => $header]);

        try {
            $request = $client->get($url);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $body = $e->getResponse()->getBody()->getContents();
            return $body;
        }

        $body = $request->getBody()->getContents();
        return $body;
    }
    public static function postApi($url, $header, $body)
    {
        $client = new \GuzzleHttp\Client(['headers' => $header]);
        try {
            $request = $client->post($url,  ['json' => $body]);
        } catch (ClientException $e) {
            $body = $e->getResponse()->getBody()->getContents();
            return $body;
        }
        $body = $request->getBody()->getContents();
        return $body;
    }

    public static function putApi($url, $header, $body)
    {
        $client = new \GuzzleHttp\Client(['headers' => $header]);
        try {
            $request = $client->put($url,  ['json' => $body]);
        } catch (ClientException $e) {
            $body = $e->getResponse()->getBody()->getContents();
            return $body;
        }
        $body = $request->getBody()->getContents();
        return $body;
    }

    public static function deleteApi($url, array $headers = [], array $data = [])
    {
        $client = new \GuzzleHttp\Client(['headers' => $headers]);
        $response = $client->request('DELETE', $url, [
            'headers' => $headers,
            'json' => $data 
        ]);

        return $response->getBody()->getContents();
    }

    public static function putApiWithToken($url, $header, $body, $token = null)
    {
        if ($token) {
            $header['Authorization'] = "Bearer $token";
        }
        $client = new \GuzzleHttp\Client(['headers' => $header]);

        try {
            $request = $client->put($url, ['json' => $body]);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $body = $e->getResponse()->getBody()->getContents();
            return $body;
        }

        $body = $request->getBody()->getContents();
        return $body;
    }

    public static function curlPostApi($url, $data, $flag = 'default', $header = null)
    {

        $curl = curl_init();
        if (!is_null($header)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json'));
        }
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

        if ($flag == 'xml') {
            return json_decode(json_encode(simplexml_load_string(curl_exec($curl))), true);
        }
        return json_decode(curl_exec($curl));
    }
}
