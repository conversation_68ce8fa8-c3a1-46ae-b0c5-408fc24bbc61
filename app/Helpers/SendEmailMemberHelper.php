<?php


namespace App\Helpers;

use App\AgentInfo;
use App\PlanOverview;
use App\CustomeHomepage;
use App\Policy;
use App\Service\MessageService;
use App\Traits\EmailRecipient;
use App\UserInfo;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use phpseclib\System\SSH\Agent;

class SendEmailMemberHelper
{
    use EmailRecipient;
    public static function sendEmailMember($policyId, $emailMessageConfiguration, $subject)
    {
        $policy = Policy::find($policyId);
        $userInfo = UserInfo::where('userid', $policy->policy_userid)->first();
        $memberFirstName = $userInfo->cfname;
        $memberLastName = $userInfo->clname;
        $memberEmail = $userInfo->cemail;
        $memberAltEmail = $userInfo->cemail_alt ? $userInfo->cemail_alt : '';
        $userEncoded = base64_encode($userInfo->userid);
        $enrollurl = "https://enroll.purenroll.com/member-home?uid=".$userEncoded;
        $a1 = $userInfo->phone1;
        $memberPhone = NULL;
        if (strlen($a1) == 10) {
            $memberPhone = "($a1[0]$a1[1]$a1[2]) $a1[3]$a1[4]$a1[5]-$a1[6]$a1[7]$a1[8]$a1[9]";
        }
        $agentFirstName = $policy->agentInfo->agent_fname;
        $agentLastName = $policy->agentInfo->agent_lname;
        if(isset($policy->agentInfo->agentCustomeHomepage)) {
            $agentEmail = $policy->agentInfo->agentCustomeHomepage->email;
            $b1 = $policy->agentInfo->agentCustomeHomepage->phone;
        } else {
           $agentEmail = $policy->agentInfo->agent_email;
            $b1 = $policy->agentInfo->agent_phone1;
        }
        $agentPhone = NULL;
        if (strlen($b1) == 10) {
            $agentPhone = "($b1[0]$b1[1]$b1[2]) $b1[3]$b1[4]$b1[5]-$b1[6]$b1[7]$b1[8]$b1[9]";
        }

        $policyPlanDetails = self::getPolicyPlanDetail($policyId);
        $temp = [
            'member_first_name' => $memberFirstName,
            'member_last_name' => $memberLastName,
            'member_phone' => $memberPhone,
            'member_email' => $memberEmail,
            'rep_first_name' => $agentFirstName,
            'rep_last_name' => $agentLastName,
            'rep_phone' => $agentPhone,
            'rep_email' => $agentEmail,
            'effective_date' => date('m-d-Y',strtotime($policy->effective_date)),
            'enrollnow' => $enrollurl,
        ];

        $contentData = array_merge($temp, $policyPlanDetails);

        $toAddress = config("testemail.TEST_EMAIL") ? [config("testemail.TEST_EMAIL")] : [$memberEmail];
        $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [$memberAltEmail];
        if (is_array($ccAddress)) {
            // Filter out null or empty values
            $ccAddress = array_filter($ccAddress);
        }
        $bccAddress = config("testemail.TEST_EMAIL_BCC") ? [config("testemail.TEST_EMAIL_BCC")] : [];

        if ($emailMessageConfiguration == 'MEMBER_MEDICAL_PLAN_WITHOUT_RIDER_EMAIL_NOTIFICATION' || 'MEMBER_PLAN_WITH_RIDER_APPROVED_EMAIL_NOTIFICATION' || 'ELEVATE_MEMBER_WELCOME_NOTIFICATION' || 'ELEVATE_MEMBER_BENEFIT_STORE_NOTIFICATION' || 'ELEVATE_MEMBER_REGISTER_MOBILE_APP_NOTIFICATION') {
            $ccAddress = config("testemail.TEST_EMAIL_CC") ? [config("testemail.TEST_EMAIL_CC")] : [$policy->agentInfo->agent_email];
        }
        $ccAddress = (new self())->addAddtionalCC($ccAddress,$policy->p_agent_num);
        $bccAddress = (new self())->addAddtionalBCC($bccAddress,$policy->p_agent_num);

        //to get uplineAgentsEmails
        $agent_id = Policy::where('policy_id', '=', $policyId)->value('p_agent_num');
        $ccAddress = UplineAgentsEmail::addUplineCcAddress($ccAddress, $agent_id);

        $body = [
            'email_message_configuration_name' => $emailMessageConfiguration,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'bccAddress' => $bccAddress,
            'subject' => $subject,
            'attachedFiles' => [],
            'generalTemplateData' => [],
            'contentData' => $contentData
        ];

        $apiUrl = config('app.messagecenter.key') . 'api/v1/send-email-with-content-data';
        $responseJson = GuzzleHelper::postApi($apiUrl, [], $body);
        $response = json_decode($responseJson, true);

        if (isset($response['status_code']) && $response['status_code'] == '200' && $response['status'] == 'success') {
            return array('type' => 'success', 'message' => 'Email Notification sent successfully.');
        } else {
            return array('type' => 'error', 'message' => 'Failed to send email.');
        }
    }

    public static function getPolicyPlanDetail($policyId)
    {
        $today = date('Y-m-d');
        $planOverview = PlanOverview::where('policy_id', $policyId)
            ->whereRaw("(pstatus = 1 or (pstatus = '2' and pterm_date > '$today'))")
            ->get();
        if (!$planOverview->first()) {
            $data = [
                'confirmation_no' => null,
                'plan_name' => null,
                'membership_fee' => null,
                'plan_tier' => null
            ];
            return $data;
        }
        $membershipFee = 0;
        $counter = 1;
        $plans = null;
        foreach ($planOverview as $plan) {
            $plans .= ' ' . $plan->web_display_name;
            if ($counter < count($planOverview)) {
                $plans .= ',';
            }
            if ($plan->is_assoc == '1') {
                $membershipFee += $plan->price_male_nons;
            } else {
                $plan_tier = $plan->tier;
            }
            $counter++;
        }
        $data = [
            'confirmation_no' => $policyId,
            'plan_name' => $plans,
            'membership_fee' => number_format($membershipFee, '2', '.', ','),
            'plan_tier' => $plan_tier
        ];
        return $data;
    }

    public static function sendBenefitEmail($bodyConfigs)
    {

        $planOverview = PlanOverview::where('policy_id', $bodyConfigs['policy_id'])
            ->whereIn('code834', ['membronze834', 'memsilver834', 'memgold834'])
            ->first();

        if ($planOverview != NULL) {
            if ($planOverview->code834 == "membronze834")
                $EmailMsgConfigName = "BRONZE";

            if ($planOverview->code834 == "memsilver834")
                $EmailMsgConfigName = "SILVER";

            if ($planOverview->code834 == "memgold834")
                $EmailMsgConfigName = "GOLD";

            $toAddress = [$planOverview->cemail];

            $userName = UserInfo::where('userid', $planOverview->userid)->first();

            // Creating body for the request
            $body = [
                'email_message_configuration_name' => $EmailMsgConfigName . '_MEMBERSHIP_ACTIVATION_NOTIFICATION',
                'toAddress' => $toAddress,
                'ccAddress' => [],
                'bccAddress' => [],
                'subject' => 'Congratulations! Your membership is active',
                'attachedFiles' => [],
                'generalTemplateData' => [],
                'contentData' => [
                    'member_first_name' => $userName->cfname
                ]
            ];

            // Send Benefit Email
            $apiUrl = config('app.messagecenter.key') . 'api/v1/send-email-with-content-data';
            $response = GuzzleHelper::postApi($apiUrl, [], $body);
        }
    }

    /**
     * @param $policy
     */
    public static function sendSmmrEmail($policy)
    {
        /**
         * @var  $policy Policy
         */
        $user = $policy->getMember;
        $toAddress = config("testemail.TEST_EMAIL") ?: $user->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: ($user->cemail_alt ? $user->cemail_alt : '');
        $emailConfigurationName = "SMMR_EMAIL";
        $subject = 'Secure my Medical Records';
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => "",
            'data' => [],
            'dataName' => $user->fullname

        ];
        $messageService = new MessageService();
        $messageService->sendEmailWithContentData($emailData);
    }


    /**
     * @param $policy
     */
    public static function sendUserEmployerEmail($policy)
    {
        /**
         * @var  $policy Policy
         */
        $user = $policy->getMember;
        $agentFirstName = $policy->agentInfo->agent_fname;
        $agentLastName = $policy->agentInfo->agent_lname;
        $agentFullName = $agentFirstName . ' ' . $agentLastName;
        if(isset($policy->agentInfo->agentCustomeHomepage)) {
            $agentEmail = $policy->agentInfo->agentCustomeHomepage->email;
            $b1 = $policy->agentInfo->agentCustomeHomepage->phone;
        } else {
           $agentEmail = $policy->agentInfo->agent_email;
            $b1 = $policy->agentInfo->agent_phone1;
        }
        $agentPhone = NULL;
        if (strlen($b1) == 10) {
            $agentPhone = "($b1[0]$b1[1]$b1[2]) $b1[3]$b1[4]$b1[5]-$b1[6]$b1[7]$b1[8]$b1[9]";
        }
        $toAddress = config("testemail.TEST_EMAIL") ?: $user->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: ($user->cemail_alt ? $user->cemail_alt : '');
        $emailConfigurationName = "POLICY_WITHDRAWN_EMAIL";
        $subject = 'User Employer Update';
//        $contentData = [
//            'Name' => $user->fullname,
//        ];
        $message = "This is automated notification to notify that your employer information has been updated on " . Carbon::now()->format('m/d/Y') . ".";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => [],
            'repname' => $agentFullName,
            'repphone' => $agentPhone,
            'repemail' => $agentEmail,
            'dataName' => $user->fullname
        ];
        $messageService = new MessageService();
        $messageService->sendEmailWithContentData($emailData);
    }

    public static function sendContractExpiryEmail($agent)
    {
        $log = Log::channel('agent_expiry_logs');

        $created_date = DB::table('rep_contract_act')
            ->select('ts as latest_created_contract')
            ->where('aid', '=', $agent->agent_id)
            ->orderBy('rcid', 'DESC')
            ->first();

        if (!$created_date) {
            $log->info("No contract date found for agent ID: {$agent->agent_id}");
            return;
        }

        //get latest Contract created date
        $latestCreatedContract = Carbon::createFromTimestamp($created_date->latest_created_contract);

        // Add 10 and 14 days respectively
        $fiveDayReminderDate = $latestCreatedContract->copy()->addDays(10);
        $oneDayReminderDate = $latestCreatedContract->copy()->addDays(14);

        // Calculate the number of days since last contract assign
        $daysSinceContractAssign = now()->diffInDays($latestCreatedContract);

        $subject = '';
        $logMessage = '';
        $emailConfigurationName = '';
        if (now()->isSameDay($fiveDayReminderDate)) {
            $emailConfigurationName = 'CONTRACT_DEADLINE_10_DAYS_AFTER_REGISTRATION';
            $subject = 'Action Required: Contract Signing Deadline Approaching';
            $logMessage = "Sending 5-day reminder email to agent ID: {$agent->agent_id}";
        } elseif (now()->isSameDay($oneDayReminderDate)) {
            $emailConfigurationName = 'CONTRACT_DEADLINE_14_DAYS_AFTER_REGISTRATION';
            $subject = 'Final Reminder: Contract Signing Deadline 24hrs left';
            $logMessage = "Sending 1-day reminder email to agent ID: {$agent->agent_id}";
        } elseif ($daysSinceContractAssign > 15) {
            try {
                $request = Request::create('api/v2/delete-agent', 'DELETE', [
                    'agent_id' => $agent->agent_id
                ]);

                $res = app()->handle($request);
                $responseBody = $res->getContent();

                $log->info("Soft deleted agent ID: {$agent->agent_id} with response body: {$responseBody}");
                return $responseBody;
            } catch (\Exception $e) {
                $log->error("Failed to soft delete agent ID: {$agent->agent_id}. Error: {$e->getMessage()}");
                return response()->json(['error' => 'Unable to delete agent at this time'], 500);
            }
        }

        if ($logMessage) {

            $log->info($logMessage);

            if ($emailConfigurationName) {

                $toAddress = [$agent->agent_email];

                $uplineAgent = AgentInfo::where('agent_id', $agent->agent_ga)->first();

                if (!isset($uplineAgent)) {
                    $log->info("Upline agent not found for agent ID: {$agent->agent_id}");
                    $ccAddress = [];
                } else {
                    $displayInfo = CustomeHomepage::where('agent_id', $uplineAgent->agent_id)->first();
                    $ccAddress = [$uplineAgent->agent_email];

                    if (!filter_var($ccAddress[0], FILTER_VALIDATE_EMAIL)) {
                        $log->info("Invalid upline email address in CC for agent ID: {$agent->agent_id}. Email: " . $ccAddress[0]);
                        $ccAddress = [];
                    }
                    $contentData = [
                        'agent_name' => "{$agent->agent_fname} {$agent->agent_mname} {$agent->agent_lname}",
                        'upline_agent_name' => "{$uplineAgent->agent_fname} {$uplineAgent->agent_mname} {$uplineAgent->agent_lname}",
                        'upline_agent_email' => $displayInfo ? $displayInfo->email : $uplineAgent->agent_email,
                        'upline_agent_phone' => $displayInfo ? CommonHelper::format_phone($displayInfo->phone) : CommonHelper::format_phone($uplineAgent->agent_phone1),
                    ];
                    $data = [
                        'email_message_configuration_name' => $emailConfigurationName,
                        'toAddress' => $toAddress,
                        'ccAddress' => !empty($ccAddress[0]) ? $ccAddress : [],
                        'bccAddress' => [],
                        'subject' => $subject,
                        'data' => $contentData,
                        'contentData' => $contentData,
                    ];
                    $messageService = new MessageService();
                    $response = $messageService->sendEmailWithTemplateAndData($data);

                    $log->info("Email response for agent ID: {$agent->agent_id}", [
                        'status' => $response['status'],
                        'message' => $response['message'],
                        'emailLogId' => $response['emailLogId'],
                    ]);
                }
            }
        }
    }

    public static function failedResponse($message = 'Failed To fetch data', $code = 400)
    {
        return response([
            'success' => false,
            'code' => $code,
            'message' => $message,
        ], $code);
    }
}
