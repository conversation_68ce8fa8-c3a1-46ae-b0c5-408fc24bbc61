<?php


namespace App\Helpers;

use App\Plan;
use App\PlanOverview;

class CheckPlanTypeHelper
{

    public static function checkPlanType($policyId)
    {
        // If consist any medical plan then $planType = 1, medical plan with other plan then $plantype = 2, if no medical plan then $plantype = 0(Ancillary)
        $planType = 0;
        $medicalPlanOverview = PlanOverview::where([
            ['policy_id', $policyId],
            ['pl_type', 'MM'],
            ['pstatus', '1']
        ])->count();
        if ($medicalPlanOverview > 0) {
            $planType = 1;
            $riderPlans = PlanOverview::where([
                ['policy_id', $policyId],
                ['pl_type', '!=', 'MM'],
                ['pstatus', '1'],
                ['is_assoc', '0']
            ])->count();
            if ($riderPlans > 0) {
                $planType = 2;   // Medical Plans with add on plans
            }
        }
        return $planType;
    }

    public static function checkSmmrPlan($policyId)
    {
        return PlanOverview::where([
            ['policy_id', $policyId],
            ['cid', PlanOverview::PLAN_OVERVIEW_SMMR_CID],
            ['pstatus', '1'],
        ])->exists();
    }

    public static function checkL713ProductByPolicy($policyId): bool
    {
        $status = false;
        $plan_id = PlanOverview::where('policy_id', $policyId)->pluck('pid');
        $require = Plan::whereIn('pid',$plan_id)->where('require_1_child' , 1)->get();
        if(count($require)>0){
            $status = true;
        }

        return $status;
    }

    public static function checkIHAPlan($policyId){
        $status = false;
        $plan_id = PlanOverview::where('policy_id', $policyId)->where('cid', '77')->get();
        if(count($plan_id)>0){
            $status = true;
        }
        return $status;
    }

    public static function isPlanExtraHealth($policyId): bool
    {
        $extraHealthPIDS = [1886, 1885];
        return PlanOverview::query()
            ->where('policy_id', '=', $policyId)
            ->whereIn('pid', $extraHealthPIDS)
            ->exists();
    }
}
