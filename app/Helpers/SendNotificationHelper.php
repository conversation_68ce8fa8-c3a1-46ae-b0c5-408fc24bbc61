<?php

namespace App\Helpers;

use App\PlanOverview;
use App\Policy;
use App\UserInfoPolicyAddress;
use GuzzleHttp\Client;

class SendNotificationHelper
{
    public static function sendNotification($notificationMessageData)
    {
        $client = new Client();
        $request = $client->request('POST', config('notification.PUSH_NOTIFICATION_URL')."api/v1/send-notification", [
            'json' => [
                "agent_id" => $notificationMessageData['agent_id'],
                "message_title" => $notificationMessageData['message_title'],
                "message_body" => $notificationMessageData['message_body'],
                "type" => isset($notificationMessageData['type'])  ? $notificationMessageData['type'] : '',
                "data" => isset($notificationMessageData['data'])  ? $notificationMessageData['data'] : '',
                "image" => isset($notificationMessageData['image'])  ? $notificationMessageData['image'] : ''
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);
        $body = $request->getBody()->getContents();
        return $body;
    }

    public static function sendNotificationMember($notificationMessageData)
    {
        $client = new Client();
        $request = $client->request('POST', config('notification.PUSH_NOTIFICATION_URL')."api.member/v1/send-notification", [
            'json' => [
                "member_id" => $notificationMessageData['agent_id'],
                "message_title" => $notificationMessageData['message_title'],
                "message_body" => $notificationMessageData['message_body'],
                "type" => isset($notificationMessageData['type'])  ? $notificationMessageData['type'] : '',
                "data" => isset($notificationMessageData['data'])  ? $notificationMessageData['data'] : '',
                "image" => isset($notificationMessageData['image'])  ? $notificationMessageData['image'] : ''
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);
        $body = $request->getBody()->getContents();
        return $body;
    }

    public static function sendMemberApprovalNotification($policyDetails){
        try {
            $approval = Policy::where('policy_id',$policyDetails->policy_id)->pluck('Approval')->first();
            $notice = ($approval == 1) ? 'Enrollment Approved' : 'Enrollment Rejected';
            $effeciveDate = date('m/d/Y',strtotime($policyDetails->effective_date));
            $termDate = date('m/d/Y',strtotime($policyDetails->term_date));
            $plansLists = PlanOverview::where('policy_id',$policyDetails->policy_id)->pluck('web_display_name')->toArray();
            $plans =  implode(',',$plansLists);
            $notificationData = [
                'agent_id' => $policyDetails->agent_id,
                'message_title' => $notice,
                'message_body' => "Action: $notice" . PHP_EOL . "Policy: $policyDetails->policy_id " . PHP_EOL . "Member: $policyDetails->cfname $policyDetails->clname" . PHP_EOL . "Eff Date: $effeciveDate".PHP_EOL."Plans: $plans",
                'data' => ['policy_id' => $policyDetails->policy_id],
                'type' => 'POLICY_TERMED'
            ];
            self::sendNotification($notificationData);
        } catch (Exception $e) {

        }
    }

    public static function sendReinstatePolicyNotification($policyDetails){
        try {
            $notice = 'Policy Reinstate';
            $effeciveDate = date('m/d/Y',strtotime($policyDetails->effective_date));
            $plansLists = PlanOverview::where('policy_id',$policyDetails->policy_id)->pluck('web_display_name')->toArray();
            $plans =  implode(',',$plansLists);
            $notificationData = [
                'agent_id' => $policyDetails->agent_id,
                'message_title' => $notice,
                'message_body' =>  "Member: $policyDetails->cfname $policyDetails->clname" . PHP_EOL . "Eff Date: $effeciveDate"
                                    .PHP_EOL."Rep: $policyDetails->agent_fname $policyDetails->agent_lname".PHP_EOL."Plans: $plans",
                'data' => ['policy_id' => $policyDetails->policy_id],
                'type' => 'POLICY_WITHDRAWN'
            ];
            self::sendNotification($notificationData);
        } catch (Exception $e) {

        }
    }

    public static function sendBankApprovalNotification($policyDetails,$data,$notice)
    {
        try {
            $notificationData = [
                'agent_id' => $policyDetails->agent_id,
                'message_title' => $notice,
                'message_body' => "Member: $policyDetails->cfname $policyDetails->clname" . PHP_EOL . "Rep: $policyDetails->agent_fname $policyDetails->agent_lname" . PHP_EOL . "Bank Name: " . $data['Bank Name'],
                'type' => 'General'
            ];
            self::sendNotification($notificationData);
        } catch (Exception $e) {

        }
    }
    public static function sendTerminationNotification($policyDetails,$termDate,$reasonFullName)
    {
        try {
            $plansLists = PlanOverview::where('policy_id',$policyDetails->policy_id)->pluck('web_display_name')->toArray();
            $plans =  implode(',',$plansLists);
            $effeciveDate = date('m/d/Y', strtotime($policyDetails->effective_date));
            $termDate = date('m/d/Y', strtotime($termDate));
              $notificationData = [
                'agent_id' => Policy::where('policy_id',$policyDetails->policy_id)->pluck('p_agent_num')->first(),
                'message_title' => 'Termination Notice',
                'message_body' => "Member: $policyDetails->cfname $policyDetails->clname".PHP_EOL."Reason: $reasonFullName".PHP_EOL."Eff Date: $effeciveDate".PHP_EOL."Rep: $policyDetails->agent_fname $policyDetails->agent_lname".PHP_EOL."Term Date: $termDate".PHP_EOL."Plans: ".$plans,
                'data'=> ['policy_id' => $policyDetails->policy_id],
                'type' => 'POLICY_TERMED'
            ];
            self::sendNotification($notificationData);
            $notificationData['message_body'] = "Reason: $reasonFullName".PHP_EOL."Eff Date: $effeciveDate".PHP_EOL."Rep: $policyDetails->agent_fname $policyDetails->agent_lname".PHP_EOL."Term Date: $termDate".PHP_EOL."Plans: ".$plans;
            self::sendNotificationMember($notificationData);
        } catch (Exception $e) {

        }
    }
    public static function sendWithdrawnNotification($policyDetails,$termDate,$reasonFullName)
    {
        try {
            $plansLists = PlanOverview::where('policy_id',$policyDetails->policy_id)->pluck('web_display_name')->toArray();
            $plans =  implode(',',$plansLists);
            $effeciveDate = date('m/d/Y', strtotime($policyDetails->effective_date));
            $termDate = date('m/d/Y', strtotime($termDate));
            $notificationData = [
                'agent_id' => Policy::where('policy_id',$policyDetails->policy_id)->pluck('p_agent_num')->first(),
                'message_title' => 'Withdraw Notice',
                'message_body' => "Member: $policyDetails->cfname $policyDetails->clname".PHP_EOL."Reason: $reasonFullName".PHP_EOL."Eff Date: $effeciveDate".PHP_EOL."Rep: $policyDetails->agent_fname $policyDetails->agent_lname".PHP_EOL."Term Date: $termDate".PHP_EOL."Plans: ".$plans,
                'data'=> ['policy_id' => $policyDetails->policy_id],
                'type' => 'POLICY_WITHDRAWN'
            ];
            self::sendNotification($notificationData);
            $notificationData['message_body'] ="Reason: $reasonFullName".PHP_EOL."Eff Date: $effeciveDate".PHP_EOL."Rep: $policyDetails->agent_fname $policyDetails->agent_lname".PHP_EOL."Term Date: $termDate".PHP_EOL."Plans: ".$plans;
            self::sendNotificationMember($notificationData);
        } catch (Exception $e) {

        }
    }

    public static function sendPushNotification($data)
    {
        $client = new Client();
        $data['user_id'] = config('notification_user.NOTIFICATION_USER') ? config('notification_user.NOTIFICATION_USER') : $data['user_id'];
        $request = $client->request('POST', config('send_notification.SEND_PUSH_NOTIFICATION').'api/v1/save-push-notification', [
            'json' => [
                "data" => $data['data'] ? $data['data'] : null,
                "message_title" => $data['message_title'],
                "message_body" => $data['message_body'],
                "type"=> $data['type'],
                "user_type" => "M",
                "user_id" => [
                    $data['user_id']
                ],
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);
        $body = $request->getBody()->getContents();
        return $body;
    }


}
