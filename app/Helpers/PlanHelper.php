<?php

namespace App\Helpers;

use App\AgentInfo;
use App\BundelPlan;
use App\CarrierInfo;
use App\Dependent;
use App\DependentInPolicy;
use App\IHAAdminPlanSelection;
use App\IHAPolicyStatusInfo;
use App\Plan;
use App\PlanBenefitMapping;
use App\PlanOverview;
use App\PlanPricingDisplay;
use App\Policy;
use App\PolicyUpdate;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PlanHelper
{

    public static function getAvailabePlans($planId = [])
    {
        return optional(PlanPricingDisplay::where("forsale", 1)
            ->where('web_display_name', '!=', '')
            ->where('forsale', 1)
            ->where('available', 1)
            ->where('is_assoc', '!=', 1)
            ->where('pricing_status', 1)
            ->where('available', 1)
            ->whereNotIn('pid', $planId)
            ->IHAL4()
            ->groupBy('pid')
            ->get(['pid', 'plan_name_system as plan_name']))->toArray();
    }

    public static function getPlanPricingWithTier($pid)
    {
        return PlanPricingDisplay::where('pid', $pid)
            ->where('pricing_status', 1)
            ->where('forsale', 1)
            ->get(['tier', 'price_male_nons as price']);
    }

    public static function disablityFunction($pricingId)
    {
        return PlanBenefitMapping::with(['benefitElimination'])
            ->has('benefitElimination')
            ->whereHas('planPricing', function ($q) use ($pricingId) {
                $q->where('plan_pricing_id', $pricingId);
            })
            ->first();
    }

    public static function disabiliyPlanName($planPricingId, $originalPlanName, $tier = '')
    {
        $tier = $tier ? "/$tier" : '';

        $data = self::disablityFunction($planPricingId);
        if (!$data instanceof PlanBenefitMapping) {
            return $originalPlanName . $tier;
        }

        return $originalPlanName . $tier . PHP_EOL . "[ BEP : " . $data->benefitElimination->display_name . " CLASS : " . $data->class_id . "  BENEFIT AMOUNT : $" . $data->benefit_amount . " ]";
    }

    public static function calculateAge($dob)
    {
        //y-m-d format expected
        $birthDate = explode("-", $dob);
        $age = (date("md", date("U", mktime(0, 0, 0, $birthDate[1], $birthDate[2], $birthDate[0]))) > date("md")
            ? ((date("Y") - $birthDate[0]) - 1)
            : (date("Y") - $birthDate[0]));
        return $age;
    }

    public static function calculatePrudentialPlanPrice($userDetials)
    {
        $memberAge = self::calculateAge(date('Y-m-d', strtotime($userDetials->user_dob)));
        $pruMemberPrice = PlanPricingDisplay::where([
            'pid' => $userDetials->pid,
            'tier' => 'IO',
            'pricing_status' => 1
        ])
            ->whereRaw(" age2 >= $memberAge AND age1 <= $memberAge ")->pluck('price_male_nons')->first();
        $pruMemberPrice = $pruMemberPrice / 0.92;

        //calculate child's age as it will be fixed
        $pruChildPrice = PlanPricingDisplay::where([
            'pid' => $userDetials->pid,
            'tier' => 'IC',
            'pricing_status' => 1
        ])
            ->whereRaw("age2 >= 18 AND age1 <=24")->pluck('price_male_nons')->first();
        $prudChildrenPrice = $pruChildPrice / 0.92;
        return self::getPlanPrice($userDetials, $pruMemberPrice, $prudChildrenPrice);

    }

    public static function getPlanPrice($userDetials, $pruMemberPrice, $prudChildrenPrice)
    {

        $planPrice = 0.00;
        $dependentCount = 0;
        if (in_array($userDetials->tier, ['IF', 'IC'])) {
            $dependentCount = DependentInPolicy::where([
                'policy_id' => $userDetials->policy_id,
                'userid' => $userDetials->userid
            ])->count();
        }

        switch ($userDetials->tier) {
            case 'IO':
                $planPrice = $pruMemberPrice;
                break;

            case 'IS':
                $spouseDob = Dependent::where('userid', $userDetials->userid)->pluck('d_dob')->first();
                $spouseDob = self::calculateAge(date('Y-m-d', strtotime($spouseDob)));

                $pruSpoPrice = PlanPricingDisplay::where([
                    'pid' => $userDetials->pid,
                    'tier' => 'IS',
                    'pricing_status' => 1
                ])
                    ->whereRaw(" age2 >= $spouseDob AND age1 <= $spouseDob ")->pluck('price_male_nons')->first();
                $planPrice = $pruMemberPrice + ($pruSpoPrice / 0.92);
                break;

            case 'IC':
                $planPrice = $dependentCount * $prudChildrenPrice;
                break;

            case 'IF':
                $spouseDob = Dependent::where('userid', $userDetials->userid)->where('d_relate', 'S')->pluck('d_dob')->first();
                $spouseDob = date('Y-m-d', strtotime($spouseDob));
                $spouseDob = self::calculateAge($spouseDob);

                $pruSpoPrice = PlanPricingDisplay::where([
                    'pid' => $userDetials->pid,
                    'tier' => 'IF',
                    'pricing_status' => 1
                ])
                    ->whereRaw(" age2 >= $spouseDob AND age1 <= $spouseDob ")->pluck('price_male_nons')->first();
                $pruSpousePrice = ($pruSpoPrice / 0.92);
                $planPrice = $pruSpousePrice + (($dependentCount - 1) * $prudChildrenPrice);
                break;
        }
        return number_format($planPrice, 2);
    }

    /**
     * @param $tier
     * @return string
     */
    public static function tierMapping($tier): string
    {
        return [
            'IO' => 'Member Only',
            'IS' => 'Member + Spouse',
            'IC' => 'Member + Children',
            'IF' => 'Family'
        ][$tier];

    }

    public static function plansWiseMessage($policyID): string
    {
        return PlanOverview::where('policy_id', $policyID)
            ->whereIn('cid', CarrierInfo::SPECIAL_MM_CID)
            ->where('status', Policy::ACTIVE_STATUS)
            ->exists()
            ? CarrierInfo::SPECIAL_MM_CARRIER_MESSAGE
            : '';
    }

    public static function isBundleHasPrudentailPlan(int $planId): bool
    {
        $mainBundlePlanIsPrudential = BundelPlan::query()
            ->whereHas('plan', function ($q) {
                $q->where('carrier', 80);
            })
            ->where('bundle_pid', $planId)
            ->exists();

        $subPlanIsPrudentails = Plan::whereIn('pid', BundelPlan::query()
            ->where('bundle_pid', $planId)
            ->pluck('pid'))->where('carrier', 80)->exists();

        return $mainBundlePlanIsPrudential || $subPlanIsPrudentails;
    }

    public static function isHospitalOrCriticalPrudentialPlan(int $planId): bool
    {
        /* we have different calculaton fro Prudentail cid 80 hospital and crtical, but later one we got similary calcuation for some salary based prudentials */
        return Plan::query()
                ->where('pid', $planId)
                ->where('carrier', 80)
                ->whereIn('pl_type', ['HOSPITAL', 'CRITICAL'])
                ->exists() || in_array($planId, Plan::PRUDENTIAL_SALARY_BELOW_100k);
    }

    public static function getAssignedPlans($planId = [])
    {

        return optional(PlanPricingDisplay::where("forsale", 1)
            ->where('web_display_name', '!=', '')
            ->where('forsale', 1)
            ->where('available', 1)
            ->where('is_assoc', '!=', 1)
            ->where('pricing_status', 1)
            ->whereIn('pid', $planId)
            ->IHAL4()
            ->groupBy('pid')
            ->get(['pid', 'plan_name_system as plan_name']))->toArray();
    }

        public static function getAssignedPlansGroup($planId = [])
    {

        return optional(PlanPricingDisplay::where("forsale", 1)
            ->where('web_display_name', '!=', '')
            ->where('forsale', 1)
            ->where('is_assoc', '!=', 1)
            ->where('pricing_status', 1)
            ->groupBy('pid')
            ->get(['pid', 'plan_name_system as plan_name']))->toArray();
    }

    public static function planSwitchEfeectiveDate(): string
    {
        /* effective lower than this date will able to change the plan */

        return date('Y-m-d', strtotime('first day of january next year'));
        // return Carbon::now()->addYear()->format('Y-m-d');
    }

    public static function getAllEligiblePolicy()
    {
        // $excludedusers = IHAAdminPlanSelection::pluck('userid')->toArray();

        $excludedusers = self::alreadySwitchedPolicy();
        return PlanOverview::where([
            /* ['pl_type', 'MM'],*/
            ['pstatus', 1],
            ['status', 'ACTIVE'],
            ['effective_date', "<", PlanHelper::planSwitchEfeectiveDate()]
        ])
            ->where(function ($query) {
                $query->where('brand', Plan::$L713Brand)
                ->orWhere(function ($query1) {
                    $query1->where('cid', Policy::MAJOR_MEDICAL_IHA_CARRIER_ID)
                    ->whereIn('policy_id', self::planSwitchIHAPolicyEnableList());
                });
            })
            ->whereNotIn('policy_id', $excludedusers)
            //->whereNotIn('userid', [132873,132873,132852,133178,133179])

            ->pluck('policy_id');
    }

    public static function alreadySwitchedPolicy()
    {
        return PolicyUpdate::where('elgb_act', 'IHASWITCH')->pluck('elgb_policyid');
    }

    /**
     * @return mixed
     */
    /* excuding policies with specific status */
    public static function planSwitchIHAPolicyEnableList()
    {
            /* list of active IHA plans */
        return IHAPolicyStatusInfo::whereIn('iha_dashboard_status', PlanHelper::includedIHAStatus())->pluck('policy_id');
    }

    public static function includedIHAStatus()
    {
        return [
            'RENEWAL-SIGNATURE-RECEIVED-READY-TO-ACTIVATE',
            'ACTIVE'
        ];
        /*return [
            "PENDING-UNDERWRITING",
            "PENDING-SIGNATURE",
            "SIGNATURE-RECEIVED-READY-TO-ACTIVATE",
            "TERMED",
            "PENDING-RENEWAL-UNDERWRITING",
            "PENDING-RENEWAL-SIGNATURE",
            "PLAN-SWITCH-SIGNATURE-RECEIVED-READY-TO-ACTIVATE",
            "PLAN-SWITCH-PENDING-SIGNATURE"
        ];*/
    }

    public static function ifExtraHealthGroup($policyID): bool {
        return Policy::where('policy_id', $policyID)->value('eid') == 4385 || in_array(Policy::where('policy_id', $policyID)->value('p_agent_num'),AgentInfo::EXTRA_HEALTH_REPS);
    }

}
