<?php

namespace App\Helpers;

use App\Helper\EmailCCHelper;
use App\Jobs\NotifyPolicyEmailJob;
use App\PlanPolicy;
use App\Policy;
use App\GroupInfo;
use App\EmailPreference;
use App\Service\MessageService;
use App\Traits\EmailRecipient;
use App\UserInfoPolicyAddress;
use App\CompanyInformation;
use App\AgentInfo;
use App\PlanOverview;
use App\Dependent;
use Carbon\Carbon;

class EmailSendHelper
{
    use EmailRecipient;

    private $bccEmail = [];
    private $ccEmail = [];
    private $ccUplineEmails;

    public static function readEmailPref($code, $type)
    {
        $emailPreference = optional(EmailPreference::where('pref-code', $code)->first())->toArray();
        if ($type == '0' && $emailPreference['pref-enabled'] == '1') {
            return true;
        } else {
            if ($type == '1' && $emailPreference['pref-cc-agent']) {
                return true;
            } else {
                return false;
            }
        }
    }

    public static function companyPhone()
    {
        $companyPhone = CompanyInformation::where('company_id', 1)->pluck('support_800')->first();
        return "(" . substr($companyPhone, 0, 3) . ") " . substr($companyPhone, 3, 3) . "-" . substr($companyPhone, 6, 12);
    }

    public static function getGroupInfo($uid)
    {
        $groupInfoData = optional(GroupInfo::where('gid', $uid)->first())->toArray();
        if ($groupInfoData) {
            $pfa = $groupInfoData['gphone'];
            $pf = "$pfa[0]$pfa[1]$pfa[2]-$pfa[3]$pfa[4]$pfa[5]-$pfa[6]$pfa[7]$pfa[8]$pfa[9]";
            $data = "<b>$groupInfoData[gname]</b> <br/>$groupInfoData[gaddress1] $groupInfoData[gaddress2]<br />$groupInfoData[gcity], $groupInfoData[gstate] $groupInfoData[gzip]<br />$pf";
            return $data;
        }
    }

    public static function sendGenericPolicyEmailUser($userID, $subject, $message, $action, $data = [], $actionCode = '')
    {
        try {
            $userPolicyData = UserInfoPolicyAddress::where('userid', $userID)
                ->get(['eid', 'term_date', 'cemail', 'agent_email', 'cfname', 'clname', 'policy_id', 'effective_date', 'weburl', 'recurring_payment_type'])->first()->toArray();
            $emailCC = [];
            $emailBCC = [];
            array_push($emailBCC, '<EMAIL>');
            $emailDetails = self::getUserAgentEmail($actionCode, $userPolicyData);
            if ($emailDetails === false) return;
            $email = $emailDetails['email'];
            if ($emailDetails['agent_email'] != '') array_push($emailCC, $emailDetails['agent_email']);
            $messageTop = "This is an automated notice that the information about " . strtolower($message) . " on " . date('m/d/Y', time()) . '.';
            if ($subject == 'Billing Information EFT') {
                $messageTop = "This is an automated notice that your account billing information for eft has been updated on " . date('m/d/Y', time()) . '.';
            }
            $emailData = [
                'messageTop' => $messageTop,
                'middleMessage' => $message,
                'data' => $data,
                'companyPhone' => self::companyPhone(),
                'subject' => $subject,
                'templateName' => 'genericUserEmail',
            ];
            if ($action == 'selfrecurringstatus') {
                $emailData['monthlyPremium'] = PolicyUpdateHelper::getMonthlyPreium($userID);
                $monthlyPremium = $emailData['monthlyPremium'];
                $recurringType = strtoupper($userPolicyData['recurring_payment_type']);
                $emailData['messageTop'] = "This is to notifiy that your billing method has been switched to $recurringType . We will use this bank to charge your monthly premium $$monthlyPremium every month.";
                if ($recurringType != 'EFT') {
                    $emailData['data'] = [];
                    $emailData['middleMessage'] = 'Payment method switched to ' . $recurringType;
                    $emailData['messageTop'] = "This is to notifiy that your billing method switched to $recurringType.";
                }
            }
            $emails = config('testemail.TEST_EMAIL') ? [config('testemail.TEST_EMAIL')] : [$email];
            $emailCC = config('testemail.TEST_EMAIL_CC') ? [config('testemail.TEST_EMAIL_CC')] : $emailCC;
            $emailBCC = config('testemail.TEST_EMAIL_BCC') ? [config('testemail.TEST_EMAIL_BCC')] : $emailBCC;
            $job = (new NotifyPolicyEmailJob($emailData, $emails, $emailCC, $emailBCC));
            dispatch($job);
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    public static function sendGenericPolicyEmailUserPolicyDetails($policyID, $subject, $message, $action, $actionCode = '', $reason = '', $planPolicyIds = [])
    {
        try {
            $userPolicyData = UserInfoPolicyAddress::where('policy_id', $policyID)
                ->get(['eid', 'term_date', 'cemail', 'agent_email', 'cfname', 'cmname', 'clname', 'policy_id', 'effective_date', 'weburl', 'agent_id'])
                ->first()
                ->toArray();

            if (is_array($planPolicyIds) && count($planPolicyIds) >= 1) {
                $planOverview = PlanOverview::whereIn('p_ai', $planPolicyIds)->pluck('web_display_name')->toArray();
                $termDate = PlanPolicy::whereIn('p_ai', $planPolicyIds)->pluck('pterm_date')->first();
            } else {
                $planOverview = PlanOverview::where('policy_id', $policyID)->pluck('web_display_name')->toArray();
                $termDate = $userPolicyData['term_date'];
            }
            $plans = '';
            foreach ($planOverview as $key => $plan) {
                $plans .= 1 + $key . ') ' . $plan . '<br>';
            }
            $emailCC = [];
            $emailBCC = [];
            array_push($emailBCC, '<EMAIL>');
            if ($action == 'withdraw' || $action == 'terminate' || $action == "reinstate") {
                $repsToGetEmails = [1901];
                foreach ($repsToGetEmails as $repID) {
                    $downlineRepsRob = CommonHelper::getAgentListForVIP($repID);
                    if (in_array($userPolicyData['agent_id'], $downlineRepsRob)) {
                        $repEmail = AgentInfo::where('agent_id', $repID)->pluck('agent_email')->first();
                        if ($repEmail != '') {
                            array_push($emailBCC, $repEmail);
                        }
                    }
                }
            }
            $emailDetails = self::getUserAgentEmail($actionCode, $userPolicyData);
            if ($emailDetails === false) return;
            $email = $emailDetails['email'];
            if ($emailDetails['agent_email'] != '') array_push($emailCC, $emailDetails['agent_email']);
            $messageTop = "This is an automated notice to notify that the " . strtolower($message);
            $messageTop .= ($action != 'terminate') ? " on " . date('m/d/Y', time()) . '.' : ':';
            $middleName = $userPolicyData['cmname'] != '' ? " " . $userPolicyData['cmname'] : "";

            $effectiveDate = ($userPolicyData['effective_date'] != '') ? date('m/d/Y', strtotime($userPolicyData['effective_date'])) : '';
            $data = [
                "Confirmation #" => $userPolicyData['policy_id'],
                "Primary Name" => $userPolicyData['cfname'] . $middleName . " " . $userPolicyData['clname'],
                "Plan Name" => $plans,
                "Eff. Date" => $effectiveDate,
                "Term Date" => ($termDate != '') ? date('m/d/Y', strtotime($termDate)) : '',
            ];
            if ($action == "memberapproval" || $action == "reinstate" || $action == "defaultrecurring" || $action == 'memberrejection') {
                unset($data['Eff. Date']);
                unset($data['Term Date']);
            }
            $templateName = 'genericUserEmailPolicyData';
            if ($action == 'memberapproval') {
                $data['Eff. Date'] = $effectiveDate;
                $templateName = 'genericUserEmailPolicyDataMemberApproval';
            }
            $emailData = [
                'messageTop' => $messageTop,
                'middleMessage' => '',
                //'groupInfo' => $groupInfo,
                'companyPhone' => self::companyPhone(),
                'data' => $data,
                'subject' => $subject,
                'reason' => $reason,
                'effectiveDate' => $effectiveDate,
                'templateName' => $templateName,
            ];
            $emails = config('testemail.TEST_EMAIL') ? [config('testemail.TEST_EMAIL')] : [$email];
            $emailCC = config('testemail.TEST_EMAIL_CC') ? [config('testemail.TEST_EMAIL_CC')] : $emailCC;
            $emailBCC = config('testemail.TEST_EMAIL_BCC') ? [config('testemail.TEST_EMAIL_BCC')] : $emailBCC;
            $job = (new NotifyPolicyEmailJob($emailData, $emails, $emailCC, $emailBCC));
            dispatch($job);
            return true;
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    public static function getUserAgentEmail($actionCode, $userPolicyData)
    {
        $email = '';
        $agentEmail = '';
        if ($actionCode != '') {
            if (EmailSendHelper::readEmailPref($actionCode, 0)) {
                $email = $userPolicyData['cemail'];
            }
            if (EmailSendHelper::readEmailPref($actionCode, 1)) {
                if ($email == '') {
                    $email = $userPolicyData['agent_email'];
                } else {
                    $agentEmail = $userPolicyData['agent_email'];
                }
            }
            if ($email == '') return false;
        } else {
            $email = $userPolicyData['cemail'];
            $agentEmail = $userPolicyData['agent_email'];
        }
        return ['email' => $email, 'agent_email' => $agentEmail];
    }

    public static function sendPolicyActionEmail($policyId, $subject, $message, $action, $actionCode = '', $reason = '', $planPolicyIds = [] , $groupEmails='')
    {
        $policy = Policy::find($policyId);
        
        $toAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policy->getMember->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : [
            $policy->agentInfo->agent_email,
            $groupEmails,
            $policy->getMember->cemail_alt
        ];

        if (is_array($ccAddress)) {
            // Filter out null or empty values
            $ccAddress = array_filter($ccAddress);
        }

        $agentFirstName = $policy->agentInfo->agent_fname;
        $agentLastName = $policy->agentInfo->agent_lname;
        $agentFullName = $agentFirstName . ' ' . $agentLastName;
        if(isset($policy->agentInfo->agentCustomeHomepage)) {
            $agentEmail = $policy->agentInfo->agentCustomeHomepage->email;
            $b1 = $policy->agentInfo->agentCustomeHomepage->phone;
        } else {
            $agentEmail = $policy->agentInfo->agent_email;
            $b1 = $policy->agentInfo->agent_phone1;
        }
        $agentPhone = NULL;
        if (strlen($b1) == 10) {
            $agentPhone = "($b1[0]$b1[1]$b1[2]) $b1[3]$b1[4]$b1[5]-$b1[6]$b1[7]$b1[8]$b1[9]";
        }
        $tiers = [
            'IF' => "Family",
            "IO" => "Member Only",
            "IS" => "Member and Spouse",
            "IC" => "Member and Child(ren)"
        ];

        $newTier = null;
        $newPlan = null;

        if($action === 'policytier') {
            $messageArray = explode(' ', $message);
            $newTier = $messageArray[count($messageArray) - 1];

            if (in_array($newTier, array_keys($tiers))) {
                $newTier = $tiers[$newTier];
                $messageArray[count($messageArray) - 1] = $newTier;
                $message = implode(' ', $messageArray);
            }
        }

        $dataName = isset($policy->getMember) ? $policy->getMember->fullname : '';
        $messageTop = "This is an email notification to inform you that the " . strtolower($message);
        $messageTop .= ($action != 'terminate') ? " on " . date('m/d/Y', time()) . '.' : '.';

        $userPolicyData = UserInfoPolicyAddress::query()
            ->where('policy_id', $policyId)
            ->get(['eid', 'term_date', 'cemail', 'agent_email', 'cfname', 'cmname', 'clname', 'policy_id', 'effective_date', 'weburl', 'agent_id'])
            ->first()
            ->toArray();
        if (is_array($planPolicyIds) && count($planPolicyIds) >= 1) {
            $planOverview = PlanOverview::whereIn('p_ai', $planPolicyIds)->select(['web_display_name', 'pterm_date', 'tier', 'cid'])->get()->toArray();
            $termDate = PlanPolicy::whereIn('p_ai', $planPolicyIds)->pluck('pterm_date')->first();
        } else {
            //            $planOverview = PlanOverview::where('policy_id', $policyId)->pluck('web_display_name')->toArray();
            $planOverview = PlanOverview::where('policy_id', $policyId)->select(['web_display_name', 'pterm_date', 'peffective_date', 'tier', 'cid'])->get()->toArray();
            $termDate = $userPolicyData['term_date'];
        }
        $plans = '';

        foreach ($planOverview as $key => $plan) {
            $index = 1 + $key . " )";
            $planName = $plan['web_display_name'];
            $tier = $plan['tier'];
            if ($action == 'reinstate') {
                $planOverviewEffDate = isset($plan['peffective_date']) ? Carbon::parse($plan['peffective_date'])->format('m/d/Y') : '';
                $newPlanEffDate = "<span style='color: green'>{$planOverviewEffDate}</span>";
                if (in_array($tier, array_keys($tiers))) {
                    $tier = $tiers[$tier];
                }
                $plans .= "{$index} {$planName} / {$tier} - {$newPlanEffDate}<br/>";
            } else {
                $planOverviewTermDate = isset($plan['pterm_date']) ? ' - ' . Carbon::parse($plan['pterm_date'])->format('m/d/Y') : "";
                $newPlanTermDate = "<span style='color: red'>{$planOverviewTermDate}</span>";
                if (in_array($tier, array_keys($tiers))) {
                    $tier = $tiers[$tier];
                }

                if($action === 'policytier') {
                    $plans .= "{$index} {$planName} / {$tier} <br/>";
                    $newPlan .= "{$index} {$planName} / {$newTier} <br/>";
                }
                else {
                    $plans .= "{$index} {$planName} / {$tier} {$newPlanTermDate}  <br/>";
                }
            }
        }

        $dependents = Dependent::join('dependents_in_policy', 'dependents_in_policy.dependent_id', '=', 'dependents.did')
            ->where('dependents_in_policy.policy_id', '=', $policyId)
            ->select('dependents.d_fname', 'dependents.d_mname', 'dependents.d_lname', 'dependents.d_dob', 'dependents.d_relate', 'dependents.did', 'dependents.d_gender', 'dependents.d_ssn', 'dependents.additional_notes AS d_notes', 'dependents.dhrq','dependents.dhrq2','dependents.dwrq')
            ->get();
        $dependentName = '';
        foreach ($dependents as $key => $dependent) {
            $index = 1 + $key . ")";
            $fullname = $dependent['d_fname'];
            if ($dependent['d_mname'] !== null) {
                $fullname .= ' ' . $dependent['d_mname'];
            }
            $fullname .= ' ' . $dependent['d_lname'];
            if ($dependent['d_relate'] == "0" || $dependent['d_relate'] == "S") {
                $relation = "Spouse";
            } else if($dependent['d_relate'] == "1" || $dependent['d_relate'] == "C") {
                $relation = "Child";
            } else if ($dependent['d_relate'] == "O") {
                $relation = "Other";
            }
            $dependentName .= "{$index} {$fullname} - {$relation} <br/>";

        }

        $effectiveDate = ($userPolicyData['effective_date'] != '') ? date('m/d/Y', strtotime($userPolicyData['effective_date'])) : '';

        if($action === 'policytier') {
            $contentData = [
                'Confirmation #' => $policyId,
                'Primary Name' => $dataName,
                'Plan Name' => preg_replace('/\d+\s*\)\s*/', '', $plans),
                'New Plan Name' => preg_replace('/\d+\s*\)\s*/', '', $newPlan),
                'Effective Date' => $effectiveDate,
                //            'Term Date' => ($termDate != '') ? date('m/d/Y', strtotime($termDate)) : '',
            ];
        }
        else {
            $contentData = [
                'Confirmation #' => $policyId,
                'Primary Name' => $dataName,
                'Plan Name' => $plans,
                'Effective Date' => $effectiveDate,
                //            'Term Date' => ($termDate != '') ? date('m/d/Y', strtotime($termDate)) : '',
            ];
        }

        if ($subject == "Member Approval Notice" && $dependents->isNotEmpty()) {
            $dependent = [
                'Dependent' => $dependentName
            ];
        } else {
            $dependent = null;
        }

//        if ($termDate == '' || $termDate == null) unset($contentData['Term Date']);
        if ($action == "memberapproval" || $action == "reinstate" || $action == "defaultrecurring" || $action == 'memberrejection' || $action == 'terminate' || $action == 'withdraw') {
            unset($contentData['Effective Date']);
//            unset($contentData['Term Date']);
        }
        if ($action == "memberapproval") {
            if(!is_array($ccAddress)) { $ccAddress = [$ccAddress]; }
             $ccAddress = (new self())->addAddtionalCC($ccAddress, $policy->p_agent_num);
        }
        $cids = collect($planOverview)->pluck('cid')->toArray();
        $emailConfigurationName = in_array(92, $cids) ? "POLICY_WITHDRAWN_EMAIL_EXTRA_HEALTH" : "POLICY_WITHDRAWN_EMAIL";
        if ($action == 'defaultrecurring') $emailConfigurationName = "NUERA_BILLING";

        //to get uplineAgentsEmails
        $agent_id = Policy::where('policy_id', '=', $policyId)->value('p_agent_num');
        $ccAddress = UplineAgentsEmail::addUplineCcAddress($ccAddress, $agent_id);

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $messageTop,
//            'reason' => $reason,
            'data' => $contentData,
            'dependents' => $dependent,
            'dataName'=>$dataName,
            'repname' => $agentFullName,
            'repphone' => $agentPhone,
            'repemail' => $agentEmail,
        ];

        try {
            $n = new MessageService();
            $n->sendEmailWithContentData($emailData);
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    public static function sendChangeDateEmail($policyId, $subject, $message, $planPolicyIds = [], $oldDates=[])
    {
        $policy = Policy::find($policyId);
        $agentFirstName = $policy->agentInfo->agent_fname;
        $agentLastName = $policy->agentInfo->agent_lname;
        $agentFullName = $agentFirstName . ' ' . $agentLastName;
        if(isset($policy->agentInfo->agentCustomeHomepage)) {
            $agentEmail = $policy->agentInfo->agentCustomeHomepage->email;
            $b1 = $policy->agentInfo->agentCustomeHomepage->phone;
        } else {
            $agentEmail = $policy->agentInfo->agent_email;
            $b1 = $policy->agentInfo->agent_phone1;
        }
        $agentPhone = NULL;
        if (strlen($b1) == 10) {
            $agentPhone = "($b1[0]$b1[1]$b1[2]) $b1[3]$b1[4]$b1[5]-$b1[6]$b1[7]$b1[8]$b1[9]";
        }
        $toAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policy->getMember->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : [$policy->agentInfo->agent_email,$policy->getMember->cemail_alt];
        if (is_array($ccAddress)) {
            // Filter out null or empty values
            $ccAddress = array_filter($ccAddress);
        }

        $agent_id = Policy::where('policy_id', '=', $policyId)->value('p_agent_num');
        $ccAddress = UplineAgentsEmail::addUplineCcAddress($ccAddress, $agent_id);

        $dataName = isset($policy->getMember) ? $policy->getMember->fullname : '';
        $messageTop = "This is an email notification to inform you that the " . strtolower($message);
        $messageTop .= " on " . date('m/d/Y', time()) . '.';
        if (is_array($planPolicyIds) && count($planPolicyIds) >= 1) {
            $planOverview = PlanOverview::whereIn('p_ai', $planPolicyIds)->select(['web_display_name', 'peffective_date'])->get()->toArray();
        }
        else{
            $planOverview = PlanOverview::where('policy_id', $policyId)->select(['web_display_name', 'peffective_date'])->get()->toArray();
        }

        $plans ='';
        $newPlanEffectiveDate ='';
        if (count($oldDates)){
            foreach ($planOverview as $key => $plan) {
                $index = 1 + $key . " )";
                $planName = $plan['web_display_name'];
                $oldEffectiveDate = Carbon::parse($oldDates[$key])->format('m/d/Y');
                $newEffectiveDate = Carbon::parse($plan['peffective_date'])->format('m/d/Y');
                $newPlanEffectiveDate .= "<span style='color: green'>{$oldEffectiveDate} to {$newEffectiveDate}</span> <br/>";
                $plans .= "{$index} {$planName} <br/>";
            }
            $contentData = [
                'Confirmation #' => $policyId,
                'Primary Name' => $dataName,
                'Plan Name' => $plans,
                'Plan Eff. Date' => $newPlanEffectiveDate,
                'Eff. Date' => Carbon::parse($policy->effective_date)->format('m/d/Y'),
            ];
        }
        else{
            foreach ($planOverview as $key => $plan) {
                $index = 1 + $key . " )";
                $planName = $plan['web_display_name'];
                $plans .= "{$index} {$planName} <br/>";
            }
            $contentData = [
                'Confirmation #' => $policyId,
                'Primary Name' => $dataName,
                'Plan Name' => $plans,
                'Eff. Date' => Carbon::parse($policy->effective_date)->format('m/d/Y'),
            ];
        }

        $emailConfigurationName = "POLICY_WITHDRAWN_EMAIL";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $messageTop,
            'data' => $contentData,
            'dataName'=>$dataName,
            'repname' => $agentFullName,
            'repphone' => $agentPhone,
            'repemail' => $agentEmail,
        ];
        try {
            $n = new MessageService();
            $n->sendEmailWithContentData($emailData);
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    public static function sendUserPolicyActionEmail($userID, $subject, $message, $action, $data = [], $actionCode = '')
    {
        $userPolicyData = UserInfoPolicyAddress::where('userid', $userID)
            ->get(['eid', 'term_date', 'cemail', 'agent_email', 'cfname', 'clname', 'policy_id', 'effective_date', 'weburl', 'recurring_payment_type'])->first()->toArray();
        $policy = Policy::query()->where('policy_userid', '=', $userID)->first();
        $agentFirstName = $policy->agentInfo->agent_fname;
        $agentLastName = $policy->agentInfo->agent_lname;
        $agentFullName = $agentFirstName . ' ' . $agentLastName;
        if(isset($policy->agentInfo->agentCustomeHomepage)) {
            $agentEmail = $policy->agentInfo->agentCustomeHomepage->email;
            $b1 = $policy->agentInfo->agentCustomeHomepage->phone;
        } else {
           $agentEmail = $policy->agentInfo->agent_email;
            $b1 = $policy->agentInfo->agent_phone1;
        }
        $agentPhone = NULL;
        if (strlen($b1) == 10) {
            $agentPhone = "($b1[0]$b1[1]$b1[2]) $b1[3]$b1[4]$b1[5]-$b1[6]$b1[7]$b1[8]$b1[9]";
        }
        $dataName = isset($policy->getMember) ? $policy->getMember->fullname : '';
        $toAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policy->getMember->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : [$policy->agentInfo->agent_email,$policy->getMember->cemail_alt];
        if (is_array($ccAddress)) {
            // Filter out null or empty values
            $ccAddress = array_filter($ccAddress);
        }
        $messageTop = "This is an automated notice that the information about " . strtolower($message) . " on " . date('m/d/Y', time()) . '.';
        if ($subject == 'Billing Information EFT') {
            $messageTop = "This is an automated notice that your account billing information for eft has been updated on " . date('m/d/Y', time()) . '.';
        }
        if ($subject == 'Billing Information Change') {
            $messageTop = "Thank you, your billing information has been updated. <br><br><br>If this isn't something you've done or authorized, please call our office at (914) 428-6400";
        }
        if ($action == 'updatepolicy') {
            $dashboardUrl = config('corenroll.corenroll_dashboard_url');
            $messageTop = "This is an automated email notification.<br/>" . $message . " on " . date('m/d/Y', time()) . '.';
            $messageTop .= "<br/>Please verify the information is correct by logging into your dashboard by using this link: {$dashboardUrl}";
            $messageTop .= "<br/><br/><p>The Elevate Wellness Team</p>";
        }
        $emailConfigurationName = "POLICY_WITHDRAWN_EMAIL";
        if ($action == 'selfrecurringstatus' || $action == 'billinginformation') $emailConfigurationName = "NUERA_BILLING";

        $agent_id = $policy->p_agent_num;
        $ccAddress = UplineAgentsEmail::addUplineCcAddress($ccAddress, $agent_id);

        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $messageTop,
            'data' => $action !== 'updatepolicy' ? $data : [],
            'dataName' => $dataName,
            'repname' => $agentFullName,
            'repphone' => $agentPhone,
            'repemail' => $agentEmail,
            'reason' => null
        ];
        if ($action == 'selfrecurringstatus') {
            $emailData['monthlyPremium'] = PolicyUpdateHelper::getMonthlyPreium($userID);
            $monthlyPremium = $emailData['monthlyPremium'];
            $recurringType = strtoupper($userPolicyData['recurring_payment_type']);
            $emailData['messageTop'] = "This is to notifiy that your billing method has been switched to $recurringType . We will use this bank to charge your monthly premium $$monthlyPremium every month.";
            if (strtolower($recurringType) != 'eft') {
                $emailData['data'] = [];
                //$emailData['middleMessage'] = 'Payment method switched to ' . $recurringType;
                //$emailData['messageTop'] = "This is to notifiy that your billing method switched to $recurringType.";
                $emailData['messageTop'] = "Thank you, your billing information has been updated. <br><br><br>If this isn't something you've done or authorized, please call our office at (914) 428-6400";

            }
        }
        try {
            $n = new MessageService();
            $n->sendEmailWithContentData($emailData);
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    public function sendReferralEmail($email, $subject, $name = '', $message = '', $data = [], $from = '', $template = '', $attachment = '', $middlemessage = '', $endMessage = '', $context = null): bool
    {
        try {
            $toAddress = config('globalvar.TEST_EMAIL') ? config('globalvar.TEST_EMAIL')[0] : $email;

            $requestData = [
                'email_message_configuration_name' => $template ?: 'POLICY_WITHDRAWN_EMAIL',
                'toAddress' => is_array($toAddress) ? $toAddress : [$toAddress],
                'ccAddress' => $this->ccUplineEmails ? $this->ccUplineEmails : [],
                'bccAddress' => $this->bccEmail,
                'subject' => $subject,
                'generalTemplateData' => [],
                'contentData' => [
                    'countData' => 1,
                    'name' => $name,
                    'content' => [
                        'messageTop' => $message,
                        'middleMessage' => $middlemessage,
                        'endMessage' => $endMessage,
                        'data' => $data,
                        "from" => $from,
                        "name" => $name
                    ]
                ]
            ];
            if ($attachment) {
                $requestData['attachedFiles'] = is_array($attachment) ? $attachment : [$attachment];
            }
            return $this->sendEmailRequest($requestData, $context);
        } catch (Exception $e) {
            return false;
        }
    }

    public static function sendEmailRequest($requestData, ?string $context = null, $agentId = null): bool
    {
        // Determine the effective template
        $template = $requestData['email_message_configuration_name'];

        // Use the helper to prepare CC emails
        $allCcEmails = self::prepareCcEmails($template, $requestData['ccAddress'] ?? [], $context, $agentId);

        // Update the requestData with the combined CC emails
        $requestData['ccAddress'] = $allCcEmails;

        $endPoint = 'send-email-with-content-data';

        $url = config('app.SMS_SERVICE_URL')['key'] . $endPoint;

        $response = GuzzleHelper::curlPostApi($url, json_encode($requestData), 'default', true);
        if (isset($response->status) && $response->status == 'success') {
            return true;
        }
        else {
            return false;
        }
    }

    public static function prepareCcEmails(string $template, array $existingCcEmails = [], ?string $context = null, $agentId = null): array
    {
        try {
            // Fetch CC emails dynamically based on the template
            $dynamicCcEmails = EmailCCHelper::fetchCcEmails($template, $context, $agentId);

            // Combine dynamic CC emails with existing ones
            return array_unique(array_merge($existingCcEmails, $dynamicCcEmails));
        } catch (\Exception $e) {
            return $existingCcEmails; // Return existing emails if something goes wrong
        }
    }

}
