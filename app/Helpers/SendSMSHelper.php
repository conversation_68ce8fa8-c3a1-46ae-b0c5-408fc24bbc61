<?php

namespace App\Helpers;

use App\PlanOverview;
use App\Policy;
use App\UserInfoPolicyAddress;
use GuzzleHttp\Client;

class SendSMSHelper
{
    public static function sendSms($phone, $body)
    {
        try {
            $data = array(
                'phone' => $phone,
                'body' =>$body
            );
            $client = new \GuzzleHttp\Client();
            $requestBody = $client->request('POST', config('app.messagecenter')['key'] . "api/v1/send-sms-with-content", [
                'json' => $data
            ]);
            $body = $requestBody->getBody()->getContents();
            $responseArray = json_decode(($body), true);
            if (isset($responseArray['status'])) {
                return true;
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }

}
