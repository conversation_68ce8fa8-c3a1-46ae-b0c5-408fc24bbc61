<?php

namespace App\Helpers;

use App\PlanOverview;
use App\Policy;
use App\NbPayment;
use App\UserInfoPolicyAddress;
use GuzzleHttp\Client;
use App\Helpers\GuzzleHelper;
use DateTime;
use App\NbInvoice;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class InvoiceHelper
{
    public static function checkIfPaymentExists($invoice_id)
    {
        return NbPayment::where('invoice_id', $invoice_id)
            ->count() > 0;
    }

    public static function updatePaidNBInvoice($termDate, $policyId, $channel = null,$dedicatedMonths=null) {
        ini_set('max_execution_time', 300);
        $nbInvoices = NbInvoice::where('invoice_policy_id', $policyId)->where('invoice_start_date', '>=', $termDate)->orderBy('invoice_date','ASC')->get();
        if(!$nbInvoices->isEmpty()) {
            foreach($nbInvoices as $key => $nb_invoice) {
                if($nb_invoice->invoice_payment_status != "UNPAID" || ($nb_invoice->invoice_payment_status == "UNPAID" && $nb_invoice->payment_party_status == "PROCESSING") || $nb_invoice->invoice_payment_status == "UNPAID" && self::checkIfPaymentExists($nb_invoice->invoice_id)) {
                    $url = config('app.purenroll_system.url') . "regenerate-paid-invoice/{$nb_invoice->invoice_id}";
                    $responseJson = GuzzleHelper::getApi($url, []);
                    $response = json_decode($responseJson, true);
                    if ($response['statusCode'] == '200') {
                        Log::channel($channel)->info(PHP_EOL ."Paid Invoice regenerated successfully for Invoice ID " . $nb_invoice->invoice_id . ".");
                    } else {
                        Log::channel($channel)->info(PHP_EOL ."There was error while regeneration invoice for Invoice ID " . $nb_invoice->invoice_id . ".");
                    }

                } else {
                    $url = config('app.purenroll_system.url') . "regenerate-invoice/{$nb_invoice->invoice_id}";
                    $responseJson = GuzzleHelper::getApi($url, []);
                    $response = json_decode($responseJson, true);
                    if ($response['statusCode'] == '200') {
                        Log::channel($channel)->info(PHP_EOL ."Invoice regenerated successfully for Invoice ID " . $nb_invoice->invoice_id . ".");
                    } else {
                        Log::channel($channel)->info(PHP_EOL ."There was error while regeneration invoice for Invoice ID " . $nb_invoice->invoice_id . ".");
                    }
                }
            }
        } else {
            $today = date('Y-m-d');

            if(strtotime($termDate) < strtotime($today)) {

                $isAnnualPolicy=Policy::where('policy_id',$policyId)->where('pay_type','annual')->first();
                $startDate = new DateTime($termDate);
                $startDate->modify('first day of next month');
                $endDate = new DateTime($today);
                $endDate->modify('first day of this month');
                $months = array();
                $annual_month=[];

                
                if (isset($isAnnualPolicy)) {
                    $latest_invoice = NbInvoice::where('invoice_policy_id', $policyId)
                        ->orderBy('invoice_end_date', 'DESC')
                        ->first();
                
                    if ($latest_invoice && $latest_invoice->invoice_end_date) {
                        $next_annual_date = date('Y-m-01', strtotime('+1 month', strtotime($latest_invoice->invoice_end_date)));
                        while ($next_annual_date <= $endDate->format('Y-m-d')) {
                            $annual_month[] = $next_annual_date;
                            $next_annual_date = date('Y-m-d', strtotime('+12 month', strtotime($next_annual_date)));
                        }
                    } else {
                        $annual_month[] = date('Y-m-01', strtotime($isAnnualPolicy->effective_date));
                    }
                } else {
                    while ($startDate <= $endDate) {
                        $months[] = $startDate->format('Y-m-01');
                        $startDate->modify('first day of next month');
                    }
                }
                                
                $client = new Client();
                Log::info("Commission api called for generating single invoice.");
                $url = config('app.purenroll_system.url') . "generate-single-invoice";

                $payload = [
                    'policy_id' => $policyId,
                    'effective_date' =>!empty($months) ? $months :$annual_month,
                ];
                
                $responseJson = GuzzleHelper::postApi($url, [], $payload);
                $pattern = '/\{.*?\}/';
                if (preg_match($pattern, $responseJson, $matches)) {
                    $result_string = $matches[0];
                    if($result_string) {
                        $response = json_decode($result_string, true);
                    }

                }
                if($response && $response['status'] == "success") {
                    Log::info("Invoice genereated Successfully.");
                } else {
                    Log::info("There was error while generating invoice.");
                }
            }
        }
        self::fixCarryForwards($policyId);
    }


    public static function regenerateAndFixInvoices($policy_id, $current_policy){
        $invoices_id =  NbInvoice::where('invoice_policy_id', $policy_id)
        ->where('invoice_status','ACTIVE')
        ->where('invoice_start_date', '>=', $current_policy->term_date)
        ->pluck('invoice_id')->toArray();
        foreach ($invoices_id as $invoice_id){
            $url = config('app.purenroll_system.url') ."regenerateInvoice/{$invoice_id}";
            $responseJson = GuzzleHelper::getApi($url, []);
            $response = json_decode($responseJson, true);
            Log::info($response);
        }
        self::fixCarryForwards($policy_id);
    }

    public static function fixCarryForwards($policy_id){
        $policiesId = self::getUserPolicyIds($policy_id);
        $url = config('app.purenroll_system.url') ."fixCarryforwards";
        $response = GuzzleHelper::postApi($url, [], [
            'policy_id'  => $policiesId
        ]);
        $response = json_decode($response, true);
        Log::info($response);
    }

    public static function getUserPolicyIds($policy_id){
        return PlanOverview::where('userid', PlanOverview::where('policy_id', $policy_id)->first()->userid)
        ->where(function ($query) {
            $query->where('status', 'ACTIVE')
                  ->orWhere('status', 'WITHDRAWN');
        })
        ->pluck('policy_id')
        ->toArray();
    }

    public static function refundToPaymentSystem($policy_id)
    {
        $payment = NbPayment::where('payment_policyid', $policy_id)->first();
    
        if (!$payment) {
            Log::channel('refund_withdrawn_extra_plan_payments_log')
                ->error("Refund failed: No payment record found for policy ID {$policy_id}");
    
            return ['success' => false, 'message' => 'Payment record not found'];
        }
    
        $copiedPayment = $payment->replicate();
    
        $meta = [
            "invoice_id" => $payment->invoice_id,
            "refund_by" => $payment->invoice_payment_created_user_id,
            "refund_platform" => "Purenroll"
        ];
    
        $url = config('app.payment_system.url') . 'create/refund';
    
        $payload = [
            'payment_id'  => $payment->paystand_payment_id,
            'amount'      => $payment->payment_amount,
            'currency'    => 'USD',
            'description' => 'Refund in process due to withdrawal of extra health plan.',
            'meta'        => json_encode($meta)
        ];
    
        try {
            $response = GuzzleHelper::postApi($url, [], $payload);
            $decodedResponse = json_decode($response, true);
    
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("Invalid JSON response from refund API");
            }
    
            Log::channel('refund_withdrawn_extra_plan_payments_log')
                ->info("Refund response for payment ID {$payment->paystand_payment_id}: ", $decodedResponse);

            if (isset($decodedResponse['status']) && $decodedResponse['status'] == 'paid') {
                $copiedPayment->payment_status = 'REFUNDED';
                $copiedPayment->paystand_refund_id = $decodedResponse['refund_id'] ?? null;
                $copiedPayment->payment_value_date = isset($decodedResponse['refund_value_date']) 
                    ? date('Y-m-d', strtotime($decodedResponse['refund_value_date']))
                    : null;
                $copiedPayment->payment_clearing_date = isset($decodedResponse['refund_value_date']) 
                    ? date('Y-m-d', strtotime($decodedResponse['refund_value_date']))
                    : null;
                $copiedPayment->save();
    
                return ['success' => true, 'message' => 'Refund initiated successfully'];
            }
            else{
                $copiedPayment->payment_status = 'REFUND-ERROR';
                $error = 'Error while refunding the amount';
                $copiedPayment->payment_clearing_date = null;
                if (isset($decodedResponse['errors']['payment_id'][0])) {
                    $error = $decodedResponse['errors']['payment_id'][0];
                }
                if (isset($decodedResponse['errors']['failure']['details'])) {
                    $error = $decodedResponse['errors']['failure']['details'];
                }
                $copiedPayment->payment_notes = $error;
                $copiedPayment->save();
            }
            
            return ['success' => false, 'message' => 'Refund API returned failure', 'response' => $decodedResponse];
    
        } catch (\Exception $e) {
            $copiedPayment->payment_status = 'REFUND-ERROR';
            $copiedPayment->payment_notes = 'Error while refunding the amount';
            $copiedPayment->payment_clearing_date = null;
            $copiedPayment->save();
            Log::channel('refund_withdrawn_extra_plan_payments_log')
                ->error("Refund API error: " . $e->getMessage());
    
            return ['success' => false, 'message' => 'Exception occurred while processing refund'];
        }
    }
    
}
