<?php

namespace App\Helpers;


class UplineAgentsEmail
{
    public static function getUplineAgentsEmail($agent_id)
    {
        $apiUrl = config("corenroll.corenroll_api_url") . 'api.access/v2/get-uplines-cc-email?agent_id='. $agent_id;
        $responseJson = GuzzleHelper::getApi($apiUrl, []);
        $response = json_decode($responseJson, true);
        if (array_key_exists('statusCode', $response) && $response['statusCode'] == 200) {
            return $response['data'];
        }else{
            return [];
        }
    }

    public static function addUplineCcAddress($ccAddress, $agent_id)
    {
        $uplineEmails = self::getUplineAgentsEmail($agent_id);
        $ccAddress = is_array($ccAddress) ? $ccAddress : [$ccAddress];
        $uplineEmailsToAdd = array_diff($uplineEmails, $ccAddress);
        if(!empty($uplineEmailsToAdd)){
            $ccAddress = array_merge($ccAddress, $uplineEmailsToAdd);
        }
        return $ccAddress;
    }
}
