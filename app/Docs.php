<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Docs extends Model
{
    protected $connection = 'mysql';
    protected $table = 'docs_up';
    protected $primaryKey = 'docID';
    protected $guarded = ["docID"];

    CONST DOC_LINK = "https://corenroll.com/viewfile.php?file=";
    CONST DOC_DIR = "uploaded-files-dashboard/docs/";

    static $groupFileType = [
        'My Files', 'Forms', 'Enrollment Form', 'Census File', 'Member Docs', 'Rep Doc', 'Commission Statement'
    ];
}
