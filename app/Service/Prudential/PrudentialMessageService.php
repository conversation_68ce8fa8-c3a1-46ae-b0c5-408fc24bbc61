<?php


namespace App\Service\Prudential;


use App\Service\MessageService;

class PrudentialMessageService extends MessageService
{
    const REPORT_MONTHLY = 'monthly';
    const TO_ADDRESS = "<EMAIL>";
    const CC_ADDRESS = ["<EMAIL>","<EMAIL>"];

    /**
     * @return array
     */
    protected function emailData(): array
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: self::TO_ADDRESS;
        $ccAddress = config("testemail.TEST_EMAIL") ?: self::CC_ADDRESS;
        $dataName =  "Admin";
        return array($toAddress, $ccAddress, $dataName);
    }

    public function sendCreateEmail($data,$period,$attachedFile=[])
    {
        list($toAddress, $ccAddress, $dataName) = $this->emailData();
        $data = [
            'toAddress'=>$toAddress,
            'ccAddress'=>$ccAddress,
            'dataName'=>$dataName,
            'generalTemplateData'=>[],
            'contentTemplate'=>$data,
            'attachedFiles'=>isset($attachedFile) ? $attachedFile : [],
            'email_message_configuration_name'=>"PRUDENTIAL_REPORT",
            'subject'=>$period == self::REPORT_MONTHLY ? 'Prudential - EWA - Monthly  Report' :  'Prudential - EWA - Weekly Report',
        ];
        $this->sendEmailWithContentTemplate($data);
    }
}
