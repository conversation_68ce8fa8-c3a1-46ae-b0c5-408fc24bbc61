<?php


namespace App\Service\Prudential;


use App\Repositories\Plans\PrudentialPlanRepository;

class PrudentialPlanService
{
    const REPORT_WEEKLY = 'weekly';
    const REPORT_MONTHLY = 'monthly';
    /**
     * @var PrudentialPlanRepository
     */
    private $repository;
    /**
     * @var PrudentialMessageService
     */
    private $messageService;

    /**
     * PrudentialPlanService constructor.
     * @param PrudentialPlanRepository $repository
     * @param PrudentialMessageService $messageService
     */
    public function __construct(
        PrudentialPlanRepository $repository,
        PrudentialMessageService $messageService
    )
    {
        $this->repository = $repository;
        $this->messageService = $messageService;
    }

    /**
     * @param $period
     * @return string
     * generate report dynamically
     * Fetch required data from repository
     * Generate excel file from fetched data
     * Send generated excel file <NAME_EMAIL> and <EMAIL>
     *
     */
    public function report($period)
    {
        try {
            if ($period == self::REPORT_MONTHLY) {
                return $this->monthlyReport();
            } elseif ($period == self::REPORT_WEEKLY) {
                return $this->weeklyReport();
            } else {
                return false;
            }
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    /**
     * monthly report functionality
     */
    public function monthlyReport()
    {
        $data = $this->repository->monthlyReport();
        if ($data) {
//            $excel = new PrudentialPlanMonthlyExport($data);
//            $fileFormat = \Maatwebsite\Excel\Excel::XLS;
//            $attachedFile = Excel::raw($excel, $fileFormat);
//            $file = "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64," . base64_encode($attachedFile);
            $html = view('excel.prudential.plans-content', $data)->render();
            $this->messageService->sendCreateEmail($html, self::REPORT_MONTHLY);
        }
        return true;
    }


    /**
     * monthly report functionality
     */
    public function weeklyReport()
    {
        $data = $this->repository->weeklyReport();
        if ($data) {
//            $excel = new PrudentialPlanMonthlyExport($data);
//            $fileFormat = \Maatwebsite\Excel\Excel::XLS;
//            $attachedFile = Excel::raw($excel, $fileFormat);
            $html = view('excel.prudential.plans-content', $data)->render();
            $this->messageService->sendCreateEmail($html, self::REPORT_WEEKLY);
        }
        return true;
    }




}
