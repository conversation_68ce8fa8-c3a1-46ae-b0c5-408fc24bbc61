<?php


namespace App\Service\RepInfoRequest;
use App\Service\MessageService;
use Carbon\Carbon;

class RepInfoRequestMessageService extends MessageService
{
    /**
     * @param $repInfoRequest
     * @return array
     */
    protected function repInfoRequestEmailData($repInfoRequest): array
    {
        $userInfo = isset($repInfoRequest->userInfo) ? $repInfoRequest->userInfo : null;
        $agentInfo = isset($repInfoRequest->agentInfo) ? $repInfoRequest->agentInfo : null;
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $userInfo->cemail;
        $date = Carbon::now()->format('m/d/Y');
        $dataName = isset($agentInfo) ? $agentInfo->full_name : null;
        return array($toAddress, $ccAddress, $date,$dataName);
    }


    public function sendRepInfoRequestActionEmail($repInfoRequest,$type)
    {
         
        list($toAddress, $ccAddress, $date, $dataName) = $this->repInfoRequestEmailData($repInfoRequest);
        $agentName = isset($repInfoRequest->agentInfo) ? $repInfoRequest->agentInfo->full_name : '';

        $created_at=Carbon::parse($repInfoRequest->created_at)->format('m/d/Y');
        $contentData = [
            'requestedType' => $repInfoRequest->type_name,
            'status' => $repInfoRequest->status_name,
            'agent'=>$agentName,
            'information'=> $repInfoRequest->information,
            'policy_id'=> $repInfoRequest->policy_id,
            'created_at'=> $created_at,
            'memberName'=> $repInfoRequest->userinfo->cfname.' '.$repInfoRequest->userinfo->cmname.' '.$repInfoRequest->userinfo->clname,
            'memberEmail' => $repInfoRequest->userinfo->cemail,
            'requestedBy' => $repInfoRequest->requested_by === 'A' ? 'Agent' : ($repInfoRequest->requested_by === 'M' ? 'Member' : null),
            'reject_reason' =>$type == 'Rejected' ? $repInfoRequest->admin_reason : '',
        ];
        $type = lcfirst($type);
        $message= "This is an automated notice to notify that your requested information has been {$type} on " . $date . ".";

        $data = [
            'toAddress'=>$toAddress,
            'ccAddress'=>$ccAddress,
            'dataName'=>$dataName,
            'data'=>$contentData,
            'email_message_configuration_name'=>"REP_INFO_REQUEST_ADMIN",
            'subject'=>'Information Request',
            'message'=>$message,
        ];
        $this->sendEmailWithContentData($data);
    }
}
