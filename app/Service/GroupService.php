<?php


namespace App\Service;


use App\GroupInfo;
use Carbon\Carbon;

class GroupService
{

    /**
     * @return array
     * select distinct gtype and send to array
     */
    public function getGroupTypes(){
        return GroupInfo::query()
            ->where('gtype','!=','')
            ->distinct()
            ->pluck('gtype')
            ->toArray();
    }

    /**
     * @param $groupInfo
     * @return array
     */
    protected function groupEmailData($groupInfo): array
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $groupInfo->gemail;
        $date = Carbon::now()->format('m/d/Y');
        $dataName = $groupInfo->contact_full_name;
        return array($toAddress, $ccAddress, $date,$dataName);
    }

    /**
     * @param string $emailConfigurationName
     * @param $toAddress
     * @param $ccAddress
     * @param string $subject
     * @param string $message
     * @param array $contentData
     * @param string $dataName
     * @return bool|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    protected function sendGroupEmail(
        string $emailConfigurationName,
        $toAddress, $ccAddress,
        string $subject,
        string $message,
        array $contentData,
        string $dataName = ''
    )
    {
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
            'dataName'=>$dataName
        ];
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    public function sendGroupTypeUpdateEmail($groupInfo)
    {
        list($toAddress, $ccAddress, $date,$dataName) = $this->groupEmailData($groupInfo);
        $type = ucfirst($groupInfo->gtype);
        $message = "This is an automated notice to notify that your group type has been updated and changed to {$type} on " . $date . ".";
        $contentData = [
            'Group Code' => $groupInfo->gcode,
            'Group Type' => $type,
        ];
        $emailConfigurationName = "GROUP_INFO_CHANGE";
        $subject = 'Group Type Update';
        return $this->sendGroupEmail($emailConfigurationName, $toAddress, $ccAddress, $subject, $message, $contentData,$dataName);
    }
}
