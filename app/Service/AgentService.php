<?php


namespace App\Service;


use Carbon\Carbon;

class AgentService
{
    /**
     * @param $agentInfo
     * @return array
     */
    protected function agentEmailData($agentInfo): array
    {
        $toAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $agentInfo->agent_email;
        $date = Carbon::now()->format('m/d/Y');
        $dataName = $agentInfo->full_name;
        return array($toAddress, $ccAddress, $date,$dataName);
    }

    /**
     * @param string $emailConfigurationName
     * @param $toAddress
     * @param $ccAddress
     * @param string $subject
     * @param string $message
     * @param array $contentData
     * @param string $dataName
     * @return bool|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    protected function sendAgentEmail(string $emailConfigurationName, $toAddress, $ccAddress, string $subject, string $message, array $contentData,string $dataName = '')
    {
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData,
            'dataName'=>$dataName
        ];
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    public function sendAgentWebsiteEmail($agentInfo)
    {
        list($toAddress, $ccAddress, $date,$dataName) = $this->agentEmailData($agentInfo);
        $message = "This is an automated notice to notify that your following personalized sites has been updated on " . $date . ".";
        $contentData = [
            'Agent Code' => $agentInfo->agent_code,
            'Landing Page URL' => $agentInfo->agent_web,
        ];
        $emailConfigurationName = "AGENT_INFO_CHANGE";
        $subject = 'Agent Personalized Sites Update';
        return $this->sendAgentEmail($emailConfigurationName, $toAddress, $ccAddress, $subject, $message, $contentData,$dataName);
    }

    public function sendGroupRemoveEmail($agentInfo,$groupInfo)
    {
        list($toAddress, $ccAddress, $date,$dataName) = $this->agentEmailData($agentInfo);
        $message = "This is an automated notice to notify that your following group has been removed on " . $date . ".";
        $contentData = [
            'Agent Code' => $agentInfo->agent_code,
            'Group Code'=>$groupInfo->gcode,
            'Group Name' => $groupInfo->gname,
        ];
        $emailConfigurationName = "AGENT_INFO_CHANGE";
        $subject = "Agent Group Remove";
        return $this->sendAgentEmail($emailConfigurationName, $toAddress, $ccAddress, $subject, $message, $contentData,$dataName);
    }

}
