<?php


namespace App\Service;


use App\Plan;
use App\PlanAgeRestriction;
use App\PlanAgeRestrictionDependent;
use App\PlanOverview;
use App\PlanPricingDisplay;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;

class PlanAgeService
{

    public static function checkPlanAge($planId, $age)
    {
        $planAge = PlanAgeRestriction::where('plan_id', $planId)
            ->where('status', 1)
            ->select([
                'primary_min',
                'primary_max',
                'state'
            ])
            ->first();
        $planDetail = Plan::where('pid', $planId)
            ->first();
        $result['status'] = true;
        if ($planAge && (!is_null($planAge->primary_min) || !is_null($planAge->primary_max))) {
            if ($age >= $planAge->primary_min && $age <= $planAge->primary_max) {
            } else {
                $result['min'] = $planAge->primary_min;
                $result['max'] = $planAge->primary_max;
                $result['status'] = false;
            }
        } else {
            if ($planDetail->pl_type == 'MM' || $planDetail->pl_type == 'LM') {
                if ($age >= 18 && $age <= 65) {
                } else {
                    $result['min'] = 18;
                    $result['max'] = 64;
                    $result['status'] = false;
                }
            } else {
                if ($age >= 18) {
                } else {
                    $result['min'] = 18;
                    $result['max'] = 0;
                    $result['status'] = false;
                }
            }
        }
        return $result;
    }


    public function checkPlanAgeDep($planId, $age)
    {
        $planAgeDep = PlanAgeRestrictionDependent::where('plan_id', $planId)
            ->where('dstatus', 1)
            ->select([
                'dmin',
                'dmax',
                'dstate'
            ])
            ->first();

        $result['status'] = true;
        if ($planAgeDep && (!is_null($planAgeDep->dmin) || !is_null($planAgeDep->dmax))) {
            if ($age >= $planAgeDep->dmin && $age <= $planAgeDep->dmax) {
            } else {
                $result['min'] = $planAgeDep->dmin;
                $result['max'] = $planAgeDep->dmax;
                $result['status'] = false;
            }
        } else {
            if ($age <= 25) {
            } else {
                $result['min'] = 1;
                $result['max'] = 25;
                $result['status'] = false;
            }
        }
        return $result;
    }

    public static function getPlanIdsOfPolicy(int $policyId)
    {
        return UserInfoPolicyAddress::join('plan_overview', 'plan_overview.policy_id', '=', 'userinfo_policy_address.policy_id')
            ->select('plan_overview.pid')
            ->where('userinfo_policy_address.policy_id', $policyId)
            ->get()
            ->map(function ($planIds) {
                return $planIds['pid'];
            });
    }

    public function validateSingleDependent($data)
    {
        $age = Carbon::parse($data['d_dob'])->age;
        $planIds = $this->getPlanIdsOfPolicy((int)$data['policy_id']);
        foreach ($planIds as $planId) {
            if ($data['d_relate'] == 'S') {
                $primaryInfo['relationship'] = 'Spouse';
                $checkPlanAge = $this->checkPlanAge($planId, $age);
            } else {
                $primaryInfo['relationship'] = 'Child';
                $checkPlanAge = $this->checkPlanAgeDep($planId, $age);
            }

            if (!$checkPlanAge['status']) {
                $planName = Plan::where('pid', $planId)
                    ->value('web_display_name');
                $message = ucfirst($primaryInfo['relationship']) . " age must be in between " . $checkPlanAge['min'] . " and " . $checkPlanAge['max'] . ' for plan : ' . $planName;
                if ($checkPlanAge['max'] == 0) {
                    $message = ucfirst($primaryInfo['relationship']) . " age must be above  " . $checkPlanAge['min'] . ' for plan : ' . $planName;
                }
                return [
                    'message' => $message,
                    'success' => false
                ];
            }
        }
        return [
            'message' => 'Success',
            'success' => true
        ];
    }


    /**
     * @param int $policyId
     * @return array
     */
    public function getPlanPricingIdsFromPlanOverview(int $policyId): array
    {
        return PlanOverview::query()->where('policy_id', $policyId)->pluck('plan_pricing_id')->toArray();
    }


    /**
     * @param string $column
     * @param array $planPricingIds
     * @return bool
     */
    public function checkColumn(string $column, array $planPricingIds): bool
    {
        $status = false;
        foreach ($planPricingIds as $planPricingId) {
            $requiredConf = PlanPricingDisplay::query()->where('plan_pricing_id', $planPricingId)->value($column);
            if ($requiredConf == 1) {
                $status = true;
                break;
            }
        }
        return $status;
    }

    public function checkNAWUPlan($policyId): bool
    {
        return PlanOverview::query()
            ->where([
                ['policy_id','=',$policyId],
                ['plan_cat','=','ENANAWU'],
            ])
            ->exists();
    }
}
