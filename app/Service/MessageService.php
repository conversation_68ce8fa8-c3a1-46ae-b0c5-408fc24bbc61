<?php


namespace App\Service;


use App\Helpers\GuzzleHelper;
use App\Traits\ResponseMessage;

class MessageService
{
    use ResponseMessage;

    private $baseUrl;

    /**
     * MessageService constructor.
     */
    public function __construct()
    {
        $this->baseUrl = config('app.messagecenter.key') ?: 'https://qa-api-msg.purenroll.com/';
    }

    public function sendEmailWithContentData($data)
    {
        $url = $this->baseUrl . "api/v1/send-email-with-content-data";
        $attachedFile = isset($data['attachedFiles']) ? $data['attachedFiles'] : [];
        $generalTemplate = isset($data['generalTemplateData']) ? $data['generalTemplateData'] : [];
        $requestData = [
            'email_message_configuration_name' => $data['email_message_configuration_name'],
            'toAddress' => is_array($data['toAddress']) ? $data['toAddress'] : [$data['toAddress']] ,
            'ccAddress' => isset($data['ccAddress']) ? (is_array($data['ccAddress']) ? $data['ccAddress'] : [$data['ccAddress']] ): [],
            'bccAddress' => isset($data['bccAddress']) ? (is_array($data['bccAddress']) ? $data['bccAddress'] : [$data['bccAddress']] ): [],
            'subject' => $data['subject'],
            'attachedFiles' => $attachedFile,
            'generalTemplateData' => $generalTemplate,
            'contentData' => [
                'countData' => 1,
                'countData1'=>1,
                'name' => isset($data['dataName']) ? $data['dataName'] : "",
                'content' => [
                    'messageTop' => isset($data['message']) ? $data['message'] : "",
                    'reason' => isset($data['reason']) ? $data['reason'] : "",
                    'data' => $data['data'],
                    'name' => isset($data['dataName']) ? $data['dataName'] : "",
                    'data1'=>isset($data['data1']) ? $data['data1'] : "",
                    'messageBottom'=>isset($data['messageBottom']) ? $data['messageBottom'] : "",
                    'middleMessage'=>isset($data['middleMessage']) ? $data['middleMessage'] : "",
                    'middleMessage1' => isset($data['middleMessage1']) ? $data['middleMessage1'] : "",
                    'repname' => isset($data['repname']) ? $data['repname'] : "",
                    'repphone' => isset($data['repphone']) ? $data['repphone'] : "",
                    'repemail' => isset($data['repemail']) ? $data['repemail'] : "",
                    'dependents' => isset($data['dependents']) ? $data['dependents'] : "",
                ]
            ]
        ];
        if(!isset($data['data1']))unset($requestData['contentData']['content']['data1']);
        if(!isset($data['dependents']))unset($requestData['contentData']['content']['dependents']);
        if(!isset($data['messageBottom']))unset($requestData['contentData']['content']['messageBottom']);
        if(!isset($data['middleMessage1']))unset($requestData['contentData']['content']['middleMessage1']);
        if(!isset($data['middleMessage']))unset($requestData['contentData']['content']['middleMessage']);
        if (isset($data['reason']) and $data['reason'] == null) unset($requestData['contentData']['content']['reason']);
        try {
            $responseJson = GuzzleHelper::postApi($url, [], $requestData);
            $response = json_decode($responseJson, true);
            if (isset($response['status_code']) && $response['status_code'] == '200' && $response['status'] == 'success') {
                return true;
            } else {
                return false;
            }
        } catch (\Throwable $th) {
            return $this->failedResponse('Something went wrong!');
        }
    }

    public function sendEmailWithContentTemplate($data)
    {
        $url = $this->baseUrl . "api/v1/send-email-with-content-template";
        $attachedFile = isset($data['attachedFiles']) ? $data['attachedFiles'] : [];
        $generalTemplate = isset($data['generalTemplateData']) ? $data['generalTemplateData'] : [];
        $contentTemplate = isset($data['contentTemplate']) ? $data['contentTemplate'] : [];
        $requestData = [
            'email_message_configuration_name' => $data['email_message_configuration_name'],
            'toAddress' => is_array($data['toAddress']) ? $data['toAddress'] : [$data['toAddress']] ,
            'ccAddress' => isset($data['ccAddress']) ? (is_array($data['ccAddress']) ? $data['ccAddress'] : [$data['ccAddress']] ): [],
            'bccAddress' => isset($data['bccAddress']) ? (is_array($data['bccAddress']) ? $data['bccAddress'] : [$data['bccAddress']] ): [],
            'subject' => $data['subject'],
            'attachedFiles' => $attachedFile,
            'generalTemplateData' => $generalTemplate,
            'contentTemplate' => $contentTemplate
        ];
        try {
            $header = [];
            $responseJson = GuzzleHelper::postApi($url, $header, $requestData);
            $response = json_decode($responseJson, true);
            if (isset($response['status_code']) && $response['status_code'] == '200' && $response['status'] == 'success') {
                return true;
            } else {
                return false;
            }
        } catch (\Throwable $th) {
            return $this->failedResponse('Something went wrong!');
        }
    }

    public function sendEmailWithTemplateAndData($data)
    {
        $url = $this->baseUrl . "api/v1/send-email-with-content-data";
        try {
            $responseJson = GuzzleHelper::postApi($url, [], $data);
            $response = json_decode($responseJson, true);
            return $response;
        } catch (\Throwable $th) {
            return $this->failedResponse('Something went wrong!');
        }
    }

}
