<?php

namespace App\Service;

use App\BundelPlan;
use App\Plan;
use App\PlanPricingDisplay;
use App\TempStorage;
use Carbon\Carbon;
use Exception;

class PrudentialPlanPricingService
{
    private $tier;
    private $memberAge;
    private $childCount;
    private $spouseAge;
    private $isBundle;
    public function __construct(string $tier, int $memberAge, int $childCount = null, int $spouseAge = null)
    {
        $this->tier = $tier;
        $this->memberAge = $memberAge;
        $this->childCount = $childCount;
        $this->spouseAge = $spouseAge;
    }

    /**
     * @param int $planId
     * @return float|null
     * @throws Exception
     */
    public function calculatePlanPrice(int $planId): ?float
    {


        if(! $this->isPrudentialBundlePlan($planId)) {

            return $this->calculatePrudentialPrice($planId);
        }
        $this->isBundle = true;

        $planIds = $this->getIndividualPlans($planId);

        $price = 0;
        foreach ($planIds as $planId) {

            $price += $this->calculatePrudentialPrice($planId);

        }

        return $price;
    }

    /**
     * @param int $planId
     * @return bool
     */
    private function isPrudentialBundlePlan(int $planId): bool
    {
        $mainBundlePlanIsPrudential = BundelPlan::query()
            ->whereHas('plan', function ($q) {
                $q->where('carrier', 80);
            })
            ->where('bundle_pid', $planId)
            ->exists();
        $subPrudentailPlan = $this->getIndividualPlans($planId);
        $subPlanIsPrudentails =  Plan::whereIn('pid',$subPrudentailPlan)->where('carrier', 80)->exists();

        return $mainBundlePlanIsPrudential || $subPlanIsPrudentails;
    }

    /**
     * @param int $planId
     * @return array
     */
    private function getIndividualPlans(int $planId): array
    {
        return BundelPlan::query()
            ->where('bundle_pid', $planId)
            ->pluck('pid')
            ->toArray();
    }

    /**
     * @param int $planId
     * @return bool
     */
    private function isHospitalOrCriticalPrudentialPlan(int $planId): bool
    {
        return Plan::query()
            ->where('pid', $planId)
            ->where('carrier', 80)
            ->whereIn('pl_type', ['HOSPITAL', 'CRITICAL'])
            ->exists()  || in_array($planId, Plan::PRUDENTIAL_SALARY_BELOW_100k);
    }

    private function getPrice(int $pid, string $tier, int $minAge = null, int $maxAge = null)
    {
        $planPricingType = Plan::where('pid', $pid)->value('plan_pricing_type');

        $query = PlanPricingDisplay::query();
        if ($planPricingType == '2') {
            $query = $query->where([
                ['age1', '<=', $minAge],
                ['age2', '>=', $maxAge ?? $minAge],
            ]);

        }
        $query2 = clone $query;

        $price = $query->where([
                'pid' => $pid,
                'tier' => $tier,
                'pricing_status' => 1,
            ])->value('price_male_nons');

        if (!$price && $this->isBundle){
            $price = $query2->where([
                'pid' => $pid,
                'tier' => 'IO',
                'pricing_status' => 1,
            ])->value('price_male_nons');
        }
        return $price;
    }

    /**
     * @param int $pid
     * @return float|null
     * @throws Exception
     */
    private function calculatePrudentialPrice(int $pid): ?float
    {

        if (!$this->isHospitalOrCriticalPrudentialPlan($pid)) {
            return $this->getPrice($pid, $this->tier, $this->memberAge);
        }


        $memberPrice = $this->getPrice($pid, 'IO', $this->memberAge);
        if ($this->tier == 'IO') {
            return $memberPrice;
        }

        if ($this->tier == 'IC') {
            if (!$this->childCount) {
                throw new Exception('Dependent information is required when tier is either IC,IS or IF');
            }

            return $memberPrice + ($this->childCount * $this->getPrice($pid, 'IC',24, 18));
        }

        if ($this->tier == 'IS') {
            if (!$this->spouseAge) {
                throw new Exception('Dependent information is required when tier is either IC,IS or IF');
            }

            return $this->getPrice($pid, 'IS', $this->spouseAge) + $memberPrice;
        }

        if ($this->tier == 'IF') {
            if (!$this->childCount) {
                throw new Exception('Dependent information is required when tier is either IC,IS or IF');
            }

            if (!$this->spouseAge) {
                throw new Exception('Dependent information is required when tier is either IC,IS or IF');
            }

            $spousePrice = $this->getPrice($pid, 'IS', $this->spouseAge);
            $childPrice = $this->getPrice($pid, 'IC', 24, 18);

            return $spousePrice + ($this->childCount  * $childPrice) + $memberPrice;
        }

        return null;
    }
}
