<?php


namespace App\Service;


use App\DependentInPolicy;
use App\Helpers\DecryptEcryptHelper;
use App\PlanOverview;
use App\PlanPricingDisplay;
use App\PlanQuestionView;
use App\Policy;
use App\UserInfoDepMedBex;

class DependentInfoService
{

    /**
     * @param int $policyId
     * @return array
     */
    public static function getPlanPricingIdsFromPlanOverview(int $policyId): array
    {
        return PlanOverview::query()->where('policy_id', $policyId)->pluck('plan_pricing_id')->toArray();
    }

    /**
     * @param string $column
     * @param array $planPricingIds
     * @return bool
     */
    public static function checkRequiredColumn(string $column, array $planPricingIds): bool
    {
        $requiredConf = PlanPricingDisplay::query()->whereIn('plan_pricing_id', $planPricingIds)->pluck($column);
        return $requiredConf->contains(1);
    }

    /**
     * @param $planPricingIds
     * @return bool
     */
    public static function checkHealthQuestionsRequired($planPricingIds): bool
    {
        $status = false;
        if (PlanQuestionView::query()->whereIn('plan_pricing_id', $planPricingIds)->exists()) {
            $status = true;
        }
        return $status;
    }

    public static function getDependentsOfPolicy($policyId)
    {
        $depData = DependentInPolicy::query()
            ->where('dependents_in_policy.policy_id', '=', $policyId)
            ->select('d_fname', 'd_lname', 'd_mname', 'd_dob', 'd_relate', 'dependent_id', 'd_gender', 'd_ssn','dhrq','dhrq2','dwrq')
            ->get(); 
        if (count($depData) > 0) foreach ($depData as $data){
            $data->d_ssn = DecryptEcryptHelper::decryptInfo($data->d_ssn);
        }
        return $depData;
    }

    public static function checkIfSSNExist($policyId, $ssn, $did = null)
    {
        $encSSN = DecryptEcryptHelper::encryptInfo($ssn);
        if($did){
            $existsForDependent = DependentInPolicy::where('dependent_id', $did)
            ->where('d_ssn', $encSSN)
            ->exists();
    
            if ($existsForDependent) {
                return false;
            }
        }

        $user = PlanOverview::where('policy_id', $policyId)->first(['userid']);
    
        if (!$user) {
            return false;
        }
        $policyIds = PlanOverview::where('userid', $user->userid)->pluck('policy_id')->toArray();
        return DependentInPolicy::whereNotIn('policy_id' , $policyIds)
            ->where('d_ssn', $encSSN)
            ->exists();
    }
    

    // check if health questions are configured for policy or dependent in DB
    public static function checkHealthQuestionConfiguredForPolicyDep($id, $type)
    {
        if ($type == 'dependent') {
            return UserInfoDepMedBex::query()
                ->where('dependent_id', '=', $id)
                ->exists();
        } elseif ($type == 'policy') {
            $policy = Policy::find($id);
            return $policy->userInfoDepMedBex->isNotEmpty();
        }
    }

    public static function checkIfPolicyHasExtraHealth($policy_id)
    {
        return PlanOverview::query()->where('policy_id', $policy_id)
            ->whereHas('hasExtraHelthPlans')
            ->exists();
    }
}
