<?php


namespace App\Service\Policy;


use App\Helpers\EmailSendHelper;
use App\Service\MessageService;
use Carbon\Carbon;

/**
 * Class PolicyMessageService
 * @package App\Service\Policy
 */
class PolicyMessageService extends MessageService
{


    public function sendUserInfoEmailEmail($userInfo, $oldEmail)
    {
        $contentData = [
            "Old Email" => $oldEmail,
            "New Email" => $userInfo->cemail,
        ];
        return EmailSendHelper::sendUserPolicyActionEmail($userInfo->userid, "User Email Changed", "Your email has been updated or changed", 'updateinfo', $contentData, 'PIC');
    }

    public function sendUserInfoPhoneEmail($userInfo, $oldPhones, $newPhones)
    {
        $contentData = [
            "Old Phone1" => $oldPhones['phone1'] ?: "",
            "New Phone1" => $oldPhones['phone2'] ?: "",
            "Old Phone2" => $newPhones['phone1'] ?: "",
            "New Phone2" => $newPhones['phone2'] ?: "",
        ];
        return EmailSendHelper::sendUserPolicyActionEmail($userInfo->userid, "User Email Changed", "Your contact information has been updated or changed", 'updateinfo', $contentData, 'PIC');
    }

    public function sendUserUpdateEmail($userInfo)
    {
        $contentData = [
            "Name" => isset($userInfo) ? $userInfo->fullname : null,
            "DOB" => ($userInfo->cdob != '') ? 'XX/XX/' . date('Y', strtotime($userInfo->cdob)) : '',
            "SSN" => "XXX-XX-" . $userInfo->cssn4,
            "Gender" => $userInfo->cgender == 0 ? 'Male' : 'Female'
        ];
        return EmailSendHelper::sendUserPolicyActionEmail($userInfo->userid, "User Info Changed", "Your user information has been updated or changed", 'updatepolicy', $contentData, 'PIC');
    }
}
