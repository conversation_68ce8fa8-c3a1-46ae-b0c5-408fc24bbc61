<?php


namespace App\Service;


use App\AgentInfo;
use App\Helpers\GuzzleHelper;
use App\Policy;
use App\Traits\ResponseMessage;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use phpseclib\System\SSH\Agent;

class CustomValidationService
{

    use ResponseMessage;

    protected function failedValidation($errors)
    {
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }

    public function validateAddressUSPS($address1, $address2, $city, $state, $zip)
    {
        $endpoint = "v2/service/validate-address";
        $url = config('app.SERVICE_APP_URL.url') . $endpoint;
        $body = [
            "address1" => $address1,
            "apt" => $address2,
            "city" => $city,
            "state" => $state,
            "zip" => $zip
        ];

        try {
            $token = $this->generateAuthToken();
            $responseJson = GuzzleHelper::postApi($url, ['Authorization' => "Bearer $token"], $body);
            $response = json_decode($responseJson, true);

            if($response['statusCode'] == 200 && $response['status'] == "success") {
                return true;
            }

            $failedResult = [];
            if($response['statusCode'] == 422) {
                if(isset($response['errors']['city']))
                    $failedResult['city'] = $response['errors']['city'];
                else if(isset($response['errors']['zip']))
                    $failedResult['zip'] = $response['errors']['zip'];
                else if(isset($response['errors']['state']))
                    $failedResult['state'] = $response['errors']['state'];
            }
            
            if(isset($response['isValid']) && !$response['isValid'] && $response['statusCode'] == 400) {
                $failedResult['error'] = $response['message'];
            }

            $this->failedValidation($failedResult);
        } catch (\Exception $e) {
            $error = [
                'statusCode' => 400,
                'message' => $e->getMessage()
            ];
            $this->failedValidation($error);
        }

        // if (array_key_exists('Error', $response['Address'])) {
        //     $result['address1'] = ['Address1 is not valid'];
        //     $this->failedValidation($result);
        // } else {
        //     if (strtolower($response['Address']['City']) != strtolower($city)) {
        //         $failedResult['city'] = ['City is not valid'];
        //     }
        //     if ($response['Address']['Zip5'] != $zip) {
        //         $failedResult['zip'] = ['Zip is not valid'];
        //     }
        //     if (strtolower($response['Address']['State']) != strtolower($state)) {
        //         $failedResult['state'] = ['State is not valid'];
        //     }
        //     if ($failedResult == []) {
        //         return true;
        //     }
        //     $this->failedValidation($failedResult);
        // }
    }

    protected function validateRouting($routingNumber)
    {
        try {
            $endpoint = 'v2/service/routing-number-bank-lookup';
            $url = config('app.SERVICE_APP_URL.url') . $endpoint . '?q=' . $routingNumber;
            $token = $this->generateAuthToken();
            $response = GuzzleHelper::getApi($url, ['Authorization' => "Bearer $token" ]);
            return json_decode($response, true);
        } catch (\Exception $e) {
            return ['statusCode' => 400];
        }
    }

    public function validateRoutingNumber($routingNumber)
    {
        $data = $this->validateRouting($routingNumber);
        if ($data['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    public function validateRoutingNumberWithResponse($routingNumber)
    {
        $data = $this->validateRouting($routingNumber);
        if ($data['code'] == 200) {
            $result = [
                'routing_number' => $data['rn'],
                'bank_name' => $data['name']
            ];
            return $this->successResponse('Routing number successfully validated.', $result);
        } else {
            $failedResult['routing_number'] = ['Failed to validate routing number.'];
            $this->failedValidation($failedResult);
        }
    }

    public function validateAgentEmail($email, $agentId)
    {
        $agentInfo = AgentInfo::find($agentId);
        if (!$agentInfo)return false;
        if ($agentInfo->email == $email) {
            return true;
        } else {
            $canEditThroughStatus = AgentInfo::query()
                ->where('agent_id', '!=', $agentId)
                ->where('agent_email', '=', $email)
                ->whereIn('agent_status', [AgentInfo::STATUS_APPROVED, AgentInfo::STATUS_PENDING])
                ->doesntExist();
            if ($canEditThroughStatus) {
                return AgentInfo::query()
                    ->where('agent_id', '!=', $agentId)
                    ->where('agent_email', '=', $email)
                    ->where(function ($query) {
                        $query->where('agent_status', '!=', AgentInfo::STATUS_REMOVED)
                            ->Where('agent_status', '!=', AgentInfo::STATUS_DISABLED);
                    })
                    ->whereHas('policies', function ($query) {
                        $query->where('status', '=', Policy::STATUS_ACTIVE)
                            ->orWhere('status', '=', Policy::STATUS_TERMED);
                    })
                    ->doesntExist();
            } else {
                return false;
            }
        }
    }

    public function validateAgentPhone($phone, $agentId, $phoneField = 'agent_phone1')
    {
        $agentInfo = AgentInfo::find($agentId);
        if (!$agentInfo)return false;
        if ($agentInfo->agent_phone1 == $phone) {
            return true;
        } else {
            $canEditThroughStatus = AgentInfo::query()
                ->where('agent_id', '!=', $agentId)
                ->where($phoneField, '=', $phone)
                ->whereIn('agent_status', [AgentInfo::STATUS_APPROVED, AgentInfo::STATUS_PENDING])
                ->doesntExist();
            if ($canEditThroughStatus) {
                return AgentInfo::query()
                    ->where('agent_id', '!=', $agentId)
                    ->where('agent_phone1', '=', $phone)
                    ->where(function ($query) {
                        $query->where('agent_status', '!=', AgentInfo::STATUS_REMOVED)
                            ->Where('agent_status', '!=', AgentInfo::STATUS_DISABLED);
                    })
                    ->whereHas('policies', function ($query) {
                        $query->where('status', '=', Policy::STATUS_ACTIVE)
                            ->orWhere('status', '=', Policy::STATUS_TERMED);
                    })
                    ->doesntExist();
            } else {
                return false;
            }
        }
    }

    public function validateAgentSsn($ssn, $agentId)
    {
        $agentInfo = AgentInfo::find($agentId);
        if (!$agentInfo)return false;
        if ($agentInfo->agent_ssn == $ssn) {
            return true;
        } else {
            $canEditThroughStatus = AgentInfo::query()
                ->where('agent_id', '!=', $agentId)
                ->where('agent_ssn', '=', $ssn)
                ->whereIn('agent_status', [AgentInfo::STATUS_APPROVED, AgentInfo::STATUS_PENDING])
                ->doesntExist();
            if ($canEditThroughStatus) {
                return AgentInfo::query()
                    ->where('agent_id', '!=', $agentId)
                    ->where('agent_ssn', '=', $ssn)
                    ->whereHas('policies', function ($query) {
                        $query->where('status', '=', Policy::STATUS_ACTIVE)
                            ->orWhere('status', '=', Policy::STATUS_TERMED);
                    })
                    ->doesntExist();
            } else {
                return false;
            }
        }
    }

    public function validatePhoneNumVerify($phone , $line_type = false){
        // $url = config('corenroll.corenroll_api_url')."api/v1/validate-phone-numverify?phone={$phone}";
        $endpoint = "v2/service/validate-number";
        $url = config('app.SERVICE_APP_URL.url') . $endpoint;
        $body = [
            "number" => $phone
        ];

        try {
            $token = $this->generateAuthToken();
            $responseJson = GuzzleHelper::postApi($url, ['Authorization' => "Bearer $token"], $body);
            $response = json_decode($responseJson, true);
            if($response['statusCode'] == 200){
                if($line_type){
                    return $response;
                }
                return true;
            }else{
                return false;
            }
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function validateBulkPhoneNumVerify(array $phones)
    {
        $url = config('corenroll.corenroll_api_url')."api/v1/validate-bulk-phone-numverify";

        try {
            $responseJson = GuzzleHelper::postApi($url,[], ['phones' => $phones]);
            $response = json_decode($responseJson, true);

            if($response['statusCode'] != 200) {
                throw new \Exception('Error during bulk phone validation API call.');
            }

            return $response['data']['result']['phones'];

        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    public function validateEmailNeverBounce($email)
    {
        $endpoint = "v2/service/validate-email";
        $url = config('app.SERVICE_APP_URL.url') . $endpoint;
        $body = [
            "email" => $email
        ];
        try {
            $token = $this->generateAuthToken();
            $responseJson = GuzzleHelper::postApi($url, ["Authorization" => "Bearer $token"], $body);
            $response = json_decode($responseJson, true);
            if($response['statusCode'] == 200){
                return true;
            }else{
                return false;
            }
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function validateBulkEmailNeverBounce(array $emails){
        $url = config('corenroll.corenroll_api_url')."api/v1/validate-bulk-email-neverbounce";

        try {
            $responseJson = GuzzleHelper::postApi($url, [], ['emails'=>$emails]);
            $response = json_decode($responseJson, true);

            if($response['statusCode'] != 200) {
                throw new \Exception('Error during bulk phone validation API call.');
            }

            return $response['data']['result'];

        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    /**
     * @param $agentId
     * @param $value
     * @return bool
     * edit the personalized site/agent_web should not match with any of active or pending reps.
     */
    public function validateAgentWebsite($agentId,$value){
        $agentInfo = AgentInfo::find($agentId);
        if (!$agentInfo)return false;
        if ($agentInfo->agent_web == $value) {
            return true;
        } else {
            return AgentInfo::query()
                ->where('agent_id', '!=', $agentId)
                ->where('agent_web', '=', $value)
                ->whereIn('agent_status', [AgentInfo::STATUS_APPROVED, AgentInfo::STATUS_PENDING])
                ->doesntExist();
        }
    }

    public function totalBeneficiaryPercentage($policy_id, $benId = null)
    {
        $query = PolicyBeneficiary::query()
            ->where('bpolicy_id', '=', $policy_id);
        if (isset($benId)) {
            $query->whereNotIn('ben_id', [$benId]);
        }
        return $query->sum('ben_percentage');
    }

    public function checkBeneficiaryPercentage($total, $current)
    {
        $full = (float)100;
        if ($full >= ($total + $current)) {
            return true;
        } else {
            return false;
        }
    }

    public function checkBeneficiaryRelation($policyId, $relation,$benId = null)
    {
        /**
         * $relations is unused for now
         * can be used in future - if enchantments needed to be done
         */
        $query = PolicyBeneficiary::query()
            ->where('bpolicy_id', '=', $policyId);
        if (isset($benId)) {
            $query->whereNotIn('ben_id', [$benId]);
        }
        $relations  = $query->get()
            ->pluck('brelate')
            ->toArray();
        if (in_array(PolicyBeneficiary::RELATION_SPOUSE, $relations)) {
            return false;
        } else {
            return true;
        }
    }

    // Token Generation for service-manager
    protected function generateAuthToken($body = null): string
    {
        $endpoint = 'v1/auth/token';
        $authUrl = config('app.SERVICE_APP_URL.url') . $endpoint;
        $clientId = config('app.SERVICE_APP_URL.clientId');

        try {
            $responseJson = GuzzleHelper::postApi($authUrl, [], ['client_id' => $clientId]);
            $response = json_decode($responseJson, true);

            if ($response['statusCode'] == 200 && isset($response['data']['token'])) {
                return $response['data']['token'];
            }

            throw new \Exception('Unable to generate auth token');

        } catch (\Throwable $e) {
            throw new \Exception("Token generation failed: " . $e->getMessage());
        }
    }

    
}
