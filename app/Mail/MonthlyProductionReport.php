<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class MonthlyProductionReport extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $directCount;
    protected $downlineCount;
    protected $newBusinessCount;
    protected $repsWithNoEnrollments;

    public function __construct($directCount, $downlineCount, $newBusinessCount, $repsWithNoEnrollments)
    {
        $this->directCount = $directCount;
        $this->downlineCount = $downlineCount;
        $this->newBusinessCount = $newBusinessCount;
        $this->repsWithNoEnrollments = $repsWithNoEnrollments;
      
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Monthly Production Report')
            ->markdown('emails.monthly-business-report')
            ->with([
                'directCount' => $this->directCount,
                'downlineCount' => $this->downlineCount,
                'newBusinessCount' => $this->newBusinessCount,
                'repsWithNoEnrollments' => $this->repsWithNoEnrollments
            ]);
    }
}