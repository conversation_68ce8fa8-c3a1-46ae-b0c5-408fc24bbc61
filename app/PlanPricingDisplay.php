<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PlanPricingDisplay extends Model
{
    protected $table = 'plans_pricing_display';
    protected $primaryKey = null;
    public $incrementing = false;
    public $timestamps = false;

    const MET_LIFE_CID = 57;
    const MAJOR_MEDICAL_IHA_CARRIER_ID = 77;

    public function planStates()
    {
        return $this->hasMany(PlanState::class, 'pid', 'pid');
    }

    public function planZips()
    {
        return $this->hasMany(PlanZip::class, 'pid', 'pid');
    }
}
