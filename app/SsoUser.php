<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SsoUser extends Model
{
    //
    protected $connection = 'sso';
    protected $table = 'sso_users';
    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'name',
        'email',
        'phone',
        'user_type',
        'email_verified_at',
        'password',
        'verification_code',
        'remember_token',
        'deviceid',
        'macid',
        'device_verified',
        'device_verified_at',
        'device_token',
        'verification_code_expiry_date',
        'created_at',
        'updated_at',
    ];
    
}
