<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PlanTier extends Model
{
    protected $table = 'plan_tier';
    protected $primaryKey = 'idplan_tier';

    // get plans details from plan_tier
    public function getPlanDetail()
    {
        return $this->hasOne('App\Plan', 'pid', 'pid_tier');
    }
    // get plan pricing from plan tier
    public function planPricing()
    {
        return $this->hasOne('App\PlanPricing', 'plan_id', 'idplan_tier');
    }
}
