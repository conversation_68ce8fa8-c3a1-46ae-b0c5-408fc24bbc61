<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class CarrierInfo extends Model
{
    protected $table = 'carrier_info';
    protected $primaryKey = 'cid';
    protected $fillable = ['carrier_name','carrier_code','email','carrier_company_id','added','company_logo'];
    public function carrierState()
    {
        return $this->hasMany('App\CarrierState', 'carrier_id', 'cid');
    }
}
