<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class DependentPolicy extends Model
{
    protected $table = 'dependent_policies';
    protected $primaryKey = 'iddependents_policies_ai';
    protected $guarded =['iddependents_policies_ai'];

    // get dependent info
    public function getDependentInfo(){
        return $this->hasOne('App\Dependent','did','dependent_id');
    }
    // get policy info
    public function getpolicyInfo(){
        return $this->hasOne('App\Policy','policy_id','policy_id');
    }
}
