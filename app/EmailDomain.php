<?php

namespace App;

use App\EmailCCLogs;
use Illuminate\Database\Eloquent\Model;

class EmailDomain extends Model
{
    protected $primaryKey = 'id';
    protected $connection = 'mysql';
    protected $table = 'email_domains';
    // Disable default timestamp handling
    public $timestamps = false;

    public function ccLogs()
    {
        return $this->hasMany(EmailCCLogs::class, 'email_domain_id');
    }
}
