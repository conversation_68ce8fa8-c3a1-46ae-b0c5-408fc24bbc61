<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AgentUser extends Model
{
    protected $table = 'agent_users';
    protected $primaryKey = 'id';

    protected $fillable = [
        'agent_id',
        'username',
        'password',
        'is_root',
        'last_attempt',
        'agent_code',
        'a1',
        'a2',
        'status',
        'q2',
        'q1',
        'cookie_token',
        'verification_code',
        'phone',
        'password_verification_code',
        'is_accepted',
        'is_decided',
        'registeration_verification_code',
        'is_temp_password',
        'disphone',
        'disemail',
        'tagline',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_email_valid'
    ];
}
