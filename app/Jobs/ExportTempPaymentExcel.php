<?php

namespace App\Jobs;

use App\AdminUser;
use App\ExtraHealthPaymentFileGeneration;
use App\Service\MessageService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ExportTempPaymentExcel implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $admin_data;
    /**
     * @var array
     */
    private $tempPayments;

    public function __construct(array $tempPayments, array $admin_data)
    {
        $this->tempPayments = $tempPayments;
        $this->admin_data = $admin_data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $data= [];

        $data['added_by'] = $this->admin_data['added_by'];
        $data['added_by_id'] = $this->admin_data['added_by_id'];
        $data['file_generation_started'] = time();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([
            'Payment ID',
            'Rep Name',
            'Member Name',
            'Member Email',
            'Member Phone Number',
            'Transaction Status',
            'Card Last 4 Digits',
            'Card Expiration Date',
            'Billing Address',
            'City',
            'State',
            'ZIP',
            'Rep IP Address',
            'Member IP Address',
            'Transaction Amount',
            'Transaction Date & Time'
        ], null, 'A1');

        $row = 2;
        foreach ($this->tempPayments as $item) {
            $customerFullName = trim(($item['firstname'] ?? '') . ' ' . ($item['middlename'] ?? '') . ' ' . ($item['lastname'] ?? ''));
            $billingAddress = trim(($item['street_address_line1'] ?? '') . ' ' . ($item['street_address_line2'] ?? ''));

            $sheet->setCellValue('A' . $row, $item['payment_id'] ?? '');
            $sheet->setCellValue('B' . $row, $item['agent_full_name'] ?? '');
            $sheet->setCellValue('C' . $row, $customerFullName);
            $sheet->setCellValue('D' . $row, $item['email'] ?? '');
            $sheet->setCellValue('E' . $row, $item['phone_number'] ?? '');
            $sheet->setCellValue('F' . $row, $item['transaction_status'] ?? '');
            $sheet->setCellValue('G' . $row, $item['card_detail'] ?? '');
            $sheet->setCellValue('H' . $row, $item['card_expiration_full_date'] ?? '');
            $sheet->setCellValue('I' . $row, $billingAddress);
            $sheet->setCellValue('J' . $row, $item['city_name'] ?? '');
            $sheet->setCellValue('K' . $row, $item['state_name'] ?? '');
            $sheet->setCellValue('L' . $row, $item['postal_code'] ?? '');
            $sheet->setCellValue('M' . $row, $item['agent_ip_address'] ?? '');
            $sheet->setCellValue('N' . $row, $item['member_ip_address'] ?? '');
            $sheet->setCellValue('O' . $row, $item['amount'] . '$' ?? '');
            $sheet->setCellValue('P' . $row, $item['transaction_date'] ?? '');

            $row++;
        }

        $columnCount = $sheet->getHighestColumn();
        $columnRange = 'A:' . $columnCount;
        $sheet->getStyle($columnRange)->getAlignment()->setWrapText(true);
        foreach (range('A', $columnCount) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        $data['no_of_record'] = count($this->tempPayments);
        $data['file_name'] = time() . '_' . 'EXTRA_HEALTH_PAYMENT_REPORT.xlsx';
        $data['store_path'] = storage_path() . '/' . $data['file_name'];
        $data['file_path'] = 'extra_health/files/';
        $writer = new Xlsx($spreadsheet);
        $filePath = $data['store_path'];
        $writer->save($filePath);

        Storage::disk('s3-third')->put($data['file_path'].$data['file_name'] , File::get(storage_path().'/'.$data['file_name']));
        unlink(storage_path().'/'.$data['file_name']);
        $data['file_generation_ended'] = time();
        ExtraHealthPaymentFileGeneration::create($data);
        self::sendUserEmployerEmail($data);
    }
    public static function sendUserEmployerEmail($data)
    {
        $adminDetail = AdminUser::find($data['added_by_id']);
        if($adminDetail){
            $emailConfigurationName = "EXPOER_EXCEL_TEMPLETE";
            $subject = 'Extra health payment list export excel sheet completed';
            $message = "This email is intended to inform you that the file you created using the extra health payment list has been completed. The ".'"View Generated Report" '."part of Extra Payment List offers the option to download the excel file, or you can just click <a href=".`"`.config('filesystems.disks.s3-third.url').'/' . $data['file_path']. $data['file_name'].`"`.">here</a>."."to download it.";
            $emailData = [
                'email_message_configuration_name' => $emailConfigurationName,
                'toAddress' => $adminDetail->email,
                'ccAddress' => [],
                'subject' => $subject,
                'message' => $message,
                'data' => [],
                'dataName' => $adminDetail->name
            ];
            $messageService = new MessageService();
            $messageService->sendEmailWithContentData($emailData);
        }
    }
}
