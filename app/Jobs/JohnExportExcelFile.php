<?php

namespace App\Jobs;

use App\CcDetail;
use App\ClientFileGeneration;
use App\EftDetail;
use App\GroupFileGeneration;
use App\GroupInfo;
use App\GroupPlans;
use App\Helpers\DecryptEcryptHelper;
use App\PlanOverview;
use App\UserInfoPolicyAddress;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class JohnExportExcelFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

     public $tries = 100;
     public $timeout = 9999999;

    private $filter;
    private $admin_data;
    public function __construct($filter,$admin_data)
    {
        $this->filter = $filter;
        $this->admin_data = $admin_data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
       
        // ini_set('max_execution_time', '0');
        // ini_set('memory_limit', '-1');
        // set_time_limit(0);
        $request = $this->filter;
        
       
        $queryParams = $request;

        // Define validation rules for query parameters
        $rules = [
            'group' => 'required',
            'edaterange' => 'nullable|in:0,1',
            'effdate' => 'nullable|string|required_if:edaterange,0',
            'effdate_s' => 'nullable|date|required_if:edaterange,1',
            'effdate_e' => 'nullable|date|required_if:edaterange,1',
            'termdate' => 'nullable|string',
        ];

        // Create a validator instance using the Validator facade
        $validator = Validator::make($queryParams, $rules);
       
        // Check if the validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors(),
            ], 422);
        }
        $data['added_by'] = $this->admin_data['added_by'];
        $data['added_by_id'] = $this->admin_data['added_by_id'];
        $data['file_generation_started'] = time();
        $query = UserInfoPolicyAddress::query();

        $brands = PlanOverview::select('brand')->distinct()->pluck('brand');

        // Log::info($request->all());
        $query = UserInfoPolicyAddress::select([
            'policy_id',
            'agent_id',
            'term_date',
            'cfname',
            'cmname',
            'clname',
            'status',
            'cdob',
            'phone1',
            'phone2',
            'address1',
            'address2',
            'state',
            'city',
            'zip',
            'edate',
            'effective_date',
            'payment_type',
            'recurring_payment_type',
            'payment_id',
            'recurring_payment_id',
            'cemail',
            'cgender',
            'cssn',
            'agent_code',
            'efees',
            'agent_fname',
            'agent_lname',
            'eid',
            'employer',
        ]);
       
        // Filter based on group if provided
        if (isset($request['group']) && trim($request['group']) != 'ALL') {
            $group = trim($request['group']);
            $query->where('eid', $group);
         
        }

        // Filter based on brand if provided
        if (isset($request['brand']) && trim($request['brand']) != 'ALL') {
            $brand = trim($request['brand']);

            $query->whereHas('plans', function ($query) use ($brand) {
                $query->where('brand', 'like', "%$brand%");
            });
        }

        // Check for effective date conditions
        if (isset($request['edaterange']) && $request['edaterange'] == '0' && isset($request['effdate']) && $request['effdate'] != 'ALL') {
            $effdate = trim($request['effdate']);
            $query->where('effective_date', '=', $effdate);
        }

        if (isset($request['edaterange']) && $request['edaterange'] == '1' && isset($request['effdate_s']) && trim($request['effdate_s']) != '' && isset($request['effdate_e']) && trim($request['effdate_e']) != '') {
            $effdate_s = trim($request['effdate_s']);
            $effdate_e = trim($request['effdate_e']);
            $query->whereBetween('effective_date', [$effdate_s, $effdate_e]);
        }

        // Check for termination date condition
        if (isset($request['termdate']) && $request['termdate'] != 'ALL') {
            $termdate = trim($request['termdate']);
            $query->where('userinfo_policy_address.term_date', '=', $termdate);
        }


        // Eager load plans with selected columns
        $query->with(['plans' => function ($query1) {
            $query1->select([
                'cid',
                'policy_id',
                'idilus_surcharge',
                'ppptype',
                'transferred',
                'pefees',
                'boffice',
                'brand' // Make sure to include the brand column for filtering
            ]);
            $query1->where('status', '!=', 'WITHDRAWN');
        },'getGroup']);

        // Limit the number of results
        // $list = $query->take(5)->get();
        // dd($query->toSql());
        $list = $query->get();
        // return response()->json([$list[0]]);
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header
        $sheet->setCellValue('A1', "Status")
            ->setCellValue('B1', "Term Date")
            ->setCellValue('C1', "Effective")
            ->setCellValue('D1', "Last Name")
            ->setCellValue('E1', "First Name")
            ->setCellValue('F1', "Group")
            ->setCellValue('G1', "Plan")
            ->setCellValue('H1', "Pay Terms")
            ->setCellValue('I1', "Recurring Pay Terms")
            ->setCellValue('J1', "Home Phone")
            ->setCellValue('K1', "Work Phone")
            ->setCellValue('L1', "Social Security")
            ->setCellValue('M1', "DOB")
            ->setCellValue('N1', "Employer Name")
            ->setCellValue('O1', "Contact Person")
            ->setCellValue('P1', "Home Address")
            ->setCellValue('Q1', "City/Town")
            ->setCellValue('R1', "State")
            ->setCellValue('S1', "Zip Code")
            ->setCellValue('T1', "Tier")
            ->setCellValue('U1', "Premium")
            ->setCellValue('V1', "Idilus Surcharge")
            ->setCellValue('W1', "Enrollment Fee")
            ->setCellValue('X1', "Collected")
            ->setCellValue('Y1', "Agents Name")
            ->setCellValue('Z1', "CC Name")
            ->setCellValue('AA1', "CC Number")
            ->setCellValue('AB1', "Exp Date")
            ->setCellValue('AC1', "Bank Account #")
            ->setCellValue('AD1', "Routing #")
            ->setCellValue('AE1', "Enrollment Date")
            ->setCellValue('AF1', "Upline")
            ->setCellValue('AG1', "Back Office")
            ->setCellValue('AH1', "NAWU ROLL")
            ->setCellValue('AI1', "ID");

        $i = 2; // starting row for data

        foreach ($list as $getPolicyRow) {
            $subcode_j = $getPolicyRow->getGroup->gname;
            $subcode_m = $getPolicyRow->getGroup;

            // Log::info($getPolicyRow);
            // Log::info($getPolicyRow->plans);

            // $ppptype = $getPolicyRow->plans? $getPolicyRow->plans[0]->ppptype : null;
            if ($getPolicyRow->plans->count() > 0) {
                $ppptype = $getPolicyRow->plans[0]->ppptype;
                $plan_monthly = $getPolicyRow->plans()
                    ->first()['formatted_' . $ppptype];

                $getFeesRow = GroupPlans::where('plan_id', $getPolicyRow->plans[0]->cid)->first();
                // dd($getFeesRow);
                $efs = $getFeesRow->gefees + $getFeesRow->aefees + $getFeesRow->cefees + $getFeesRow->cafees;
            } else {
                $plan_monthly = 0;
                $efs = 0;
            }




            $ssn = DecryptEcryptHelper::decryptInfo($getPolicyRow->cssn);

            $newDate = date("m/d/Y", strtotime($getPolicyRow->effective_date));
            $newDate2 = date("m/d/Y", strtotime($getPolicyRow->cdob));

            $formatted_number1 = substr($getPolicyRow->phone1, 0, 3) . '-' . substr($getPolicyRow->phone1, 3, 3) . '-' . substr($getPolicyRow->phone1, 6);
            $formatted_number2 = substr($getPolicyRow->phone2, 0, 3) . '-' . substr($getPolicyRow->phone2, 3, 3) . '-' . substr($getPolicyRow->phone2, 6);

            $foo = substr($ssn, 0, 3) . '-' . substr($ssn, 3, 2) . '-' . substr($ssn, 5);

            $cltd = $efs + $plan_monthly;

            $sheet->setCellValue("A$i", $getPolicyRow->status)
                ->setCellValue("B$i", $getPolicyRow->term_date)
                ->setCellValue("C$i", $newDate)
                ->setCellValue("D$i", $getPolicyRow->clname)
                ->setCellValue("E$i", $getPolicyRow->cfname)
                ->setCellValue("F$i", $subcode_j)
                ->setCellValue("G$i", $getPolicyRow->plan_name_system)
                ->setCellValue("H$i", $getPolicyRow->payment_type);

            $recurringPaymentType = $getPolicyRow->recurring_payment_type == "same" ? $getPolicyRow->payment_type : $getPolicyRow->recurring_payment_type;
            $sheet->setCellValue("I$i", $recurringPaymentType)
                ->setCellValue("J$i", $formatted_number1)
                ->setCellValue("K$i", $formatted_number2)
                ->setCellValue("L$i", $foo)
                ->setCellValue("M$i", $newDate2)
                ->setCellValue("N$i", $getPolicyRow->employer)
                ->setCellValue("O$i", $subcode_m->gcontact_fname . ' ' . $subcode_m->gcontact_lname)
                ->setCellValue("P$i", $getPolicyRow->address1)
                ->setCellValue("Q$i", $getPolicyRow->city)
                ->setCellValue("R$i", $getPolicyRow->state)
                ->setCellValue("S$i", $getPolicyRow->zip)
                ->setCellValue("T$i", $getPolicyRow->tier)
                ->setCellValue("U$i", $plan_monthly)
                ->setCellValue("V$i", $getPolicyRow->idilus_surcharge)
                ->setCellValue("W$i", $efs)
                ->setCellValue("X$i", $cltd)
                ->setCellValue("Y$i", $getPolicyRow->agent_lname . ', ' . $getPolicyRow->agent_fname);

            $paymentDetails = $this->getPaymentDetails($getPolicyRow);
            $sheet->setCellValue("Z$i", $paymentDetails['contact'])
                ->setCellValue("AA$i", $paymentDetails['num'])
                ->setCellValue("AB$i", $paymentDetails['exp'])
                ->setCellValue("AC$i", $paymentDetails['bank_account'])
                ->setCellValue("AD$i", $paymentDetails['bank_routing']);

            $eed = date("Y-m-d", strtotime($getPolicyRow->edate));
            $sheet->setCellValue("AE$i", $eed)
                // ->setCellValue("AF$i", getUplineCodeNameJP($getPolicyRow->agent_id))
                ->setCellValue("AF$i", $getPolicyRow->agent_id)
                ->setCellValue("AG$i", $getPolicyRow->boffice)
                ->setCellValue("AH$i", $getPolicyRow->transferred)
                ->setCellValue("AI$i", $getPolicyRow->policy_id);

            $i++;
        }

        $data['no_of_record'] = count($list);
        $data['file_type'] = 'john';
        $data['file_name'] = time() . '_' . 'JOHN_REPORT.xlsx';
        $data['store_path'] = storage_path() . '/' . $data['file_name'];
        $data['file_path'] = 'group/files/';
        $writer = new Xlsx($spreadsheet);
        $filePath = $data['store_path'];
        $writer->save($filePath);

        Storage::disk('s3-third')->put($data['file_path'] . $data['file_name'], File::get(storage_path() . '/' . $data['file_name']));
        unlink(storage_path() . '/' . $data['file_name']);
        $data['file_generation_ended'] = time();
        $client_file = GroupFileGeneration::create($data);
        Log::info('John Reprt created with id'. $client_file->id);
        Log::info($data);
    }
    private function getPaymentDetails($policyRow)
    {
        $paymentDetails = [
            'contact' => '',
            'num' => '',
            'exp' => '',
            'bank_account' => '',
            'bank_routing' => ''
        ];

        if ($policyRow->payment_type == 'chck') {
            if ($policyRow->recurring_payment_type == 'cc') {
                $payment = CcDetail::where('cc_id', $policyRow->recurring_payment_id)->first();
                if ($payment) {
                    $paymentDetails = [
                        'contact' => $payment->cc_contactfname . ' ' . $payment->cc_contactlname,
                        'num' => "" . DecryptEcryptHelper::decryptInfo($payment->cc_num),
                        'exp' => $payment->cc_expmm . '-' . $payment->cc_expyyyy,
                        'bank_account' => '',
                        'bank_routing' => ''
                    ];
                }
                
               
            } elseif ($policyRow->recurring_payment_type == 'eft') {
                $payment = EftDetail::where('bank_id', $policyRow->recurring_payment_id)->first();
                if ($payment) {
                    $paymentDetails = [
                        'contact' => '',
                        'num' => '',
                        'exp' => '',
                        'bank_account' => "" . DecryptEcryptHelper::decryptInfo($payment->bank_account),
                        'bank_routing' => "" . str_pad($payment->bank_routing, 9, '0', STR_PAD_LEFT)
                    ];
                } else {
                    $paymentDetails = [
                        'contact' => '',
                        'num' => '',
                        'exp' => '',
                        'bank_account' => "",
                        'bank_routing' => ""
                    ];
                }
            }
        } elseif (in_array($policyRow->payment_type, ['list', 'stmt']) && $policyRow->recurring_payment_type == 'same') {
            // Do nothing, leave the defaults
        } elseif ($policyRow->recurring_payment_id == '') {
            if ($policyRow->payment_type == 'cc') {
                $payment = CcDetail::where('cc_id', $policyRow->payment_id)->first();
                if ($payment) {
                    $paymentDetails = [
                        'contact' => $payment->cc_contactfname . ' ' . $payment->cc_contactlname,
                        'num' => "" . DecryptEcryptHelper::decryptInfo($payment->cc_num),
                        'exp' => $payment->cc_expmm . '-' . $payment->cc_expyyyy,
                        'bank_account' => '',
                        'bank_routing' => ''
                    ];
                } else {
                    $paymentDetails = [
                        'contact' => "",
                        'num' => "",
                        'exp' => "",
                        'bank_account' => '',
                        'bank_routing' => ''
                    ];
                }
                
             
            } elseif ($policyRow->payment_type == 'eft') {
                $payment = EftDetail::where('bank_id', $policyRow->payment_id)->first();
                if ($payment) {
                    $paymentDetails = [
                        'contact' => '',
                        'num' => '',
                        'exp' => '',
                        'bank_account' => "" . DecryptEcryptHelper::decryptInfo($payment->bank_account),
                        'bank_routing' => "" . str_pad($payment->bank_routing, 9, '0', STR_PAD_LEFT)
                    ];
                } else {
                    $paymentDetails = [
                        'contact' => '',
                        'num' => '',
                        'exp' => '',
                        'bank_account' => "" ,
                        'bank_routing' => "" 
                    ];
                }
                
                
            }
        } else {
            if ($policyRow->recurring_payment_type == 'cc') {
                $payment = CcDetail::where('cc_id', $policyRow->recurring_payment_id)->first();
                if ($payment) {
                    $paymentDetails = [
                        'contact' => $payment->cc_contactfname . ' ' . $payment->cc_contactlname,
                        'num' => "" . DecryptEcryptHelper::decryptInfo($payment->cc_num),
                        'exp' => $payment->cc_expmm . '-' . $payment->cc_expyyyy,
                        'bank_account' => '',
                        'bank_routing' => ''
                    ];
                } else {
                    $paymentDetails = [
                        'contact' => "",
                        'num' => "" ,
                        'exp' => "",
                        'bank_account' => '',
                        'bank_routing' => ''
                    ];
                }
                
                
            } elseif ($policyRow->recurring_payment_type == 'eft') {
                $payment = EftDetail::where('bank_id', $policyRow->recurring_payment_id)->first();
                if ($payment) {
                    $paymentDetails = [
                        'contact' => '',
                        'num' => '',
                        'exp' => '',
                        'bank_account' => "" . DecryptEcryptHelper::decryptInfo($payment->bank_account),
                        'bank_routing' => "" . str_pad($payment->bank_routing, 9, '0', STR_PAD_LEFT)
                    ];
                } else {
                    $paymentDetails = [
                        'contact' => '',
                        'num' => '',
                        'exp' => '',
                        'bank_account' => "",
                        'bank_routing' => ""
                    ];
                }
            }
        }

        return $paymentDetails;
    }
    public function failed(Exception $exception)
{
    // Send user notification of failure, etc...
    Log::info($exception);
}
}
