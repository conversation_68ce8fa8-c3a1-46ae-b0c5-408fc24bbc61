<?php

namespace App\Jobs;

use App\AdminUser;
use App\AgentFileGeneration;
use Illuminate\Bus\Queueable;
use App\Exports\AgentExport;
use App\Repositories\Agents\ManageAgents;
use App\Service\MessageService;
use Maatwebsite\Excel\Excel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class ExportExcelFileAgent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $filter;
    private $admin_data;

    public function __construct($filter , $admin_data)
    {
        $this->filter = $filter;
        $this->admin_data = $admin_data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(ManageAgents $agentRepository , Excel $excel)
    {
        $data['added_by'] = $this->admin_data['added_by'];
        $data['added_by_id'] = $this->admin_data['added_by_id'];
        $data['file_generation_started'] = time();
        $agents = $agentRepository->listAll($this->filter);
        $result = $agentRepository->singleExportFormattedItem($agents);
        $data['no_of_record'] = count($result);
        $data['file_name'] = time() . '_' . 'AGENT_REPORT.xlsx';
        $data['file_path'] = 'agent/files/';
        if(!Storage::disk('s3-third')->exists('agent/files')){
            Storage::disk('s3-third')->makeDirectory('agent/files');
        }
        $excel->store(new AgentExport($result), $data['file_path'].$data['file_name'], 'export');
        Storage::disk('s3-third')->put($data['file_path'].$data['file_name'] , File::get(storage_path('agent/files/'.$data['file_name'])));
        File::deleteDirectory(storage_path('agent'));
        $data['file_generation_ended'] = time();
        AgentFileGeneration::create($data);
        self::sendUserEmployerEmail($data);
    }

    public static function sendUserEmployerEmail($data)
        {
            $adminDetail = AdminUser::find($data['added_by_id']);
            if($adminDetail){
                $emailConfigurationName = "EXPOER_EXCEL_TEMPLETE";
                $subject = 'Agent list export excel sheet completed';
                $message = "This email is intended to inform you that the file you created using the agent list has been completed. The ".'"View Generated Report" '."part of Agent List offers the option to download the excel file, or you can just click <a href=".`"`.config('filesystems.disks.s3-third.url').'/' . $data['file_path']. $data['file_name'].`"`.">here</a>."."to download it.";
                $emailData = [
                    'email_message_configuration_name' => $emailConfigurationName,
                    'toAddress' => $adminDetail->email,
                    'ccAddress' => [],
                    'subject' => $subject,
                    'message' => $message,
                    'data' => [],
                    'dataName' => $adminDetail->name
                ];
                $messageService = new MessageService();
                $messageService->sendEmailWithContentData($emailData);
            }
        }

}
