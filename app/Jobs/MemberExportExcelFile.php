<?php

namespace App\Jobs;

use App\AdminUser;
use App\ClientFileGeneration;
use App\NbInvoice;
use App\PolicyUpdate;
use Maatwebsite\Excel\Excel;
use App\Exports\MemberExport;
use App\Service\MessageService;
use App\UserInfoPolicyAddress;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use App\Helpers\CommonHelper;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class MemberExportExcelFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $filter;
    private $admin_data;

    public function __construct($filter , $admin_data)
    {
        $this->filter = $filter;
        $this->admin_data = $admin_data;
    }
    /**
     * Execute the job.
     *
     * @return void
     */


    public function handle(Excel $excel)
    {
        ini_set('max_execution_time', '0');
        ini_set('memory_limit','-1');
        set_time_limit(0);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $data['added_by'] = $this->admin_data['added_by'];
        $data['added_by_id'] = $this->admin_data['added_by_id'];
        $data['file_generation_started'] = time();

        $sheet->setCellValue('A1', htmlspecialchars('System ID'));
        $sheet->setCellValue('B1', htmlspecialchars('Policy ID'));
        $sheet->setCellValue('C1', htmlspecialchars('Status'));
        $sheet->setCellValue('D1', htmlspecialchars('Member'));
        $sheet->setCellValue('E1', htmlspecialchars('Age'));
        $sheet->setCellValue('F1', htmlspecialchars('Address'));
        $sheet->setCellValue('G1', htmlspecialchars('City'));
        $sheet->setCellValue('H1', htmlspecialchars('State'));
        $sheet->setCellValue('I1', htmlspecialchars('Zip'));
        $sheet->setCellValue('J1', htmlspecialchars('Email'));
        $sheet->setCellValue('K1', htmlspecialchars('Phone Number'));
        $sheet->setCellValue('L1', htmlspecialchars('Enrollment Date'));
        $sheet->setCellValue('M1', htmlspecialchars('Effective Date'));
        $sheet->setCellValue('N1', htmlspecialchars('Eligibility Date'));
        $sheet->setCellValue('O1', htmlspecialchars('Pdth date'));
        $sheet->setCellValue('P1', htmlspecialchars('Agent'));
        $sheet->setCellValue('Q1', htmlspecialchars('Group'));
        $sheet->setCellValue('R1', htmlspecialchars('Platform'));
        $sheet->setCellValue('S1', htmlspecialchars('Enrolled By'));
        $sheet->setCellValue('T1', htmlspecialchars('Plan'));
        $sheet->setCellValue('U1', htmlspecialchars('Tier'));
        $sheet->setCellValue('V1', htmlspecialchars('Plan and Permium'));
        $sheet->setCellValue('W1', htmlspecialchars('Pay Type'));
        $sheet->setCellValue('X1', htmlspecialchars('Processing Date'));

        $rowIndex = 2;
        $members = $this->manageClients($this->filter)->get()->toArray();
        foreach ($members as $key => $member) {

            $latestpolicyupdate = PolicyUpdate::where('elgb_policyid', $member['policy_id'])->orderBy('elgb_id','DESC')->first();

            $member['PdthDate']  =  $this->getPaidThroughDate($member['payment_paid_through_date'], $member['nb_invoice_with_paid_status']['invoice_end_date']);

            $member['act'] = '(' . $latestpolicyupdate->elgb_act . ')';
            $member['date'] = date('m/d/Y', $latestpolicyupdate->elgb_act_date);
            if (isset($member['eprocess'])) {
                if ($member['eprocess'] === 'rep') {
                    // Do something if eprocess is 'rep'
                    $eprocess =  'Rep';
                } else {
                    // Do something if eprocess is not 'rep'
                    $eprocess = 'Member';
                }
            } else {
                // Do something if eprocess is not set
                $eprocess = 'Member';
            }
            $plans = explode(',', $member['plan_details']);
            $row = [];
            foreach ($plans as $plan) {
                $plan = str_replace('&', '&amp;', $plan);
                $row[] = str_replace('<br>', PHP_EOL, $plan);
            }
            if ($member['payment_type'] == 'cc') {
                $ccfees = 0.035 * $member['recurring_amount'];
                $ccfees = number_format((float)$ccfees, 2, '.', '');
                array_push($row,"Payment Service Fee: $ ". $ccfees." ");
                $member['recurring_amount'] += $ccfees;
            }

            $allplans = implode($row);
            $payType = '';
            $recurringPayType = '';

            switch ($member['payment_type']) {
                case 'eft':
                    $payType .= 'EFT' . PHP_EOL;
                    $payType .= $member['bank_name'] ?: '';
                    $payType .= ',' . ($member['bank_account'] ? 'xx' . $member['bank_account'] : '');
                    break;
                case 'cc':
                    $payType .= 'Credit Card' . PHP_EOL;
                    $payType .= $member['cc_num'] ?: '' . PHP_EOL;
                    $payType .= PHP_EOL.$member['exp_month'] ?: '';
                    $payType .= '/' . $member['exp_year'] ?: '';
                    break;
                case 'list':
                    $payType .= 'LIST';
                    break;
                case 'chck':
                    $payType .= 'CHCK';
                    break;
                case 'stmt':
                    $payType .= 'STMT';
                    break;
                case 'elist':
                    $payType .= 'ELIST';
                    break;
            }

            switch ($member['recurring_payment_type']) {
                case 'eft':
                    $recurringPayType .= 'EFT' . PHP_EOL;
                    $recurringPayType .= $member['recurring_bank_name'] ?: '';
                    $recurringPayType .= ',' . ($member['recurring_bank_account'] ? 'xx' . $member['recurring_bank_account'] : '');
                    break;
                case 'cc':
                    $recurringPayType .= 'Credit Card' . PHP_EOL;
                    $recurringPayType .= $member['recurring_cc_num'] ?: '' . PHP_EOL;
                    $recurringPayType .= PHP_EOL.$member['recurring_exp_month'] ?: '';
                    $recurringPayType .= '/' . $member['recurring_exp_year'] ?: '';
                    break;
                case 'list':
                    $recurringPayType .= 'LIST';
                    break;
                case 'chck':
                    $recurringPayType .= 'CHCK';
                    break;
                case 'stmt':
                    $recurringPayType .= 'STMT';
                    break;
                case 'elist':
                    $recurringPayType .= 'ELIST';
                    break;
            }

            $sheet->setCellValue('A' . $rowIndex, $member['policy_id'] ?? ' ' );
            $sheet->setCellValue('B' . $rowIndex , $member['user_id'] ? " " . $member['user_id'] : "");
            $sheet->setCellValue('C' . $rowIndex , ($member['status'] == 'ACTIVE' && $member['approval'] == null) ? 'PENDING' : ($member['status'] ?: ''));
            $sheet->setCellValue('D' . $rowIndex, ucfirst($member['first_name']) . ($member['middle_name'] ? ' ' . ucfirst($member['middle_name']) : '') .' ' . ucfirst($member['last_name']));      
            $sheet->setCellValue('E' . $rowIndex , (Carbon::parse($member['dob'])->age));
            $sheet->setCellValue('F' . $rowIndex, ($member['address1'] ?? '') . ' ' . ($member['address2'] ?? ''));
            $sheet->setCellValue('G' . $rowIndex ,  $member['city'] ?? '');
            $sheet->setCellValue('H' . $rowIndex ,  $member['state'] ?? '');
            $sheet->setCellValue('I' . $rowIndex ,  $member['zip'] ?? '');
            $sheet->setCellValue('J' . $rowIndex ,  $member['cemail'] ?? '');
            $sheet->setCellValue('K' . $rowIndex ,  CommonHelper::format_phone($member['phone1']) ?? '');
            $sheet->setCellValue('L' . $rowIndex ,  (isset($member['edate']) ? Carbon::createFromTimestamp($member['edate'])->format('m/d/Y') : ''));
            $sheet->setCellValue('M' . $rowIndex ,  (isset($member['effective_date']) ? \Carbon\Carbon::parse($member['effective_date'])->format('m/d/Y') : ''));
            $sheet->setCellValue('N' . $rowIndex ,  (isset($member['date']) ? $member['date'] : '') . (isset($member['act']) ? $member['act'] : '') );
            $sheet->setCellValue('O' . $rowIndex ,   (isset($member['PdthDate']) ? $member['PdthDate'] : ''));
            $sheet->setCellValue('P' . $rowIndex ,  ($member['rep_first_name'] ? $member['rep_first_name'] : '') .' '. ($member['rep_last_name'] ? $member['rep_last_name'] : ''));
            $sheet->setCellValue('Q' . $rowIndex ,  ($member['gname'] ?: ''));
            $sheet->setCellValue('R' . $rowIndex ,  ($member['weburl'] ? 'https://' . $member['weburl'] : ''));
            $sheet->setCellValue('S' . $rowIndex ,  $eprocess ?? '');
            $lines = explode("\n", trim($allplans));
            $firstLine = $lines[0];

            $afterSlash = explode('/', $firstLine)[1];
            $beforeDash = explode('-', $afterSlash)[0];
            $planType = trim($beforeDash);

            $sheet->setCellValue('T' . $rowIndex , explode('/', $firstLine)[0] ?? '');
            $sheet->setCellValue('U' . $rowIndex ,  $beforeDash?? '');
            $sheet->setCellValue('V' . $rowIndex ,  $allplans . PHP_EOL .'Recurring Monthly:' . number_format($member['recurring_amount'], 2));
            $sheet->setCellValue('W' . $rowIndex ,  'Initial Month: ' . $payType . PHP_EOL . 'Recurring: ' . $recurringPayType);
            $sheet->setCellValue('X' . $rowIndex , 'PROCESSING DATE:' . ($member['bill_date'] ?: 'NA'));
           
            $rowIndex++;
        }

        // Apply cell formatting
        $columnCount = $sheet->getHighestColumn();
        $columnRange = 'A:' . $columnCount;
        $sheet->getStyle($columnRange)->getAlignment()->setWrapText(true);
        foreach (range('A', $columnCount) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }
        // Save the Spreadsheet as a file
        $data['no_of_record'] = count($members);
        $data['file_name'] = time() . '_' . 'MEMBER_REPORT.xlsx';
        $data['store_path'] = storage_path() . '/' . $data['file_name'];
        $data['file_path'] = 'member/files/';
        $writer = new Xlsx($spreadsheet);
        $filePath = $data['store_path'];
        $writer->save($filePath);

        Storage::disk('s3-third')->put($data['file_path'].$data['file_name'] , File::get(storage_path().'/'.$data['file_name']));
        unlink(storage_path().'/'.$data['file_name']);
        $data['file_generation_ended'] = time();
        ClientFileGeneration::create($data);
        self::sendUserEmployerEmail($data);



        // Old Code
        // if(count($members) > 0){
        //     $members[0]['PdthDate']  =  $this->getPaidThroughDate($members[0]['payment_paid_through_date']);
        //     $data['no_of_record'] = count($members);
        //     $data['file_name'] = time() . '_' . 'MEMBER_REPORT.xlsx';
        //     $data['file_path'] = 'member/files/';
        //     if(!Storage::disk('s3-third')->exists('member/files')){
        //         Storage::disk('s3-third')->makeDirectory('member/files');
        //     }
        //     $excel->store(new MemberExport($members), $data['file_path'].$data['file_name'], 'export');
        //     Storage::disk('s3-third')->put($data['file_path'].$data['file_name'] , File::get(storage_path('member/files/'.$data['file_name'])));
        //     File::deleteDirectory(storage_path('member'));
        //     $data['file_generation_ended'] = time();
        //     ClientFileGeneration::create($data);
        //     self::sendUserEmployerEmail($data);
        // }
    }

    private function getPaidThroughDate($paidThrough, $nbInvoice = null): string
    {
        if (!$paidThrough && !$nbInvoice) {
            return 'N/A';
        } elseif (!$paidThrough && $nbInvoice) {
            return Carbon::parse($nbInvoice)->format('m/d/Y');
        } elseif ($paidThrough && !$nbInvoice) {
            return Carbon::parse($paidThrough)->format('m/d/Y');
        } else {
            if (date($paidThrough) > date($nbInvoice)) {
                return Carbon::parse($paidThrough)->format('m/d/Y');
            } else {
                return Carbon::parse($nbInvoice)->format('m/d/Y');
            }
        }
    }

        public static function sendUserEmployerEmail($data)
        {
            $adminDetail = AdminUser::find($data['added_by_id']);
            if($adminDetail){
                $emailConfigurationName = "EXPOER_EXCEL_TEMPLETE";
                $subject = 'Member list export excel sheet completed';
                $message = "This is a notification to notify you about the completion of the file you generated for the member list. You can download the excel file from the file list section of Manage Client List or you can simply download the file by clicking <a href=".`"`.config('filesystems.disks.s3-third.url').'/' . $data['file_path']. $data['file_name'].`"`.">here</a>.";
                $emailData = [
                    'email_message_configuration_name' => $emailConfigurationName,
                    'toAddress' => $adminDetail->email,
                    'ccAddress' => [],
                    'subject' => $subject,
                    'message' => $message,
                    'data' => [],
                    'dataName' => $adminDetail->name
                ];
                $messageService = new MessageService();
                $messageService->sendEmailWithContentData($emailData);
            }

        }

    public function manageClients($filters): Builder
    {

        $selectColumns = [
            "userinfo_policy_address.policy_id",
            "userinfo_policy_address.status",
            "userinfo_policy_address.cemail as user_email",
            "userinfo_policy_address.phone1 as phone",
            "userinfo_policy_address.cfname as first_name",
            "userinfo_policy_address.cmname as middle_name",
            "userinfo_policy_address.clname as last_name",
            "userinfo_policy_address.cdob as dob",
//            "userinfo_policy_address.idilus_type as enroll_type",
            "userinfo_policy_address.effective_date as effective_date",
            "userinfo_policy_address.edate",
            "userinfo_policy_address.agent_fname as rep_first_name",
            'userinfo_policy_address.address1',
            'userinfo_policy_address.address2',
            'userinfo_policy_address.city',
            'userinfo_policy_address.state',
            'userinfo_policy_address.zip',
            'userinfo_policy_address.cemail',
            'userinfo_policy_address.phone1',
            'userinfo_policy_address.payment_type',
            'userinfo_policy_address.recurring_payment_type',
            'userinfo_policy_address.is_benefit_store',
            'userinfo_policy_address.bill_date',
            'userinfo_policy_address.weburl',
            "userinfo_policy_address.agent_id as agent_id",
            "userinfo_policy_address.agent_lname as rep_last_name",
            "userinfo_policy_address.eid",
            "userinfo_policy_address.payment_type",
            "userinfo_policy_address.userid as user_id",
            "userinfo_policy_address.Approval as approval",
            "p.policy_id",
            "p.web_display_name",
            "p.tier",
            "p.user_dob",
            "p.pstatus",
            "p.cid",
            "p.price_male_nons",
            "p.pterm_date",
            "p.pid",
            "p.plan_id",
            "p.plan_cat",
            "p.esig",
            "p.status",
            "p.eprocess",
            "p.plan_pricing_type",
//            "ph.payment_paid_through_date as paid_through_date",
            "userinfo_policy_address.agent_code",
            "g.gname",
            "eft.bank_name",
            "eft.bank_account4 as bank_account",
            "reft.bank_name as recurring_bank_name",
            "reft.bank_account4 as recurring_bank_account",
            "pc.cc_num4 as cc_num",
            "pc.cc_expmm as exp_month",
            "pc.cc_expyyyy as exp_year",
            "rpc.cc_num4 as recurring_cc_num",
            "rpc.cc_expmm as recurring_exp_month",
            "rpc.cc_expyyyy as recurring_exp_year",
            // "polup.elgb_act as act",
            // "polup.elgb_act_date as date",
            "ph.payment_paid_through_date",
            // "nbinv.invoice_end_date"
        ];
        $planDetails = "GROUP_CONCAT(DISTINCT p.web_display_name,'/',p.tier,' - $',p.price_male_nons,'<br>',if(p.pstatus='2','Termed Date :',''),if(p.pstatus='3','Withdrawn Date :',''),if(p.pterm_date,CONCAT(p.pterm_date, '<br>'),'') ) as plan_details";
        $query = UserInfoPolicyAddress::query()
            ->rightJoin('plan_overview as p', 'userinfo_policy_address.policy_id', '=', 'p.policy_id')
            ->leftJoin('paid_through as ph', 'userinfo_policy_address.policy_id', '=', 'ph.paidthru_policyid')
            ->leftJoin('group_info as g', 'userinfo_policy_address.eid', '=', 'g.gid')
            ->leftJoin('payment_cc as pc', 'userinfo_policy_address.payment_id', '=', 'pc.cc_id')
            ->leftJoin('payment_cc as rpc', 'userinfo_policy_address.recurring_payment_id', '=', 'rpc.cc_id')
            ->leftJoin('payment_eft as eft', 'userinfo_policy_address.payment_id', '=', 'eft.bank_id')
            ->leftJoin('payment_eft as reft', 'userinfo_policy_address.recurring_payment_id', '=', 'reft.bank_id')
            ->with(['nbInvoiceWithPaidStatus'])
            // ->leftJoin('policy_updates as polup', 'userinfo_policy_address.policy_id', '=', 'polup.elgb_policyid')
            // ->leftJoin(config('database.connections.mysql2.database').'.nb_invoices as nbinv', 'userinfo_policy_address.policy_id', '=', 'nbinv.invoice_policy_id')
            ->select($selectColumns)
            ->selectRaw("$planDetails")
            ->selectRaw('SUM(DISTINCT CASE WHEN p.pstatus = 3 OR (p.pstatus = 2 AND userinfo_policy_address.effective_date < CURRENT_DATE) THEN 0 ELSE p.price_male_nons END) as recurring_amount')
            ->orderBy('userinfo_policy_address.policy_id', 'DESC')
            ->groupBy('userinfo_policy_address.policy_id');

        $this->filterContent($query, $filters);
        return $query;
    }

    public function filterContent(Builder $query, $filters)
    {
        if (isset($filters['system_id']) && $filters['system_id'] != '') {
            $query->where('userinfo_policy_address.policy_id', $filters['system_id']);
        }

        if (isset($filters['enrollment_date_from']) && $filters['enrollment_date_from'] != '') {
            $query->where('userinfo_policy_address.edate', '>=', Carbon::parse($filters['enrollment_date_from'])->timestamp);
        }

        if (isset($filters['enrollment_date_to']) && $filters['enrollment_date_to'] != '') {
            $query->where('userinfo_policy_address.edate', '<=', Carbon::parse($filters['enrollment_date_to'])->timestamp);
        }

        if (isset($filters['effective_date']) && $filters['effective_date'] != '') {
            $query->where('userinfo_policy_address.effective_date', Carbon::parse($filters['effective_date'])->format('Y-m-d'));
        }

        if (isset($filters['agent_code']) && $filters['agent_code'] != '') {
            $query->where('userinfo_policy_address.agent_code', $filters['agent_code']);
        }

        if (isset($filters['first_name']) && $filters['first_name'] != '') {
            $query->where('userinfo_policy_address.cfname', 'like', '%' . $filters['first_name'] . '%');
        }

        if (isset($filters['last_name']) && $filters['last_name'] != '') {
            $query->where('userinfo_policy_address.clname', 'like', '%' . $filters['last_name'] . '%');
        }

        if (isset($filters['email']) && $filters['email'] != '') {
            $query->where('userinfo_policy_address.cemail', $filters['email']);
        }

        if (isset($filters['phone']) && $filters['phone'] != '') {
            $query->where('userinfo_policy_address.phone1', $filters['phone']);
        }

        if (isset($filters['bill_date']) && $filters['bill_date'] != '') {
            if ($filters['bill_date'] == 'other') {
                $query->where('bill_date', '>', 31);
            } else {
                $query->where('userinfo_policy_address.bill_date', $filters['bill_date']);
            }
        }

        if (isset($filters['payment_type']) && $filters['payment_type'] != '') {
            $query->where('userinfo_policy_address.payment_type', $filters['payment_type']);
        }

        if (isset($filters['platform']) && $filters['platform'] != '') {
            if ($filters['platform'] == 'enroll.purenroll.com') {
                $query = $query->where('userinfo_policy_address.is_benefit_store', '=', 1);
            } else {
                $query = $query->where('userinfo_policy_address.weburl', 'LIKE', $filters['platform'] . '%');
            }
        }

        if (isset($filters['status']) && $filters['status'] != '') {
            if ($filters['status'] == 'PENDING') {
                $query->where('userinfo_policy_address.status', 'ACTIVE')
                    ->whereNull('userinfo_policy_address.Approval');
            } else if ($filters['status'] == 'ACTIVE') {
                $query->where('userinfo_policy_address.status', 'ACTIVE')
                    ->where('userinfo_policy_address.Approval', '1');
            } else {
                $query->where('userinfo_policy_address.status', $filters['status']);
            }
        }

        if (isset($filters['carrier']) && $filters['carrier'] != '') {
            $query->where('p.cid', $filters['carrier']);
        }

        if (isset($filters['term_withdrawn_date']) && $filters['term_withdrawn_date'] != '') {
            $value = $filters['term_withdrawn_date'];
            $query->where('p.pterm_date', Carbon::parse($value)->format('Y-m-d'));
        }

        if (isset($filters['invoice_id']) && $filters['invoice_id'] != '') {
            $policyIds = $this->getNbInvoiceFilteredPolicyIds('invoice_id', $filters['invoice_id']);
            $query->whereIn('userinfo_policy_address.policy_id', $policyIds);
        }


        if (isset($filters['registered_user']) && $filters['registered_user'] != '') {
            if ($filters['registered_user'] == 'false') {
                $query->leftJoin('user_login as ul', 'userinfo_policy_address.userid', 'ul.userid');
            } else {
                $query->join('user_login as ul', 'userinfo_policy_address.userid', 'ul.userid');
            }
        }

        if (isset($filters['payment_status']) && $filters['payment_status'] != '') {
            $policyIds = $this->getNbInvoiceFilteredPolicyIds('invoice_payment_status', $filters['payment_status']);
            $query->whereIn('userinfo_policy_address.policy_id', $policyIds);

        }

        if (isset($filters['pay_through_date']) && $filters['pay_through_date'] != '') {
            $policyIds = $this->getNbInvoiceFilteredPolicyIds('invoice_end_date', Carbon::parse($filters['pay_through_date'])->format('Y-m-d'));
            $query->whereIn('userinfo_policy_address.policy_id', $policyIds);
        }

        if (isset($filters['plan_ids']) && count($filters['plan_ids'])) {
            $query->whereHas('plans', function ($q) use ($filters) {
                $q->whereIn('pid', $filters['plan_ids']);
            });
        }
    }

    private function getNbInvoiceFilteredPolicyIds($columnName, $value)
    {
        return NbInvoice::where($columnName, $value)
            ->groupBy('invoice_policy_id')
            ->pluck('invoice_policy_id')
            ->toArray();
    }
}
