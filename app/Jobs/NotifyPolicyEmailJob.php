<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Mail\PolicyEmail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class NotifyPolicyEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $emails;
    protected $templateData;
    protected $emailCC;
    protected $emailBCC;

    public function __construct($templateData, $emails, $emailCC = [], $emailBCC = [])
    {
        $this->templateData = $templateData;
        $this->emails = $emails;
        $this->emailCC = $emailCC;
        $this->emailBCC = $emailBCC;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Mail::to($this->emails)->cc($this->emailCC)->bcc($this->emailBCC)->send(new PolicyEmail($this->templateData));
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
}
