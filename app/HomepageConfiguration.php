<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class HomepageConfiguration extends Model
{
    protected $table = 'homepage_configurations';
    protected $guarded = [];
    public $timestamps = false;

    public const CONFIGURATION_FOR_AGENT = 'A';
    public const CONFIGURATION_FOR_GROUP = 'G';

    const SITE_PREMIER_ENROLL = 'premierenroll.com';
    const SITE_ELITE_ENROLL = 'eliteenroll.com';
    const SITE_BROKER_EXCHANGE = 'brokerexchanges.com';
    const SITE_GO_ENROLLL = 'goenroll123.com';

    static $sites = [
        self::SITE_PREMIER_ENROLL,
        self::SITE_ELITE_ENROLL,
        self::SITE_BROKER_EXCHANGE,
        self::SITE_GO_ENROLLL,
    ];

    public function plan()
    {
        return $this->belongsTo(Plan::class, 'plan_id');
    }

    public function agent()
    {
        return $this->belongsTo(AgentInfo::class, 'agent_id');
    }

    public function group()
    {
        return $this->belongsTo(GroupInfo::class, 'group_id');
    }
}
