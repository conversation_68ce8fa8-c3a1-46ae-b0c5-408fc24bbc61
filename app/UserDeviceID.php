<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserDeviceID extends Model
{
    const IOS = 'ios';
    const ANDROID = 'android';
    const DEVICES = ['android','ios'];
    const DEVICES_TYPE = ['mobile','web'];
    protected $connection = 'sso';
    protected $table = 'sso_users_device_id';
    protected $primaryKey = 'id';
    protected $casts = [
        'created_at' => 'date:Y-m-d H:i:s' ?: '',
        'update_at' => 'date:Y-m-d H:i:s' ?: '',
    ];

}