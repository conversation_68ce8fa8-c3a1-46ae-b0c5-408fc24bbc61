<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * @property Collection $userActivities
 */
class UserInfoPolicyAddress extends Model
{
    protected $table = 'userinfo_policy_address';
    protected $primaryKey = null;
    public $incrementing = false;
    public $timestamps = false;
    const EXTRA_HEALTH_CARRIER_ID = 92;

    public function getGroup()
    {
        return $this->hasOne('App\GroupInfo', 'gid', 'eid');
    }
    public function healthAnswers()
    {
        return $this->hasMany('App\UserInfoDepMedBex', 'user_id', 'userid');
    }
    public function plans()
    {
        return $this->hasMany('App\PlanOverview', 'policy_id', 'policy_id');
    }
    public function policyUpdates()
    {
        return $this->hasMany('App\PolicyUpdate', 'elgb_policyid', 'policy_id');
    }
    public function policy()
    {
        return $this->hasOne('App\Policy', 'policy_id', 'policy_id');
    }
    public function creditCardInfo()
    {
        return $this->hasOne('App\PaymentCC', 'creditcard_id', 'payment_id');
    }
    public function recurringCreditCardInfo()
    {
        return $this->hasOne('App\PaymentCC', 'creditcard_id', 'recurring_payment_id');
    }
    public function ccInfo()
    {
        return $this->hasOne(PaymentTypeCc::class, 'cc_id', 'payment_id');
    }
    public function recurringCCInfo()
    {
        return $this->hasOne(PaymentTypeCc::class, 'cc_id', 'recurring_payment_id');
    }
    public function eftInfo()
    {
        return $this->hasOne('App\PaymentEft', 'bank_id', 'payment_id');
    }
    public function recurringEftInfo()
    {
        return $this->hasOne('App\PaymentEft', 'bank_id', 'recurring_payment_id');
    }
    public function beneficiary()
    {
        return $this->hasMany('App\PolicyBeneficiary', 'bpolicy_id', 'policy_id');
    }
    public function dependents()
    {
        return $this->hasMany('App\DependentInPolicy', 'policy_id', 'policy_id');
    }

    public function paidThrough()
    {
        return $this->hasOne('App\PaidThrough', 'paidthru_policyid', 'policy_id');
    }

    public function nbInvoice()
    {
        return $this->hasOne('App\NbInvoice', 'invoice_policy_id', 'policy_id')->latest('invoice_end_date');
    }

    public function merchant()
    {
        return $this->hasOne('App\NbInvoice', 'invoice_policy_id', 'policy_id')->latest('invoice_end_date');
    }

    public function nbInvoiceWithPaidStatus()
    {
        return $this->hasOne('App\NbInvoice', 'invoice_policy_id', 'policy_id')->latest('invoice_end_date')->where('invoice_payment_status','PAID');
    }

    public function policyUpdate()
    {
        return $this->hasOne('App\PolicyUpdate', 'elgb_policyid', 'policy_id')->latest('elgb_act_date');
    }

    public function userinfo()
    {
        return $this->hasOne('App\UserInfo', 'userid', 'userid');
    }

    public function getHomeAddress()
    {
        return $this->hasOne('App\Address', 'address_id', 'home_address');
    }

    public function signature()
    {
        return $this->hasOne(Signature::class,'pid','policy_id');
    }

    public function userLogin()
    {
        return $this->hasOne(UserLogin::class, 'userid', 'userid');
    }

    public function activeUserLogin()
    {
        return $this->hasOne(UserLogin::class, 'userid', 'userid')->where('status', 1);
    }

    public function userActivities()
    {
        return $this->hasMany(UserActivity::class, 'uid', 'userid');
    }

    public function getEnrollmentDateAttribute(){;
        return date('Y-m-d',$this->edate);
    }

    public function scopeFilter($query, $filter, $value, $operator)
    {
        return $query->where($filter, $operator, $value);
    }

    public function getFullNameAttribute()
    {
        return $this->cmname
            ? ucwords($this->cfname . ' ' . $this->cmname . ' ' . $this->clname)
            : ucwords($this->cfname . ' ' . $this->clname);
    }

    public function getAgeAttribute(): int
    {
        return Carbon::parse($this->cdob)->age;
    }

    public function latestNote()
    {
        return $this->hasOneThrough(
            PolicyNote::class,
            PlanOverview::class,
            'userid',
            'policy_id',
            'userid',
            'policy_id'
        )->latest();
    }

    public function hasExtraHelthPlans()
    {
        return $this->hasMany('App\Planoverview', 'policy_id', 'policy_id')->where('cid', self::EXTRA_HEALTH_CARRIER_ID);       
    }
    
}
