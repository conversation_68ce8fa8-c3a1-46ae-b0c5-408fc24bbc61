<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class InvoiceItemHistory extends Model
{
    protected $table = 'nb_invoice_item_history';
    public $timestamps = false;
     protected $fillable = [
        'nb_invoice_item_history_id',
        'invoice_item_id',
        'invoice_id',
        'policy_id',
        'eff_date',
        'start_date',
        'end_date',
        'user_id',
        'plan_id',
        'amount',
        'efee',
        'tier',
        'invoice_item_created_user_id',
        'invoice_item_create_dttm',
        'ppid',
        'is_ptc',
    ];
    
}
