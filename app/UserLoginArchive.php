<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserLoginArchive extends Model
{
    use SoftDeletes;

    protected $table = 'user_login_archive';

    protected $fillable = [
        'userid', 'username', 'password', 'q1', 'q2', 'a1', 'a2', 'udate', 'temp_pass', 'status', 'ip', 'blocked', 
        'password_validity', 'subscribe', 'last_attempt', 'verification_code', 'cookie_token', 'phone', 'ipAddress', 
        'last_login_datetime', 'password_verification_code', 'weburl', 'is_email_valid'
    ];
}
