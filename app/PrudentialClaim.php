<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PrudentialClaim extends Model
{
    use SoftDeletes;

    protected $table = 'prudential_claims';
    protected $guarded = [];

    const PRUDENTIAL_EMAIL_SENT = 'Sent';
    const PRUDENTIAL_CARRIER_ID = 80;

    public function files()
    {
        return $this->hasMany(ClaimFile::class, 'claim_id', 'id');
    }

    public function policy()
    {
        return $this->hasOne(Policy::class,'policy_id','policy_id');
    }

    public function planOverview()
    {
        return $this->belongsTo(PlanOverview::class, 'pid', 'pid');
    }

}
