<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GroupUser extends Model
{
    protected $table = 'group_users';
    protected $primaryKey = 'user_id';
    protected $guarded = ['user_id'];

    protected $fillable = [
        'user_id',
        'gid',
        'groupuser_lname',
        'groupuser_fname',
        'groupuser_email',
        'groupuser_status',
        'groupuser_signupdate',
        'groupuser_username',
        'groupuser_password',
        'groupuser_notes',
        'verification_code',
        'cookie_token',
        'last_attempt',
        'q1',
        'q2',
        'a1',
        'a2',
        'admin_type',
        'phone',
        'ipAddress',
        'last_login_datetime',
        'password_verification_code',
        'registeration_verification_code',
        'is_decided',
        'is_root',
        'is_temp_password',
        'posted_date',
        'disphone',
        'disemail',
        'tagline',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_email_valid',
    ];
    
}
