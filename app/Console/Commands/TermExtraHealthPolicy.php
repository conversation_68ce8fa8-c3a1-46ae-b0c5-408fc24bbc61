<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Repositories\PolicyTerminateWithdraw\PolicyTerminateWithdrawFeatures;
use App\Policy;
use Carbon\Carbon;

class TermExtraHealthPolicy extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'extra-health-policy:terminate 
                            {--policyIds= : Comma-separated list of policy IDs}
                            {--sendEmail=0 : Whether to send email (1 = yes, 0 = no)}';

    /**
     * The console command description.
     */
    protected $description = 'Terminate specific policies with dynamic IDs and optional email flag.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $policyIdsOption = $this->option('policyIds');
        $sendEmail = (int) $this->option('sendEmail');

        if (!$policyIdsOption) {
            $this->error("Please provide at least one policy ID using --policyIds.");
            return;
        }

        // Parse policy IDs
        $policyIds = array_filter(array_map('trim', explode(',', trim($policyIdsOption, '[]'))));

        if (empty($policyIds)) {
            $this->error("No valid policy IDs provided.");
            return;
        }

        $policyFeature = new PolicyTerminateWithdrawFeatures();
        $totalRequests = count($policyIds);

        $this->info("Processing $totalRequests policies...");
        $bar = $this->output->createProgressBar($totalRequests);
        $bar->start();

        foreach ($policyIds as $policyId) {
            $action = 'tnp';
            $state = 'terminate';

            $termDate = DB::connection('mysql2')->table('nb_invoices as inv')
                ->selectRaw('MAX(invoice_end_date) as latest_paid_end_date')
                ->where('invoice_policy_id', $policyId)
                ->where('invoice_payment_status', 'PAID')
                ->value('latest_paid_end_date');

            if (!$termDate) {
                $effectiveDate = Policy::on('mysql')->where('policy_id', $policyId)->value('effective_date');

                if ($effectiveDate) {
                    $action = 'wdr';
                    $state = 'withdrawn';
                    $termDate = $effectiveDate;
                } else {
                    $this->error("\nNo effective date found for policy_id: $policyId");
                    $bar->advance();
                    continue;
                }
            }

            $request = (object) [
                'policy_id' => $policyId,
                'tdate' => $termDate,
                'reason' => "Auto {$state} policy due to non-payment.",
                'action' => $action,
                'aid' => 3248,
                'sendEmail' => $sendEmail
            ];
            if($action === 'wdr' && $state === 'withdrawn'){
                $response = $policyFeature->setWithdrawPolicy($request);
            }
            else{
                $response = $policyFeature->setTerminatePolicy($request);
            }
            $this->info("\nResponse for policy_id {$policyId}: " . json_encode($response));

            $bar->advance();
        }

        $bar->finish();
        $this->info("\nAll policies processed successfully.");
    }
}
