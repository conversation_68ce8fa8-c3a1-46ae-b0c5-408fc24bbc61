<?php

namespace App\Console\Commands;

use App\Helpers\GuzzleHelper;
use App\RepInfoRequest;
use App\RepInfoRequestsDetail;
use App\Service\RepInfoRequest\RepInfoRequestMessageService;
use Illuminate\Console\Command;

class CheckMemberCardPaymentStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:member-card-payment-status';

    /**
     * The console command description.
     *
     * @var string
     */    
    protected $description = 'Check member card payment status from payment API and update rep_info_requests_details';
    /**
     * Create a new command instance.
     *
     * @return void
     */
        /**
     * @var RepInfoRequestMessageService
     */
    private $messageService;

    public function __construct(RepInfoRequestMessageService $messageService)
    {
        parent::__construct();
        $this->messageService = $messageService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $records = RepInfoRequestsDetail::where('status', 'Posted')->get();
        $url = config('app.payment_system.url') . 'get-client-payment-status';
        foreach ($records as $record) {
            $response = GuzzleHelper::postApi($url, [], [
                'payment_id' => $record->payment_id,
            ]);
            $decodedResponse = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("Invalid JSON response from payment API");
            }
            if (isset($decodedResponse['success']) && $decodedResponse['success'] == true) {
                $record->status = ucfirst($decodedResponse['data']['status']);
                if($decodedResponse['data']['status'] == 'failed'){
                    $record->payment_description = $decodedResponse['data']['failed_reason'] ?? 'Payment processing failed.';
                    $this->rejectMemberCardPayment($record);
                }
                else if($decodedResponse['data']['status'] == 'dispute'){
                    $record->payment_description = $decodedResponse['data']['dispute_reason'] ?? 'Payment processing failed.';
                    $this->rejectMemberCardPayment($record);
                }
                else if($decodedResponse['data']['status'] == 'paid'){
                    $record->payment_description = 'Payment paid successfully.';
                }
                $record->save();

                $this->info("Updated record ID {$record->id} to status {$record->status}");
            } else {
                $this->error("Failed for record ID {$record->id}, response: " . $decodedResponse);
            }
        }

        return 0;
    }

    public function rejectMemberCardPayment($record){
        $model = RepInfoRequest::find($record->info_request_id);
        $model->update([
            'status' => 'rejected',
            'admin_reason'=>$record->payment_description ?: null,
        ]);
        $this->messageService->sendRepInfoRequestActionEmail($model,'Rejected');
    }
}
