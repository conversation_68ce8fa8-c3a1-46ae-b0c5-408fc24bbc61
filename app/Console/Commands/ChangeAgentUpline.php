<?php

namespace App\Console\Commands;

use App\AgentInfo;
use App\AgentUpdate;
use App\AgentUplineHistory;
use App\AgentUplineScheduler;
use App\Helpers\AgentUpdatesHelper;
use Illuminate\Console\Command;

class ChangeAgentUpline extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:agent-upline-assign
                            {date? : specify date to force apply upline change (optional)}
                            {--report : show summary at the end}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Apply Upline changes of current month.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this_month = $this->argument('date') ?? date('Y-m-d', strtotime('first day of this month'));
        $scheduled_records = $this->fetchScheduledRecords($this_month);

        if (count($scheduled_records) == 0) {
            $this->error('No changes scheduled!');
            return;
        }

        $progress_bar = $this->output->createProgressBar(count($scheduled_records));
        $upline_success_count = 0;
        $upline_failure_count = 0;

        foreach ($scheduled_records as $each_record) {
            $upline_assign_status = 0;
            $current_upline_id = $each_record->agent->agent_ga;
            $current_upline_effective_date_start = $each_record->agent->agent_ga_effective_date;
            $new_upline_id = $each_record->agent_ga;

            try {
                // check if current upline is same as upline that is to be assigned
                if ($current_upline_id == $new_upline_id) {
                    $each_record->note = 'New Upline ID is same as Existing Upline ID.';
                    $each_record->save();
                    continue;
                }

                // check if upline to be assigned is active
                if ($each_record->upline->agent_status != AgentInfo::STATUS_APPROVED) {
                    $each_record->note = "The Upline Agent's Status is not active (status: {$each_record->agent->agent_status}).";
                    $each_record->save();
                    continue;
                }

                // check if upline to be assigned has level higher than current agent
                if ($each_record->agent->agent_level > $each_record->upline->agent_level) {
                    $each_record->note = "Agent's Level ({$each_record->agent->agent_level}) is higher " .
                        "than Requested Upline's Level ({$each_record->upline->agent_level}).";
                    $each_record->save();
                    continue;
                }

                $each_record->agent->agent_ga = $each_record->agent_ga;
                $each_record->agent->agent_ga_effective_date = $each_record->effective_date_start;
                $each_record->agent->save();

                $each_record->note = '';
                $each_record->save();
                $each_record->delete();

                $effective_date_end = date('Y-m-d', strtotime('last day of last month'));
                $history = AgentUplineHistory::firstOrNew([
                    'agent_id' => $each_record->agent_id,
                    'agent_ga' => $current_upline_id,
                    'effective_date_start' => $current_upline_effective_date_start,
                    'effective_date_end' => $effective_date_end
                ]);
                $history->save();

                $older_upline = AgentInfo::find($current_upline_id);
                $eligibility_data = [
                    'agent_id' => $each_record->agent_id,
                    'elgb_act' => AgentUpdate::ACT_UPLINE,
                    'elgb_act_date' => time(),
                    'elgb_agent_id' => request()->header('id'),
                    'elgb_agent_name' => request()->header('name'),
                    'changed_from' => $current_upline_id,
                    'changed_to' => $new_upline_id,
                    'elgb_comment' => "Old Upline Agent ({$older_upline->fullname} - ID: {$older_upline->agent_id}, " .
                        "Code: {$older_upline->agent_code}) terminated. New Upline Agent " .
                        "({$each_record->upline->fullname} - ID: {$each_record->upline->agent_id}, Code: {$each_record->upline->agent_code}) assigned."
                ];
                AgentUpdatesHelper::createAgentUpdateLog($eligibility_data);
                $upline_assign_status = 1;
            }
            catch (\Exception $exception) {
                $each_record->note = $exception->getMessage();
                $each_record->save();
            }
            finally {
                if ($upline_assign_status == 1) $upline_success_count++; else $upline_failure_count++;
                $progress_bar->advance();
            }
        }

        $progress_bar->finish();
        $this->info('');
        $this->info('Upline changes complete.');
        $this->table(
            ['Total Records', 'Success', 'Failure'],
            [[count($scheduled_records), $upline_success_count, $upline_failure_count]]
        );
        $this->info('Please check the `' . 'agent_upline_scheduler' . '` table for details.');
        if ($this->option('report')) {
            $this->renderReport($this_month);
        }
    }

    /**
     * @return mixed
     */
    private function fetchScheduledRecords($date)
    {
        return AgentUplineScheduler::where('effective_date_start', '=', $date)->get();
    }

    private function renderReport($date)
    {
        $all_records = AgentUplineScheduler::withTrashed()
            ->where('effective_date_start', '=', $date)
            ->get()
            ->map(function($each_record) {
                return [
                    $each_record->agent_id . ' (' . $each_record->agent->fullname . ')',
                    $each_record->upline->fullname . ' (' . $each_record->upline->agent_code . ')',
                    isset($each_record->deleted_at) ? 'success' : 'fail',
                    $each_record->note
                ];
            })->all();

        $this->table(
            ['Agent', 'Requested Upline', 'Status', 'Reason'],
            $all_records
        );
    }
}
