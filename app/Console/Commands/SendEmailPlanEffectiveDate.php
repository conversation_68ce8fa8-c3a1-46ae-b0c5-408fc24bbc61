<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\MemberEmailController;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendEmailPlanEffectiveDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:email-plan-effective-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Email for Medical Plans after one of plan effective date';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        echo Carbon::now() ." SEND MEMBER PLAN EFFECTIVE EMAIL START ".PHP_EOL;
        $agentEmailController = new MemberEmailController();
        $agentEmailController->sendMemberMedicalPlanEffectiveNotification();
        echo Carbon::now() ."  SEND MEMBER PLAN EFFECTIVE EMAIL COMPLETED ".PHP_EOL;
    }
}
