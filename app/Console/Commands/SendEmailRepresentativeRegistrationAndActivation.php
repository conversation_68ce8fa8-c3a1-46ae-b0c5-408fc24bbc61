<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\AgentEmailController;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendEmailRepresentativeRegistrationAndActivation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:representative-reminder-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder email to Agents for completing registration and activation process';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        echo Carbon::now() ." SEND REPRESENTATIVE REMINDER EMAIL START ".PHP_EOL;
        $agentEmailController = new AgentEmailController();
        $agentEmailController->sendReminderEmailRepresentativeActivationIncomplete();
        echo Carbon::now() ."  SEND REPRESENTATIVE REMINDER EMAIL COMPLETED ".PHP_EOL;
    }
}
