<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\AgentEmailController;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendAgentWeeklyReportEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:agent-weekly-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generates excel report of agents registered in last 90 days and sends email to <PERSON> and <PERSON><PERSON>';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        echo Carbon::now() ." SEND AGENT WEEKLY REPORT START ".PHP_EOL;
        $agentEmailController = new AgentEmailController();
        $agentEmailController->sendAgentWeeklyReportEmail();
        echo Carbon::now() ."  SEND AGENT WEEKLY REPORT COMPLETED ".PHP_EOL;
    }
}
