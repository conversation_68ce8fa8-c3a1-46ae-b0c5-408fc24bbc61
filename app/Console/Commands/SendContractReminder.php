<?php

namespace App\Console\Commands;

use App\AgentInfo;
use App\Helpers\SendEmailMemberHelper;
use App\RepContract;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SendContractReminder extends Command
{
    protected $signature = 'send:contract-sign-reminders';
    protected $description = 'Send reminders to agents regarding contract signing deadlines';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $log = Log::channel('agent_expiry_logs');
    
        echo Carbon::now() . "  SEND AGENT CONTRACT EXPIRY EMAIL REMINDER  START " . PHP_EOL;
        $log->info('SEND AGENT CONTRACT EXPIRY EMAIL REMINDER START');
    
        $agents = AgentInfo::where('agent_status', 'P')->get();
        foreach ($agents as $agent) {
            $agentId = $agent->agent_id;
            $is_incomplete = RepContract::query()
                ->with('repContractDetail')
                ->where('agent_id', '=', $agentId)
                ->orderBy('contract_id', 'DESC')
                ->where('is_completed', 'no')
                ->whereIn('contract_id', function ($query) use ($agentId) {
                    $query->select(DB::raw('MAX(contract_id)'))
                        ->from('rep_contract')
                        ->where('agent_id', $agentId)
                        ->where('is_completed', 'no')
                        ->groupBy('contract_type');
                })
                ->first();
    
            if ($is_incomplete) {
                SendEmailMemberHelper::sendContractExpiryEmail($agent);
            }
        }
    
        $log->info('SEND AGENT CONTRACT EXPIRY EMAIL REMINDER COMPLETED');
        echo Carbon::now() . " SEND AGENT CONTRACT EXPIRY EMAIL REMINDER COMPLETED " . PHP_EOL;
    }
}
