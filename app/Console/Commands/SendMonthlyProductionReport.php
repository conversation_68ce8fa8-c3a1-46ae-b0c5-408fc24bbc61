<?php

namespace App\Console\Commands;

use App\Http\Controllers\MonthlyReport\MonthlyReportController;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendMonthlyProductionReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:monthly-production-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send monthly production report emails';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        echo Carbon::now() . " SEND MONTHLY REPORT START " . PHP_EOL;
        $reportController = new MonthlyReportController();
        $reportController->report();
        echo Carbon::now() . "  SEND MONTHLY REPORT COMPLETED " . PHP_EOL;
    }
}
