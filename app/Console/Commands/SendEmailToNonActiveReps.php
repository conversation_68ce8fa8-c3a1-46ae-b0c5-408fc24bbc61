<?php

namespace App\Console\Commands;

use App\CustomeHomepage;
use App\Helpers\CommonHelper;
use App\Helpers\LogFileHelper;
use App\Service\MessageService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SendEmailToNonActiveReps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:email-to-non-active-reps';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is for sendings emails to non active reps';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $august1 = '2023-08-01';
            $agents = DB::table('agent_info')->where('agent_status', 'A')->where('created_at','>=',$august1)->get();
            foreach ($agents as $agent) {
                if (!$this->checkIfAgentHasActivePolicies($agent->agent_id)) {
                    $mname = !empty($agent->agent_mname) ? ' ' . $agent->agent_mname : '';
                    $agent_name = $agent->agent_fname . $mname . ' ' . $agent->agent_lname;
                    $this->info('Running for agent : ' . $agent->agent_fname);
                    if ($this->checkAgentHasStatusWithDrawnOrNot($agent->agent_id)) {
                        $this->info('FROM withdrawn');
                        $agent_date = date('Y-m-d', $agent->agent_signup_date); //convert timestamp to date format
                    } else {
                        $this->info('from termed');
                        $termDate = $this->getLastestTermDate($agent->agent_id);
                        $agent_date = $termDate->term_date;
                    }
                    $this->sendEmails($agent_name, $agent_date, $agent->agent_ga, $agent->agent_email);
                }
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
            LogFileHelper::writeLogFile('./SEND-EMAIL-TO-NON-ACTIVE-REPS-', 'SendEmailToNonActiveReps', 'ERROR', 'ERROR Message from command SendEmailToNonActiveReps  : ' .  $e->getMessage());
        }
    }

    public function checkIfAgentHasActivePolicies($agent_id)
    {
        return DB::table('policies')->whereRaw("p_agent_num = $agent_id and (status = 'ACTIVE' or (status = 'TERMED' and term_date > curdate()))")->exists();
    }

    public function sendEmails($agent_name, $agent_date, $uplineId, $agent_email)
    {
        $twoMonth = date('Y-m-d', strtotime("-2 months"));
        $threeMonth = date('Y-m-d', strtotime("-3 months"));
        $fourMonth = date('Y-m-d', strtotime("-4 months"));
        $sixMonth = date('Y-m-d', strtotime("-6 months"));
        $oneYear = date('Y-m-d', strtotime("-1 year"));


        //upline info
        $uplineAgentInfo = $this->getAgentUplineInfo($uplineId);
        $displayInfo=CustomeHomepage::where('agent_id',$uplineAgentInfo->agent_id)->first();
        $uplineAgentEmail=$displayInfo? $displayInfo->email : $uplineAgentInfo->agent_email;
        $uplineAgentPhone1=$displayInfo ? CommonHelper::format_phone($displayInfo->phone) :CommonHelper::format_phone($uplineAgentInfo->agent_phone1);


        $upline_mname = !empty($uplineAgentInfo->agent_mname) ? ' ' . $uplineAgentInfo->agent_mname : '';
        $uplineDisplayName=$uplineAgentInfo->agent_web_name ;
        $upline_name = $uplineDisplayName ? $uplineDisplayName : $uplineAgentInfo->agent_fname . $upline_mname . ' ' . $uplineAgentInfo->agent_lname;
        if ($agent_date == $oneYear) {
            $this->info('Api calling for one year Agent Name : ' . $agent_name);
            LogFileHelper::writeLogFile('./SEND-EMAIL-TO-NON-ACTIVE-REPS-', 'SendEmailToNonActiveReps', 'INFO', 'Api calling for one year Agent Name : ' . $agent_name);
            //call api
            try {
                $this->callApiForSendingEmail('How can we help?',"AFTER_TWELVE_MONTHS_WITH_NO_CASES", $agent_email, $agent_name,$uplineAgentEmail, $uplineAgentInfo->agent_code);
            } catch (Exception $e) {
                throw $e;
            }
        } else if ($agent_date == $sixMonth) {
            $this->info('Api calling for six month Agent Name : ' . $agent_name);
            LogFileHelper::writeLogFile('./SEND-EMAIL-TO-NON-ACTIVE-REPS-', 'SendEmailToNonActiveReps', 'INFO', 'Api calling for six month Agent Name : ' . $agent_name);
            try {
                $this->callApiForSendingEmail('We are here to support you.',"AFTER_SIX_MONTHS_WITH_NO_CASES", $agent_email, $agent_name,$uplineAgentEmail);
            } catch (Exception $e) {
                throw $e;
            }
        } else if ($agent_date == $fourMonth) {
            $this->info('Api calling for four month Agent Name : ' . $agent_name);
            LogFileHelper::writeLogFile('./SEND-EMAIL-TO-NON-ACTIVE-REPS-', 'SendEmailToNonActiveReps', 'INFO', 'Api calling for three month Agent Name : ' . $agent_name);
            try {
                $this->callApiForSendingEmail('Let’s get this partnership moving.',"AFTER_THREE_MONTHS_WITH_NO_CASES", $agent_email, $agent_name, $uplineAgentEmail,$upline_name, $uplineAgentEmail, $uplineAgentPhone1, $uplineAgentInfo->agent_code);
            } catch (Exception $e) {
                throw $e;
            }
        } else if ($agent_date == $threeMonth) {
            
            $this->info('Api calling for three month Agent Name : ' . $agent_name);
            LogFileHelper::writeLogFile('./SEND-EMAIL-TO-NON-ACTIVE-REPS-', 'SendEmailToNonActiveReps', 'INFO', 'Api calling for two month Agent Name : ' . $agent_name);
            try {
                $this->callApiForSendingEmail('Don’t be a stranger.',"AFTER_TWO_MONTHS_WITH_NO_CASES", $agent_email, $agent_name,$uplineAgentEmail, $upline_name, $uplineAgentEmail, $uplineAgentPhone1);
            } catch (Exception $e) {
                throw $e;
            }
        } else if ($agent_date == $twoMonth) {
            $this->info('Api calling for two month Agent Name : ' . $agent_name);
            LogFileHelper::writeLogFile('./SEND-EMAIL-TO-NON-ACTIVE-REPS-', 'SendEmailToNonActiveReps', 'INFO', 'Api calling for one month Agent Name : ' . $agent_name);
            try {
                $this->callApiForSendingEmail('How can we help?',"AFTER_ONE_MONTH_WITH_NO_CASES", $agent_email, $agent_name,$uplineAgentEmail, $upline_name, $uplineAgentEmail, $uplineAgentPhone1,$uplineAgentInfo->agent_code);
            } catch (Exception $e) {
                throw $e;
            }
        }
    }

    public function checkAgentHasStatusWithDrawnOrNot($agent_id)
    {
        $res = DB::table('agent_totals')->where('agent_id', $agent_id);
        if ($res->doesntExist()) {
            return true;
        } else {
            return $res->where('status', 'WITHDRAWN')->count() == 1;
        }
    }

    public function getLastestTermDate($agent_id)
    {
        return DB::table('policies')->where('p_agent_num', $agent_id)->orderByDesc('term_date')->first();
    }


    public function getAgentUplineInfo($agent_id)
    {
        return DB::table('agent_info')->where('agent_id', $agent_id)->first();
    }

    public function callApiForSendingEmail($subject,$configName, $toAddress, $agent_name,$ccAddress, $upline_name = null, $upline_email = null, $upline_phone = null, $upline_agent_code = null)
    {
        if($upline_agent_code && $upline_agent_code == "5BHFL457") {
            $upline_phone = "************";
        }
        $payload = [
            "email_message_configuration_name" => $configName,
            "toAddress" => [$toAddress],
            "ccAddress" => [$ccAddress],
            "subject" => $subject,
            "attachedFiles" => [],
            "generalTemplateData" => [],
            "contentData" => [
                "agent_name" => $agent_name,
                "upline_name" =>  $upline_name,
                "upline_email" => $upline_email,
                "upline_phone" => $upline_phone
            ]
        ];
        if (empty($upline_name)) unset($payload['contentData']['upline_name']);
        if (empty($upline_email)) unset($payload['contentData']['upline_email']);
        if (empty($upline_phone)) unset($payload['contentData']['upline_phone']);

        $messageService = new MessageService;
        $response =  $messageService->sendEmailWithTemplateAndData($payload);
        
        LogFileHelper::writeLogFile('./SEND-EMAIL-TO-NON-ACTIVE-REPS-', 'SendEmailToNonActiveReps', 'INFO', 'MAIL RESPONSE of Agent Name : ' . $agent_name . ' RESPONSE : ' . json_encode($response));
        return $response;
    }
}
