<?php

namespace App\Console\Commands;

use App\Policy;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Helpers\SendEmailMemberHelper;

class SendApprovalEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:send-benefit-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Member Benefit Email for a Policy on the Effective Date';

    /**
     * Create a new command instance.
     *
     * @return void
     */

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        echo Carbon::now() ." SEND MEMBER BENFIT EMAIL FOR POLICY ON EFFECTIVE DATE START ".PHP_EOL;
        Log::info('SEND MEMBER BENFIT EMAIL FOR POLICY ON EFFECTIVE DATE START');
        $subject = '';
        $message = '';
        $date = date('Y-m-d');
        $policies = Policy::where('effective_date','=',$date)->where('Approval',1)->get();
        foreach ($policies as $policy) {
            $bodyConfigs = [
                'policy_id' => $policy->policy_id,
            ];
            SendEmailMemberHelper::sendBenefitEmail($bodyConfigs);
        }
        Log::info('SEND MEMBER BENFIT EMAIL FOR POLICY ON EFFECTIVE DATE COMPLETED');
        echo Carbon::now() ."  SEND MEMBER BENFIT EMAIL FOR POLICY ON EFFECTIVE DATE COMPLETED ".PHP_EOL;
    }
}
