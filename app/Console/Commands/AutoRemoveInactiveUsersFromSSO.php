<?php

namespace App\Console\Commands;

use App\GroupInfo;
use App\Helpers\CommonHelper;
use App\PlanOverview;
use App\UserInfoPolicyAddress;
use Exception;
use App\Helpers\GuzzleHelper;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;

class AutoRemoveInactiveUsersFromSSO extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command auto removes inactive ssers from SSO';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $today = date('Y-m-d');
            $yesterday = date('Y-m-d',strtotime('-1 days'));
            $users = UserInfoPolicyAddress::get(['userid', 'cemail']);
            foreach ($users as $user) {
                $this->info('Running for user :' . $user->userid);
                $activeUserExists = PlanOverview::where('userid', $user->userid)->whereRaw("(pstatus = 1 or (pstatus = '2' and pterm_date > '$today'))")->exists();
                if (!$activeUserExists) {
                    CommonHelper::deleteSSOUser($user->cemail, 'M');
                }
            }

            $groupInfos = GroupInfo::where('termed_date', $yesterday)->get();
            foreach($groupInfos as $groupInfo) {
                $this->info('Running for group ID:' . $groupInfo->gid);
                if($this->updateStatusInSsoUser($groupInfo->gid)) {
                    Log::info('Successfully removed records from sso_users table for group ID:' . $groupInfo->gid);
                } else {
                    Log::info('There was error while removing records from sso_users table for group ID:' . $groupInfo->gid);
                }
            }

        } catch (Exception $e) {
            $this->error('Error :' . $e->getMessage());
        }
    }

    protected function updateStatusInSsoUser($groupId)
    {
        $url = config('corenroll.corenroll_api_url') . "api/v1/remove-group-sso/{$groupId}";
        $requestData = [];
        $responseJson = GuzzleHelper::postApi($url, [], $requestData);
        $response = json_decode($responseJson, true);
        if ($response['status'] == 'success') {
            return true;
        } else {
            return false;
        }
    }
}
