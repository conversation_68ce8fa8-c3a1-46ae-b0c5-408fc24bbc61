<?php

namespace App\Console\Commands;
use Illuminate\Console\Command;

use Carbon\Carbon;
use App\Http\Controllers\Api\V1\AgentTextMessageController;


class SendRepresentativeMonthlyReportText extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:month-by-month-report-for-reps';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Month by Month Report for Representative (Text Message for LOU)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        echo Carbon::now() ." Month By Month Reports for Reps".PHP_EOL;
        $agentTextMessageController = new AgentTextMessageController();
        $agentTextMessageController->sendScheduledMonthlyReportsForRepresentative();
        $this->info('');
        $this->info('MONTH BY MONTH REPORTS FOR REPRESENTATIVE SEND SUCCESSFULLY!');
    }
}
