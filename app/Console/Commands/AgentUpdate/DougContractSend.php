<?php

namespace App\Console\Commands\AgentUpdate;

use App\AgentInfo;
use Carbon\Carbon;
use App\AgentUpdate;
use App\Helpers\GuzzleHelper;
use Illuminate\Http\Request;
use App\Service\MessageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Helpers\AgentUpdatesHelper;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use App\Repositories\Agents\v2\ManageAgents;
class DougContractSend extends Command
{
    protected $signature = 'doug:send-contract-with-email {--emailSend=} {--agent_id=} {--batch=}';
    public $counter = 0;
    public function handle()
    {
        $emailSend  = $this->option('emailSend');
        $agent_id   = $this->option('agent_id');
        $batch      = $this->option('batch');
        if($batch){
            $this->info('Assign Contract Level with sending email levels...');
            
            $this->sendContractWithEmail($emailSend, $agent_id, $batch);
    
            $this->info('Assign contract level successfully.');
            Log::channel('repcontactlog')->info("Total Count:". $this->counter);
        }else{
            $this->info('Batch required.');
        }
    }
 
    private function sendContractWithEmail($emailSend, $agent_id, $batch)
    {
        // Fetch agents that need updating
        if($agent_id){
            $agents = AgentInfo::where('agent_level', 2)
                ->where('agent_id', $agent_id)
                ->get();
        }else{
            $agents = AgentInfo::where('agent_level', 2)
                ->whereIn('agent_id', $this->getEligibleAgentIds($batch))
                ->get();
        }
        if(count($agents) > 0){
            foreach ($agents as $agent) {
                $this->assignLevelsSendingContract($agent,$emailSend);
            }
        }else{
            Log::channel('repcontactlog')->info("Data not found.");
        }
    }

    private function assignLevelsSendingContract($agent,$emailSend){

        try {
            $contract_level = $this->calculateContractLevel($agent);
            if($emailSend == 'yes'){
                Log::channel('repcontactlog')->info("----------------------------- Contract Sign With Email ------------------------------");
                $requestData = [
                    "sendEmail" => 1,
                    "agent_id" => $agent->agent_id,
                    "action_origin" => "admin",
                    "contract_type" => "N-CatContrs",
                    "data" => [
                        "agent_level_alc" => $agent->agent_level,
                        "agent_level_pec" => $agent->agent_level_2,
                        "agent_level_pec_bex" => 0,
                        "agent_level_alc_bex" => 0,
                        "agent_level_fegli_ancillary" => 0,
                        "agent_level_patriot_ancillary" => $contract_level,
                    ]
                ];
                $this->counter = $this->counter + 1;
                
                $apiUrl = config('app.nuera_api_url.url').'/api/v2/save-contract-levels';
                Log::channel('repcontactlog')->info($this->counter.' API: '.$apiUrl);
                $responseJson = GuzzleHelper::postApi($apiUrl,[],$requestData);

                Log::channel('repcontactlog')->info('responseJson: '.$responseJson);
                Log::channel('repcontactlog')->info($this->counter." Contract sent for Agent: ".$agent->agent_id." - ". $agent->agent_fname." ".$agent->agent_lname."- premium:".$agent->agent_level."- smart:".$agent->agent_level_2);
                sleep(2);
            }else {
                Log::channel('repcontactlog')->info("----------------------------- Contract Sign Without Email ------------------------------");
                // Union
                Artisan::call('assignNonCatContract', [
                    '--agent_id' => $agent->agent_id,
                    '--level' => $agent->agent_level,
                    '--contract_type' => 'ancillary',
                ]);
                $output1 = Artisan::output();
                Log::channel('repcontactlog')->info($output1);
                // Smart
                Artisan::call('assignNonCatContract', [
                    '--agent_id' => $agent->agent_id,
                    '--level' => $agent->agent_level,
                    '--contract_type' => 'premier',
                ]);
                $output2 = Artisan::output();
                Log::channel('repcontactlog')->info($output2);
                // patriot
                Artisan::call('assignNonCatContract', [
                    '--agent_id' => $agent->agent_id,
                    '--level' => $contract_level,
                    '--contract_type' => 'patriotAncillary',
                ]);
                $output3 = Artisan::output();
                Log::channel('repcontactlog')->info($output3);

            }
        } catch (\Exception$e) {
            Log::channel('repcontactlog')->error("Failed to send contract for Agent ID {$agent->agent_id}: " . $e->getMessage());
        }
    }
    private function calculateContractLevel($agent)
    {
        $specialIds = [100850, 100733, 100630, 237, 101803, 101295, 100627];
 
        if ($agent->agent_id == 1508) {
            return '1'; // Michael Stout [ MSNJ5899 ]
        }
        if ($agent->agent_id == 102813 || $agent->agent_id == 100624) {
            return '3'; // Andy Sawyer [ ASCT9834 ] || Mark Lukachuko
        }
        if (in_array($agent->agent_id, $specialIds)) {
            return '2'; // Mark Lukachuko downline
        }
        $contract = DB::table('rep_contract')
            ->join('rep_contract_details', 'rep_contract.contract_id', '=', 'rep_contract_details.contract_id')
            ->where('rep_contract.agent_id', $agent->agent_id)
            ->orderByDesc('rep_contract.contract_id')
            ->first();
 
        $level = ($contract && $contract->contract_level >= 3) ? $contract->contract_level : '3';
        Log::channel('repcontactlog')->info("Existing contract level:". $level);
        return $level;
    }
    private function checkRepContract($agent)
    {
        $status = false;
        $latestContracts = DB::table('rep_contract')
            ->selectRaw('MAX(contract_id) as contract_id')
            ->where('agent_id', $agent->agent_id)
            ->groupBy('contract_type');

        $contracts = DB::table('rep_contract as rc')
            ->join('rep_contract_details as rcd', 'rc.contract_id', '=', 'rcd.contract_id')
            ->whereIn('rc.contract_id', $latestContracts)
            ->where('rc.agent_id', $agent->agent_id)
            ->whereIn('rc.contract_type', ['categoryL9','patriotAncillary'])
            ->where('rcd.contract_level','>=',3)
            ->where('rc.is_completed', 'yes')
            ->orderBy('rc.created_at', 'desc')
            ->select('rc.agent_id', 'rc.contract_type', 'rcd.contract_item_name', 'rcd.contract_level')
            ->get();
        if(count($contracts) > 0){
            $status = true;
            Log::channel('repcontactlog')->info($contracts);
        }
        return $status;
    }
 
    private function getEligibleAgentIds($batch)
    {
        if($batch == 1){
            $data = [
                1461,1462,1463,1465
            ];
        }
        return $data;
    }
}
