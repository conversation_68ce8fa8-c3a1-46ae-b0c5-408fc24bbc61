<?php

namespace App\Console\Commands\AgentUpdate;

use App\AgentInfo;
use Carbon\Carbon;
use App\AgentUpdate;
use Illuminate\Http\Request;
use App\Service\MessageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Helpers\AgentUpdatesHelper;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use App\Repositories\Agents\v2\ManageAgents;

class UpdateUpline extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'upline:update-with-level';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Updating downline levels...');

        DB::transaction(function () {
            $this->updateLevels();
        });

        $this->info('Downline levels updated successfully.');
    }
    private function updateLevels()
    {
        // Fetch agents that need updating
        $agents = AgentInfo::where('agent_level', 1)
            ->whereIn('agent_id', $this->getEligibleAgentIds())
            ->get();

        foreach ($agents as $agent) {
            $agent->update([
                'agent_level'   => $this->calculateAgentLevel($agent),
                'agent_level_2' => $this->calculateAgentLevel($agent),
            ]);

            $this->updateUpline($agent);
        }
    }
    private function calculateAgentLevel($agent)
    {
        if ($agent->agent_id == 1508) {
            return '1'; // Michael Stout [ MSNJ5899 ]
        }
        if ($agent->agent_id == 102813 || $agent->agent_id == 100624) {
            return '3'; // Andy Sawyer [ ASCT9834 ] || Mark Lukachuko
        }
        return ($agent->agent_level > 1) ? $agent->agent_level : '2';
    }

    private function calculateAgentLevel2($agent)
    {
        return !in_array($agent->agent_id, [1508, 102813]) ? ($agent->agent_id == 1508 ? '1' : '3') : '2';
    }
    private function calculateContractLevel($agent)
    {
        $specialIds = [100850, 100733, 100630, 237, 101803, 101295, 100627];

        if ($agent->agent_id == 1508) {
            return '1';
        }
        if ($agent->agent_id == 102813) {
            return '3';
        }
        if (in_array($agent->agent_id, $specialIds)) {
            return '2';
        }


        $contract = DB::table('rep_contract')
            ->join('rep_contract_details', 'rep_contract.contract_id', '=', 'rep_contract_details.contract_id')
            ->where('rep_contract.agent_id', $agent->agent_id)
            ->orderByDesc('rep_contract.contract_id')
            ->first();

        return ($contract && $contract->contract_level >= 3) ? $contract->contract_level : '3';
    }
    private function getEligibleAgentIds()
    {
        return [
            100628
        ];
    }
    public function updateUpline($agent)
    {
        $uplineUpdates = [
            // agent_id => [pre_upline, newUpline], agent_name -> upline_name
            100628    => [638, 10029], // Jeffrey Harder -> Eric Bagnall [ EBCT2696 ]
        ];

        // Check if the agent's ID exists in the upline updates array
        if (isset($uplineUpdates[$agent->agent_id])) {
            list($from, $to) = $uplineUpdates[$agent->agent_id];

            // Update the agent's upline
            $agent->update([
                'agent_ga' => $to
            ]);
            \Log::info('Upline updated from Agent ' . $from . ' to Agent ' . $to);

            $successMessage = 'Upline updated from Agent ' . $from . ' to Agent ' . $to;

            // Record the update in the agent_updates table
            $fromTo['changed_from'] = $from;
            $fromTo['changed_to'] = $to;

            $data = [
                "agent_id" => $agent->agent_id,
                "elgb_act" => 'UPCHG',
                "elgb_comment" => $successMessage,
                "elgb_act_date" => time(),
                "elgb_agent_id" => 3229,
                "elgb_agent_name" => 'Dharma'
            ];
            AgentUpdatesHelper::createAgentUpdateLog($data);
        }
    }
}