<?php

namespace App\Console\Commands\AgentUpdate;

use App\AdminUser;
use App\AgentUpdate;
use App\SsoUsers;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class syncAgentIdWithName extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'syncAgentIdWithName:AgentUpdate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update agent update id from name column to user name';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        DB::beginTransaction();
        try{
            $collection = new Collection();
            $results = AgentUpdate::whereRaw('CAST(elgb_agent_name AS UNSIGNED) IS NOT NULL')
                ->get();
            
            foreach ($results as $result) {
                if (is_numeric($result->elgb_agent_name)) {
                    $collection->push($result);
                }
            }
            $progressBar = $this->output->createProgressBar($collection->count());
            $progressBar->setOverwrite(false);
            $progressBar->start();
            foreach($collection as $collected){
                $adminUsers = AdminUser::where('id',$collected->elgb_agent_id)->first();
                if($adminUsers){
                    $collected->elgb_agent_name = $adminUsers->name;
                    $collected->save();

                }
                $progressBar->advance();
            }
            $this->info('Agent Update id to name sync completed.');
            DB::commit();
            $progressBar->finish();
        }
        catch(Exception $e){
            $this->info($e->getMessage());
            DB::rollBack();
        }

    }
}
