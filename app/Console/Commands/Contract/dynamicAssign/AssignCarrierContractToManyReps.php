<?php

namespace App\Console\Commands\Contract\dynamicAssign;

use App\AgentInfo;
use App\Helpers\AgentUpdatesHelper;
use Illuminate\Console\Command;
use App\RepContract;
use App\RepContractsDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\RepContractAct;


class AssignCarrierContractToManyReps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assignCarrierContractPatriot {--agent_id=} {--level=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assigning Carrier contract';
    protected static $array_downlines;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $agent_id = $this->option('agent_id');
        $level = $this->option('level');
        $this->info("Carrier contract assign to agentId ".$agent_id.' of level '.$level.' processed.');

            DB::beginTransaction();
            try {
                $this->assignRepContract($agent_id, $level);
                if($this->isAgentPending($agent_id)){
                    $this->activateAgent($agent_id);
                }
                // $this->addAgentGroup($agent_id);
                DB::commit();
            $this->info("script ended for agentId ".$agent_id.' of level '.$level.' with success.');
            } catch (\Exception $e) {
                DB::rollBack();
                $this->info("script ended for agentId ".$agent_id.' of level '.$level.' with Error.');
                $this->error('Error :' . $e->getMessage());
            }
    }

    public function assignRepContract(int $agent_id, int $level){
        $data = [
            'contract_type' => 'carrier',
            'agent_id' => $agent_id,
            'contract_display_name' => 'Carrier Contract',
            'is_completed' => 'yes',
            'screenshot' => null,
            'clicked_at' => null,
            'signature' => null,
        ];
        $repContractSave = RepContract::create($data);
        $detailDataL9 = [
            [
                'contract_item_display_name' =>'Local 713',
                'contract_item_name' => 'local713',
            ],
            [
                'contract_item_display_name' =>'Cigna',
                'contract_item_name' => 'cigna',
            ],
            [
                'contract_item_display_name' =>'L713 Anthem',
                'contract_item_name' => 'l713Anthem',
            ],
            [
                'contract_item_display_name' =>'Northwell Anthem',
                'contract_item_name' => 'northwellAnthem',
            ],
            [
                'contract_item_display_name' =>'IHA Health',
                'contract_item_name' => 'IHAHealth',
            ],
            [
                'contract_item_display_name' =>'ENANAWU',
                'contract_item_name' => 'ENANAWU',
            ],
            [
                'contract_item_display_name' =>'Lifeline Medical',
                'contract_item_name' => 'lifelineMedical',
            ],
            [
                'contract_item_display_name' =>'Solstice Benefits',
                'contract_item_name' => 'solsticeBenefits',
            ],
            [
                'contract_item_display_name' =>'Options Plus',
                'contract_item_name' => 'optionsPlus',
            ],
            [
                'contract_item_display_name' =>'Metropolitan Life Insurance Company',
                'contract_item_name' => 'metropolitanLifeInsuranceCompany',
            ],
            [
                'contract_item_display_name' =>'Pinnacle Benefits Services LLC',
                'contract_item_name' => 'pinnacleBenefitsService',
            ],
            [
                'contract_item_display_name' =>'Prudential',
                'contract_item_name' => 'prudential',
            ],
            [
                'contract_item_display_name' =>'Beyond Medical',
                'contract_item_name' => 'beyondMedical',
            ]
        ];

        foreach($detailDataL9 as $detailData){
            RepContractsDetail::create([
                'contract_id' => $repContractSave->contract_id,
                'contract_item_display_name' => $detailData['contract_item_display_name'],
                'contract_item_name' => $detailData['contract_item_name'],
                'contract_level' => $level,
            ]);
        }
        $contracts = $this->generateContractsString($level);
        $contractSentData = [
            'action' => 'contracts sent',
            'aid' => $agent_id,
            'userid' => 3248,
            'contracts' => $contracts,
            'contract_type' => 'carrContrs',
            'ts' => time(),
            'ipaddress' => null
        ];
        RepContractAct::query()->create($contractSentData);
        $contractSubmitData = [
            'action' => 'contract submitted',
            'aid' => $agent_id,
            'userid' => 3248,
            'contracts' => 'carrier -',
            'contract_type' => 'carrContrs',
            'ts' => time(),
            'ipaddress' => null
        ];
        RepContractAct::query()->create($contractSubmitData);
    }

    public function generateContractsString($level)
    {
        $contractTypes = [
            'LOCAL713',
            'CIGNA',
            'L713ANTHEM',
            'NORTHWELLANTHEM',
            'IHAHEALTH',
            'ENANAWU',
            'LIFELINEMEDICAL',
            'SOLSTICEBENEFITS',
            'OPTIONSPLUS',
            'METROPOLITANLIFEINSURANCECOMPANY',
            'PINNACLEBENEFITSSERVICE',
            'PRUDENTIAL',
            'BEYONDMEDICAL'
        ];
        $mappedContracts = array_map([$this, 'appendLevel'], $contractTypes, array_fill(0, count($contractTypes), $level));
        return implode(' ', $mappedContracts) . '-';
    }

    public function appendLevel($contract, $level)
    {
        return $contract . " $level";
    }
    
    public function activateAgent(int $agent_id){
        $agentUpdatesHelper =new AgentUpdatesHelper();
        $agentInfo = AgentInfo::where('agent_id', $agent_id)->first();
        if ($agentInfo) {
            AgentInfo::where('agent_id', $agent_id)
            ->update([
                'agent_status' => 'A',
            ]);
            $updates = [
                'agent_id' => $agent_id,
                'elgb_act' => 'LNSCHNG',
                'elgb_act_date' => Carbon::now()->timestamp,
                'elgb_comment' => 'Agent status updated. ' .
                    'Updated in agent_info table. ' .
                    'Old Values: Status-' . $agentInfo->agent_status ,
                'elgb_agent_id' => 3248,//3248
                'elgb_agent_name' => 'Sailesh'//Sailesh
            ];
            $agentUpdatesHelper->logAgentUpdates($updates);
        }
    }

    public function isAgentPending(int $agent_id){
        return AgentInfo::query()
        ->where('agent_status','P')
        ->where('agent_id', '=', $agent_id)
        ->exists();
    }

    // public function addAgentGroup($agent_id){
    //     $agentInfo = AgentInfo::find($agent_id);
    //     $gid = 4123;
    //     $count = $this->addAgentInGroup($gid, $agentInfo);
    //     if ($count['count'] > 0) {
    //         $successMessage = "{$count['count']} group(s) added successfully.";
    //         $this->createAgentUpdate($agent_id, \App\AgentUpdate::ACT_ADD_GROUP, $successMessage);
    //     }
    // }

    // protected function addAgentInGroup($gid, $agentInfo)
    // {
    //     $count = 0;
    //     $addedGids = [];
    //         $groupInfo = $this->getGroup($gid);
    //         $data = [
    //             'agent_id' => $agentInfo->agent_id,
    //             'gid' => $groupInfo->gid,
    //             'ts' => time(),
    //             'isadmin' => isset($agentInfo->admin_only) ? $agentInfo->admin_only : 0,
    //             'lvl' => $agentInfo->agent_level,
    //             'ag_status' => $agentInfo->agent_status,
    //             'lvl2' => $agentInfo->agent_level_2,
    //         ];
    //         $checkAgentInGroup = $this->checkAgentGroupExist($groupInfo->gid, $agentInfo->agent_id);
    //         if (!$checkAgentInGroup) {
    //             $agentInGroup = AgentGroup::create($data);
    //             array_push($addedGids, $agentInGroup->gid);
    //             $count++;
    //         }
    //     return [
    //         'count' => $count,
    //         'addedGids' => $addedGids
    //     ];
    // }

    protected function createAgentUpdate($agent_id, $act = "", $comment = "")
    {
        $data = [
            "agent_id" => $agent_id,
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent_id" => 3248,
            "elgb_agent_name" => 'Sailesh',
            "changed_from" => null,
            "changed_to" => null
        ];
        AgentUpdatesHelper::createAgentUpdateLog($data);
    }

    // protected function getGroup($gId)
    // {
    //     return GroupInfo::find($gId);
    // }

    // protected function checkAgentGroupExist($gid, $agentId)
    // {
    //     return AgentGroup::query()
    //         ->where([
    //             'agent_id' => $agentId,
    //             'gid' => $gid,
    //             'ag_status' => AgentGroup::STATUS_APPROVED
    //         ])
    //         ->exists();
    // }

    // public function getDownlines($agentID)
    // {
    //     $array_agents = array();
    //     $agents = AgentInfo::where('agent_ga', '=', $agentID)->get();

    //     foreach ($agents as $agentIds) {
    //         $array_agents[] = $agentIds->agent_id;
    //         self::$array_downlines[] = $agentIds->agent_id;
    //     }
    //     $allDownlines = self::getallDownlines($array_agents);

    //     return $allDownlines;

    // }

    // //function to fetch all downline
    // public static function getallDownlines($agentArray)
    // {
    //     $array_agents = array();
    //     $temp_array = array();
    //     $array_agents = array_merge($array_agents, $agentArray);
    //     $data_agent = AgentInfo::whereIn('agent_ga', $agentArray)->get();

    //     foreach ($data_agent as $agentIds) {
    //         self::$array_downlines[] = $agentIds->agent_id;
    //         $temp_array[] = $agentIds->agent_id;
    //     }
    //     if (count($temp_array) > 0) {
    //         self::getallDownlines($temp_array);
    //     }
    //     return self::$array_downlines;
    // }
}
