<?php

namespace App\Console\Commands\Contract\dynamicAssign;

use App\AgentGroup;
use App\AgentInfo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\RepContract;

class AssignAgentGroupsFromRepContracts extends Command
{
    protected $signature = 'agents:assign-groups {agentIds* : List of agent IDs (space-separated)}';
    protected $description = 'Assign agents to groups based on their latest completed rep contracts.';

    const GO_ENROLL_GROUP_ID = 1881;
    const PATRIOT_GROUP_ID = 4123;
    const ELITE_GROUP_ID = 1073;
    const PREMIER_GROUP_ID = 1926;
    const BROKER_EXCHANGE_ID = 1399;
    const FEGLI_ID = 4054;

    const CONTRACT_BASED_GROUPS = [4054, 1881, 4123, 1073, 1926, 1399];

    public function handle()
    {
        $agentIds = $this->argument('agentIds');

        foreach ($agentIds as $agentId) {
            DB::beginTransaction();

            try {
                $contracts = RepContract::query()
                    ->with('repContractDetail')
                    ->where('agent_id', $agentId)
                    ->where('is_completed', 'yes')
                    ->whereIn('contract_id', function ($query) use ($agentId) {
                        $query->select(DB::raw('MAX(contract_id)'))
                            ->from('rep_contract')
                            ->where('agent_id', $agentId)
                            ->where('is_completed', 'yes')
                            ->groupBy('contract_type');
                    })
                    ->orderBy('contract_id', 'DESC')
                    ->get();

                $agentInfo = AgentInfo::where('agent_id', $agentId)->first();

                if (!$agentInfo) {
                    $msg = "⚠️ Agent ID $agentId not found.";
                    $this->warn($msg);
                    Log::channel('agent_group_assignment')->warning($msg);
                    continue;
                }

                $allValidGroupIds = [];

                // Track what types we have already from contracts
                $contractTypes = $contracts->pluck('contract_type')->all();

                foreach ($contracts as $contract) {
                    $groupIds = $this->getGroupIdsByContractType($contract->contract_type);
                    $allValidGroupIds = array_merge($allValidGroupIds, $groupIds);

                    foreach ($groupIds as $gid) {
                        $result = $this->addAgentInGroup($gid, $agentInfo);

                        if ($result['count'] > 0) {
                            $msg = "✅ Agent {$agentId} added to group {$gid}";
                            $this->info($msg);
                            Log::channel('agent_group_assignment')->info($msg);
                        } else {
                            $msg = "ℹ️ Agent {$agentId} already in group {$gid}";
                            Log::channel('agent_group_assignment')->info($msg);
                        }
                    }
                }

                // Apply extra contract types based on agent level if missing
                if (!in_array('premier', $contractTypes) && $agentInfo->agent_level > 0) {
                    $groupIds = $this->getGroupIdsByContractType('premier');
                    $allValidGroupIds = array_merge($allValidGroupIds, $groupIds);
                    foreach ($groupIds as $gid) {
                        $result = $this->addAgentInGroup($gid, $agentInfo);
                        if ($result['count'] > 0) {
                            $msg = "✅ Agent {$agentId} added to group {$gid} (via agent_level)";
                            $this->info($msg);
                            Log::channel('agent_group_assignment')->info($msg);
                        }
                    }
                }

                if (!in_array('ancillary', $contractTypes) && $agentInfo->agent_level_2 > 0) {
                    $groupIds = $this->getGroupIdsByContractType('ancillary');
                    $allValidGroupIds = array_merge($allValidGroupIds, $groupIds);
                    foreach ($groupIds as $gid) {
                        $result = $this->addAgentInGroup($gid, $agentInfo);
                        if ($result['count'] > 0) {
                            $msg = "✅ Agent {$agentId} added to group {$gid} (via agent_level_1)";
                            $this->info($msg);
                            Log::channel('agent_group_assignment')->info($msg);
                        }
                    }
                }

                $allValidGroupIds = array_unique($allValidGroupIds);
                $this->deactivateUnwantedGroups($agentId, $allValidGroupIds);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $msg = "❌ Error processing agent {$agentId}: " . $e->getMessage();
                $this->error($msg);
                Log::channel('agent_group_assignment')->error($msg);
            }
        }

        $this->info("🎉 Group assignment process completed.");
        Log::channel('agent_group_assignment')->info("🎉 Group assignment process completed.");
    }

    protected function getGroupIdsByContractType($type)
    {
        if ($type === 'premier') {
            return [self::ELITE_GROUP_ID, self::PREMIER_GROUP_ID];
        } elseif ($type === 'ancillary') {
            return [self::GO_ENROLL_GROUP_ID];
        } elseif ($type === 'bexAncillary') {
            return [self::BROKER_EXCHANGE_ID];
        } elseif ($type === 'bexPremier') {
            return [self::BROKER_EXCHANGE_ID, self::PREMIER_GROUP_ID];
        } elseif ($type === 'fegliAncillary') {
            return [self::FEGLI_ID];
        } elseif ($type === 'categoryL9' || $type === 'patriotAncillary') {
            return [self::PATRIOT_GROUP_ID];
        } else {
            return [];
        }
    }

    protected function addAgentInGroup($gid, $agentInfo)
    {
        $count = 0;
        $addedGids = [];

        $groupInfo = $this->getGroup($gid);

        $data = [
            'agent_id' => $agentInfo->agent_id,
            'gid' => $groupInfo->gid,
            'ts' => time(),
            'isadmin' => $agentInfo->admin_only ?? 0,
            'lvl' => $agentInfo->agent_level,
            'ag_status' => $agentInfo->agent_status,
            'lvl2' => $agentInfo->agent_level_2,
        ];

        if (!$this->checkAgentGroupExist($groupInfo->gid, $agentInfo->agent_id)) {
            $agentInGroup = AgentGroup::create($data);
            $addedGids[] = $agentInGroup->gid;
            $count++;
        }

        return [
            'count' => $count,
            'addedGids' => $addedGids
        ];
    }

    protected function getGroup($gid)
    {
        return (object)[
            'gid' => $gid
        ];
    }

    protected function checkAgentGroupExist($gid, $agentId)
    {
        return AgentGroup::query()
            ->where([
                'agent_id' => $agentId,
                'gid' => $gid,
                'ag_status' => AgentGroup::STATUS_APPROVED
            ])
            ->exists();
    }

    protected function deactivateUnwantedGroups($agentId, array $validGroupIds)
    {
        $toDeleteGroups = array_diff(self::CONTRACT_BASED_GROUPS, $validGroupIds);
        sort($toDeleteGroups);

        if (empty($validGroupIds)) {
            $affected = AgentGroup::where('agent_id', $agentId)
                ->whereIn('gid', $toDeleteGroups)
                ->where('ag_status', 'A')
                ->delete();
        } else {
            $affected = AgentGroup::where('agent_id', $agentId)
                ->whereNotIn('gid', $validGroupIds)
                ->whereIn('gid', $toDeleteGroups)
                ->where('ag_status', 'A')
                ->delete();
        }

        if ($affected > 0) {
            $msg = "🛑 Agent {$agentId} removed from {$affected} unwanted group(s) deleted";
            $this->info($msg);
            Log::channel('agent_group_assignment')->info($msg);
        }
    }
}
