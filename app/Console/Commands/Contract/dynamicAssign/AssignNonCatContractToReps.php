<?php

namespace App\Console\Commands\Contract\dynamicAssign;

use App\AgentInfo;
use App\Helpers\AgentUpdatesHelper;
use Illuminate\Console\Command;
use App\RepContract;
use App\RepContractsDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\AgentGroup;
use App\GroupInfo;
use App\RepContractAct;


class AssignNonCatContractToReps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assignNonCatContract {--agent_id=} {--level=} {--contract_type=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assigning Non Category contract';
    protected static $array_downlines;
    protected  $GO_ENROLL_GROUP_ID = 1881; 
    protected  $PATRIOT_GROUP_ID = 4123; 
    protected  $ELITE_GROUP_ID = 1073;
    protected  $PREMIER_GROUP_ID = 1926;
    protected  $BROKER_EXCHANGE_ID  = 1399;
    protected  $FEGLI_ID  = 4054;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $agent_id = $this->option('agent_id');
        $level = $this->option('level');
        // ancillary, premier, bexPremier, bexAncillary, fegliAncillary, patriotAncillary : these are the expected contract type.
        $contract_type = $this->option('contract_type');
        $this->info("Non category contract assign to agentId ".$agent_id.' of level '.$level.' for contract '.$contract_type.' processed.');

            DB::beginTransaction();
            try {
                if($this->checkContractType($contract_type)){
                    $this->assignRepContract($agent_id, $level, $contract_type);
                    if($this->isAgentPending($agent_id)){
                        $this->activateAgent($agent_id);
                    }
                    $this->addAgentGroup($agent_id,$contract_type);
                    DB::commit();
                $this->info("script ended for agentId ".$agent_id.' of level '.$level.' for contract '.$contract_type.' with success.');
                }
                else{
                    $this->info("script ended for agentId ".$agent_id.' of level '.$level.' for contract '.$contract_type.' with Error.');
                    $this->error('Error : Inputed contract type is invalid.');
                }

            } catch (\Exception $e) {
                DB::rollBack();
                $this->info("script ended for agentId ".$agent_id.' of level '.$level.' for contract '.$contract_type.' with Error.');
                $this->error('Error :' . $e->getMessage());
            }
    }
    

    public function checkContractType($contract_type){
        return RepContract::where('contract_type',$contract_type)->where('contract_display_name','Non-Category Contract')->exists();
    }

    public function assignRepContract(int $agent_id, int $level, string $contract_type){
        $data = [
            'contract_type' => $contract_type,
            'agent_id' => $agent_id,
            'contract_display_name' => 'Non-Category Contract',
            'is_completed' => 'yes',
            'screenshot' => null,
            'clicked_at' => null,
            'signature' => null,
        ];
        $repContractSave = RepContract::create($data);


        $detailData = [
            'contract_id' => $repContractSave->contract_id,
            'contract_item_display_name' => $this->getContractDisplayName($contract_type),
            'contract_item_name' => $contract_type,
            'contract_level' =>$level,
        ];
        RepContractsDetail::create($detailData);
        $contractSentData = [
            'action' => 'contracts sent',
            'aid' => $agent_id,
            'userid' => 3248,
            'contracts' => $this->getContractAct($contract_type,$level),
            'contract_type' => 'N-CatContrs',
            'ts' => time(),
            'ipaddress' => null
        ];
        RepContractAct::query()->create($contractSentData);
        $contractSubmitData = [
            'action' => 'contract submitted',
            'aid' => $agent_id,
            'userid' => 3248,
            'contracts' => $contract_type,
            'contract_type' => null,
            'ts' => time(),
            'ipaddress' => null
        ];
        RepContractAct::query()->create($contractSubmitData);
        if ($contract_type === 'premier' || $contract_type === 'bexPremier') {
            AgentInfo::query()
                ->where('agent_id', '=', $agent_id)
                ->update([
                    'agent_level' => $level
                ]);
        } elseif ($contract_type === 'ancillary' || $contract_type === 'bexAncillary') {
            AgentInfo::query()
                ->where('agent_id', '=', $agent_id)
                ->update([
                    'agent_level_2' => $level
                ]);
        }

    }

    public function getContractDisplayName($contract_type){
        $contractNames =
        [
            'premier' => 'EWA Contract (Union Only)',
            'ancillary' => 'Smart - Ancillary Contract',
            'bexPremier' => 'BEX Premier Contract',
            'bexAncillary' => 'BEX Ancillary Contract',
            'fegliAncillary' => 'GPBP (FEGLI) Contract',
            'patriotAncillary' => 'Patriot - Ancillary Contract',
        ];

        return isset($contractNames[$contract_type]) ? $contractNames[$contract_type] : $contract_type;
        
    } 

    public function getContractAct($contract_type,$level){
        $contractAct =
        [
            'premier' => 'PEC '.$level.'-',
            'ancillary' => 'ALC '.$level.'-',
            'bexPremier' => 'BEX PREMIER '.$level.'-',
            'bexAncillary' => 'BEX ANCILLARY '.$level.'-',
            'fegliAncillary' => 'FEGLI ANCILLARY '.$level.'-',
            'patriotAncillary' => 'PATRIOT ANCILLARY '.$level.'-',
        ];

        return isset($contractAct[$contract_type]) ? $contractAct[$contract_type] : $contract_type;
    }

    
    public function activateAgent(int $agent_id){
        $agentUpdatesHelper =new AgentUpdatesHelper();
        $agentInfo = AgentInfo::where('agent_id', $agent_id)->first();
        if ($agentInfo) {
            AgentInfo::where('agent_id', $agent_id)
            ->update([
                'agent_status' => 'A',
            ]);
            $updates = [
                'agent_id' => $agent_id,
                'elgb_act' => 'LNSCHNG',
                'elgb_act_date' => Carbon::now()->timestamp,
                'elgb_comment' => 'Agent status updated. ' .
                    'Updated in agent_info table. ' .
                    'Old Values: Status-' . $agentInfo->agent_status ,
                'elgb_agent_id' => 3248,//3248
                'elgb_agent_name' => 'Sailesh'//Sailesh
            ];
            $agentUpdatesHelper->logAgentUpdates($updates);
        }
    }

    public function isAgentPending(int $agent_id){
        return AgentInfo::query()
        ->where('agent_status','P')
        ->where('agent_id', '=', $agent_id)
        ->exists();
    }

    public function addAgentGroup($agent_id , $contract_type){
        if($contract_type === 'premier'){
            $gids = [$this->ELITE_GROUP_ID,$this->PREMIER_GROUP_ID];
        }
        else if($contract_type === 'ancillary'){
            $gids = [$this->GO_ENROLL_GROUP_ID];
        }
        else if($contract_type === 'bexAncillary'){
            $gids = [$this->BROKER_EXCHANGE_ID];
        }
        else if($contract_type === 'bexPremier'){
            $gids = [$this->BROKER_EXCHANGE_ID,$this->PREMIER_GROUP_ID];
        }
        else if($contract_type === 'fegliAncillary'){
            $gids = [$this->FEGLI_ID];
        }
        else if($contract_type === 'patriotAncillary'){
            $gids = [$this->PATRIOT_GROUP_ID];
        }
        foreach($gids as $gid){
            $agentInfo = AgentInfo::find($agent_id);
            $count = $this->addAgentInGroup($gid, $agentInfo);
            if ($count['count'] > 0) {
                $successMessage = "{$count['count']} group(s) added successfully.";
                $this->createAgentUpdate($agent_id, \App\AgentUpdate::ACT_ADD_GROUP, $successMessage);
            }
        }

    }

    protected function addAgentInGroup($gid, $agentInfo)
    {
        $count = 0;
        $addedGids = [];
            $groupInfo = $this->getGroup($gid);
            $data = [
                'agent_id' => $agentInfo->agent_id,
                'gid' => $groupInfo->gid,
                'ts' => time(),
                'isadmin' => isset($agentInfo->admin_only) ? $agentInfo->admin_only : 0,
                'lvl' => $agentInfo->agent_level,
                'ag_status' => $agentInfo->agent_status,
                'lvl2' => $agentInfo->agent_level_2,
            ];
            $checkAgentInGroup = $this->checkAgentGroupExist($groupInfo->gid, $agentInfo->agent_id);
            if (!$checkAgentInGroup) {
                $agentInGroup = AgentGroup::create($data);
                array_push($addedGids, $agentInGroup->gid);
                $count++;
            }
        return [
            'count' => $count,
            'addedGids' => $addedGids
        ];
    }

    protected function createAgentUpdate($agent_id, $act = "", $comment = "")
    {
        $data = [
            "agent_id" => $agent_id,
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent_id" => 3248,
            "elgb_agent_name" => 'Sailesh',
            "changed_from" => null,
            "changed_to" => null
        ];
        AgentUpdatesHelper::createAgentUpdateLog($data);
    }

    protected function getGroup($gId)
    {
        return GroupInfo::find($gId);
    }

    protected function checkAgentGroupExist($gid, $agentId)
    {
        return AgentGroup::query()
            ->where([
                'agent_id' => $agentId,
                'gid' => $gid,
                'ag_status' => AgentGroup::STATUS_APPROVED
            ])
            ->exists();
    }

    public function getDownlines($agentID)
    {
        $array_agents = array();
        $agents = AgentInfo::where('agent_ga', '=', $agentID)->get();

        foreach ($agents as $agentIds) {
            $array_agents[] = $agentIds->agent_id;
            self::$array_downlines[] = $agentIds->agent_id;
        }
        $allDownlines = self::getallDownlines($array_agents);

        return $allDownlines;

    }

    //function to fetch all downline
    public static function getallDownlines($agentArray)
    {
        $array_agents = array();
        $temp_array = array();
        $array_agents = array_merge($array_agents, $agentArray);
        $data_agent = AgentInfo::whereIn('agent_ga', $agentArray)->get();

        foreach ($data_agent as $agentIds) {
            self::$array_downlines[] = $agentIds->agent_id;
            $temp_array[] = $agentIds->agent_id;
        }
        if (count($temp_array) > 0) {
            self::getallDownlines($temp_array);
        }
        return self::$array_downlines;
    }
}
