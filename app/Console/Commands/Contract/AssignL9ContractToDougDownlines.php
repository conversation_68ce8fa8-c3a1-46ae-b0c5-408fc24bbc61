<?php

namespace App\Console\Commands\Contract;

use App\AgentInfo;
use App\Helpers\AgentUpdatesHelper;
use Illuminate\Console\Command;
use App\RepContract;
use App\RepContractsDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\AgentGroup;
use App\GroupInfo;
use App\RepContractAct;


class AssignL9ContractToDougDownlines extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assignL9ToDougDownlinesContract:Patriot';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assigning L9 contract to Doug Downlines';
    protected static $array_downlines;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    private $dougAgentId = 638;

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("script started");
        $downlineAgents = $this->getDownlines($this->dougAgentId);
        $progressBar = $this->output->createProgressBar(count($downlineAgents));
        $progressBar->setOverwrite(false);
        $progressBar->start();
        foreach($downlineAgents as $agent_id){
            DB::beginTransaction();
            try {
                $this->info($agent_id);
                $this->assignRepContract($agent_id);
                if($this->isAgentPending($agent_id)){
                    $this->activateAgent($agent_id);
                }
                $this->addAgentGroup($agent_id);
            } catch (\Exception $e) {
                DB::rollBack();
                $this->error('Error :' . $e->getMessage());
            }
            $progressBar->advance();
            DB::commit();
        }
        $progressBar->finish();
        $this->info("script ended");
    }

    public function assignRepContract(int $agent_id){
        $data = [
            'contract_type' => 'categoryL9',
            'agent_id' => $agent_id,
            'contract_display_name' => 'Category Patriot-Ancillary Contract',
            'is_completed' => 'yes',
            'screenshot' => null,
            'clicked_at' => null,
            'signature' => null,
        ];
        $repContractSave = RepContract::create($data);
        $detailDataL9 = [
            [
                'contract_item_display_name' =>'Medical',
                'contract_item_name' => 'medical',
            ],
            [
                'contract_item_display_name' =>'Dental',
                'contract_item_name' => 'dental',
            ],
            [
                'contract_item_display_name' =>'Vision',
                'contract_item_name' => 'vision',
            ],
            [
                'contract_item_display_name' =>'Term Life',
                'contract_item_name' => 'termLife',
            ],
            [
                'contract_item_display_name' =>'Bundles',
                'contract_item_name' => 'bundled',
            ],
            [
                'contract_item_display_name' =>'Limited Medical',
                'contract_item_name' => 'limitedMedical',
            ],
            [
                'contract_item_display_name' =>'Accident',
                'contract_item_name' => 'accident',
            ],
            [
                'contract_item_display_name' =>'Critical',
                'contract_item_name' => 'critical',
            ],
            [
                'contract_item_display_name' =>'Hospital',
                'contract_item_name' => 'hospital',
            ],
            [
                'contract_item_display_name' =>'Life Style',
                'contract_item_name' => 'lifeStyle',
            ],
            [
                'contract_item_display_name' =>'Pet',
                'contract_item_name' => 'pet',
            ],
            [
                'contract_item_display_name' =>'Guaranteed Issue',
                'contract_item_name' => 'guaranteedIssue',
            ],
            [
                'contract_item_display_name' =>'Telemedicine',
                'contract_item_name' => 'teleMedicine',
            ],
        ];
        foreach($detailDataL9 as $detailData){
            RepContractsDetail::create([
                'contract_id' => $repContractSave->contract_id,
                'contract_item_display_name' => $detailData['contract_item_display_name'],
                'contract_item_name' => $detailData['contract_item_name'],
                'contract_level' => 3,
            ]);
        }
        
        $contractSentData = [
            'action' => 'contracts sent',
            'aid' => $agent_id,
            'userid' => 3248,
            'contracts' => 'MEDICAL 3-DENTAL 3-VISION 3-TERMLIFE 3-BUNDLED 3-LIMITEDMEDICAL 3-ACCIDENT 3-CRITICAL 3-HOSPITAL 3-LIFESTYLE 3-PET 3-GUARANTEEDISSUE 3-TELEMEDICINE 3-',
            'contract_type' => 'catContrs',
            'ts' => time(),
            'ipaddress' => null
        ];
        RepContractAct::query()->create($contractSentData);
        $contractSubmitData = [
            'action' => 'contract submitted',
            'aid' => $agent_id,
            'userid' => 3248,
            'contracts' => 'categoryL9 -',
            'contract_type' => 'catContrs',
            'ts' => time(),
            'ipaddress' => null
        ];
        RepContractAct::query()->create($contractSubmitData);
    }

    public function activateAgent(int $agent_id){
        $agentUpdatesHelper =new AgentUpdatesHelper();
        $agentInfo = AgentInfo::where('agent_id', $agent_id)->first();
        if ($agentInfo) {
            AgentInfo::where('agent_id', $agent_id)
            ->update([
                'agent_status' => 'A',
            ]);
            $updates = [
                'agent_id' => $agent_id,
                'elgb_act' => 'LNSCHNG',
                'elgb_act_date' => Carbon::now()->timestamp,
                'elgb_comment' => 'Agent status updated. ' .
                    'Updated in agent_info table. ' .
                    'Old Values: Status-' . $agentInfo->agent_status ,
                'elgb_agent_id' => 3248,//3248
                'elgb_agent_name' => 'Sailesh'//Sailesh
            ];
            $agentUpdatesHelper->logAgentUpdates($updates);
        }
    }

    public function isAgentPending(int $agent_id){
        return AgentInfo::query()
        ->where('agent_status','P')
        ->where('agent_id', '=', $agent_id)
        ->exists();
    }

    public function addAgentGroup($agent_id){
        $agentInfo = AgentInfo::find($agent_id);
        $gid = 4123;
        $count = $this->addAgentInGroup($gid, $agentInfo);
        if ($count['count'] > 0) {
            $successMessage = "{$count['count']} group(s) added successfully.";
            $this->createAgentUpdate($agent_id, \App\AgentUpdate::ACT_ADD_GROUP, $successMessage);
        }
    }

    protected function addAgentInGroup($gid, $agentInfo)
    {
        $count = 0;
        $addedGids = [];
            $groupInfo = $this->getGroup($gid);
            $data = [
                'agent_id' => $agentInfo->agent_id,
                'gid' => $groupInfo->gid,
                'ts' => time(),
                'isadmin' => isset($agentInfo->admin_only) ? $agentInfo->admin_only : 0,
                'lvl' => $agentInfo->agent_level,
                'ag_status' => $agentInfo->agent_status,
                'lvl2' => $agentInfo->agent_level_2,
            ];
            $checkAgentInGroup = $this->checkAgentGroupExist($groupInfo->gid, $agentInfo->agent_id);
            if (!$checkAgentInGroup) {
                $agentInGroup = AgentGroup::create($data);
                array_push($addedGids, $agentInGroup->gid);
                $count++;
            }
        return [
            'count' => $count,
            'addedGids' => $addedGids
        ];
    }

    protected function createAgentUpdate($agent_id, $act = "", $comment = "")
    {
        $data = [
            "agent_id" => $agent_id,
            "elgb_act" => $act,
            "elgb_comment" => $comment,
            "elgb_act_date" => time(),
            "elgb_agent_id" => 3248,
            "elgb_agent_name" => 'Sailesh',
            "changed_from" => null,
            "changed_to" => null
        ];
        AgentUpdatesHelper::createAgentUpdateLog($data);
    }

    protected function getGroup($gId)
    {
        return GroupInfo::find($gId);
    }

    protected function checkAgentGroupExist($gid, $agentId)
    {
        return AgentGroup::query()
            ->where([
                'agent_id' => $agentId,
                'gid' => $gid
            ])
            ->exists();
    }

    public function getDownlines($agentID)
    {
        $array_agents = array();
        $agents = AgentInfo::where('agent_ga', '=', $agentID)->get();

        foreach ($agents as $agentIds) {
            $array_agents[] = $agentIds->agent_id;
            self::$array_downlines[] = $agentIds->agent_id;
        }
        $allDownlines = self::getallDownlines($array_agents);

        return $allDownlines;

    }

    //function to fetch all downline
    public static function getallDownlines($agentArray)
    {
        $array_agents = array();
        $temp_array = array();
        $array_agents = array_merge($array_agents, $agentArray);
        $data_agent = AgentInfo::whereIn('agent_ga', $agentArray)->get();

        foreach ($data_agent as $agentIds) {
            self::$array_downlines[] = $agentIds->agent_id;
            $temp_array[] = $agentIds->agent_id;
        }
        if (count($temp_array) > 0) {
            self::getallDownlines($temp_array);
        }
        return self::$array_downlines;
    }
}
