<?php

namespace App\Console\Commands;

use App\Helpers\GuzzleHelper;
use App\Policy;
use App\Repositories\MemberApproval;
use App\Repositories\Plans\PrudentialPlanRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class AutoApproveHealthPlans extends Command
{
    private $unapprovedPolicies, $approvablePolicies;
    private const LOG_CHANNEL_NAME = 'healthPlanApprovalLog';
    private $progressBar, $successCount = 0, $failureCount = 0;

    protected $signature = 'approve:health-plans';
    protected $description = 'Automatically Approve Policies with Health Plans';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * https://docs.google.com/document/d/1jIuJmhmjQ7ZwZ_eVzJlgWCgtaqo0L-BY0dxha2l_ZNA/edit?usp=sharing
     * @return void
     */
    public function handle()
    {
        Log::channel(self::LOG_CHANNEL_NAME)
            ->info(PHP_EOL . "■ ↓ ■ ↓ ■ ↓ ■ ↓ ■ ↓ ■ ↓ ■ ↓ " . PHP_EOL .
                "HEALTH POLICY AUTO APPROVAL " . date('Y-m-d') . " COMMENCE" . PHP_EOL);

        try {
            $this->fetchPolicies();

            if ($this->unapprovedPolicies->isEmpty()) {
                $this->error('No Unapproved Policies found!');
                return;
            }

            $this->filterPolicies();

            if ($this->approvablePolicies->isEmpty()) {
                $this->error('No Policies eligible for Approval found!');
                return;
            }

            $this->progressBar = $this->output->createProgressBar($this->approvablePolicies->count());
            $message = 'Eligible for approval: ' . $this->approvablePolicies->count() . ' Policies.';
            Log::channel(self::LOG_CHANNEL_NAME)->notice($message);
            $this->info($message . PHP_EOL);

            $this->approveAndNotifyPolicies();
            $this->generateInvoices();

            $this->progressBar->finish(); $this->info(PHP_EOL);
            $this->table(
                ['Total Policies', 'Approved & Notified', 'Failed'],
                [[$this->approvablePolicies->count(), $this->successCount, $this->failureCount]]
            );
            Log::channel(self::LOG_CHANNEL_NAME)
                ->notice('Total Policies: ' . $this->approvablePolicies->count() .
                    ' | Approved: ' . $this->successCount . ' | Failed: ' . $this->failureCount);
        }
        catch (Exception $exception) {
            Log::channel(self::LOG_CHANNEL_NAME)
                ->error('Exception encountered: ' . PHP_EOL . $exception->getMessage());
        }
        finally {
            Log::channel(self::LOG_CHANNEL_NAME)
                ->info(PHP_EOL . "END OF HEALTH POLICY AUTO APPROVAL FOR " . date('Y-m-d') .
                    PHP_EOL . "■ ↑ ■ ↑ ■ ↑ ■ ↑ ■ ↑ ■ ↑ ■ ↑ " . PHP_EOL);
        }
    }

    private function fetchPolicies()
    {
        $this->unapprovedPolicies = Policy::where('status', 'ACTIVE')
            ->whereDate('effective_date', '>=', '2024-01-01')
            ->whereNull('Approval')
            ->get();
    }

    private function filterPolicies()
    {
        $this->approvablePolicies = $this->unapprovedPolicies->filter(function ($eachPolicy) {
            if ($eachPolicy->hasSignature && $eachPolicy->planOverview->pluck('pid')->intersect(Policy::PRUDENTIAL_LIFE_PLANS_IDS)->isNotEmpty()) {
                return true;
            }
    
            if (!$eachPolicy->hasSignature || !$eachPolicy->hasL713Plan || $eachPolicy->hasIHAPlan) {
                return false;
            }
            foreach ($eachPolicy->planOverview as $eachPlan) {
                if ($eachPlan->cid == PrudentialPlanRepository::PLAN_CARRIER_ID) {
                    if ((int) preg_replace("/\D/", '', $eachPlan->plan_name_system) > 50) {
                        return false;
                    }
                }
            }
            return !$eachPolicy->userInfoDepMedBex->contains('selected_answer', 1);
        });
    }
    

    private function approveAndNotifyPolicies()
    {
        $memberApprovalObj = new MemberApproval();

        foreach ($this->approvablePolicies as $eachPolicy) {
            $status = $memberApprovalObj->changeMemberStatus([
                'type' => 'approve',
                'policy_id' => $eachPolicy->policy_id
            ]);

            if (isset($status['type']) && $status['type'] == 'success') {
                $eachPolicy->update(['is_auto_approved' => 1]);

                // Update AI status
                \Log::info("Updating AI status during auto-approval");
                $reqData = [
                    'policy_id' => $eachPolicy->policy_id,
                    'approved' => 1
                ];
                $request = Request::create('api/v1/update-ai-status', 'POST', $reqData);
                $response = app()->handle($request);
                $responseBody = $response->getContent();
                \Log::info("Updating AI status during auto-approval - Response: " . $responseBody);

                $this->successCount++;
                $message = 'Policy ID: ' . $eachPolicy->policy_id . ', Approved and Member notified.';
                Log::channel(self::LOG_CHANNEL_NAME)->info($message);
                $this->info(PHP_EOL . $message);
            } else {
                $this->failureCount++;
                $message = 'Policy ID: ' . $eachPolicy->policy_id . ', not Approved.' .
                    PHP_EOL . 'Reason: ' . $status['message'] ?? 'N/A';
                Log::channel(self::LOG_CHANNEL_NAME)->error($message);
                $this->error(PHP_EOL . $message);
            }

            $this->progressBar->advance();
        }
    }

    private function generateInvoices()
    {
        $url = config('app.purenroll_system.url') . "generate-single-invoice";

        foreach ($this->approvablePolicies as $eachPolicy) {
            try {
                $response = GuzzleHelper::postApi($url, [], [
                    'policy_id' => [$eachPolicy->policy_id],
                    'effective_date' => date('Y-m', strtotime($eachPolicy->effective_date))
                ]);
                $response = json_decode($response, true);

                if ($response['status'] == 'success') {
                    $message = 'Policy ID: ' . $eachPolicy->policy_id . ' | Effective Date: ' .
                        $eachPolicy->effective_date . ' Invoice Generated.';
                    Log::channel(self::LOG_CHANNEL_NAME)->info($message);
                    $this->info(PHP_EOL . $message);
                } else {
                    $message = 'Policy ID: ' . $eachPolicy->policy_id . ', Invoice Not Generated.' .
                        PHP_EOL . 'Reason: ' . $response['message'] ?? 'N/A';
                    Log::channel(self::LOG_CHANNEL_NAME)->error($message);
                    $this->error(PHP_EOL . $message);
                }
            }
            catch (Exception $exception) {
                $message = 'Policy ID: ' . $eachPolicy->policy_id . ', Invoice Not Generated.' .
                    PHP_EOL . 'Reason: ' . $exception->getMessage() ?? 'N/A';
                Log::channel(self::LOG_CHANNEL_NAME)->error($message);
                $this->error(PHP_EOL . $message);
            }
        }
    }
}
