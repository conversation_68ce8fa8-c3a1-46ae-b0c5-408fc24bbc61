<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\MemberTextMessageController;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendTextMessageMemberStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:text-message-member-statistics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send member dashboard and app login info.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        echo Carbon::now() ." SEND TEXT MESSAGE ON MEMBER STATUS ".PHP_EOL;
        $memberTextMessageController = new MemberTextMessageController();
        $memberTextMessageController->sendTextMessageMemberStatistics();
        echo Carbon::now() ."  SEND TEXT MESSAGE ON MEMBER STATUS ".PHP_EOL;
    }
}
