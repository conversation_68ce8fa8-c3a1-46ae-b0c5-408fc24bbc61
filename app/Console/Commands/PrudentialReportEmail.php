<?php

namespace App\Console\Commands;

use App\Service\Prudential\PrudentialPlanService;
use Illuminate\Console\Command;

class PrudentialReportEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:prudential-report {period}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send report email to prudential people.';
    /**
     * @var PrudentialPlanService
     */
    private $service;

    /**
     * Create a new command instance.
     *
     * @param PrudentialPlanService $service
     */
    public function __construct(PrudentialPlanService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $period = $this->argument('period');
        $result =  $this->service->report($period);
        $newPeriod = ucfirst($period);
        if ($result){
            $message = "Prudential - EWA - {$newPeriod} Report Email Sent.";
            $this->info($message);
        }else{
            $message = "Prudential - EWA - {$newPeriod} Report Failed To Sent.";
            $this->info($message);
        }
    }
}
