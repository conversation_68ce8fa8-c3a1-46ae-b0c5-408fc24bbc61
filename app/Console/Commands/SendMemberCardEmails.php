<?php

namespace App\Console\Commands;

use App\Repositories\MemberCard\MemberCardRepository;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SendMemberCardEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:member-card {--type=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $date = date('Y-m-d');
        $type = $this->option('type');
        echo Carbon::now() ." SEND MEMBER CARD - SCRIPT STARTED".PHP_EOL;
        $memberCard = new MemberCardRepository();
        $memberCard->sendMemberCardEmail($date, $type);
        echo Carbon::now() ."  SEND MEMBER CARD - SCRIPT COMPLETED".PHP_EOL;
    }
}
