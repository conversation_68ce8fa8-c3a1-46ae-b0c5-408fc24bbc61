<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\AgentEmailController;
use App\Http\Controllers\Api\V1\MemberEmailController;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendEmailMemberApproval extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:member-reminder-email {--schedule=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder email to Members to login to Mobile App and Web Dashboard.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        $schedule = $this->option('schedule');
        echo Carbon::now() ." SEND MEMBER REMINDER EMAIL START ".PHP_EOL;
        $agentEmailController = new MemberEmailController();
        $agentEmailController->sendReminderEmailMemberApproval($schedule);
        echo Carbon::now() ."  SEND MEMBER REMINDER EMAIL COMPLETED ".PHP_EOL;
    }
}
