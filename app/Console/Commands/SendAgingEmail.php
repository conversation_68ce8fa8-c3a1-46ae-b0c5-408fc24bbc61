<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\FilterUserController;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendAgingEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:aging-off-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send aging off emails.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        echo Carbon::now() ." SEND AGING OFF EMAILS ".PHP_EOL;
        $agingEmail = new FilterUserController();
        $agingEmail->agingOffDeps();
        echo Carbon::now() ."  SEND AGING OFF EMAILS ".PHP_EOL;
    }
}
