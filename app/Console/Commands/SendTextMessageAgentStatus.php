<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\AgentTextMessageController;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendTextMessageAgentStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:text-message-agent-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Text message of agent activation details will be sent';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        echo Carbon::now() ." SEND TEXT MESSAGE ON AGENT STATUS ".PHP_EOL;
        $agentTextMessageController = new AgentTextMessageController();
        $agentTextMessageController->sendScheduledDailyMessageOnAgentStatus();
        echo Carbon::now() ."  SEND TEXT MESSAGE ON AGENT STATUS ".PHP_EOL;
    }
}
