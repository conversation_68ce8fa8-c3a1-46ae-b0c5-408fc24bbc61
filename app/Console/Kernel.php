<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\SendEmailRepresentativeRegistrationAndActivation::class,
        Commands\SendEmailMemberApproval::class,
        Commands\SendAgentWeeklyReportEmail::class,
        Commands\SendTextMessageAgentStatus::class,
        Commands\SendTextMessageMemberStatistics::class,
        Commands\SendEmailPlanEffectiveDate::class,
        Commands\SendRepresentativeMonthlyReportText::class,
        Commands\SendAgingEmail::class,
        Commands\PrudentialReportEmail::class,
        Commands\ChangeAgentUpline::class,
        Commands\SendEmailToNonActiveReps::class,
        Commands\AutoApproveHealthPlans::class,
        Commands\AutoRemoveInactiveUsersFromSSO::class,
        Commands\SendMonthlyProductionReport::class,
        Commands\CheckMemberCardPaymentStatus::class,

    ];

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')
        //          ->hourly();
        $schedule->command('send:representative-reminder-email')
            ->timezone('America/New_York')
            ->dailyAt('10:00')
            ->appendOutputTo(storage_path('logs/email_reminder.log'));
        $schedule->command('send:member-reminder-email --schedule=DAILY')
            ->timezone('America/New_York')
            ->dailyAt('10:00')
            ->appendOutputTo(storage_path('logs/email_reminder_member_daily.log'));
        $schedule->command('send:member-reminder-email --schedule=HOURLY')
            ->timezone('America/New_York')
            ->hourlyAt(10)
            ->appendOutputTo(storage_path('logs/email_reminder_member_hourly.log'));
        $schedule->command('send:agent-weekly-report')
            ->timezone('America/New_York')
            ->weeklyOn(1,'10:00')
            ->appendOutputTo(storage_path('logs/email_agent_weekly_report.log'));
        // $schedule->command('send:text-message-agent-status')
        //     ->timezone('America/New_York')
        //     ->dailyAt('20:02')
        //     ->appendOutputTo(storage_path('logs/text_message_daily_report.log'));
        // $schedule->command('send:text-message-member-statistics')
        //     ->timezone('America/New_York')
        //     ->dailyAt('20:03')
        //     ->appendOutputTo(storage_path('logs/text_message_member_daily_report.log'));
        $schedule->command('send:email-plan-effective-date')
            ->timezone('America/New_York')
            ->dailyAt('10:05')
            ->appendOutputTo(storage_path('logs/email_plan_effective_notification_daily.log'));

        // $schedule->command('send:month-by-month-report-for-reps')
        // ->timezone('America/New_York')
        // ->dailyAt('20:00')
        // ->appendOutputTo(storage_path('logs/text_message_month_by_month_report_for_reps.log'));
        $schedule->command('send:aging-off-email')
            ->timezone('America/New_York')
            ->dailyAt('20:30')
            ->appendOutputTo(storage_path('logs/aging-off-email.log'));
        // ->lastDayOfMonth('20:00')
        // ->appendOutputTo(storage_path('logs/text_message_month_by_month_report_for_reps.log'));

        /**
         * Prudential Plan scheduling
         * Sending prudential plan weekly on sunday 8:00 pm.
         */
        $schedule->command('send:prudential-report weekly')
            ->timezone('America/New_York')
            ->weeklyOn(7, '20:00')
            ->appendOutputTo(storage_path('logs/prudential_plan_weekly_report_email.log'));

        /**
         * Prudential Plan scheduling
         * Sending prudential plan monthly on last day of the month at 8:00 pm.
         */
        $schedule->command('send:prudential-report monthly')
            ->timezone('America/New_York')
            ->monthlyOn(date('t'),'20:00')
            ->appendOutputTo(storage_path('logs/prudential_plan_monthly_report_email.log'));

        // Assign Scheduled Upline Agent - first day of each month @ 9 AM
        $schedule->command('update:agent-upline-assign')
            ->cron('0 9 1 * *')
            ->timezone('America/New_York');

        /**
         * member benefit email after effective date gets
         */
        // $schedule->command('email:send-benefit-email')
        //     ->timezone('America/New_York')
        //     ->dailyAt('10:05')
        //     ->appendOutputTo(storage_path('logs/email_approval_effective_date.log'));

        //send emails to reps who are not active
        $schedule->command('send:email-to-non-active-reps')
            ->timezone('America/New_York')
            ->dailyAt('9:00');

// for member card payment status check
        $schedule->command('check:member-card-payment-status')->hourly();

        // $schedule->command('remove:users')
        //     ->timezone('America/New_York')
        //     ->dailyAt('23:30');

        $schedule->command('approve:health-plans')
            ->cron('0 */3 * * *')
            ->timezone('America/New_York');

        // Send Notification on effective date to member regarding digital card
        $schedule->command('send:member-card')
        ->cron('00 3 * * *')
        ->timezone('America/New_York')
        ->appendOutputTo(storage_path('logs/email_member_digital_card.log'));

        //Send Notification on plan,tier update to member regarding digital card
        $schedule->command('send:member-card --type=updatedCard')
        ->cron('00 5 * * *')
        ->timezone('America/New_York')
        ->appendOutputTo(storage_path('logs/email_member_digital_card.log'));

        //Send Monthly Production Report
//        $schedule->command('send:send:monthly-production-email')
//        ->timezone('America/New_York')
//        ->monthlyOn(1,'8:00');

        $schedule->command('send:monthly-production-email')
        ->timezone('America/New_York')
        ->monthlyOn(1,'8:00');

        //send contract expiry email to reps
        $schedule->command('send:contract-sign-reminders')
        ->timezone('America/New_York')
        ->dailyAt('9:00');

    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
