<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserEmployer extends Model
{
    protected $guarded = ['id'];
    protected $table = 'user_employers';
    protected $primaryKey = 'id';
    public $timestamps = false;
    //status
    const STATUS_ACTIVE = 'A';
    const STATUS_RETIRE = 'R';
    const STATUS_COBRA = 'C';
    const STATUS_OTHER = 'O';

    //compensation
    const COMPENSATION_MW = 'MW';
    const COMPENSATION_GMWL10 = 'GMWL10';
    const COMPENSATION_G10L20 = 'G10L20';
    const COMPENSATION_G20 = 'G20';
    const COMPENSATION_Salary = 'Salary';

    //work hours
    const WORK_HOUR_L30 = 'Less than 30 hours per week';
    const WORK_HOUR_M30 = '30 hours per week or more';
    const WORK_HOUR_OTHER = 'Other';

    static $statuses = [
        'Active' => self::STATUS_ACTIVE,
        'Retire' => self::STATUS_RETIRE,
        'Cobra' => self::STATUS_COBRA,
        'Other' => self::STATUS_OTHER,
    ];

    static $compensationTypes = [
        'Minimum Wage' => self::COMPENSATION_MW,
        'Greater than Minimum Wage but less than $10/hour' => self::COMPENSATION_GMWL10,
        'Greater than $10/hour but less than $20/hour' => self::COMPENSATION_G10L20,
        'Greater than $20/hour' => self::COMPENSATION_G20,
        'Salary' => self::COMPENSATION_Salary,
    ];

    static $workHours=[
        self::WORK_HOUR_L30,
        self::WORK_HOUR_M30,
        self::WORK_HOUR_OTHER,
    ];

    public function userInfo()
    {
        return $this->belongsTo(UserInfo::class, 'userid', 'userid');
    }

}
