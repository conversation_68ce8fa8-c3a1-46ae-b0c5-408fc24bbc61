<?php

namespace App\Http\Middleware;

use Closure;

class formFeildParameterAdjustment
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $request->request->add([
            'a_userid' => $request->userid,
            'address1' => $request->address_address1,
            'address2' => $request->address_address2,
            'city' => $request->address_city,
            'zip' => $request->address_zip,
            'type' => $request->address_type,
            'policy_id' => $request->policyID,
            'reason' => $request->address_notes,
            'is_usps_verified' => $request->uspsValidate,
        ]);
        $removeParam = ['userid','address_address1','address_address2','address_city','address_zip','address_type','policyID','address_notes','uspsValidate'];
        foreach ($removeParam as $key => $value) {
            unset($request[$value]);
        }
        return $next($request);
    }
}
