<?php

namespace App\Http\Requests\AgentUpdates;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class UpdateAgentLevelStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agent_id' => 'required|integer|exists:agent_info,agent_id',
            'agent_gid' => 'nullable|array',
            'agent_gid.*' => 'nullable|numeric',
            'premier_level' => 'integer|bail|between:0,9',
            'ancillary_level' => 'integer|bail|between:0,9'
        ];
    }
    public function messages()
    {
        return[
            'agent_id.required' => 'Agent ID is required.',
            'agent_id.numeric' => 'Agent ID must be numeric.',
            'agent_id.exists' => 'Invalid Agent ID, Agent does not exist.',
            'agent_gid.numeric' => 'Agent Group ID must be numeric.',
            'level.required' => 'Agent Level is required.',
            'level.integer' => 'Agent Level must be an integer.',
            'level.between' => 'Agent Level must be between 0 and 9.',
            'premier_level.integer' => 'Agent Premier Level must be an integer.',
            'premier_level.between' => 'Agent Premier Level must be between 0 and 9.',
            'ancillary_level.integer' => 'Agent Ancillary Level must be an integer.',
            'ancillary_level.between' => 'Agent Ancillary Level must be between 0 and 9.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], Response::HTTP_UNPROCESSABLE_ENTITY));
    }
}
