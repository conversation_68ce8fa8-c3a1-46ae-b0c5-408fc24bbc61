<?php

namespace App\Http\Requests\AgentUpdates;

use App\AgentInfo;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateAgentStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statusApproved = AgentInfo::STATUS_APPROVED;
        $statusSuspend = AgentInfo::STATUS_SUSPENDED;
        $statusText = "{$statusApproved},{$statusSuspend}";
        return [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            "status" => "required|in:{$statusText}"
        ];
    }

    public function messages()
    {
        $statusApproved = AgentInfo::STATUS_APPROVED;
        $statusSuspend = AgentInfo::STATUS_SUSPENDED;
        return [
            'agent_id.required' => 'Agent is required.',
            'agent_id.integer' => 'Agent Id should be number.',
            'agent_id.exists' => 'Agent not found.',
            'status.required' => 'Status is required.',
            'status.in' => "Status should either be {$statusApproved} or {$statusSuspend}"
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
