<?php

namespace App\Http\Requests\AgentUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class DeleteAgentLicenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agent_id' => 'required|numeric',
            'license_id'=> 'required|numeric'
        ];
    }
    public function messages()
    {
        return [
            'agent_id.numeric' => 'Agent ID must be numeric.',
            'agent_id.required' => 'Agent ID is required.',
            'license_id.numeric' => 'License ID must be numeric.',
            'license_id.required' => 'License ID is required.'            
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
