<?php

namespace App\Http\Requests\Payment;

use App\PaymentTypeCc;
use Carbon\Carbon;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class CreateCcPaymentRequest extends FormRequest
{
    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $date = Carbon::createFromDate($this->expiryYear, $this->expiryMonth)->format('Y-m');
        $this->merge([
            'expiryYear' => $date
        ]);
    }
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function paymentTypes(){
        $paymentTypes = PaymentTypeCc::$paymentTypes;
        return implode(",",$paymentTypes);
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $currentDate = Carbon::now()->format('Y-m');
        $types = $this->paymentTypes();
        return [
            'credit_type' => 'required|in:'.$types,
            'card_num' => 'required|numeric|digits:16',
            'credit_status' => 'required',
            'userId' => 'required|integer|exists:userinfo_policy_address,userid',
            'policyId' => [
                'required', 'integer',
                Rule::exists('userinfo_policy_address','policy_id')
                    ->where(function ($query) {
                        $query->where('userid', $this->input('userId'));
                    })
            ],
            'expiryMonth' => 'required',
            'expiryYear' => 'required|date|date_format:Y-m|after_or_equal:' . $currentDate,
            'fname' => 'required',
            'lname' => 'required',
            'maddress' => 'integer',
            'credit_cvc' => 'required|numeric|digits:3',
            'sendEmail' => 'integer',
            'address1' => 'required',
            'city' => 'required',
            'zip' => 'required',
            'state' => 'required',
            'address2'=>'nullable',
            'merchant'=>'nullable',
        ];
    }

    public function messages()
    {
        $types = str_replace(",",", ",$this->paymentTypes());
        return [
            'credit_type.required' => 'Credit Type is required.',
            'credit_type.in' => "Invalid credit type. {$types} are allowed.",
            'card_num.required' => 'CC Number is required.',
            'card_num.numeric' => 'CC Number should be number.',
            'card_num.digits' => 'CC Number should have 16 digits.',
            'credit_status.required' => 'CC Status is required.',
            'userId.required' => 'User ID is required.',
            'userId.integer' => 'User ID should be in integer',
            'userId.exists' => 'Invalid User ID. User does not exist.',
            'policyId.required' => 'Policy ID is required.',
            'policyId.integer' => 'Policy ID should be in integer',
            'policyId.exists' => 'Policy does not exist or is not associated with the User ID.',
            'expiryMonth.required' => 'CC Expiry Month is required.',
            'expiryYear.required' => 'CC Expiry Year is required.',
            'fname.required' => 'First Name is required',
            'lname.required' => 'Last Name is required',
            'maddress.integer' => 'Maddress should be integer',
            'credit_cvc.required' => 'CVC is required',
            'credit_cvc.numeric' => 'CVC should be number',
            'credit_cvc.digits' => 'CVC should have 3 digits.',
            'sendEmail.integer' => 'SendEmail flag should be integer.',
            'address1.required' => 'Address1 is required.',
            'city.required' => 'City is required.',
            'zip.required' => 'Zip is required.',
            'state.required' => 'State is required.',
            'expiryYear.after_or_equal' => 'The credit expiry year must be a date after or equal to current date.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
