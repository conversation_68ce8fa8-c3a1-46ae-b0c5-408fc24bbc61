<?php

namespace App\Http\Requests\Payment;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class VerifyCCPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * @return void
     */
    public function prepareForValidation()
    {
        $this->merge([
            'merchant'  => $this->route('merchant'),
            'id'        => $this->route('id')
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'merchant'  => 'required|string|in:nuera,corenroll,extra_health',
            'id'        => 'required|integer|exists:payment_cc,cc_id'
        ];
    }

    /**
     * @return string[]
     */
    public function messages(): array
    {
        return [
            'merchant.required' => 'Merchant is required.',
            'merchant.string'   => 'Merchant must be represented in string.',
            'merchant.in'       => 'Merchant must be either `nuera`, `extra health` or `corenroll`.',
            'id.required'       => 'Credit Card ID is required.',
            'id.integer'        => 'Credit Card ID must be represented in integer.',
            'id.exists'         => 'Invalid Credit Card ID. Credit Card does not exist.'
        ];
    }

    /**
     * @param Validator $validator
     * @return mixed
     */
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_BAD_REQUEST,
                'error' => $errors,
            ]), Response::HTTP_BAD_REQUEST
        ));
    }
}
