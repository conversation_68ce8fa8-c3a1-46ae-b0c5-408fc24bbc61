<?php

namespace App\Http\Requests;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class DependentRemovalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'dependent_id' => 'required|numeric',
            'policy_id' => 'required|numeric',
            'aid' => 'required|numeric',
            'sendEmail' => 'integer'
        ];
    }

    public function messages()
    {
        return [
            'dependent_id.required' => 'Dependent Id is required.',
            'policy_id.required' => 'Policy Id is required.',
            'dependent_id.numeric' => 'Dependent Id must be numeric.',
            'policy_id.numeric' => 'Policy ID must be numeric.',
            'aid.numeric' => 'Agent ID must be numeric.',
            'aid.required' => 'Agent ID is required.',
            'sendEmail.integer' => 'SendEmail flag should be integer.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
