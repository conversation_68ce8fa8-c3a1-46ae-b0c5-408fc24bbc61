<?php

namespace App\Http\Requests;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class SaveEmailQueue extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'from_name' => 'required|string|max:100',
            'to_name' => 'required|string|max:100',
            'from_email' => 'required|email',
            'to_email' => 'required|email',
            'subject' => 'required',
            'content' => 'required',
            'first_name' => 'required',
            'weburl' => 'required',
        ];
    }
    public function messages()
    {
        return [
            'from_name.required' => 'Sender Name is required.',
            'to_name.required' => 'To Name is required.',
            'from_email.required' => 'Sender Email is required.',
            'to_email.required' => 'To Email is required.',
            'subject.required' => 'Email Subject is required.',
            'content.required' => 'Email Content is required.',
            'first_name.required' => 'First Name is required.',
            'weburl.required' => 'Submitted url is required.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
