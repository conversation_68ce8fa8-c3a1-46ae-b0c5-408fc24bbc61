<?php

namespace App\Http\Requests\GroupUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateWaiveFeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'group_id' => 'required|integer',
            'agent_id' => 'required|integer',
            'agent_name' => 'required',
            'fee_flag' => 'required|integer',
            'sendEmail' => 'integer'
        ];
    }

    public function messages()
    {
        return [
            'group_id.required' => 'Group Id is required',
            'group_id.integer' => 'Group Id should be integer.',
            'agent_id.required' => 'Agent id is required',
            'agent_name.required' => 'Agent name is required',
            'agent_id.integer' => 'Agent id should be integer.',
            'fee_flag.required' => 'Waive Flag is required',
            'fee_flag.integer' => 'Invalid waive flag should be 0 or 1.',
            'sendEmail.integer' => 'Send email param should be 0 or 1'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
