<?php

namespace App\Http\Requests;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class AddEnrollText extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'enrollment_text_head' => 'required',
            'make_required_chk' => 'required',
            'make_required_text' => 'required',
            'enrollment_text_body' => 'required',
            'details' => 'required',
        ];
    }
    public function messages()
    {
        return [
            'enrollment_text_head.required' => 'Enrollment Heading is required.',
            'make_required_chk.required' => 'Enrollment Text Condition is required.',
            'make_required_text.required' => 'Requirement Text is required.',
            'enrollment_text_body.required' => 'Enrollment Body is required.',
            'details.required' => 'Details for Enrollment Text is required.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
