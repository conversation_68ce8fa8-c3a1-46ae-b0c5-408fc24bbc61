<?php

namespace App\Http\Requests\AgentAchPayment;

use App\AgentAchPaymentBankDetail;
use App\Rules\RoutingNumberBankNameRule;
use App\Rules\RoutingNumberRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AddAchPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $checking = AgentAchPaymentBankDetail::AGENT_ACH_ACCOUNT_TYPE_CHECKING;
        $saving = AgentAchPaymentBankDetail::AGENT_ACH_ACCOUNT_TYPE_SAVING;
        $individual = AgentAchPaymentBankDetail::AGENT_ACH_ACCOUNT_HOLDER_TYPE_INDIVIDUAL;
        $company = AgentAchPaymentBankDetail::AGENT_ACH_ACCOUNT_HOLDER_TYPE_COMPANY;
        $typeText = "{$saving},{$checking}";
        $holderText = "{$individual},{$company}";
        $routingNumber = Request::get('routing');
        $validation = Request::get('validation') == 1;
        $rules = [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            "payee_name" => "required",
            "bank_name" => ['required'],
            "routing" => ['required','numeric','bail','digits:9'],
            "account" => ['required', 'numeric', 'bail', 'digits_between:4,17', 'not_regex:/^([0-9])\1{3,16}$/i'],
            "is_primary" => "nullable|integer|in:0,1",
            "account_type" => "required|in:" . $typeText,
            "account_holder_type" => "required|in:" . $holderText,
            "send_email" => "nullable|integer|in:0,1",
            "loginUserId" => "nullable",
            "loginUserName" => "nullable"
        ];
        if ($validation) {
            $rules['bank_name'] = ['required',new RoutingNumberBankNameRule($routingNumber)];
            $rules['routing'] = ['required','numeric','bail','digits:9',new RoutingNumberRule()];
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'agent_id.required' => 'Agent ID is required.',
            'agent_id.integer' => 'Agent ID must be an integer.',
            'agent_id.exists' => 'Agent ID not found.',
            'payee_name.required' => 'Payee name is required.',
            'bank_name.required' => 'Bank name is required.',
            'routing.required' => 'Routing number is required.',
            'routing.numeric' => 'Routing number should be in number.',
            'routing.digits' => 'Routing number should be of 9 digits.',
            'account.required' => 'Bank Account Number is required.',
            'account.numeric' => 'Bank Account Number should be in number.',
            'account.digits_between' => 'Bank Account Number must have 4 to 17 digits.',
            'account.not_regex' => 'Invalid Bank Account Number format.',
            'is_primary.integer' => 'Is Primary must be an integer.',
            'is_primary.in' => 'Is Primary must be either 0(No) or 1(Yes).',
            'account_type.required' => 'Account type is required.',
            'account_type.in' => 'Account type should be either checking or savings.',
            'account_holder_type.required' => 'Account holder type is required.',
            'account_holder_type.in' => 'Account holder type should be either individual or company.',
            'send_email.integer' => 'Send email must be an integer.',
            'send_email.in' => 'Send email must be either 0(No) or 1(Yes).'
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
