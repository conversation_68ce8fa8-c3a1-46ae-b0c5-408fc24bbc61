<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class ValidateMemberIdRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'member_id' => [
                'required',
                'integer',
                Rule::exists('userinfo', 'userid')
            ]
        ];
    }

    public function messages()
    {
        return [
            'member_id.required' => 'The member ID is required.',
            'member_id.integer' => 'The member ID must be an integer.',
            'member_id.exists' => 'The member ID does not exist in the userinfo table.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json([
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
            'errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
