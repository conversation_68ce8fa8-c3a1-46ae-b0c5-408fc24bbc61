<?php

namespace App\Http\Requests;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class DefaultAndRecurringRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => 'required',
            'recurring'=> 'integer',
            'policy_id' => 'required|integer',
            'payid' => 'required|integer',
            'user_id' => 'required|integer',
            'aid'=> 'required',
        ];
    }
    public function messages()
    {
        return [
            'type.required' => 'Payment Type  is required.',
            'recurring.integer' => 'Recurring flag need to be in number',
            'policy_id.required' => 'Policy ID is required.',
            'payid.required' => 'Pay ID is required.',
            'user_id.required' => 'User ID is required.',
            'user_id.integer' => 'User ID should be number.',
            'payid.integer' => 'Pay ID should be number.',
            'policy_id.integer' => 'Policy Number should be number.',
            'aid.integer' => 'Agent ID should be number.',
            'aid.required' => 'Agent ID is required.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
