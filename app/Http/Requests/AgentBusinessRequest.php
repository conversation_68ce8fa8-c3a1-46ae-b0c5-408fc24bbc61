<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class AgentBusinessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'business_name' => 'required',
            'business_agent_id' => 'required',
            'business_tax' => 'required | digits:9 | gt:0',
            'cover_img' => 'nullable|mimes:jpg,jpeg,JPEG,PNG,JPG,Jpeg,Jpg,Png,png|bail|max:20480',
            'biz_logo' => 'nullable|mimes:jpg,jpeg,JPEG,PNG,JPG,Jpeg,Jpg,Png,png|bail|max:20480',
        ];
    }
    public function messages()
    {
        return [
            'business_name.required' => 'Business Name is required.',
            'business_agent_id.required' => 'Agent Id is required.',
            'business_tax.required' => 'Business Tax Id is required.',
            'business_tax.digits' => 'Business Tax Id must have 9 digits.',
            'business_tax.gt' => 'Business Tax Id cannot be zero.',
            'cover_img.mimes' => 'Cover Image must be type of .jpeg,.jpg or .png format.',
            'cover_img.max' => 'Cover Image must be less than 20 MB.',
            'biz_logo.mimes' => 'Business Logo must be type of .jpeg,.jpg or .png format.',
            'biz_logo.max' => 'Business Logo must be less than 20 MB.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}