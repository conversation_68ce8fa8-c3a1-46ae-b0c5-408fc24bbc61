<?php

namespace App\Http\Requests;

use App\PolicyNote;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class NotesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function noteStatus(){
        $statuses = array_values(PolicyNote::$statuses);
        return implode(',',$statuses);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $policy_id = $this->request->get('policy_id');
        $existing_notes = PolicyNote::where('policy_id', $policy_id)->pluck('anID');

        $statuses = $this->noteStatus();
        return [
            'policy_id' => 'required|integer|exists:policies,policy_id',
            'aid' => ['nullable', 'exists:policy_notes,anID', 'bail', Rule::in($existing_notes)],
            'note' => 'required|string',
            'status' => "required|integer|in:{$statuses}"
        ];
    }
    public function messages()
    {
        $statuses = $this->noteStatus();
        return [
            'policy_id.required' => 'Policy ID is required.',
            'policy_id.integer' => 'Policy ID should be an integer.',
            'policy_id.exists' => 'Invalid Policy ID. Policy does not exist.',
            'aid.exists' => 'Invalid Client Note ID. Note does not exist.',
            'aid.in' => 'This Note ID does not belong to this Policy.',
            'note.required' => 'Note is required.',
            'note.string' => 'Note value must be a string.',
            'status.required' => 'Status ID is required.',
            'status.integer' => 'Status ID must be an integer.',
            'status.in' => "Invalid status. Only {$statuses} are allowed.",
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
