<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class PrudentialClaimCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "policy_id" => ["required"],
            'plan_id' => ["required"],
            "description" => ["nullable"],
            "files" => ["nullable", "array"],
            "files.*" => ["file", "max:10240"],
            "claim_type" => ["required", "string", "bail", "in:ADMIN"]
        ];
    }

    public function messages()
    {
        return [
            'policy_id.required' => 'Policy ID is required.',
            'plan_id.required' => 'Plan ID is required.',
            "files.required" => "File is required.",
            "files.*.file" => "File must be file.",
            "files.*.max" => "Please upload file smaller than 10 MB.", 
            "claim_type.required" => "Claim type is required.",
            "claim_type.in" => "Claim type must ADMIN."

        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json([
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
            'errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
