<?php

namespace App\Http\Requests\Plan;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class ActivePlanTermedRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'planPolicyId' => 'required|numeric',
            'termDate' => 'required|date',
            'reason'=>'required',
            'notes'=>'nullable',
            'loginUserId'=>'required',
            'newPlanId'=>'nullable',
            'newEffDate'=>'nullable',
            'newplanPricingId'=>'nullable'

        ];
    }

    public function messages()
    {
        return [
            'planPolicyId.required' => 'Plan Policy Id is required.',
            'planPolicyId.numeric' => 'Plan Policy must be numeric.',
            'termDate.required' => 'Termination Date is required.',
            'reason.required' => 'Reason is required.',
            'loginUserId.required'=>'Logged In User is required.'
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
