<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class ValidateGroupIdRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'group_id' => [
                'required',
                'integer',
                Rule::exists('group_info', 'gid')
            ]
        ];
    }

    public function messages()
    {
        return [
            'group_id.required' => 'The group ID is required.',
            'group_id.integer' => 'The group ID must be an integer.',
            'group_id.exists' => 'The group ID does not exist in the group_info table.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json([
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
            'errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
