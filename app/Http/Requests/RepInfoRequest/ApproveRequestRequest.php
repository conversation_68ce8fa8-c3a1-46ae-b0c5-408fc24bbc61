<?php

namespace App\Http\Requests\RepInfoRequest;

use App\RepInfoRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class ApproveRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "id" => "required|exists:mysql2.rep_info_requests,id",
            "status" => "required|in." . RepInfoRequest::STATUS_PENDING,
        ];
    }

    public function messages()
    {
        return [
            "id.required" => "Information Request is required.",
            'id.exists' => "Information Request doesn't exists.",
            'status.required' => "Status is required.",
            'status.in' => "Status must be " . RepInfoRequest::STATUS_PENDING . " .",
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
