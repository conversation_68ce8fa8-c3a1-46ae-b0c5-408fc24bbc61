<?php

namespace App\Http\Requests\Group;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class GroupCodeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        if (request()->isMethod('GET'))
            $this->merge([
                'group_code' => $this->route('groupCode')
            ]);
    }

    public function rules(): array
    {
        $rules = [
            'group_id'      => 'required|integer|bail|exists:App\GroupInfo,gid',
            'group_code'    => "required|bail|string|size:7|regex:/[A-Z]{3}\d{4}/i|unique:App\GroupInfo,gcode,{$this->get('group_id')},gid"
        ];

        if (request()->isMethod('GET'))
            unset($rules['group_id']);

        return $rules;
    }

    public function messages(): array
    {
        return [
            'group_id.required'     => 'Group ID is required.',
            'group_id.integer'      => 'Group ID must be represented in integer.',
            'group_id.exists'       => 'Invalid Group ID, does not exist.',
            'group_code.required'   => 'Group Code is required.',
            'group_code.unique'     => 'Group Code is in use. Group Code must be unique.',
            'group_code.size'       => 'Group Code must be 7 digits.',
            'group_code.regex'      => 'Invalid pattern. Group Code must 3 letters followed by 4 numbers.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
