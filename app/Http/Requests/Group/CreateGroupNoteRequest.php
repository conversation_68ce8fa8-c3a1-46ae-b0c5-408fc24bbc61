<?php

namespace App\Http\Requests\Group;

use App\PolicyNote;
use App\Rules\GroupNoteValidateFile;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class CreateGroupNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function prepareForValidation()
    {
        if ( request()->isMethod('DELETE') )
            $this->merge(['noteId' => $this->route('noteId')]);
    }

    public function noteStatus(){
        $statuses = array_values(PolicyNote::$statuses);
        return implode(',',$statuses);
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ( request()->isMethod('DELETE') )
        {
            return [ 'noteId' => 'required|integer|bail|exists:policy_notes,anID'];
        }

        $statuses = $this->noteStatus();
        return [
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "note" => "required",
            "status" => "required|in:{$statuses}",
            "file" =>["nullable","max:4096",new GroupNoteValidateFile()],
            "loginUserId" => "nullable",
            "loginUserName" => "nullable",
        ];
    }

    public function messages()
    {
        $statuses = $this->noteStatus();
        return [
            'group_id.required' => 'Group is required.',
            'group_id.integer' => 'Group Id should be number.',
            'group_id.exists' => 'Group not found.',
            'note.required'=>'Note is required.',
            'status.required'=>'Note is required.',
            'status.in'=>"Invalid status. Only {$statuses} are allowed",
            'file.max'=>'File too large. Max allowed is 4 mb.',
            'noteId.required' => 'Note Id is required.',
            'noteId.integer' => 'Note Id should be a numeric value',
            'noteId.exists' => 'Invalid Note Id. Note does not exist.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
