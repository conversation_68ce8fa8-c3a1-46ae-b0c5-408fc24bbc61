<?php

namespace App\Http\Requests\Group;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class AssocFeeWaiverRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'schedule_date' => date('Y-m-t', strtotime($this->schedule_month))
        ]);
    }

    public function rules(): array
    {
        return [
            'group_id' => 'required|integer|bail|exists:group_info,gid',
            'schedule_month' => 'required|date|bail|date_format:Y-m',
            'schedule_date' => 'after:today'
        ];
    }

    public function messages(): array
    {
        return [
            'group_id.required' => 'Group ID is required.',
            'group_id.integer' => 'Group ID must be represented in integer.',
            'group_id.exists' => 'Invalid Group ID. Group ID does not exist.',
            'schedule_month.required' => 'Schedule Date is required.',
            'schedule_month.date' => 'Schedule Date must be a date value.',
            'schedule_month.date_format' => "Schedule Date must be in 'YYYY-MM' format",
            'schedule_date.after' => 'Schedule Date cannot be the equal to today.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
                'message' => 'Validation failed.'
            ]), Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
