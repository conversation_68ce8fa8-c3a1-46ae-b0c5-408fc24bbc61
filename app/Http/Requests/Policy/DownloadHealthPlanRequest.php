<?php

namespace App\Http\Requests\Policy;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class DownloadHealthPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'effective_date' => 'required',
            'from_edate' => 'nullable|date_format:Y-m-d',
            'to_edate' => 'nullable|date_format:Y-m-d',
        ];
    }


    public function messages()
    {
        return [
            'effective_date.required' => 'Effective Date is required.',
            'from_edate.date_format' => 'From Enrollment Date must be in Y-m-d format.',
            'to_edate.date_format' => 'From Enrollment Date must must be in Y-m-d format.',
//            'from_edate.before' => 'From Enrollment Date must have less date than To Enrollment Date',
//            'to_edate.after' => 'From Enrollment Date must have greater date than To Enrollment Date',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
