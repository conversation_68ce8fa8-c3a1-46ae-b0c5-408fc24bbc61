<?php

namespace App\Http\Requests\Policy;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class ChangeEffectiveDateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'policyID' => 'required|integer',
            'new_effective_date' => 'required|date_format:Y-m-01',
            'plans.*.primaryID' =>'required|distinct|integer',
            'plans.*.effective_date' =>'required|date_format:Y-m-d|after_or_equal:new_effective_date',
            'sendEmail' => 'required|boolean',
            'loginUserId' => 'nullable'
        ];
    }

    public function messages()
    {
        return [
            'policyID.required' => 'Policy ID is required.',
            'policyID.integer' => 'Policy ID should be integer.',
            'plans.*.primaryID.required' =>'Plan ID is required.',
            'plans.*.primaryID.distinct' =>'Plan ID should be unique.',
            'plans.*.primaryID.integer' =>'Plan ID should be integer.',
            'new_effective_date.required' => 'New policy effective date is required.',
            'new_effective_date.date_format' => 'Policy date format should be YYYY-MM-01.',
            'plans.*.effective_date.required' =>'New plan effective date is required.',
            'plans.*.effective_date.date_format' => 'Plan date format should be YYYY-MM-DD.',
            'plans.*.effective_date.after_or_equal' => 'Plan date should be equal to or greater than new policy date.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
