<?php

namespace App\Http\Requests\Policy;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class GetHealthQuestionsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'policy_id' => 'required|numeric|bail|exists:policies,policy_id',
            'dep_id'    => 'nullable|numeric|bail|exists:dependents,did'
        ];
    }

    public function messages()
    {
        return [
            'policy_id.required' => 'Policy ID is required.',
            'policy_id.numeric' => 'Policy ID must be numeric value.',
            "policy_id.exists" => "Policy ID does not exist.",
            'dep_id.numeric' => 'Dependent ID must be numeric value.',
            "dep_id.exists" => "Dependent ID does not exist.",
        ];
    }

    public function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
