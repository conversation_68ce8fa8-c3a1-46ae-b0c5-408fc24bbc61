<?php

namespace App\Http\Requests\Policy;

use App\Rules\ValidateSSN;
use App\Service\DependentInfoService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class UpdateDependentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $policy_id = $this->request->get('policy_id');
        $depService = new DependentInfoService();
        $planPricingIds = $depService->getPlanPricingIdsFromPlanOverview($policy_id);
        $checkHeight = $depService->checkRequiredColumn('heightrq', $planPricingIds);
        $checkWeight = $depService->checkRequiredColumn('weightrq', $planPricingIds);
        return [
            'dep_id' => 'required|numeric|bail|exists:dependents,did',
            'policy_id' => 'required|numeric|bail|exists:policies,policy_id',
            'd_fname' => 'required',
            'd_mname' => 'nullable',
            'd_lname' => 'required',
            'd_relate' => 'required|in:C,S',
            'd_gender' => 'required',
            "d_dob"=> "required|date_format:m/d/Y",
            "d_notes"=>"nullable",
            "dhrq"=>[$checkHeight ? "required" :"nullable","numeric"],
            "dhrq2"=>[$checkHeight ? "required" :"nullable","numeric"],
            "dwrq"=>[$checkWeight ? "required" :"nullable","numeric"],
            "login_user_id"=>"nullable",
            "healthQuestionnaires"=>"nullable",
            "additional_notes"=>"nullable",
            "d_ssn" => ['required',new ValidateSSN()],
        ];
    }

    public function messages()
    {
        return [
            'dep_id.numeric' => 'Dependent ID must be numeric.',
            'dep_id.required' => 'Dependent ID is required.',
            "dep_id.exists" => "Dependent not found.",
            'policy_id.numeric' => 'Policy ID must be numeric.',
            'policy_id.required' => 'Policy ID is required.',
            "policy_id.exists" => "Policy not found.",
            'd_fname.required' => 'First name is required.',
            'd_lname.required' => 'Last name is required.',
            'd_ssn.required' => 'SSN is required.',
            'd_relate.required' => 'Relation is required.',
            'd_gender.required' => 'Gender is required.',
            'd_notes.required'=>'Notes is required.',
            "d.dob.required"=>"Date of birth is required",
            "d.dob.date_format"=>"Date of birth must be in format m/d/Y.",
            "dhrq"=>"Height(feet) is required.",
            "dhrq2"=>"Height(inch) is required.",
            "dwrq"=>"Weight is required",
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
