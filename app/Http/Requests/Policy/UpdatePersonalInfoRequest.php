<?php

namespace App\Http\Requests\Policy;

use App\Rules\ValidateSSN;
use App\Service\PlanAgeService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use App\Rules\NeverBounceRule;

class UpdatePersonalInfoRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $policy_id = $this->request->get('policy_id');
        $planService = new PlanAgeService();
        $planPricingIds = $planService->getPlanPricingIdsFromPlanOverview($policy_id);
        $checkHeight = $planService->checkColumn('heightrq', $planPricingIds);
        $checkHeight2 = $planService->checkColumn('heightrq2', $planPricingIds);
        $checkWeight = $planService->checkColumn('weightrq', $planPricingIds);
        return [
            'cfname' => 'required',
            'cmname' => 'nullable',
            'clname' => 'required',
            'cemail_alt' => ['nullable', 'email', new NeverBounceRule()],
            'cgender' => 'required|in:1,0',
            'cssn' => $this->input('group') !== 'ExtraHealth'
                ? ['required', new ValidateSSN(), 'digits:9']
                : ['nullable'],
            'policy_id' => 'required|numeric|bail|exists:policies,policy_id',
            'login_user_id' => 'nullable',
            'cdob' => 'date|date_format:Y-m-d|bail|before:today',
            'send_email' => 'boolean',
            "height_feet" => [$checkHeight ? "required" : "nullable", "numeric", "gt:0"],
            "height_inches" => [$checkHeight2 ? "required" : "nullable", "numeric", "gte:0", "lte:12"],
            "weight_lbs" => [$checkWeight ? "required" : "nullable", "numeric", "gt:0"],
            "pic_reason" => "nullable",
            "group" => "string"
        ];
    }

    public function messages()
    {
        return [
            'policy_id.required' => 'Policy Id is required.',
            'policy_id.numeric' => 'Policy Id must be numeric.',
            "policy_id.exists" => "Policy not found.",
            'cdob.date' => 'Date of Birth should be in the format(mm/dd/yyyy)',
            'cdob.before' => 'Date of Birth can\'t be in the future.',
            'cfname.required' => 'First Name is required.',
            'cmname.required' => 'Middle Name is required.',
            'cmname.regex' => 'Middle Name should be a single Alphabet.',
            'clname.required' => 'Last Name is required.',
            'cgender.required' => 'Gender is required.',
            'cssn.required' => 'SSN number is required.',
            'cssn.digits' => 'SSN number should be 9 digits.',
            'send_email.boolean' => 'SendEmail flag should be True or False.',
            "height_feet.required"=>"Height(feet) is required.",
            "height_inches.required"=>"Height(inch) is required.",
            "weight_lbs.required"=>"Weight is required",
            'cfname.alpha' => 'First Name contain only letters.',
            'clname.alpha' => 'Last Name contain only letters.',
            'cmname.alpha' => 'Middle Name contain only letters.',
            "height_feet.gt"=>"Height(feet) must be greater than 0.",
            "height_inches.gte"=>"Height(inch) must be greater than or equal 0.",
            "weight_lbs.gt"=>"Weight must be greater than 0.",
            "cdob.date_format"=>"Date of birth must be in format Y-m-d.",
            "cgender.in"=>"Gender value must be either 1(Male) or 0(Female).",
            "height_inches.lte"=>"Height(inch) must be less than or equal 12.",
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
