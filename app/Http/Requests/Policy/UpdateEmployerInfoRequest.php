<?php

namespace App\Http\Requests\Policy;

use App\GroupIndustry;
use App\PlanOverview;
use App\Rules\NumVerifyRule;
use App\Service\PlanAgeService;
use App\UserEmployer;
use App\UserInfo;
use http\Env\Request;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class UpdateEmployerInfoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function compensationTypes()
    {
        return implode(',', array_values(UserEmployer::$compensationTypes));
    }

    protected function employerStatus()
    {
        return implode(',', array_values(UserEmployer::$statuses));
    }

    protected function workHours()
    {
        return implode(',', UserEmployer::$workHours);
    }

    protected function vacPolicies(){
        return implode(',',UserInfo::$vacPolicies);
    }

    protected function bargUnits()
    {
        return implode(',', array_values(UserInfo::$bargUnits));
    }

    protected function groupIndustries()
    {
        $groupIndustries = GroupIndustry::query()
            ->where('status', true)
            ->orderBy('title')
            ->pluck('title')
            ->toArray();
        return implode(',', $groupIndustries);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $policyId = $this->request->get('policy_id');
        $id = $this->request->get('id');
        $planService = new PlanAgeService();
        $planPricingIds = $planService->getPlanPricingIdsFromPlanOverview($policyId);
        $checkEmpStatus = $planService->checkColumn('emp_status', $planPricingIds);
        $checkOccupation = $planService->checkColumn('rq_occupation', $planPricingIds);
        $checkStartDate = $planService->checkColumn('rq_employ_start_date', $planPricingIds);
        $checkAverageWorkHours = $planService->checkColumn('emp_hours', $planPricingIds);
        $checkNAWU = $planService->checkNAWUPlan($policyId);
        return [
            'id'=>'required|exists:user_employers,id',
            'name' => 'required',
            'policy_id' => 'required|numeric|bail|exists:policies,policy_id',
            'phone' => ['required', 'size:10'],
            'address1' => 'required',
            'address2' => 'nullable',
            'city' => 'required',
            'zip' => 'required|size:5',
            'state' => 'required',
            'compensation' => ["required", "in:{$this->compensationTypes()}"],
            'with_validation' => ["required","in:1,0"],
            "status" => [$checkEmpStatus ? "required" : "nullable", "in:{$this->employerStatus()}"],
            "occupation" => [$checkOccupation ? "required" : "nullable"],
            "start_date" => [$checkStartDate ? "required" : "nullable", "date_format:m/d/Y"],
            "hours" => [$checkAverageWorkHours ? "required" : "nullable", "in:{$this->workHours()}"],
            "emp_industry" => [$checkNAWU ? "required" : "nullable","in:{$this->groupIndustries()}"],
            "emp_num_employed" => [$checkNAWU ? "required" : "nullable","bail","numeric"],
            "emp_vac_policy" => [$checkNAWU ? "required" : "nullable","in:{$this->vacPolicies()}"],
            "emp_barg_unit" => [$checkNAWU ? "required" : "nullable","in:{$this->bargUnits()}"],
            "emp_vac_policy_other" => ["nullable"], //will set values from repo
            "login_user_id"=>["nullable"]
        ];
    }

    public function messages()
    {
        return [
            'id.numeric' => 'Employer ID must be numeric.',
            'id.required' => 'Employer ID is required.',
            "id.exists" => "Employer not found.",
            'policy_id.numeric' => 'Policy ID must be numeric.',
            'policy_id.required' => 'Policy ID is required.',
            "policy_id.exists" => "Policy not found.",
            'state.exists' => 'Invalid state name.',
            'address1.required' => 'Primary address is required.',
            'city.required' => 'City is required.',
            'zip.required' => 'Zip is required.',
            'zip.size' => 'Zip must be 5 digits.',
            'status.required' => 'Employment status is required.',
            'occupation.required' => 'Occupation is required.',
            'compensation.in' => "Compensation type must be {$this->compensationTypes()}",
            'status.in' => "Employer status must be {$this->compensationTypes()}",
            'hours.required' => 'Average work week is required',
            'hours.in' => "Average work week must be {$this->workHours()}",
            'emp_industry.required' => "Employer industry is required.",
            'emp_industry.in' => "Invalid employer industry.",
            'emp_num_employed.required' => "No of employees is required.",
            'emp_vac_policy.required' => "Employment vacation policy is required.",
            'emp_vac_policy.in' => "Employment vacation policy must be {$this->vacPolicies()}.",
            'emp_barg_unit.required' => "Bargaining unit is required.",
            'emp_barg_unit.in' => "Bargaining unit must be {$this->bargUnits()}.",
            'start_date.required'=>'Start date is required.',
            'start_date.date_format'=>'Start date does not match the format m/d/Y.',
            'emp_num_employed.numeric'=>'No of employees must be numeric.',
            'name.required' => 'Name is required.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
