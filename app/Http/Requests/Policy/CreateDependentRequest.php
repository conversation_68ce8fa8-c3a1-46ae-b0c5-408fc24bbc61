<?php

namespace App\Http\Requests\Policy;

use App\Rules\ValidateSSN;
use App\Service\DependentInfoService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class CreateDependentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function prepareForValidation()
    {
        if ($this->input('is_legally_disabled') == 0) {
            $this->merge([
                'disability_condition' => null,
            ]);
        }
    }

    /**
     * @return array
     */
    public function rules()
    {
        $policy_id = $this->request->get('policy_id');
        $dep_id = $this->request->get('did');
        if(!empty($policy_id)){
            $depService = new DependentInfoService();
            $planPricingIds = $depService->getPlanPricingIdsFromPlanOverview($policy_id);
            $existingDependentIds = $depService->getDependentsOfPolicy($policy_id)->pluck('dependent_id');
            $checkHeight = $depService->checkRequiredColumn('heightrq', $planPricingIds) && !$depService->checkIfPolicyHasExtraHealth($policy_id);
            $checkWeight = $depService->checkRequiredColumn('weightrq', $planPricingIds) && !$depService->checkIfPolicyHasExtraHealth($policy_id);
            $checkHealthQ = $depService->checkHealthQuestionsRequired($planPricingIds) && !$depService->checkIfPolicyHasExtraHealth($policy_id);

            // check if plan and dep has healthQuestion configured in database
            $planHasHealthQuesConfigured = $depService->checkHealthQuestionConfiguredForPolicyDep($policy_id, 'policy');
            if (!empty($dep_id)) {
                $depHasHealthQuesConfigured = $depService->checkHealthQuestionConfiguredForPolicyDep($dep_id, 'dependent');
            }

            if ( request()->isMethod('POST') || request()->isMethod('PUT') ) {
                $rules = [
                    'policy_id' => 'numeric|bail|exists:policies,policy_id',
                    'd_fname' => 'required|string',
                    'd_mname' => 'nullable',
                    'd_lname' => 'required|string',
                    'd_relate' => 'required|in:C,S',
                    'd_gender' => 'required|in:0,1',
                    'is_legally_disabled' => 'required|in:0,1',
                    'disability_condition' => 'required_if:is_legally_disabled,1|nullable|string',
                    "d_dob"=> "required|date_format:m/d/Y",
                    "d_notes"=>"nullable",
                    "login_user_id"=>"nullable",
                    'd_ssn' => !$depService->checkIfPolicyHasExtraHealth($policy_id)
                        ? [
                            'required',
                            Rule::notIn($depService->getDependentsOfPolicy($policy_id)->pluck('d_ssn')),
                            new ValidateSSN(),
                        ]
                        : ['nullable'],
                    'dhrq' => $checkHeight ? 'required|numeric' : 'nullable',
                    'dhrq2' => $checkHeight ? 'required|numeric|between:0,11' : 'nullable',
                    'dwrq' => $checkWeight ? 'required|numeric' : 'nullable',
                    'd_option' => 'required|in:instant,later', // Add a rule for d_option

                    'new_effective_date' => function ($attribute, $value, $fail) {
                        // Access the value of d_option from the request
                        $dOption = request('d_option');

                        // If d_option is 'later', validate that new_effective_date is required
                        if ($dOption === 'later' && empty($value)) {
                            $fail('The new effective date is required when selecting later options.     ');
                        }
                    },
                    "comments"=>"nullable",
                    // "group"=>"string"
                ];
                if (request('d_option') === 'instant') {
                    // Add rules for the 'instant' option
                    $rules = array_merge($rules, [
                        'new_effective_date' => 'nullable', // add other rules as needed
                    ]);
                }
            } elseif ( request()->isMethod('DELETE') ) {
                $rules = [
                    'policy_id' => 'numeric|bail|exists:policies,policy_id',
                    'login_user_id' => 'nullable',
                    'reason' => 'required|string|nullable',
                    'd_option' => 'nullable', // Add a rule for d_option
                ];
            }
            if ( request()->isMethod('PUT') || request()->isMethod('DELETE')) {
                $rules = array_merge($rules, [
                    'did' => [
                        'required', 'numeric', 'bail', 'exists:dependents,did', Rule::in($existingDependentIds)],
                    'd_option' => 'nullable', // Add a rule for d_option
                ]);
            }
            if (request()->isMethod('PUT')) {
                $rules = array_merge($rules, [
                    'd_ssn' => !$depService->checkIfPolicyHasExtraHealth($policy_id)
                        ? [
                            'required',
                            function ($attribute, $value, $fail) use ($depService, $policy_id) {
                                $did = request()->input('did');
                                if ($depService->checkIfSSNExist($policy_id, $value, $did)) {
                                    $fail('The SSN already exists in another policy.');
                                }
                            },
                            new ValidateSSN(),
                        ]
                        : ['nullable']
                ]);
            }
            if ( request()->isMethod('POST') ) {
                $rules = array_merge($rules, [
                    'd_ssn' => !$depService->checkIfPolicyHasExtraHealth($policy_id)
                        ? [
                            'required',
                            function ($attribute, $value, $fail) use ($depService, $policy_id) {
                                if ($depService->checkIfSSNExist($policy_id, $value, null)) {
                                    $fail('The SSN already exists in another policy.');
                                }
                            },
                            new ValidateSSN(),
                        ]
                        : ['nullable']
                ]);
            }
            if( (request()->isMethod('POST') && $checkHealthQ && $planHasHealthQuesConfigured && !$depService->checkIfPolicyHasExtraHealth($policy_id)) ||
                (request()->isMethod('PUT') && $checkHealthQ && $depHasHealthQuesConfigured && !$depService->checkIfPolicyHasExtraHealth($policy_id))
            ) {
                $rules = array_merge($rules, [
                    'healthQuestionnaires' => 'required|array',
                    'health_notes' => 'nullable|string'
                ]);
                if (! empty( $this->request->get('healthQuestionnaires') ) ) {
                    foreach ($this->request->get('healthQuestionnaires') as $key => $healthQNA) {
                        $rules['healthQuestionnaires.*.med_id'] = 'required|numeric';
                        $rules['healthQuestionnaires.*.question_id'] = 'required|numeric';
                        $rules['healthQuestionnaires.*.question'] = 'required';
                        $rules['healthQuestionnaires.*.status'] = 'required|in:1';
                        $rules['healthQuestionnaires.*.selectedAnswer'] = 'required|in:0,1';
                        $rules['healthQuestionnaires.*.medMedications'] = 'required_if:healthQuestionnaires.*.type,medication';
                        $rules['healthQuestionnaires.*.medConditions'] = 'required_if:healthQuestionnaires.*.type,condition';

                        if ($healthQNA['type'] == 'condition') {
                            $rules['healthQuestionnaires.*.medConditions.health_condition'] =
                                'sometimes|required_if:healthQuestionnaires.*.selectedAnswer,1';
                            $rules['healthQuestionnaires.*.medConditions.date_of_onset'] =
                                'sometimes|required_if:healthQuestionnaires.*.selectedAnswer,1';
                            // $rules['healthQuestionnaires.*.medConditions.date_of_recovery'] =
                            //     'sometimes|required_if:healthQuestionnaires.*.selectedAnswer,1';
                            $rules['healthQuestionnaires.*.medConditions.is_treatment'] =
                                'nullable';
                            $rules['healthQuestionnaires.*.medConditions.is_medicate'] =
                                'nullable';
                            $rules['healthQuestionnaires.*.medConditions.d_last_seen'] =
                                'sometimes|required_if:healthQuestionnaires.*.selectedAnswer,1';
                            $rules['healthQuestionnaires.*.medConditions.symptoms'] =
                                'sometimes|required_if:healthQuestionnaires.*.selectedAnswer,1';
                        }
                        $rules['healthQuestionnaires.*.medMedications.*.dosage'] = 'sometimes|required_if:healthQuestionnaires.*.selectedAnswer,1';
                        $rules['healthQuestionnaires.*.medMedications.*.medical_condition'] = 'sometimes|required_if:healthQuestionnaires.*.selectedAnswer,1';
                        $rules['healthQuestionnaires.*.medMedications.*.medication'] = 'sometimes|required_if:healthQuestionnaires.*.selectedAnswer,1';

                    }
                }
            }
        }
        else{
            $rules['policy_id'] = 'required';
        }
        return $rules;
    }

    public function messages()
    {
        if(!empty($this->request->get('policy_id'))){
            $messages = [
                'policy_id.numeric' => 'Policy ID must be numeric.',
                'policy_id.required' => 'Policy ID is required.',
                'policy_id.exists' => 'Policy ID does not not exist.',
                'd_fname.required' => 'First name is required.',
                'd_fname.string' => 'First name should be a string.',
                'd_lname.required' => 'Last name is required.',
                'd_lname.string' => 'Last name should be a string.',
                'd_ssn.required' => 'SSN is required.',
                // 'd_ssn.numeric' => 'SSN can only contain numbers.',
                'd_ssn.digits' => 'SSN can only have 9 digits.',
                'd_ssn.not_in' => 'SSN must be unique. SSN similar to an existing Dependent.',
                'd_relate.required' => 'Relation is required.',
                'd_relate.in' => 'Relation value should be either Child(C) or Spouse(S).',
                'd_gender.required' => 'Gender is required.',
                'd_gender.in' => 'Gender value should be Male(0) or Female(1).',
                "d_dob.required"=>"Date of birth is required",
                "d_dob.date_format"=>"Date of birth must be in format m/d/Y.",
                "dhrq.required"=>"Height (feet) is required.",
                "dhrq.numeric"=>"Height (feet) must be numeric.",
                "dhrq2.required"=>"Height (inch) is required.",
                "dhrq2.numeric"=>"Height (inch) must be numeric.",
                "dhrq2.between"=>"Height (inch) value must be between 0 and 11.",
                "dwrq.required"=>"Weight is required.",
                "dwrq.numeric"=>"Weight must be numeric.",
                "healthQuestionnaires.required"=>"HealthQuestionnaires are required.",
                "healthQuestionnaires.array"=>"HealthQuestionnaires must be an array.",
                'health_notes.string' => 'Health Notes must be a string.',
                'd_option.required' => 'The dependent option is required.',
                'd_option.in' => 'The dependent option must be either instant or later.',
                'new_effective_date.required_if' => 'The new effective date is required when selecting "For Later".',
            ];
            if ( request()->isMethod('PUT') || request()->isMethod('DELETE') ) {
                $messages = array_merge($messages, [
                    'did.numeric' => 'Dependent ID must be numeric.',
                    'did.required' => 'Dependent ID is required.',
                    'did.exists' => 'Dependent ID does not exist.',
                    'did.in' => 'The Dependent ID does not belong to this Policy.'
                ]);
            }
            $messages['healthQuestionnaires.*.med_id.required'] = 'Med Id is required.';
            $messages['healthQuestionnaires.*.med_id.numeric'] = 'Med Id must be numeric.';
            $messages['healthQuestionnaires.*.question_id.required'] = 'Question Id is required.';
            $messages['healthQuestionnaires.*.question_id.numeric'] = 'Question Id must be numeric.';
            $messages['healthQuestionnaires.*.question.required'] = 'Question is required.';
            $messages['healthQuestionnaires.*.status.required'] = 'Status is required.';
            $messages['healthQuestionnaires.*.status.in'] = 'Status must be 1.';
            $messages['healthQuestionnaires.*.selectedAnswer.required'] = 'Selected answer is required';
            $messages['healthQuestionnaires.*.selectedAnswer.in'] = 'Selected answer must be 1 or 0.';
            $messages['healthQuestionnaires.*.medMedications.required_if'] = 'Med medication is required';
            $messages['healthQuestionnaires.*.medConditions.required_if'] = 'Med condition is required.';

            $messages['healthQuestionnaires.*.medConditions.health_condition.required_if'] = 'Health condition is required';
            $messages['healthQuestionnaires.*.medConditions.date_of_onset.required_if'] = 'Date of onset is required';
            // $messages['healthQuestionnaires.*.medConditions.date_of_recovery.required_if'] = 'Date of recovery condition is required';
            $messages['healthQuestionnaires.*.medConditions.is_treatment.required_if'] = 'Treatment option is required';
            $messages['healthQuestionnaires.*.medConditions.is_medicate.required_if'] = 'Medicate option is required';
            $messages['healthQuestionnaires.*.medConditions.d_last_seen.required_if'] = 'Last seen is required';
            $messages['healthQuestionnaires.*.medConditions.symptoms.required_if'] = 'Symptoms is required';

            $messages['healthQuestionnaires.*.medMedications.*.dosage.required_if'] = 'Dosage of med medication is required';
            $messages['healthQuestionnaires.*.medMedications.*.medical_condition.required_if'] = 'Medical condition of med medication is required';
            $messages['healthQuestionnaires.*.medMedications.*.medication.required_if'] = 'Medication of med medication is required';

        }
        else{
            $messages['policy_id.required'] = 'Policy ID is required.';
        }
        return $messages;
    }

    protected function failedValidation(Validator $validator)
    {
        $failReason = $validator->failed();
        $errors = $validator->errors();
        $errorCode = 422;

        $hasHealthQuestion = false;
        foreach (array_keys($failReason) as $key) {
            if (!empty($this->request->get('healthQuestionnaires'))
                && strpos($key, 'healthQuestionnaires') !== false)
                $hasHealthQuestion = true;
        }

        // pluck Index of healthQuestion with error from error_message_bag
        // fetch complete text of that question
        // pluck the questionNumber (A/B/C) from questionText
        // form message
        if ($hasHealthQuestion && !request()->isMethod('DELETE')) {
            $healthQuestionIndex = (int) filter_var( key($failReason) , FILTER_SANITIZE_NUMBER_INT);
            if ($healthQuestionIndex >= 8)
                $healthQuestionIndex = key(array_slice(request()->input( 'healthQuestionnaires' ), -1, 1, true));
            $questionText = request()->input( 'healthQuestionnaires' )[$healthQuestionIndex]['question'];
            $questionNumber = substr($questionText, strpos($questionText, '.')-1, 1);
            $message = 'Please answer all fields of health question (' . $questionNumber . ').';
        } else {
            $message = $errors->first();
        }

        $responseBody = [
            'status' => 'error',
            'statusCode' => $errorCode,
            'errors' => $errors,
            'message' => $message
        ];

        throw new HttpResponseException(response()->json($responseBody, $errorCode));
    }

}
