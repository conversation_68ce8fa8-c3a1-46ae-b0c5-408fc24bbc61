<?php

namespace App\Http\Requests\Policy;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class DeleteDependentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'dep_id' => 'required|numeric|bail|exists:dependents,did',
            'policy_id' => 'required|numeric|bail|exists:policies,policy_id',
            "reason"=>"required",
            "login_user_id"=>"nullable",
        ];
    }

    public function messages()
    {
        return [
            'dep_id.numeric' => 'Dependent ID must be numeric.',
            'dep_id.required' => 'Dependent ID is required.',
            "dep_id.exists" => "Dependent not found.",
            'policy_id.numeric' => 'Policy ID must be numeric.',
            'policy_id.required' => 'Policy ID is required.',
            "policy_id.exists" => "Policy not found.",
            "reason"=>"Reason is required",
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
