<?php

namespace App\Http\Requests\Policy;

use App\Rules\NumVerifyRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class UpdatePhoneRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'policy_id' => 'required|numeric|bail|exists:policies,policy_id',
            'login_user_id' => 'nullable',
            'phone1' => ['required','numeric','digits:10','bail', 'unique:userinfo_policy_address,phone1,' . $this->policy_id . ',policy_id' , 'bail', new NumVerifyRule()],
            'phone2' => ['nullable','numeric','digits:10','bail', 'unique:userinfo_policy_address,phone2,' . $this->policy_id . ',policy_id' , 'bail', new NumVerifyRule()],
        ];
    }

    public function messages()
    {
        return [
            'policy_id.required' => 'Policy Id is required.',
            'policy_id.numeric' => 'Policy Id must be numeric.',
            "policy_id.exists" => "Policy not found.",
            'phone1.numeric' => 'Phone should be in number.',
            'phone1.digits' => 'Phone Number should be 10 digits',
            'phone2.numeric' => 'Phone should be in number.',
            'phone2.digits' => 'Phone Number should be 10 digits.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
