<?php

namespace App\Http\Requests\HomepageConfiguration;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class HomepageAgentPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agentId' => 'required',
            'website' => 'required',
            'plans' => 'required|array',
            'plans.*' => 'required|min:1'
        ];
    }

    public function messages()
    {
        return [
            'agentId.required' => 'Agent Id is required.',
            'website.required' => 'Website is required.',
            'plans.required' => 'At least one plan must be selected.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
