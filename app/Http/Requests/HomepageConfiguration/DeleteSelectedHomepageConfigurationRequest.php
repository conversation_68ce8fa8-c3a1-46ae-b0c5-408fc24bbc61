<?php

namespace App\Http\Requests\HomepageConfiguration;

use App\HomepageConfiguration;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class DeleteSelectedHomepageConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $type = $this->request->get('selected_user_type');
        $exists = '';
        if ($type == HomepageConfiguration::CONFIGURATION_FOR_AGENT) {
            $exists = '|exists:agent_info,agent_id';
        } elseif ($type == HomepageConfiguration::CONFIGURATION_FOR_GROUP) {
            $exists = '|exists:group_info,gid';
        }
        $selectedUserType = HomepageConfiguration::CONFIGURATION_FOR_AGENT.",".HomepageConfiguration::CONFIGURATION_FOR_GROUP;
        return [
            'ids' => 'required|array',
            'ids.*' => 'required|min:1',
            'selected_all' => "nullable|boolean",
            'selected_user_type' => "required|in:{$selectedUserType}",
            'selected_user_id' => "required_if:selected_user_type,{$selectedUserType}{$exists}"
        ];
    }

    public function messages()
    {
        return [
            'ids.required' => 'At least one id must be selected.',
            'selected_user_type.required' => 'Selected user type is required.',
            'selected_user_type.in' => 'Selected user type must be either '.HomepageConfiguration::CONFIGURATION_FOR_AGENT." or ".HomepageConfiguration::CONFIGURATION_FOR_GROUP,
            'selected_user_id.required' => 'Selected user id is required.',
            'selected_user_id.exists' => ucfirst($this->request->get('selected_user_type')) . " not found.",
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
