<?php

namespace App\Http\Requests\Member;

use Carbon\Carbon;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class SwitchPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'current_plan_policy_id'=>'required|integer|bail|exists:plan_policies,p_ai',
            'current_plan_id'=>'required|integer|bail|exists:plans_pricing_display,pid',
            'plan_id'=>'required|integer|bail|exists:plans_pricing_display,pid',
            'plan_pricing_id'=>'required|integer|bail|exists:plan_pricing,plan_pricing_id',
            'effective_date' => 'required|date|date_format:Y-m-d',
            'login_user_id'=>'nullable',
            'reason'=>'required'
        ];
    }

    public function messages()
    {
        return [
            'current_plan_policy_id.required' => 'Current plan policy id is required.',
            'current_plan_policy_id.integer' => 'Current plan policy id must be integer.',
            'current_plan_policy_id.exists' => 'Current plan policy not found.',
            'current_plan_id.required' => 'Current Plan id is required.',
            'current_plan_id.integer' => 'Current Plan id must be integer.',
            'current_plan_id.exists' => 'Current Plan not found.',
            'plan_id.required' => 'Plan id is required.',
            'plan_id.integer' => 'Plan id must be integer.',
            'plan_id.exists' => 'Plan not found.',
            'plan_pricing_id.required' => 'Plan pricing id  is required.',
            'plan_pricing_id.integer' => 'Plan pricing id  must be integer.',
            'plan_pricing_id.exists' => 'Plan pricing not found.',
            'effective_date.required' => 'Effective date is required.',
            'effective_date.date_format' => 'Effective date format must be Y-m-d.',
            'effective_date.after'=>'Effective date must be greater than current date',
            'reason.required'=>'Reason is required.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
