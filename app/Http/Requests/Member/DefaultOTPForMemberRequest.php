<?php

namespace App\Http\Requests\Member;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class DefaultOTPForMemberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    // public function prepareForValidation()
    // {
    //     $this->merge([
    //         'defaultotp' => $this->route('default_otp'),
    //     ]);
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
{
return [
'user_id' => 'required|integer|exists:mysql.userinfo,userid',
];
}
    public function messages(){
        return [
            'userid.required' => 'user id is required.',
            'userid.integer' => 'user id must be represented in integer.',
            'userid.exists' => 'user id. The user id does not exist.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), JsonResponse::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
