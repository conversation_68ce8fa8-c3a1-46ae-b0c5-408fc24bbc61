<?php

namespace App\Http\Requests\Member;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class UpdatePlatform extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'policy_id'     => 'required|integer', 
            'agent_id'      => 'required|integer', 
            'platform_website' => 'required|string',
            'login_user'    => 'required|integer', 
        ];
    }
    public function messages()
    {
        return [
            'policy_id.required' => 'The policy id is required.',
            'agent_id.required' => 'The agent id is required.',
            'platform_website.required' => 'The platform field is required.',
            'login_user.required' => 'The logged in user id is required.'
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errorsList = (new ValidationException($validator))->errors();
        $counter = 0;
        foreach($errorsList as $key => $errorList) {
            $data[$counter]['element'] = $key;
            $data[$counter]['message'] = $errorList[0];
            $counter ++;
        }
        $errors = [
            'status' => false,
            'code' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
            'errors' => $data
        ];
        throw new HttpResponseException(
            response()->json($errors, JsonResponse::HTTP_UNPROCESSABLE_ENTITY)
        );
    }
}