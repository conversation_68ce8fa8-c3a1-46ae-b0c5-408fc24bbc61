<?php

namespace App\Http\Requests\PolicyRider;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class AddPolicyRiderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'policy_id' => 'required|integer',
            'reason' => 'required',
            'plan_pricing_id' => 'required',
            'aid' => 'required|integer',
        ];
    }
    public function messages()
    {
        return [
            'policy_id.required' => 'Policy Id is required',
            'policy_id.integer' => 'Policy id should be integer.',
            'reason.required' => 'Reason is required.',
            'plan_pricing_id.required' => 'Plan pricing id is required',
            'plan_pricing_id.integer' => 'Plan pricing id should be integer.',
            'aid.required' => 'Agent id is required',
            'aid.integer' => 'Agent id should be integer.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
