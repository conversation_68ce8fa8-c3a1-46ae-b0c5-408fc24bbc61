<?php

namespace App\Http\Requests\PolicyTerminationWithdrawn;

use App\Rules\ValidateSSN;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class PolicyInformationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'cfname' => 'required',
            'cmname' => 'sometimes|nullable|regex:/^[A-Za-z]$/i',
            'clname' => 'required',
            'cgender' => 'required',
            'phone1' => 'required|numeric|digits:10',
            'phone2' => 'sometimes|nullable|numeric|digits:10',
            'cemail' => 'required|email',
            'cssn' => ['required',new ValidateSSN(),'digits:9'],
            'userid' => 'required|integer',
            'policyID' => 'required|integer',
            'aid' => 'required',
            'cdob' => 'date|before:today',
            'sendEmail' => 'boolean',
            'numeric' => 'sometimes|nullable|numeric',
            'cemail_alt' => 'sometimes|nullable|email',
            'employed' => 'sometimes|nullable|boolean',
        ];
    }

    public function messages()
    {
        return [
            'userid.required' => 'User ID is required.',
            'userid.integer' => 'User ID should be an integer.',
            'policyID.required' => 'Policy ID is required.',
            'policyID.integer' => 'Policy ID should be an integer.',
            'aid.required' => 'Agent ID is required.',
            'cdob.date' => 'Date of Birth should be in the format(mm/dd/yyyy)',
            'cdob.before' => 'Date of Birth can\'t be in the future.',
            'cfname.required' => 'First Name is required.',
            'cmname.required' => 'Middle Name is required.',
            'cmname.regex' => 'Middle Name should be a single Alphabet.',
            'clname.required' => 'Last Name is required.',
            'cgender.required' => 'Gender is required.',
            'phone1.required' => 'Phone Number is required.',
            'cemail.required' => 'Email is required.',
            'cemail.email' => 'Invalid email format.',
            'phone1.numeric' => 'Phone should be in number.',
            'phone1.digits' => 'Phone Number should be 10 digits',
            'phone2.numeric' => 'Phone should be in number.',
            'phone2.digits' => 'Phone Number should be 10 digits.',
            'cssn.required' => 'SSN number is required.',
            'cssn.digits' => 'SSN number should be 9 digits.',
            'sendEmail.boolean' => 'SendEmail flag should be True or False.',
            'cemail_alt.email' => 'Invalid alternate email.',
            'employed.boolean' => 'Employed should be 1 or 0.',

        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
