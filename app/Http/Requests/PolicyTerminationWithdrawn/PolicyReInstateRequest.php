<?php

namespace App\Http\Requests\PolicyTerminationWithdrawn;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class PolicyReInstateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'reason' => 'required',
            'substartdate' => 'required|date',
            'policyID' => 'required|integer',
            'aid' => 'required',
            'sendEmail' => 'integer',
        ];
    }
    public function messages()
    {
        return [
            'policyID.required' => 'Policy ID is required.',
            'policyID.integer' => 'Policy ID should be an integer.',
            'aid.required' => 'Agent ID is required.',
            'substartdate.required' => 'SubStartDate is required.',
            'substartdate.date' => 'SubStartDate is invalid.',
            'reason.required' => 'Reason is required.',
            'sendEmail.integer' => 'SendEmail flag should be integer.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
