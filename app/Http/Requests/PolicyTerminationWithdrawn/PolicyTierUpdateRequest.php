<?php

namespace App\Http\Requests\PolicyTerminationWithdrawn;

use App\Policy;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class PolicyTierUpdateRequest extends FormRequest
{
    protected $effective_date;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $policy_id = $this->request->get('policy_id');
        $policy = Policy::find($policy_id);

        $this->effective_date = isset($policy->effective_date)
            ? date_format( date_create($policy->effective_date), 'Y-m' )
            : date('Y-m');

        return [
            'policy_id' => 'required|numeric|exists:policies,policy_id',
            'aid' => 'required|numeric',
            'new_tier' => 'required|in:IC,IS,IF,IO',
            'sendEmail' => 'numeric|in:0,1',
            'pid' => 'nullable',
        ];
    }

    public function messages()
    {
        $customMessages = [
            'policy_id.required' => 'Policy ID is required.',
            'policy_id.numeric' => 'Policy ID should be integer',
            'policy_id.exists' => 'Policy ID is invalid or does not exist.',
            'aid.required' => 'Agent ID is required.',
            'new_tier.required' => 'New Policy Plan is required',
            'new_tier.in' => 'Tier should be IC,IS,IF or IO',
            'sendEmail.numeric' => 'SendEmail flag should be integer.',
            'sendEmail.in' => 'SendEmail flag should be either 0 or 1.',
        ];

        return $customMessages;
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json([
            'status' => 'error',
            'statusCode' => 422,
            'data' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
