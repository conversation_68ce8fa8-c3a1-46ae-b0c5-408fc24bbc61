<?php

namespace App\Http\Requests\PolicyTerminationWithdrawn;

use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Factory;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class BillingInformationCCRequest extends FormRequest
{

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $date = Carbon::createFromDate($this->credit_expyear, $this->credit_expmonth)->format('Y-m');
        $this->merge([
            'credit_expyear' => $date
        ]);
    }


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $currentDate = Carbon::now()->format('Y-m');
        return [
            'credit_type' => 'required',
            'credit_num' => 'required|integer',
            'credit_status' => 'required',
            'credit_userid' => 'required|integer',
//            'credit_date' => 'required',
            'credit_expmonth' => 'required',
            'credit_expyear' => 'required|date|date_format:Y-m|after_or_equal:'.$currentDate,
            'firstname' => 'required',
            'lastname' => 'required',
//            'credit_shipaddress' => 'required',
            'maddress' => 'integer',
            'credit_cvc' => 'integer|required',
            'sendEmail' => 'integer',
            'address1'=>'required',
            'city'=>'required',
            'zip'=>'required',
            'state'=>'required'
        ];
    }
    public function messages()
    {
        return [
            'credit_type.required' => 'Credit Type is required.',
            'credit_num.required' => 'CC Number is required.',
            'credit_num.integer' => 'CC Number should be number.',
            'credit_status.required' => 'CC Status is required.',
            'credit_userid.integer' => 'User ID should be integer',
            'credit_userid.required' => 'User ID is required.',
//            'credit_date.required' => 'CC Date is required.',
            'credit_expmonth.required' => 'CC Expiry Month is required.',
            'credit_expyear.required' => 'CC Expiry Year is required.',
            'firstname.required' => 'First Name is required',
            'lastname.required' => 'Last Name is required',
//            'credit_shipaddress.required' => 'Credit Shipping Address is required',
            'maddress.integer' => 'Maddress should be integer',
            'credit_cvc.required' => 'CVC is required',
            'credit_cvc.integer' => 'CVC should be integer',
            'sendEmail.integer' => 'SendEmail flag should be integer.',
            'address1.required' => 'Address1 is required.',
            'city.required' => 'City is required.',
            'zip.required' => 'Zip is required.',
            'state.required' => 'State is required.',
            'credit_expyear.after_or_equal'=>'The credit expiry year must be a date after or equal to current date.'
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
