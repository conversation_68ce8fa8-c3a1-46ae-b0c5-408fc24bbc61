<?php

namespace App\Http\Requests\PolicyTerminationWithdrawn;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class BillingInformationEFTRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation()
    {
        if (request()->isMethod('DELETE')) {
            $this->merge([
                'bank_id' => $this->route('id')
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if (request()->isMethod('DELETE')) {
            $rules = [
                'bank_id' => 'required|integer|exists:payment_eft,bank_id'
            ];
        } else {
            $rules = [
                'bank_accountname' => 'required',
                'bank_name' => 'required',
                'bank_routing' => 'required|numeric|digits:9',
                'bank_account' => ['required', 'numeric', 'bail', 'digits_between:4,17', 'not_regex:/^([0-9])\1{3,16}$/i'],
                'userid' => 'required|integer|exists:userinfo,userid',
                'sendEmail' => 'integer|in:0,1',
            ];
        }
        return $rules;
    }
    public function messages()
    {
        return [
            'bank_id.required' => 'Bank ID is required.',
            'bank_id.integer' => 'Bank ID must be an integer.',
            'bank_id.exists' => 'Invalid Bank ID. Bank ID does not exist.',
            'bank_accountname.required' => 'Bank Account Name is required.',
            'bank_name.required' => 'Bank Name is required.',
            'bank_routing.required' => 'Routing Number is required.',
            'bank_account.required' => 'Bank Account Number is needed.',
            'bank_account.numeric' => 'Bank Account Number should be in number.',
            'bank_account.digits_between' => 'Bank Account Number must have 4 to 17 digits.',
            'bank_account.not_regex' => 'Invalid Bank Account Number format.',
            'bank_routing.numeric' => 'Routing Number should be in number.',
            'userid.required' => 'User ID is required.',
            'userid.integer' => 'User ID should be an integer.',
            'userid.exists' => 'Invalid User ID. User ID does not exist.',
            'sendEmail.integer' => 'Send Email flag should be an integer.',
            'sendEmail.in' => 'Send Email flag can have value of Yes(1) or No(0).'
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
