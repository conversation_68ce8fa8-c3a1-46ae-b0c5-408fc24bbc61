<?php

namespace App\Http\Requests\PolicyTerminationWithdrawn;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class PolicyWithdrawRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'tdate' => 'required|date_format:Y-m-d',
            'reason' => 'required',
            'policy_id' => 'required|integer',
            'aid' => 'required',
            'sendEmail' => 'integer',
            'action' => 'required|in:wnp,wdr,wno,wcp',
        ];
    }
    public function messages()
    {
        return [
            'tdate.required' => 'Date is required.',
            'tdate.date_format' => 'Date should be in YYYY-MM-DD format.',
            'reason.required' => 'Reason is required.',
            'policy_id.required' => 'Policy ID is required.',
            'policy_id.integer' => 'Policy ID should be integer',
            'aid.required' => 'Agent ID is required.',
            'sendEmail.integer' => 'SendEmail flag should be integer.',
            'action.required' => 'Policy action is required',
            'action.in' => 'To Withdraw Policy, action need to be either wnp,wdr,wno & wcp',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
