<?php

namespace App\Http\Requests;

use App\PolicyNote;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class AgentNotesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function noteStatus(){
        $statuses = array_values(PolicyNote::$statuses);
        return implode(',',$statuses);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statuses = $this->noteStatus();
        return [
            'agent_id' => 'required|integer|exists:mysql.agent_info,agent_id',
            'message' => 'required',
            'subject' => 'nullable',
            'agent_email' => 'required|email',
            'status' => "required|integer|in:{$statuses}",
            'attachment' => 'nullable|max:4200000'
        ];
    }
    public function messages()
    {
        $statuses = $this->noteStatus();
        return [
            'note.required' => 'Note is required.',
            'agent_id.required' => 'Agent ID is required.',
            'agent_id.integer' => 'Agent ID should be an integer.',
            'agent_id.exists' => 'Agent ID is invalid.',
            'agent_email.required' => 'Agent Email is required.',
            'agent_email.email' => 'Agent Email is invalid.',
            'attachment.max' => 'File too large, max allowed is 4 MB.',
            'status.required' => 'Status ID is required.',
            'status.integer' => 'Status ID must be an integer.',
            'status.in' => "Invalid status. Only {$statuses} are allowed.",
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
