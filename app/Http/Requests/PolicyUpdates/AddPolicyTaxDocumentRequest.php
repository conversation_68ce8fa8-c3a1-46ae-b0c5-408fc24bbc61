<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class AddPolicyTaxDocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'aid' => 'required',
            'policy_id' => 'required|numeric',
            'file' => 'required|array|max:10',
            'file.*' => 'required|max:10240',
        ];
    }
    public function messages()
    {
        return [
            'aid.required' => 'User Id is required.',
            'policy_id.required' => 'Policy Id is required.',
            'policy_id.numeric' => 'Policy Id must be numeric.',
            'file.required' => 'File is required',
            'file.max' => 'File must be less than 10 MB',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
