<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class DeletePolicyDocument extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file_id' => 'required|numeric',
            'policy_id' => 'required|numeric',
            'aid' => 'required',
            'is_table' => 'required'
        ];
    }
    public function messages()
    {
        return [
            'file_id.required' => 'File ID is required',
            'file_id.numeric' => 'File ID must be numeric',
            'policy_id.required' => 'Policy ID is required',
            'policy_id.numeric' => 'Policy ID must be numeric',
            'aid.required' => 'User ID is required',
            'is_table.required' => 'is_table Flag is required'
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
