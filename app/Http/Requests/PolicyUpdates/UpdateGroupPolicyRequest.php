<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateGroupPolicyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'policy_id' => 'required|integer',
            'gid' => 'required|integer',
            'reason' => 'required',
            'aid' => 'required|integer',
            'sendEmail' => 'integer'
        ];
    }

    public function messages()
    {
        return [
            'policy_id.required' => 'Policy Id is required',
            'policy_id.integer' => 'Policy id should be integer.',
            'reason.required' => 'Reason is required.',
            'aid.required' => 'Agent id is required',
            'aid.integer' => 'Agent id should be integer.',
            'gid.required' => 'Group id is required',
            'gid.integer' => 'Group id should be integer.',
            'sendEmail.integer' => 'Send Email param should 0 or 1',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
