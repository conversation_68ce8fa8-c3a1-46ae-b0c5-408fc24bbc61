<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class AddPolicyDocument extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agent_id' => 'required|numeric',
            'policy_id' => 'required|numeric',
            'app_name' => 'required',
            'file_upload' => 'required|mimes:jpeg,jpg,pdf|max:2048',
        ];
    }
    public function messages()
    {
        return [
            'agent_id.required' => 'Agent Id is required.',
            'policy_id.required' => 'Policy Id is required.',
            'policy_id.numeric' => 'Policy Id must be numeric.',
            'app_name.required' => 'Note is required.',
            'file_upload.required' => 'File is required',
            'file_upload.mimes' => 'File must be .jpeg or .jpg or .pdf',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
