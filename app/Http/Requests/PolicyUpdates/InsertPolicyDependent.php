<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class InsertPolicyDependent extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'policy_id' => 'required|numeric',
            'userid' => 'required|numeric',
            'd_fname' => 'required',
            'd_lname' => 'required',
            'd_ssn4' => 'required',
            'd_ssn' => 'required',
            'd_relate' => 'required',
            'd_gender' => 'required',
            'aid' => 'required',       
        ];
    }
        public function messages()
    {
        return [
            'policy_id.numeric' => 'Policy ID must be numeric.',
            'policy_id.required' => 'Policy ID is required.',
            'userid.numeric' => 'User ID must be numeric.',
            'userid.required' => 'User ID is required.',
            'd_fname.required' => 'First name is required.',
            'd_lname.required' => 'Last name is required.',
            'd_ssn.required' => 'SSN is required.',
            'd_ssn4.required' => 'Last 4 digit of SSN is required.',
            'd_relate.required' => 'Relation is required.',
            'd_gender.required' => 'Gender is required.',
            'aid.required' => 'User ID is required.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
