<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdatePolicyAgent extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agent_code' => 'required|exists:agent_info,agent_code',
            'policy_id' => 'required|numeric|exists:policies,policy_id',
            'aid' => 'required',
            'elgb_new_effdate'=>'required|date|date_format:Y-m-d'
        ];
    }
    public function messages()
    {
        return [
            'agent_code.required' => 'Agent Code is required.',
            'agent_code.exists' => 'Invalid Agent Code. Agent Code does not exist.',
            'policy_id.numeric' => 'Policy Id must be numeric.',
            'policy_id.required' => 'Policy Id is required.',
            'aid.required' => 'Admin Agent Id is required.',
            'elgb_new_effdate.required'=>'Effective date is required.',
            'elgb_new_effdate.date'=>'Effective date must be date.',
            'elgb_new_effdate.date_format'=>'Effective date must be Y-m-d format.',
            'policy_id.exists'=>'Policy not found.'
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['type' => 'error', 'message' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
