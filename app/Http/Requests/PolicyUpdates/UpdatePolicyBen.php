<?php

namespace App\Http\Requests\PolicyUpdates;

use App\Helpers\PolicyUpdateHelper;
use App\PolicyBeneficiary;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Rule;
use App\Rules\ValidateSSN;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdatePolicyBen extends FormRequest
{
    protected $policy_id;
    protected $existing_ben;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $this->policy_id = $this->request->get('policy_id');
        $this->existing_ben = PolicyUpdateHelper::getPolicyBeneficiary($this->policy_id)
            ->transform(function ($item) {
                return ['ben_id' => $item['ben_id'],
                    'b_ssn' => $item['b_ssn']];
            });

        if (request()->isMethod('POST') && $this->existing_ben->count() >= 3) {
            $responseBody = [
                'success' => false,
                'code' => $errorCode = 422,
                'errors' => 'Policy can have upto 3 Beneficiary. Cannot add additional Beneficiary.'
            ];
            throw new HttpResponseException(response()->json($responseBody, $errorCode));
        }

        $rules = [
            'policy_id' => 'required|numeric|exists:policies,policy_id',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'dob' => 'required|date_format:Y-m-d|before_or_equal:now',
            'relation' => ['required', 'string', Rule::in(PolicyBeneficiary::$beneficiaryRelations)],
            'ssn' => [
                'required',
                $this->existing_ben->isNotEmpty() ?
                    Rule::notIn($this->existing_ben
                        ->where('ben_id', '!=', ($this->request->get('ben_id')))->pluck('b_ssn'))
                    : 'bail',  new ValidateSSN()
            ],
            'ben_percent' => 'required|integer|between:1,100'
        ];

        if ($this->existing_ben->isNotEmpty() && request()->isMethod('POST')) {
            $rules = array_merge($rules, [
                'existing_ben' => 'required|array|size:' . $this->existing_ben->count()
            ]);

            if (! empty($this->request->get('existing_ben')))
                foreach ($this->request->get('existing_ben') as $each_existing_ben) {
                    $rules['existing_ben.*.id'] = [
                        'required', 'numeric', 'exists:policy_benificiery,ben_id', 'distinct', 'bail',
                        Rule::in($this->existing_ben->pluck('ben_id'))
                    ];
                    $rules['existing_ben.*.ben_percent'] = 'required|integer|between:0,100';
                }
        }

        if(request()->isMethod('PUT')) {
            $rules = array_merge($rules, [
                'ben_id' => [
                    'required', 'numeric', 'exists:policy_benificiery,ben_id', 'bail',
                    Rule::in($this->existing_ben->pluck('ben_id'))
                ],
                'reason' => 'required|string'
            ]);

            if ($this->existing_ben->isNotEmpty() && $this->existing_ben->count() > 1) {
                $rules['ben_percent'] = 'required|integer|between:0,100';

                $rules = array_merge($rules, [
                    'existing_ben' => 'required|array|size:' . ($this->existing_ben->count() - 1)
                ]);

                foreach ($this->request->get('existing_ben') as $each_existing_ben) {
                    $rules['existing_ben.*.id'] = [
                        'required', 'numeric', 'exists:policy_benificiery,ben_id', 'distinct', 'bail',
                        Rule::in($this->existing_ben
                            ->where('ben_id', '!=', ($this->request->get('ben_id')))->pluck('ben_id'))
                    ];
                    $rules['existing_ben.*.ben_percent'] = 'required|integer|between:0,100';
                }
            }
        }

        return $rules;
    }
    public function messages()
    {
        $benCount = request()->isMethod('PUT') ? $this->existing_ben->count() - 1 : $this->existing_ben->count();
        return [
            'policy_id.required' => 'Policy ID is required.',
            'policy_id.numeric' => 'Policy ID must be numeric.',
            'policy_id.exists' => 'Invalid Policy ID. Policy ID does not exist.',
            'ben_id.required' => 'Beneficiary ID is required.',
            'ben_id.numeric' => 'Beneficiary ID must be numeric.',
            'ben_id.exists' => 'Invalid Beneficiary ID. Beneficiary ID does not exist.',
            'ben_id.in' => 'Beneficiary ID does not belong to this Policy.',
            'first_name.required' => 'Beneficiary First Name is required.',
            'first_name.string' => 'Beneficiary First Name must be a string.',
            'last_name.required' => 'Beneficiary Last Name is required.',
            'last_name.string' => 'Beneficiary Last Name must be a string.',
            'dob.required' => 'Beneficiary Date of Birth is required.',
            'dob.date_format' => 'Beneficiary Date of Birth must be in \'YYYY-MM-DD\' format.',
            'dob.before_or_equal' => 'Beneficiary Date of Birth cannot be future date.',
            'relation.required' => 'Beneficiary Relation is required.',
            'relation.string' => 'Beneficiary Relation must be a string.',
            'relation.in' => 'Beneficiary Relation must have either of these accepted values: ' . implode(', ', PolicyBeneficiary::$beneficiaryRelations) . '.',
            'ssn.required' => 'Beneficiary SSN is required.',
            'ssn.digits' => 'Beneficiary SSN must be 9 digits.',
            'ssn.not_in' => 'Beneficiary SSN must be unique. SSN similar to an existing Beneficiary.',
            'ben_percent.required' => 'Beneficiary Percentage is required.',
            'ben_percent.integer' => 'Beneficiary Percentage must be an integer.',
            'ben_percent.between' => (request()->isMethod('PUT'))
                ? 'Beneficiary Percentage must be in the range of 0 to 100.'
                : 'Beneficiary Percentage must be in the range of 1 to 100.',
            'reason.required' => 'Reasoning for update must be mentioned.',
            'reason.string' => 'Reasoning for update must be string.',
            'existing_ben.required' => 'Existing Beneficiaries is required.',
            'existing_ben.array' => 'Existing Beneficiaries must be an array.',
            'existing_ben.size' => "Need details of {$benCount} existing beneficiaries.",
            'existing_ben.*.id.required' => 'Existing Beneficiary ID is required.',
            'existing_ben.*.id.numeric' => 'Existing Beneficiary ID must be numeric.',
            'existing_ben.*.id.exists' => 'Invalid Existing Beneficiary ID. Beneficiary ID does not exist.',
            'existing_ben.*.id.distinct' => 'Existing Beneficiary ID is repeated.',
            'existing_ben.*.id.in' => 'Existing Beneficiary ID does not belong to the existing beneficiaries of this Policy.',
            'existing_ben.*.ben_percent.required' => 'Existing Beneficiary Percentage is required.',
            'existing_ben.*.ben_percent.integer' => 'Existing Beneficiary Percentage must be an integer.',
            'existing_ben.*.ben_percent.between' => 'Existing Beneficiary Percentage must be in the range of 0 to 100.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errorCode = 422;
        $responseBody = [
            'success' => false,
            'code' => $errorCode,
            'errors' => $validator->errors()
        ];
        throw new HttpResponseException(response()->json($responseBody, $errorCode));
    }
}
