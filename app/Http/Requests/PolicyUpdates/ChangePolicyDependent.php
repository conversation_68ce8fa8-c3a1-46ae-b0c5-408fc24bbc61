<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class ChangePolicyDependent extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
        public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'did'       => 'required|numeric|exists:dependents,did',
            'aid'       => 'required|numeric',
            'policy_id' => 'required|numeric|exists:policies,policy_id',
            'd_fname'   => 'required|string',
            'd_mname'   => 'string',
            'd_lname'   => 'required|string',
            'd_relate'  => 'required|in:C,S',
            'd_gender'  => 'required|in:0,1',
            'd_ssn'     => 'required|numeric|digits:9',
            'd_notes'   => 'string|nullable'
        ];
    }
    public function messages()
    {
        return [
            'did.required' => 'Dependent ID is required.',
            'did.numeric' => 'Dependent ID must be numeric.',
            'did.exists' => 'Dependent ID is invalid or does not exist.',
            'aid.required' => 'User ID is required.',
            'aid.numeric' => 'User ID must be numeric.',
            'policy_id.required' => 'Policy ID is required.',
            'policy_id.numeric' => 'Policy ID must be numeric.',
            'policy_id.exists' => 'Policy ID is invalid or does not exist.',
            'd_fname.required' => 'First name is required.',
            'd_fname.string' => 'First name must contain only letters.',
            'd_mname.string' => 'Middle name must contain only letters.',
            'd_lname.required' => 'Last name is required.',
            'd_lname.string' => 'Last name must contain only letters.',
            'd_relate.required' => 'Relation is required.',
            'd_relate.in' => 'Relation type must be either Spouse(S) or Child(C).',
            'd_gender.required' => 'Gender is required.',
            'd_gender.in' => 'Gender must be either Male(0) or Female(1).',
            'd_ssn.required' => 'SSN number is required.',
            'd_ssn.numeric' => 'SSN number should be in number.',
            'd_ssn.digits' => 'SSN number should be of 9 digits.',
            'd_notes.string' => 'Additional notes must be a string.'
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $failReason = $validator->failed();
        $errors = (new ValidationException($validator))->errors();
        $errorCode = ( isset($failReason['aid']['Required']) ||
            isset($failReason['did']['Required']) ||
            isset($failReason['policy_id']['Required']) ) ? 400 : 422;

        throw new HttpResponseException(response()->json([
            'status' => 'error',
            'statusCode' => $errorCode,
            'data' => $errors
        ], $errorCode));
    }
}
