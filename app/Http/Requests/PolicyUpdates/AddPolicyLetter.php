<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class AddPolicyLetter extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required',
            'details' => 'required',
            'aid' => 'required',
            'policy_id' => 'required|numeric',
            'file_upload' => 'required|mimes:xlsx,xls,pdf|max:10240',
        ];
    }
    public function messages()
    {
        return [
            'title.required' => 'Title is required.',
            'details.required' => 'Details is required.',
            'aid.required' => 'User Id is required.',
            'policy_id.required' => 'Policy Id is required.',
            'policy_id.numeric' => 'Policy Id must be numeric.',
            'file_upload.required' => 'File is required',
            'file_upload.mimes' => 'File must be .xlsx or .xls or .pdf',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
