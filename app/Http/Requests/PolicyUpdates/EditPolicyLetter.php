<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class EditPolicyLetter extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file_id' => 'required|numeric',
            'title' => 'required',
            'details' => 'required',
            'aid' => 'required',
            'policy_id' => 'required|numeric',
        ];
    }
    public function messages()
    {
        return [
            'file_id.required' => 'File Id is required.',
            'file_id.numeric' => 'File Id must be numeric.',
            'title.required' => 'Title is required.',
            'details.required' => 'Details is required.',
            'aid.required' => 'User Id is required.',
            'policy_id.required' => 'Policy Id is required.',
            'policy_id.numeric' => 'Policy Id must be numeric.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
