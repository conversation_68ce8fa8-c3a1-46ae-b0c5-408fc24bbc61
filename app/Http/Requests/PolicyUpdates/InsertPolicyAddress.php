<?php

namespace App\Http\Requests\PolicyUpdates;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class InsertPolicyAddress extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'policy_id' => 'required|numeric',
            'a_userid' => 'required|numeric',
            'address1' => 'required',
            'city' => 'required',
            'state' => 'required',
            'zip' => 'required|max:5',
            'type' => 'required',
            'aid' => 'required',       ];
    }
    public function messages()
    {
        return [
            'policy_id.numeric' => 'Policy ID must be numeric.',
            'policy_id.required' => 'Policy ID is required.',
            'a_userid.numeric' => 'User ID must be numeric.',
            'a_userid.required' => 'User ID is required.',
            'address1.required' => 'Address is required.',
            'city.required' => 'City is required.',
            'state.required' => 'State is required.',
            'zip.required' => 'Zip is required. Only 5 digits.',
            'type.required' => 'Type is required.',
            'aid.required' => 'User ID is required.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
