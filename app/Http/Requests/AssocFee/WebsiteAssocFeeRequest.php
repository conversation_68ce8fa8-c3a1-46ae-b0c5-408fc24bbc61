<?php

namespace App\Http\Requests\AssocFee;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class WebsiteAssocFeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'website' => 'required',
            'assocPlanId' => 'required',
            'plans' => 'required|array',
            'plans.*' => 'required|min:1'
        ];
    }

    public function messages()
    {
        return [
            'website.required' => 'Website is required.',
            'plans.required' => 'At least one plan must be selected.',
            'assocPlanId.required' => 'Association Fee must be required.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
