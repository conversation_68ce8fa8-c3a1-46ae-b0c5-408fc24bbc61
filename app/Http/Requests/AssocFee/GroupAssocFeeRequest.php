<?php

namespace App\Http\Requests\AssocFee;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class GroupAssocFeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'gId' => 'required',
            'assocPlanId' => 'required',
            'plans' => 'nullable|array',
            'plans.*' => 'nullable|min:1',
            'cid' => 'nullable|array',
            'pl_type' => 'nullable|array',
        ];
    }
    
    
    
    public function messages()
    {
        return [
            'gId.required' => 'Group is required.',
            'assocPlanId.required' => 'Association Fee must be required.',
            'plans.array' => 'Plans must be an array.',
            'plans.*.min' => 'Each plan must have at least one value.',
            'cid.array' => 'CID must be an array.',
            'pl_type.array' => 'PL Type must be an array.',
        ];
    }
    

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
