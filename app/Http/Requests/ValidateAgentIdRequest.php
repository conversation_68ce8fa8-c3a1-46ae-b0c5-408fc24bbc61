<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class ValidateAgentIdRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'agent_id' => [
                'required',
                'integer',
                Rule::exists('agent_info', 'agent_id')
            ]
        ];
    }

    public function messages()
    {
        return [
            'agent_id.required' => 'The agent ID is required.',
            'agent_id.integer' => 'The agent ID must be an integer.',
            'agent_id.exists' => 'The agent ID does not exist in the agent_info table.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json([
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
            'errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
