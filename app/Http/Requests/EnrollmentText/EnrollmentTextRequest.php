<?php

namespace App\Http\Requests\EnrollmentText;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class EnrollmentTextRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'textHead' => 'required',
            'textBody' => 'required',
            'details' => 'required',
            'makeRequiredText' => 'nullable',
            'makeRequiredCheck' => 'nullable|boolean',
        ];
    }

    public function messages()
    {
        return [
            'textHead.required' => 'Heading is required.',
            'textBody.required' => 'Body is required.',
            'details.required' => 'Detail is required.',
            'makeRequiredCheck.boolean' => 'Make Required Check must be boolean.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
