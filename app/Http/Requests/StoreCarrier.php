<?php

namespace App\Http\Requests;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreCarrier extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'carrier_name' => 'required',
            'carrier_code' => 'required',
            'email' => 'required|email',
            'carrier_logo' => 'image|mimes:jpeg,jpg,png',
            "state"    => "required|array|min:1",
        ];
    }
    public function messages()
    {
        return [
            'carrier_name.required' => 'Carrier Name is required.',
            'carrier_code.required' => 'Carrier Code is required.',
            'email.required' => 'Carrier Email is required.',
            'carrier_logo.required' => 'Carrier Logo is required.',
            'carrier_logo.image' => 'The Carrier Logo must be an image.',
            'state.required' => 'Carrier State is required.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
