<?php

namespace App\Http\Requests\ManageAgent;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class EditAgentImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            'agent_img' => 'required|mimes:jpg,jpeg,png|bail|max:20480',
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable"
        ];
    }
    public function messages()
    {
        return [
            'agent_id.required' => 'Agent is required.',
            'agent_id.integer' => 'Agent Id should be integer.',
            'agent_id.exists' => 'Agent not found.',
            'agent_img.required' => 'Agent Image is required.',
            'agent_img.mimes' => 'Agent Image must be type of .jpeg,.jpg or .png format.',
            'agent_img.max' => 'Agent Image must be less than 20 MB.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
