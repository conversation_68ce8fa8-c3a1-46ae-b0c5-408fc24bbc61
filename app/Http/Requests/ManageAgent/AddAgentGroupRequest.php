<?php

namespace App\Http\Requests\ManageAgent;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class AddAgentGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            'gids' => 'required|array',
            'gids.*' => 'required|integer|min:1',
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable"
        ];
    }

    public function messages()
    {
        return [
            'agent_id.required' => 'Agent is required.',
            'agent_id.integer' => 'Agent Id should be integer.',
            'agent_id.exists' => 'Agent not found.',
            'gids.required' => 'At least one group Id must be selected.',
            'gids.*.integer' => 'Given Group Id :input  must be integer.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
