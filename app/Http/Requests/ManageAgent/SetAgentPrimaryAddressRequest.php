<?php

namespace App\Http\Requests\ManageAgent;

use App\AgentBusinessAddress;
use App\AgentChequeAddress;
use App\AgentPersonalAddress;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class SetAgentPrimaryAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $personalType = AgentPersonalAddress::AGENT_ADDRESS_TYPE_PERSONAL;
        $businessType = AgentBusinessAddress::AGENT_ADDRESS_TYPE_BUSINESS;
        $chequeType = AgentChequeAddress::AGENT_ADDRESS_TYPE_PAYMENT;
        $typeInText = "{$personalType},{$businessType},{$chequeType}";
        return [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            "type" => "required|in:" . $typeInText,
            "id" => "required|integer",
            "loginUserId" => "nullable",
            "loginUserName" => "nullable"
        ];
    }


    public function messages()
    {
        return [
            'agent_id.required' => 'Agent is required.',
            'agent_id.integer' => 'Agent Id should be integer.',
            'agent_id.exists' => 'Agent not found.',
            'type.required' => 'Type is required.',
            'type.in' => 'Type should be either personal , business or payment.',
            'id.required' => 'Agent Address Id is required.',
            'id.integer' => 'Agent Address Id should be integer.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
