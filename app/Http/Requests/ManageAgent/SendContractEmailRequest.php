<?php

namespace App\Http\Requests\ManageAgent;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class SendContractEmailRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            "agent_id"=>"required|integer|bail|exists:agent_info,agent_id",
            'contract_type' => 'required|string|in:N-CatContrs,catContrs,catContrsL7,carrContrs',
            'action_origin' => 'string',
            'repid' => 'sometimes|integer',

            "data.agent_level_alc"=>"required_if:contract_type,N-CatContrs|integer|bail|lte:7",
            "data.agent_level_pec"=>"required_if:contract_type,N-CatContrs|integer|bail|lte:7",
            "data.agent_level_pec_bex"=>"required_if:contract_type,N-CatContrs|integer|bail|lte:9",
            "data.agent_level_alc_bex"=>"required_if:contract_type,N-CatContrs|integer|bail|lte:9",
            'data.agent_level_fegli_ancillary'=>'required_if:contract_type,N-CatContrs|integer|bail|lte:7',
            'data.agent_level_patriot_ancillary'=>'required_if:contract_type,N-CatContrs|integer|bail|lte:9',

            "data.agent_level_local713"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_cigna"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_l713Anthem"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_northwellAnthem"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_IHAHealth"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_ENANAWU"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_lifelineMedical"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_solsticeBenefits"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_optionsPlus"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_metropolitanLifeInsuranceCompany"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_pinnacleBenefitsService"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_prudential"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',
            "data.agent_level_beyondMedical"=>'required_if:contract_type,carrContrs|integer|bail|lte:7|min:1',

            "data.agent_level_med"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_dent"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_vision"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_term_life"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_bundled"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_lim_med"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_accident"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_critical"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_hospital"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_life_style"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_pet"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_guarn_issue"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',
            "data.agent_level_tele_med"=>'required_if:contract_type,catContrs|integer|bail|lte:9|min:1',

            "data.agent_level_medL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_dentL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_dentSolsticeL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_visionL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_visionSolsticeL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_term_lifeL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_bundledL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_lim_medL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_accidentL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_criticalL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_hospitalL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_life_styleL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_petL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_guarn_issueL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
            "data.agent_level_tele_medL7"=>'required_if:contract_type,catContrsL7|integer|bail|lte:7|min:1',
        ];
    }

    public function messages(): array
    {
        return [
            "agent_id.required"=>"Agent Id is required.",
            "agent_id.integer"=>"Agent Id must be integer.",
            "agent_id.exists"=>"Agent Id is invalid.",
            "contract_type.required"=>"Contract type is required.",
            "contract_type.string"=>"Contract type must be string.",
            "contract_type.in"=>"Contract type has to be N-CatContrs, catContrs, catContrsL7 or carrContrs.",
            "data.agent_level_alc.required_if"=>"Ancillary/LifeStyle contracts level is required.",
            "data.agent_level_alc.integer"=>"Ancillary/LifeStyle contracts level must be integer.",
            "data.agent_level_alc.lte"=>"Ancillary/LifeStyle contracts level must be less than or equal 7.",
            "data.agent_level_pec.required_if"=>"Premier/Elite contracts level is required.",
            "data.agent_level_pec.integer"=>"Premier/Elite contracts level must be integer.",
            "data.agent_level_pec.lte"=>"Premier/Elite level must be less than or equal 7.",
            "data.agent_level_pec_bex.required_if"=>"BEX contracts level is required.",
            "data.agent_level_pec_bex.integer"=>"BEX contracts level must be integer.",
            "data.agent_level_pec_bex.lte"=>"BEX contracts level must be less than or equal 9.",
            "data.agent_level_alc_bex.required_if"=>"BEX contracts level is required.",
            "data.agent_level_alc_bex.integer"=>"BEX contracts level must be integer.",
            "data.agent_level_alc_bex.lte"=>"BEX contracts level must be less than or equal 9.",
            'data.agent_level_fegli_ancillary.required_if'=>'Fegli Ancillary Contract Level is required.',
            'data.agent_level_fegli_ancillary.integer'=>'Fegli Ancillary Contract Level must be integer.',
            'data.agent_level_fegli_ancillary.lte'=>'Fegli Ancillary Contract Level must be less than or equal to 7.',
            'data.agent_level_patriot_ancillary.required_if'=>'Patriot Ancillary Contract Level is required.',
            'data.agent_level_patriot_ancillary.integer'=>'Patriot Ancillary Contract Level must be integer.',
            'data.agent_level_patriot_ancillary.lte'=>'Patriot Ancillary Contract Level must be less than or equal to 9.',

            "data.agent_level_local713.required_if"=>'Local 713 Contract Level is required.',
            "data.agent_level_local713.integer"=>'Local 713 Contract Level must be integer.',
            "data.agent_level_local713.lte"=>'Local 713 Contract Level must be less than or equal to 7.',
            "data.agent_level_local713.min"=>'Local 713 Contract Level must be more than or equal to 1.',
            "data.agent_level_cigna.required_if"=>'Cigna Contract Level is required.',
            "data.agent_level_cigna.integer"=>'Cigna Contract Level must be integer.',
            "data.agent_level_cigna.lte"=>'Cigna Contract Level must be less than or equal to 7.',
            "data.agent_level_cigna.min"=>'Cigna Contract Level must be more than or equal to 1.',
            "data.agent_level_l713Anthem.required_if"=>'L713 Anthem Contract Level is required.',
            "data.agent_level_l713Anthem.integer"=>'L713 Anthem Contract Level must be integer.',
            "data.agent_level_l713Anthem.lte"=>'L713 Anthem Contract Level must be less than or equal to 7.',
            "data.agent_level_l713Anthem.min"=>'L713 Anthem Contract Level must be more than or equal to 1.',
            "data.agent_level_northwellAnthem.required_if"=>'Northwell Anthem Contract Level is required.',
            "data.agent_level_northwellAnthem.integer"=>'Northwell Anthem Contract Level must be integer.',
            "data.agent_level_northwellAnthem.lte"=>'Northwell Anthem Contract Level must be less than or equal to 7.',
            "data.agent_level_northwellAnthem.min"=>'Northwell Anthem Contract Level must be more than or equal to 1.',
            "data.agent_level_IHAHealth.required_if"=>'IHA Health Contract Level is required.',
            "data.agent_level_IHAHealth.integer"=>'IHA Health Contract Level must be integer.',
            "data.agent_level_IHAHealth.lte"=>'IHA Health Contract Level must be less than or equal to 7.',
            "data.agent_level_IHAHealth.min"=>'IHA Health Contract Level must be more than or equal to 1.',
            "data.agent_level_ENANAWU.required_if"=>'ENANAWU Contract Level is required.',
            "data.agent_level_ENANAWU.integer"=>'ENANAWU Contract Level must be integer.',
            "data.agent_level_ENANAWU.lte"=>'ENANAWU Contract Level must be less than or equal to 7.',
            "data.agent_level_ENANAWU.min"=>'ENANAWU Contract Level must be more than or equal to 1.',
            "data.agent_level_lifelineMedical.required_if"=>'Lifeline Medical Contract Level is required.',
            "data.agent_level_lifelineMedical.integer"=>'Lifeline Medical Contract Level must be integer.',
            "data.agent_level_lifelineMedical.lte"=>'Lifeline Medical Contract Level must be less than or equal to 7.',
            "data.agent_level_lifelineMedical.min"=>'Lifeline Medical Contract Level must be more than or equal to 1.',
            "data.agent_level_solsticeBenefits.required_if"=>'Solistice Benefits Contract Level is required.',
            "data.agent_level_solsticeBenefits.integer"=>'Solistice Benefit Contract Level must be integer.',
            "data.agent_level_solsticeBenefits.lte"=>'Solistice Benefit Contract Level must be less than or equal to 7.',
            "data.agent_level_solsticeBenefits.min"=>'Solistice Benefit Contract Level must be more than or equal to 1.',
            "data.agent_level_optionsPlus.required_if"=>'Options Plus Contract Level is required.',
            "data.agent_level_optionsPlus.integer"=>'Options Plus Contract Level must be integer.',
            "data.agent_level_optionsPlus.lte"=>'Options Plus Contract Level must be less than or equal to 7.',
            "data.agent_level_optionsPlus.min"=>'Options Plus Contract Level must be more than or equal to 1.',
            "data.agent_level_metropolitanLifeInsuranceCompany.required_if"=>'Metropolitan Life insurance Company Contract Level is required.',
            "data.agent_level_metropolitanLifeInsuranceCompany.integer"=>'Metropolitan Life insurance Company Contract Level must be integer.',
            "data.agent_level_metropolitanLifeInsuranceCompany.lte"=>'Metropolitan Life insurance Company Contract Level must be less than or equal to 7.',
            "data.agent_level_metropolitanLifeInsuranceCompany.min"=>'Metropolitan Life insurance Company Contract Level must be more than or equal to 1.',
            "data.agent_level_pinnacleBenefitsService.required_if"=>'Pinnacle Benefits Service Contract Level is required.',
            "data.agent_level_pinnacleBenefitsService.integer"=>'Metropolitan Life insurance Company Contract Level must be integer.',
            "data.agent_level_pinnacleBenefitsService.lte"=>'Metropolitan Life insurance Company Contract Level must be less than or equal to 7.',
            "data.agent_level_pinnacleBenefitsService.min"=>'Metropolitan Life insurance Company Contract Level must be more than or equal to 1.',
            "data.agent_level_prudential.required_if"=>'Prudential Contract Level is required.',
            "data.agent_level_prudential.integer"=>'Prudential Company Contract Level must be integer.',
            "data.agent_level_prudential.lte"=>'Prudential Company Contract Level must be less than or equal to 7.',
            "data.agent_level_prudential.min"=>'Prudential Company Contract Level must be more than or equal to 1.',
            "data.agent_level_beyondMedical.required_if"=>'Beyond Medical Contract Level is required.',
            "data.agent_level_beyondMedical.integer"=>'Beyond Medical Company Contract Level must be integer.',
            "data.agent_level_beyondMedical.lte"=>'Beyond Medical Company Contract Level must be less than or equal to 7.',
            "data.agent_level_beyondMedical.min"=>'Beyond Medical Company Contract Level must be more than or equal to 1.',

            "data.agent_level_med.required_if"=>'Medical Contract Level is required.',
            "data.agent_level_med.integer"=>'Medical Contract Level must be integer.',
            "data.agent_level_med.lte"=>'Medical Contract Level must be less than or equal to 9.',
            "data.agent_level_med.min"=>'Medical Contract Level must be more than or equal to 1.',
            "data.agent_level_dent.required_if"=>'Dental Contract Level is required.',
            "data.agent_level_dent.integer"=>'Dental Contract Level must be integer.',
            "data.agent_level_dent.lte"=>'Dental Contract Level must be less than or equal to 9.',
            "data.agent_level_dent.min"=>'Dental Contract Level must be more than or equal to 1.',
            "data.agent_level_vision.required_if"=>'Vision Contract Level is required.',
            "data.agent_level_vision.integer"=>'Vision Contract Level must be integer.',
            "data.agent_level_vision.lte"=>'Vision Contract Level must be less than or equal to 9.',
            "data.agent_level_vision.min"=>'Vision Contract Level must be more than or equal to 1.',
            "data.agent_level_term_life.required_if"=>'Term Life Contract Level is required.',
            "data.agent_level_term_life.integer"=>'Term Life Contract Level must be integer.',
            "data.agent_level_term_life.lte"=>'Term Life Contract Level must be less than or equal to 9.',
            "data.agent_level_term_life.min"=>'Term Life Contract Level must be more than or equal to 1.',
            "data.agent_level_bundled.required_if"=>'Bundles Contract Level is required.',
            "data.agent_level_bundled.integer"=>'Bundles Contract Level must be integer.',
            "data.agent_level_bundled.lte"=>'Bundles Contract Level must be less than or equal to 9.',
            "data.agent_level_bundled.min"=>'Bundles Contract Level must be more than or equal to 1.',
            "data.agent_level_lim_med.required_if"=>'Limited Medical Contract Level is required.',
            "data.agent_level_lim_med.integer"=>'Limited Medical Contract Level must be integer.',
            "data.agent_level_lim_med.lte"=>'Limited Medical Contract Level must be less than or equal to 9.',
            "data.agent_level_lim_med.min"=>'Limited Medical Contract Level must be more than or equal to 1.',
            "data.agent_level_accident.required_if"=>'Accident Contract Level is required.',
            "data.agent_level_accident.integer"=>'Accident Contract Level must be integer.',
            "data.agent_level_accident.lte"=>'Accident Contract Level must be less than or equal to 9.',
            "data.agent_level_accident.min"=>'Accident Contract Level must be more than or equal to 1.',
            "data.agent_level_critical.required_if"=>'Critical Contract Level is required.',
            "data.agent_level_critical.integer"=>'Critical Contract Level must be integer.',
            "data.agent_level_critical.lte"=>'Critical Contract Level must be less than or equal to 9.',
            "data.agent_level_critical.min"=>'Critical Contract Level must be more than or equal to 1.',
            "data.agent_level_hospital.required_if"=>'Hospital Contract Level is required.',
            "data.agent_level_hospital.integer"=>'Hospital Contract Level must be integer.',
            "data.agent_level_hospital.lte"=>'Hospital Contract Level must be less than or equal to 9.',
            "data.agent_level_hospital.min"=>'Hospital Contract Level must be more than or equal to 1.',
            "data.agent_level_life_style.required_if"=>'Lifestyle Contract Level is required.',
            "data.agent_level_life_style.integer"=>'Lifestyle Contract Level must be integer.',
            "data.agent_level_life_style.lte"=>'Lifestyle Contract Level must be less than or equal to 9.',
            "data.agent_level_life_style.min"=>'Lifestyle Contract Level must be more than or equal to 1.',
            "data.agent_level_pet.required_if"=>'Pet Contract Level is required.',
            "data.agent_level_pet.integer"=>'Pet Contract Level must be integer.',
            "data.agent_level_pet.lte"=>'Pet Contract Level must be less than or equal to 9.',
            "data.agent_level_pet.min"=>'Pet Contract Level must be more than or equal to 1.',
            "data.agent_level_guarn_issue.required_if"=>'Guaranteed Issue Contract Level is required.',
            "data.agent_level_guarn_issue.integer"=>'Guaranteed Issue Company Contract Level must be integer.',
            "data.agent_level_guarn_issue.lte"=>'Guaranteed Issue Company Contract Level must be less than or equal to 9.',
            "data.agent_level_guarn_issue.min"=>'Guaranteed Issue Company Contract Level must be more than or equal to 1.',
            "data.agent_level_tele_med.required_if"=>'Telemedicine Contract Level is required.',
            "data.agent_level_tele_med.integer"=>'Telemedicine Company Contract Level must be integer.',
            "data.agent_level_tele_med.lte"=>'Telemedicine Company Contract Level must be less than or equal to 9.',
            "data.agent_level_tele_med.min"=>'Telemedicine Company Contract Level must be more than or equal to 1.',

            "data.agent_level_medL7.required_if"=>'Medical Contract Level is required.',
            "data.agent_level_medL7.integer"=>'Medical Contract Level must be integer.',
            "data.agent_level_medL7.lte"=>'Medical Contract Level must be less than or equal to 7.',
            "data.agent_level_medL7.min"=>'Medical Contract Level must be less than or equal to 1.',
            "data.agent_level_dentL7.required_if"=>'Dental Contract Level is required.',
            "data.agent_level_dentL7.integer"=>'Dental Contract Level must be integer.',
            "data.agent_level_dentL7.lte"=>'Dental Contract Level must be less than or equal to 7.',
            "data.agent_level_dentL7.min"=>'Dental Contract Level must be more than or equal to 1.',
            "data.agent_level_dentSolsticeL7.required_if"=>'Dental Solstice Contract Level is required.',
            "data.agent_level_dentSolsticeL7.integer"=>'Dental Solstice Contract Level must be integer.',
            "data.agent_level_dentSolsticeL7.lte"=>'Dental Solstice Contract Level must be less than or equal to 7.',
            "data.agent_level_dentSolsticeL7.min"=>'Dental Solstice Contract Level must be more than or equal to 1.',
            "data.agent_level_visionL7.required_if"=>'Vision Contract Level is required.',
            "data.agent_level_visionL7.integer"=>'Vision Contract Level must be integer.',
            "data.agent_level_visionL7.lte"=>'Vision Contract Level must be less than or equal to 7.',
            "data.agent_level_visionL7.min"=>'Vision Contract Level must be more than or equal to 1.',
            "data.agent_level_visionSolsticeL7.required_if"=>'Vision Solstice Contract Level is required.',
            "data.agent_level_visionSolsticeL7.integer"=>'Vision Solstice Contract Level must be integer.',
            "data.agent_level_visionSolsticeL7.lte"=>'Vision Solstice Contract Level must be less than or equal to 7.',
            "data.agent_level_visionSolsticeL7.min"=>'Vision Solstice Contract Level must be more than or equal to 1.',
            "data.agent_level_term_lifeL7.required_if"=>'Term Life Contract Level is required.',
            "data.agent_level_term_lifeL7.integer"=>'Term Life Contract Level must be integer.',
            "data.agent_level_term_lifeL7.lte"=>'Term Life Contract Level must be less than or equal to 7.',
            "data.agent_level_term_lifeL7.min"=>'Term Life Contract Level must be more than or equal to 1.',
            "data.agent_level_bundledL7.required_if"=>'Bundles Contract Level is required.',
            "data.agent_level_bundledL7.integer"=>'Bundles Contract Level must be integer.',
            "data.agent_level_bundledL7.lte"=>'Bundles Contract Level must be less than or equal to 7.',
            "data.agent_level_bundledL7.min"=>'Bundles Contract Level must be more than or equal to 1.',
            "data.agent_level_lim_medL7.required_if"=>'Limited Medical Contract Level is required.',
            "data.agent_level_lim_medL7.integer"=>'Limited Medical Contract Level must be integer.',
            "data.agent_level_lim_medL7.lte"=>'Limited Medical Contract Level must be less than or equal to 7.',
            "data.agent_level_lim_medL7.min"=>'Limited Medical Contract Level must be more than or equal to 1.',
            "data.agent_level_accidentL7.required_if"=>'Accident Contract Level is required.',
            "data.agent_level_accidentL7.integer"=>'Accident Contract Level must be integer.',
            "data.agent_level_accidentL7.lte"=>'Accident Contract Level must be less than or equal to 7.',
            "data.agent_level_accidentL7.min"=>'Accident Contract Level must be more than or equal to 1.',
            "data.agent_level_criticalL7.required_if"=>'Critical Contract Level is required.',
            "data.agent_level_criticalL7.integer"=>'Critical Contract Level must be integer.',
            "data.agent_level_criticalL7.lte"=>'Critical Contract Level must be less than or equal to 7.',
            "data.agent_level_criticalL7.min"=>'Critical Contract Level must be more than or equal to 1.',
            "data.agent_level_hospitalL7.required_if"=>'Hospital Contract Level is required.',
            "data.agent_level_hospitalL7.integer"=>'Hospital Contract Level must be integer.',
            "data.agent_level_hospitalL7.lte"=>'Hospital Contract Level must be less than or equal to 7.',
            "data.agent_level_hospitalL7.min"=>'Hospital Contract Level must be more than or equal to 1.',
            "data.agent_level_life_styleL7.required_if"=>'Lifestyle Contract Level is required.',
            "data.agent_level_life_styleL7.integer"=>'Lifestyle Contract Level must be integer.',
            "data.agent_level_life_styleL7.lte"=>'Lifestyle Contract Level must be less than or equal to 7.',
            "data.agent_level_life_styleL7.min"=>'Lifestyle Contract Level must be more than or equal to 1.',
            "data.agent_level_petL7.required_if"=>'Pet Contract Level is required.',
            "data.agent_level_petL7.integer"=>'Pet Contract Level must be integer.',
            "data.agent_level_petL7.lte"=>'Pet Contract Level must be less than or equal to 7.',
            "data.agent_level_petL7.min"=>'Pet Contract Level must be more than or equal to 1.',
            "data.agent_level_guarn_issueL7.required_if"=>'Guaranteed Issue Contract Level is required.',
            "data.agent_level_guarn_issueL7.integer"=>'Guaranteed Issue Company Contract Level must be integer.',
            "data.agent_level_guarn_issueL7.lte"=>'Guaranteed Issue Company Contract Level must be less than or equal to 7.',
            "data.agent_level_guarn_issueL7.min"=>'Guaranteed Issue Company Contract Level must be more than or equal to 1.',
            "data.agent_level_tele_medL7.required_if"=>'Telemedicine Contract Level is required.',
            "data.agent_level_tele_medL7.integer"=>'Telemedicine Company Contract Level must be integer.',
            "data.agent_level_tele_medL7.lte"=>'Telemedicine Company Contract Level must be less than or equal to 7.',
            "data.agent_level_tele_medL7.min"=>'Telemedicine Company Contract Level must be more than or equal to 1.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}