<?php

namespace App\Http\Requests\ManageAgent;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\NumVerifyRule;

class UpdateDisplaySettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */

    public function rules()
    {

        return [
            'display_phone' => ['required', new NumVerifyRule],
            'display_email' => 'required|email',
            'agent_id' => 'required|exists:mysql.agent_info,agent_id',
            'display_name' => 'required',
            'banner_text' => 'required',
            'help_text' => 'required',
            'tagline' => 'required',
            'loginUserId' => 'nullable',
            'loginUserName' => 'nullable'
        ];
    }

    public function messages()
    {
        return [
            'display_phone.required' => 'Display Phone number is required',
            'display_email.agent_id' => 'Display Email is required',
            'display_email.email' => 'Display Email must be valid',
            'agent_id.agent_id' => 'Agent Id is required',
            'agent_id.exists' => 'Agent Id must be valid',
            'display_name.required' => 'Display name is required',
            'banner_text.required' => 'Banner text is required',
            'help_text.required' => 'Help text is required',
            'tagline.required' => 'Tagline is required',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}