<?php

namespace App\Http\Requests\ManageAgent;

use App\AgentInfo;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class UpdateUplineRepRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            "agent_ga"=> "required|integer|bail|exists:agent_info,agent_id",
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable"
        ];
    }

    public function messages()
    {
        return [
            'agent_id.required' => 'Agent is required.',
            'agent_id.integer' => 'Agent Id should be integer.',
            'agent_id.exists' => 'Agent not found.',
            'agent_ga.required'=>'Upline Rep is required.',
            'agent_ga.integer' => 'Upline Rep Id should be integer.',
            'agent_ga.exists' => 'Upline Rep not found.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
