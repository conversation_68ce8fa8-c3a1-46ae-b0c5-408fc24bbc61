<?php

namespace App\Http\Requests\ManageAgent;

use App\Rules\AgentEmailRule;
use App\Rules\AgentPhoneRule;
use App\Rules\AgentSsnRule;
use App\Rules\NeverBounceRule;
use App\Rules\NumVerifyRule;
use App\Rules\ValidateSSN;
use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class EditPersonalInfoRequest extends FormRequest
{

    private $customValidationService;

    public function __construct(CustomValidationService $customValidationService)
    {
        $this->customValidationService = $customValidationService;
    }
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $agentId = Request::get('agent_id');
        $rules =  [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            "agent_fname" => "required",
            "agent_lname" => "required",
            "agent_email" => ["required",new NeverBounceRule(),"bail",new AgentEmailRule($agentId)],
            "agent_phone1" => ["required", "bail", "digits:10", "bail",
                function ($attribute, $value, $fail) {
                    $mobileValidation = $this->customValidationService->validatePhoneNumVerify($value, true);
                    if (!$mobileValidation) {
                        $fail('Please enter valid mobile number');
                    }else if($mobileValidation['data']['line_type'] !== 'mobile') {
                        $fail('Only mobile numbers are allowed');
                    }
            }, "bail",new AgentPhoneRule($agentId, 'agent_phone1')],
            "agent_phone2" => ["nullable","digits:10", "bail", new NumVerifyRule(), 'bail', new AgentPhoneRule($agentId, 'agent_phone2')],
            "agent_dob" => "required|date|bail|date_format:Y-m-d",
            "agent_ssn" => ["required", "bail", new ValidateSSN(),"bail", new AgentSsnRule($agentId)],
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable"
        ];
        return $rules;
    }

    public function messages()
    {
        $messages = [
            "agent_id.required" => "Agent is required.",
            "agent_id.integer" => "Agent Id should be number.",
            "agent_id.exists" => "Agent not found.",
            "agent_fname.required" => "First name is required.",
            "agent_lname.required" => "Last name is required.",
            "agent_email.required" => "Email is required.",
            "agent_dob.required" => "Date of birth is required.",
            "agent_dob.date" => "Date of birth should be date.",
            "agent_dob.date_format" => "Date of birth date should in format Y-m-d.",
            "agent_ssn.required" => "Social security number is required.",
            "agent_ssn.integer" => "Social security number should be number.",
            "agent_ssn.digits" => "Social security number must have 9 digits.",
            "agent_phone1.integer" => "Mobile number should be number.",
            "agent_phone1.digits" => "Mobile number must have 10 digits.",
            "agent_phone2.digits" => "Phone number must have 10 digits.",

        ];
        return $messages;
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
