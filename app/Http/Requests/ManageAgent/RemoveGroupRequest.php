<?php

namespace App\Http\Requests\ManageAgent;

use App\Rules\ValidateAgentWebsiteRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class RemoveGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable"
        ];
    }

    public function messages()
    {
        return [
            'agent_id.required' => 'Agent is required.',
            'agent_id.integer' => 'Agent Id should be integer.',
            'agent_id.exists' => 'Agent not found.',
            'group_id.required' => 'Group is required.',
            'group_id.integer' => 'Group Id should be integer.',
            'group_id.exists' => 'Group not found.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
