<?php

namespace App\Http\Requests\ManageAgent;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class ScheduleUpdateUplineRepRequest extends FormRequest
{
    private $next_month;

    public function __construct(array $query = [], array $request = [], array $attributes = [], array $cookies = [], array $files = [], array $server = [], $content = null)
    {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);
        $this->next_month = date('Y-m-d');
    }

    public function prepareForValidation()
    {
        if (request()->isMethod('GET')) {
            $this->merge(['agent_id' => $this->route('agentId')]);
        }
        else{
            if(request()->get('effective_date') == null) {
                $this->merge(['effective_date' => date('Y-m-d')]);
                }
        }
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        if (request()->isMethod('GET')) {
            return [
                'agent_id' => 'required|integer|bail|exists:agent_info,agent_id'
            ];
        } else {
            return [
                'agent_id' => 'required|integer|bail|exists:agent_info,agent_id',
                'agent_ga' => 'required|integer|bail|exists:agent_info,agent_id|different:agent_id',
                'effective_date' => 'required|date_format:Y-m-d|after_or_equal:' . $this->next_month,
                'schedule_type' => 'nullable'
            ];
        }
    }

    public function messages(): array
    {
        return [
            'agent_id.required' => 'Agent ID is required',
            'agent_id.integer' => 'Agent ID must be an integer.',
            'agent_id.exists' => 'Invalid Agent ID. Agent ID does not exist.',
            'agent_ga.required' => 'Upline Agent ID is required',
            'agent_ga.integer' => 'Upline Agent ID must be an integer.',
            'agent_ga.exists' => 'Invalid Upline Agent ID. Upline Agent ID does not exist.',
            'agent_ga.different' => 'Upline Agent ID cannot be same as Agent ID.',
            'effective_date.required' => 'Effective Date is required.',
            'effective_date.date_format' => 'Effective Date must be date in YYYY-mm-dd format.',
            'effective_date.after_or_equal' => "Effective Date must be a future date: equal or after next month ($this->next_month)",
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
