<?php

namespace App\Http\Requests\ManageAgent;

use App\Rules\ValidateAgentWebsiteRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class UpdateWebsiteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $agentId = Request::get('agent_id');
        return [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            "agent_web"=> ["required","bail","alpha_num",new ValidateAgentWebsiteRule($agentId)],
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable"
        ];
    }

    public function messages()
    {
        return [
            'agent_id.required' => 'Agent is required.',
            'agent_id.integer' => 'Agent Id should be integer.',
            'agent_id.exists' => 'Agent not found.',
            'agent_web.alpha_num' => 'Web access code must only contain alphabets and numbers.',
            'agent_web.required'=>'Web access code is required.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], Response::HTTP_UNPROCESSABLE_ENTITY));
    }
}
