<?php

namespace App\Http\Requests\ManageAgent;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class ChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "confirm_password" => 'required',
            "agent_email" => 'required | exists:agent_info,agent_email',

        ];
    }

    public function messages()
    {
        return [
            'confirm_password.required' => "Confirm Password is Required",
            'agent_email.required' => "Agent Email is Required",
            'agent_email.exists' => "Agent not found."
        ];
    }

    protected function failedValidation(Validator $validator)
    {

        $errorsList = (new ValidationException($validator))->errors();
        $errors = ['status' => 'error',
            'message' => array_values($errorsList)[0][0],
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
