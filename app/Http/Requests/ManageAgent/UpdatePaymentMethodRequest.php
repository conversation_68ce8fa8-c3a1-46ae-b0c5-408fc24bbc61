<?php

namespace App\Http\Requests\ManageAgent;

use App\AgentAchPaymentBankDetail;
use App\AgentInfo;
use App\Rules\RoutingNumberBankNameRule;
use App\Rules\RoutingNumberRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class UpdatePaymentMethodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $paymentAch = AgentInfo::AGENT_PAYMENT_METHOD_ACH;
        $paymentCheck = AgentInfo::AGENT_PAYMENT_METHOD_CHECK;
        $paymentText = "{$paymentAch},{$paymentCheck}";
        return [
            "agent_id" => "required|integer|bail|exists:agent_info,agent_id",
            "payment_method" => "required|in:" . $paymentText,
            "send_email" => "nullable|integer",
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable"
        ];
    }

    public function messages()
    {
        $paymentAch = AgentInfo::AGENT_PAYMENT_METHOD_ACH;
        $paymentCheck = AgentInfo::AGENT_PAYMENT_METHOD_CHECK;
        return [
            'payment_method.required' => 'Payment Method is required.',
            'payment_method.in' => "Payment Method should be either {$paymentAch} or {$paymentCheck}.",
            'agent_id.required' => 'Agent is required.',
            'agent_id.integer' => 'Agent Id should be integer.',
            'agent_id.exists' => 'Agent not found.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
