<?php

namespace App\Http\Requests\ManageAgent;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\JsonResponse;
use App\Rules\TimeZone;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Foundation\Http\FormRequest;

class UpdateBusinessProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $sunFormat = '';
        $satFormat = '';
        if ($this->request->get('sun_open')) {
            $sunFormat ='|date_format:h:i A';
        }
        if ($this->request->get('sat_open')) {
            $satFormat = '|date_format:h:i A';
        }

        return [
            "from" => ['required', 'date_format:h:i A',function($attribute, $val, $fail) {
                if($message = $this->validateTimeCompare('from','to')){
                    $fail($message);
                }
            }],
            "to" => ['required', 'date_format:h:i A'],
            "sat_open" => 'required',
            'sat_from' => "required_if:sat_open,==,true$satFormat",
            'sat_to' => "required_if:sat_open,==,true$satFormat",
            "sun_open" => 'required',
            'sun_from' => "required_if:sun_open,==,true$sunFormat",
            'sun_to' => "required_if:sun_open,==,true$sunFormat",
            'bio' => "required",
            'timezone' => ["required", new TimeZone()],
        ];
    }

    public function validateTimeCompare($from,$to) {

        $fromDate = strtotime($this->request->get($from));
        $toDate = strtotime($this->request->get($to));
        if($fromDate > $toDate) {
           return 'Ending date should be higher.';
        }
        if ($this->request->get('sun_open')) {
            $fromDate = strtotime($this->request->get('sun_from'));
            $toDate = strtotime($this->request->get('sun_to'));
            if($fromDate > $toDate) {
                return 'Sunday ending date should be higher.';
            }
        }
        if ($this->request->get('sat_open')) {
            $fromDate = strtotime($this->request->get('sat_from'));
            $toDate = strtotime($this->request->get('sat_to'));
            if($fromDate > $toDate) {
                return 'Saturday ending date should be higher.';
            }
        }
    }

    public function messages()
    {
        return [
            'from.required' => "The Monday-Friday from field is required",
            'to.required' => "The Monday-Friday end field is required"
        ];
    }

    protected function failedValidation(Validator $validator)
    {

        $errorsList = (new ValidationException($validator))->errors();
        $errors = ['status' => 'error',
            'message' => array_values($errorsList)[0][0],
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}