<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class UpdatePrudentialClaimRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "claim_id" => ["required", "exists:mysql.prudential_claims,id"],
            "policy_id" => ["required"],
            'plan_id' => ["required"],
            "description" => ["nullable"],
            "files" => ["nullable", "array"],
            "files.*" => ["file", "max:10240"],
            "claim_type" => ["required", "string", "bail", "in:ADMIN"]
        ];
    }

    public function messages()
    {
        return [
            "claim_id.required" => "Claim ID is required.",
            "claim_id.exists" => "Claim ID does not exist.",
            'policy_id.required' => 'Policy ID is required.',
            'plan_id.required' => 'Plan ID is required.',
            "description.required" => "Description is required.",
            "files.required" => "File is required.",
            "files.*.file" => "File must be file.",
            "files.*.max" => "Please upload file smaller than 10 MB.",
            "claim_type.required" => "Claim type is required.",
            "claim_type.in" => "Claim type has to be ADMIN"
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json([
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
            'errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
