<?php

namespace App\Http\Requests\Agent;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class AgentCodeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        if (request()->isMethod('GET'))
            $this->merge([
                'agent_code' => $this->route('agentCode')
            ]);
    }

    public function rules(): array
    {
        $rules = [
            'agent_id'      => 'required|integer|bail|exists:App\AgentInfo,agent_id',
            'agent_code'    => "required|bail|string|size:8|regex:/[A-Z]{4}\d{4}/i|unique:App\AgentInfo,agent_code,{$this->get('agent_id')},agent_id"
        ];

        if (request()->isMethod('GET'))
            unset($rules['agent_id']);

        return $rules;
    }

    public function messages(): array
    {
        return [
            'agent_id.required'     => 'Agent ID is required.',
            'agent_id.integer'      => 'Agent ID must be represented in integer.',
            'agent_id.exists'       => 'Invalid Agent ID, does not exist.',
            'agent_code.required'   => 'Agent Code is required.',
            'agent_code.unique'     => 'Agent Code is in use. Agent Code must be unique.',
            'agent_code.size'       => 'Agent Code must be 8 digits.',
            'agent_code.regex'      => 'Invalid pattern. Agent Code must 4 letters followed by 4 numbers.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
