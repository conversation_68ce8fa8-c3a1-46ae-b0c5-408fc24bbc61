<?php

namespace App\Http\Requests\Agent;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class AgentLeadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        if (request()->isMethod('DELETE') || request()->isMethod('PUT')) {
            $this->merge([
                'lead_id' => $this->route('leadId')
            ]);
        }
        if (request()->isMethod('PUT')) {
            $this->merge([
               'agent_id' => $this->query('agent_id')
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        if (request()->isMethod('POST')) {
            $rules = [
                'question_id' => 'required|integer|bail|exists:assistant_question,id',
                'device_id' => 'nullable|string',
                'answer' => 'required|string',
                'web_url' => 'nullable|string'
            ];
        }

        if (request()->isMethod('DELETE') || request()->isMethod('PUT')) {
            $rules = [
                'lead_id' => 'required|integer|bail|exists:assistant_question_answer,id'
            ];
        }

        if (request()->isMethod('POST') || request()->isMethod('PUT')) {
            $rules = array_merge($rules, [
                'agent_id' => 'required|integer|bail|exists:agent_info,agent_id'
            ]);
        }

        return $rules;
    }

    public function messages(): array
    {
        if (request()->isMethod('DELETE')) {
            $message = [
                'lead_id.required' => 'Lead ID is required.',
                'lead_id.integer' => 'Lead ID must be represented in integer.',
                'lead_id.exists' => 'Invalid Lead ID. This Lead does not exist.'
            ];
        } else {
            $message = [
                'agent_id.required' => 'Agent ID is required.',
                'agent_id.integer' => 'Agent ID must be represented in integer.',
                'agent_id.exists' => 'Invalid Agent ID. The Agent ID does not exist.',
                'question_id.required' => 'Question ID is required.',
                'question_id.integer' => 'Question ID must be represented in integer.',
                'question_id.exists' => 'Question ID. The Question ID does not exist.',
                'device_id.string' => 'Device ID must be represented in string.',
                'answer.string' => 'Answer must be represented in string.',
                'web_url.string' => 'Web URL must be represented in string.'
            ];
        }

        return $message;
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
