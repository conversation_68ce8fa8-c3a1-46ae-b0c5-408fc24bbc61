<?php

namespace App\Http\Requests\Agent;

use App\Http\Resources\ErrorResource;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class RemoveRepLicenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            "license_id" => "required|integer|exists:agent_license,license_id",
        ];
    }

    public function messages(): array
    {
        return [
            'license_id.required' => 'License ID is required.',
            'license_id.integer' => 'License ID must be an integer.',
            'license_id.exists' => 'Invalid License ID. License does not exist.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
