<?php

namespace App\Http\Requests\Agent;

use App\Http\Resources\ErrorResource;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class AgentInfoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agent_email' => [
                'required',
                'email',
                function ($attribute, $value, $fail) {
                    $existsInAgentInfo = DB::table('agent_info')->where('agent_email', $value)->exists();
                    $existsInAgentUsers = DB::table('agent_users')->where('username', $value)->exists();

                    if (!$existsInAgentInfo && !$existsInAgentUsers) {
                        $fail('The selected ' . $attribute . ' is invalid.');
                    }
                },
            ],
        ];
    }
    public function messages(): array
    {
        return [
            'agent_email.required' => 'Agent email is required.'
        ];
    }
    protected function failedValidation(Validator $validator)
    {

        $errorsList = (new ValidationException($validator))->errors();
        $counter = 0;
        foreach($errorsList as $key => $errorList) {
            $errors[$counter]['key']        = $key;
            $errors[$counter]['message']    = $errorList[0];
            $counter ++;
        }
        $response = [
            'status'    => false,
            'code'      => 400,
            'errors'    => $errors
        ];
        throw new HttpResponseException( response()->json($response, 400) );
    }
}
