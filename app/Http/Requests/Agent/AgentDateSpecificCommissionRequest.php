<?php

namespace App\Http\Requests\Agent;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class AgentDateSpecificCommissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation()
    {
        $this->merge([
            'agent_id' => $this->route('agentId'),
            'year' => $this->route('year'),
            'month' => $this->route('month'),
            'userid' => $this->get('userid')
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agent_id' => 'required|integer|bail|exists:agent_info,agent_id',
            'year' => 'required|integer|date_format:Y|before_or_equal:' . date('Y'),
            'month' => 'required|numeric|date_format:m'
        ];
    }

    public function messages()
    {
        return [
            'agent_id.required' => 'Agent ID is required.',
            'agent_id.integer' => 'Agent ID must be represented in integer.',
            'agent_id.exists' => 'Invalid Agent ID. The Agent ID does not exist.',
            'year.required' => 'Year is required.',
            'year.integer' => 'Year must be represented in integer.',
            'year.date_format' => 'Year must be represented in \'20XX\' format.',
            'month.required' => 'Month is required.',
            'month.numeric' => 'Month must be represented in number.',
            'month.date_format' => 'Month must be in \'MM\' format.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), JsonResponse::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
