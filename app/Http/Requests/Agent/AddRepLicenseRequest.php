<?php

namespace App\Http\Requests\Agent;

use App\Http\Resources\ErrorResource;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class AddRepLicenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            "agent_id" => "required|exists:agent_info,agent_id",
            "state" => "required|exists:states,abbrev",
            "license_number" => "required|numeric|gt:0",
            "license_exp_date" => "required|date|date_format:Y-m-d|after_or_equal:".date('Y-m-d', strtotime(' +1 day')),
            "license_resident" => "required|bool",
            "license_doc" => "nullable|file|mimes:jpeg,png,jpg,webp,pdf|max:4048",
        ];
    }

    public function messages(): array
    {
        return [
            'agent_id.required' => 'Agent ID is required.',
            'agent_id.exists' => 'Invalid Agent ID. The Agent does not exist.',
            'state.required' => 'State is required.',
            'state.exists' => 'Invalid State abbreviation.',
            'license_number.required' => 'License number is required.',
            'license_number.numeric' => 'License number must be numeric.',
            'license_number.gt' => 'Invalid license number.',
            'license_exp_date.required' => 'License Expiry Date number is required.',
            'license_exp_date.date_format' => 'Invalid License Expiry Date.',
            'license_exp_date.after_or_equal' => 'License Expiry Date must be a future date.',
            'license_resident.required' => 'License Resident is required.',
            'license_resident.bool' => 'License Resident must be 1 or 0.',
            'license_doc.file' => 'License Document should be a valid File.',
            'license_doc.mimes' => 'License Document must be in either of these formats: JPEG, PNG, JPG, WEBP, PDF.',
            'license_doc.max' => 'License Document file should not be greater than 4 MB.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
