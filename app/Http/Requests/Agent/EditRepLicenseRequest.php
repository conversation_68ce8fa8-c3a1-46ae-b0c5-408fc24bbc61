<?php

namespace App\Http\Requests\Agent;

use App\Http\Resources\ErrorResource;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class EditRepLicenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            "license_id" => 'required|integer|exists:agent_license,license_id',
            "state" => "required|exists:states,abbrev",
            "license_number" => "required|numeric|gt:0|unique:agent_license,license_number,{$this->request->get('license_id')},license_id",
            "license_exp_date" => "required|date|date_format:Y-m-d",
            "license_status" => "required|alpha|in:A,T,D,P",
            "license_resident" => "required|bool",
            "license_doc" => "nullable|file|mimes:jpeg,png,jpg,webp,pdf|max:4048",
        ];
    }

    public function messages(): array
    {
        return [
            'license_id.required' => 'License ID is required.',
            'license_id.integer' => 'License ID must be an integer.',
            'license_id.exists' => 'Invalid License ID. The License does not exist.',
            'state.required' => 'State is required.',
            'state.exists' => 'Invalid State abbreviation.',
            'license_number.required' => 'License Number is required.',
            'license_number.numeric' => 'License Number must be numeric.',
            'license_number.gt' => 'Invalid License Number.',
            'license_number.unique' => 'License Number already exists.',
            'license_exp_date.required' => 'License Expiry Date number is required.',
            'license_exp_date.date_format' => 'Invalid License Expiry Date.',
            'license_status.required' => 'License Status is required.',
            'license_status.alpha' => 'License Status must be an alphabet.',
            'license_status.in' => 'License Status can be either Active(A), Terminated(T), Disabled(D), or Pending(P).',
            'license_resident.required' => 'License Resident is required.',
            'license_resident.bool' => 'License Resident must be 1 or 0.',
            'license_doc.file' => 'License Document should be a valid File.',
            'license_doc.mimes' => 'License Document must be in either of these formats: JPEG, PNG, JPG, WEBP, PDF.',
            'license_doc.max' => 'License Document file should not be greater than 4 MB.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            new ErrorResource([
                'statusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => $errors,
            ]), Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
