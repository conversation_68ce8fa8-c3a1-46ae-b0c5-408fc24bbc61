<?php

namespace App\Http\Requests;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;

class PaymentBankAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'bank_accountname' => 'required',
            'bank_name' => 'required',
            'bank_routing' => 'required|integer|digits:9',
            'bank_account' => 'required|integer',
        ];
    }
    public function messages()
    {
        return [
            'bank_accountname.required' => 'Bank Account Name is required.',
            'bank_name.required' => 'Bank Name is required.',
            'bank_routing.required' => 'Routing Number is required.',
            'bank_account.required' => 'Bank Account is needed.',
            'bank_account.integer' => 'Bank Account should be number.',
            'bank_routing.integer' => 'Routing Number should be number.',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
