<?php

namespace App\Http\Requests\GroupEft;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class SetPrimaryGroupEftBankRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "bank_id" => "required|integer|exists:group_eft,bank_id",
            "loginUserId" => "nullable",
            "loginUserName" => "nullable"
        ];
    }

    public function messages()
    {
        return [
            'bank_id.exists' => 'Bank Id not found.',
            'bank_id.required' => 'Bank Id is required.',
            'bank_id.integer' => 'Bank Id should be integer.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
