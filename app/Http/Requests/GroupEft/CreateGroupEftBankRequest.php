<?php

namespace App\Http\Requests\GroupEft;

use App\GroupEft;
use App\Rules\RoutingNumberBankNameRule;
use App\Rules\RoutingNumberRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class CreateGroupEftBankRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $checking = GroupEft::ACCOUNT_TYPE_CHECKING;
        $saving = GroupEft::ACCOUNT_TYPE_SAVING;
        $individual = GroupEft::ACCOUNT_HOLDER_TYPE_INDIVIDUAL;
        $company = GroupEft::ACCOUNT_HOLDER_TYPE_COMPANY;
        $typeText = "{$saving},{$checking}";
        $holderText = "{$individual},{$company}";
        $routingNumber = Request::get('bank_routing');
        $validation = Request::get('validation') == 1;
        $rules = [
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "bank_name" => ['required'],
            "bank_routing" => ['required','numeric','bail','digits:9'],
            "bank_account" => ['required', 'numeric', 'bail', 'digits_between:4,17', 'not_regex:/^([0-9])\1{3,16}$/i'],
            "bank_accountname" => "required",
            "is_primary" => "nullable|integer|in:0,1",
            "account_type" => "required|in:" . $typeText,
            "account_holder_type" => "required|in:" . $holderText,
            "loginUserId" => "nullable",
            "loginUserName" => "nullable"
        ];
        if ($validation) {
            $rules['bank_name'] = ['required',new RoutingNumberBankNameRule($routingNumber)];
            $rules['bank_routing'] = ['required','numeric','bail','digits:9',new RoutingNumberRule()];
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'group_id.required' => 'Group ID is required.',
            'group_id.integer' => 'Group ID must be an integer.',
            'group_id.exists' => 'Group ID not found.',
            'bank_name.required' => 'Bank name is required.',
            'bank_routing.required' => 'Routing number is required.',
            'bank_routing.numeric' => 'Routing number should be in number.',
            'bank_routing.digits' => 'Routing number should be of 9 digits.',
            'bank_account.required' => 'Bank Account Number is required.',
            'bank_account.numeric' => 'Bank Account Number should be in number.',
            'bank_account.digits_between' => 'Bank Account Number must have 4 to 17 digits.',
            'bank_account.not_regex' => 'Invalid Bank Account Number format.',
            'is_primary.integer' => 'Is Primary must be an an integer.',
            'is_primary.in' => 'Is Primary must be either 0(No) or 1(Yes).',
            'account_type.required' => 'Account type is required.',
            'account_type.in' => 'Account type should be either checking or savings.',
            'account_holder_type.required' => 'Account holder type is required.',
            'account_holder_type.in' => 'Account holder type should be either individual or company.'
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
