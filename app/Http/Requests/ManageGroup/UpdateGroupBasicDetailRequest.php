<?php

namespace App\Http\Requests\ManageGroup;

use App\Rules\NeverBounceRule;
use App\Rules\NumVerifyRule;
use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class UpdateGroupBasicDetailRequest extends FormRequest
{

    private $customValidationService;

    public function __construct(CustomValidationService $customValidationService)
    {
        $this->customValidationService = $customValidationService;
    }
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $groupId = Request::get('group_id');
        return [
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable",
            "contact_first_name"=> "required",
            "contact_last_name"=> "required",
            "mobile"=> ["nullable","numeric","bail","digits:10","bail","unique:group_info,gmobile,{$groupId},gid","bail",
            function ($attribute, $value, $fail) {
                $mobileValidation = $this->customValidationService->validatePhoneNumVerify($value, true);
                if (!$mobileValidation) {
                    $fail('Please enter valid mobile number');
                }else if($mobileValidation['data']['line_type'] !== 'mobile') {
                    $fail('Only mobile numbers are allowed');
                }
        }],
            "phone"=> ["required","numeric","bail","digits:10","bail","unique:group_info,gphone,{$groupId},gid","bail",
            function ($attribute, $value, $fail) {
                $phoneValidation = $this->customValidationService->validatePhoneNumVerify($value, true);
                if (!$phoneValidation) {
                    $fail('Please enter valid phone number');
                }else if($phoneValidation['data']['line_type'] !== 'mobile') {
                    $fail('Only mobile numbers are allowed');
                }
            }],
            "email"=>  ["required","unique:group_info,gemail,{$groupId},gid","bail",new NeverBounceRule()],
            "industry"=> "nullable",
            "tax_id"=> "required|digits:9|bail|unique:group_info,gein,".$groupId.',gid',
            'cover_img' => 'nullable|mimes:jpg,jpeg,JPEG,PNG,JPG,Jpeg,Jpg,Png,png|bail|max:20480',
            'logo' => 'nullable|mimes:jpg,jpeg,JPEG,PNG,JPG,Jpeg,Jpg,Png,png|bail|max:20480',
        ];
    }

    public function messages()
    {
        return [
            'group_id.required' => 'Group is required.',
            'group_id.integer' => 'Group Id should be integer.',
            'group_id.exists' => 'Group not found.',
            'contact_first_name.required' => 'Contact first name is required.',
            'contact_last_name.required' => 'Contact last name is required.',
            'phone.numeric' => 'Phone must be numeric.',
            'phone.digits' => 'Phone must have 10 digits.',
            'mobile.numeric' => 'Mobile must be numeric.',
            'mobile.digits' => 'Mobile must have 10 digits.',
            'email.required' => 'Email is required.',
            'tax_id.digits' => 'Tax Id must have 9 digits.',
            'tax_id.numeric' => 'Tax Id must be numeric.',
            'tax_id.unique' => 'Tax Id must be unique.',
            'email.unique' => 'Email must be unique.',
            'phone.unique' => 'Phone must be unique.',
            'mobile.unique' => 'Mobile must be unique.',
            'cover_img.mimes' => 'Cover Image must be type of .jpeg,.jpg or .png format.',
            'cover_img.max' => 'Cover Image must be less than 20 MB.',
            'logo.mimes' => 'Business Logo must be type of .jpeg,.jpg or .png format.',
            'logo.max' => 'Business Logo must be less than 20 MB.',
//            'industry.required' => 'Group Industry is required.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
