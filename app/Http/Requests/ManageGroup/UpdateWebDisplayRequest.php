<?php

namespace App\Http\Requests\ManageGroup;

use App\Rules\NeverBounceRule;
use App\Rules\NumVerifyRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class UpdateWebDisplayRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "web_display_name"=> "required",
            "web_display_number"=> ["required","digits:10","bail",new NumVerifyRule(), "bail"],
            "web_display_email"=>  ["required",new NeverBounceRule()],
            "web_access" => [
                "required", "alpha_num",
                Rule::unique('group_info', 'gwebaccess')
                    ->ignore($this->get('group_id'), 'gid')
            ],
            // "tagline" => 'required',
            // "bio" => 'required',
            "web_display_sa_fees"=> "nullable|numeric|bail|gt:0",
            "loginUserId" => 'nullable',
            "loginUserName" => 'nullable',
        ];
    }

    public function messages()
    {
        return [
            'group_id.required' => 'Group is required.',
            'group_id.integer' => 'Group Id should be integer.',
            'group_id.exists' => 'Group not found.',
            'web_display_name.required' => 'Web display name is required.',
            'web_display_number.required' => 'Web display number is required.',
            'web_display_number.digits' => 'Web display number must have 10 digits.',
            'web_display_email.required' => 'Web display email is required.',
            'web_access.required' => 'Web access is required.',
            'web_access.alpha_num' => 'Web access may contain only alphanumeric characters.',
            'web_access.unique' => 'Web access must be unique. Provided value is already in use.',
            // 'tagline.required' => 'Tagline is required.',
            // 'bio.required' => 'Group Bio is required.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], Response::HTTP_UNPROCESSABLE_ENTITY));
    }
}
