<?php

namespace App\Http\Requests\ManageGroup;

use App\Rules\NeverBounceRule;
use App\Rules\NumVerifyRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class UpdateGroupBillingDetailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable",
            "contact_first_name"=> "required",
            "contact_last_name"=> "required",
            "phone"=> ["required","numeric","digits:10","bail",new NumVerifyRule(), "bail"],
            "email"=>  ["required",new NeverBounceRule()],
            "fax"=> "nullable",
        ];
    }

    public function messages()
    {
        return [
            'group_id.required' => 'Group is required.',
            'group_id.integer' => 'Group Id should be integer.',
            'group_id.exists' => 'Group not found.',
            'contact_first_name.required' => 'Contact first name is required.',
            'contact_last_name.required' => 'Contact last name is required.',
            'phone.required' => 'Phone is required.',
            'phone.numeric' => 'Phone must be numeric.',
            'phone.digits' => 'Phone must have 10 digits.',
            'email.required' => 'Email is required.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
