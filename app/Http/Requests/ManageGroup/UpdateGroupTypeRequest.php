<?php

namespace App\Http\Requests\ManageGroup;

use App\Service\GroupService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class UpdateGroupTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function groupTypes(){
        $groupService = new GroupService();
        $groupTypes = $groupService->getGroupTypes();
        return implode(',',$groupTypes);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $groupTypes = $this->groupTypes();
        return [
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "group_type"=> "required|in:{$groupTypes}",
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable",
            "group_contribution"=>"nullable"
        ];
    }

    public function messages()
    {
        $groupTypes = $this->groupTypes();
        return [
            'group_id.required' => 'Group is required.',
            'group_id.integer' => 'Group Id should be integer.',
            'group_id.exists' => 'Group not found.',
            'group_type.required'=>'Group type is required.',
            'group_type.in' => "Group type must be either {$groupTypes}.",
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
