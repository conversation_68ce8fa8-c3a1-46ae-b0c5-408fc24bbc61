<?php

namespace App\Http\Requests\ManageGroup;

use App\GroupAddress;
use App\GroupBillingAddress;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class DeleteGroupAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $businessType = GroupAddress::GROUP_ADDRESS_TYPE_BUSINESS;
        $billingType = GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING;
        $billingText = ucfirst($billingType) . "," . strtolower($billingType) . "," . strtoupper($billingType);
        $businessText = ucfirst($businessType) . "," . strtolower($businessType) . "," . strtoupper($businessType);
        $typeInText = "{$billingText},{$businessText}";
        return [
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "id" => "required|integer",
            "type" => "required|in:" . $typeInText,
            "loginUserId" => "nullable",
            "loginUserName" => "nullable"
        ];
    }

    public function messages()
    {
        return [
            'id.required' => 'Group Address Id is required.',
            'id.integer' => 'Group Address Id should be integer.',
            'type.required' => 'Type is required.',
            'type.in' => 'Type should be either business or billing.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
