<?php

namespace App\Http\Requests\ManageGroup;

use App\Docs;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Foundation\Http\FormRequest;

class groupFilerequest extends FormRequest
{
    private $groupFileType;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation()
    {
        $this->groupFileType = Docs::$groupFileType;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'group_id' => 'required|integer|exists:mysql.group_info,gid',
            'name' => 'required|string',
            'type' => ['required', Rule::in($this->groupFileType)],
            'attachment' => 'required|file',
            'note' => 'required|string'
        ];
    }
    public function messages()
    {
        return [
            'group_id.required' => 'Group ID is required.',
            'group_id.integer' => 'Group ID should be an integer.',
            'group_id.exists' => 'Group ID is invalid.',
            'name.required' => 'File name is required.',
            'name.string' => 'File name must be a string.',
            'type.required' => 'File type is required.',
            'type.in' => 'Invalid File type. Only value of \'' . implode('\', \'', $this->groupFileType) . '\' are allowed.',
            'attachment.required' => 'File is required.',
            'attachment.file' => 'File must be a valid file.',
            'note.required' => 'File Note is required.',
            'note.string' => 'File Note must be a string.'
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
