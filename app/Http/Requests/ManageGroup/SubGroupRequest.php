<?php

namespace App\Http\Requests\ManageGroup;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;

class SubGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        foreach ($this->request->get('data') as $key => $value) {
            $rules['data.'.$key.'.sub_group_id'] = 'required|integer|exists:mysql.group_info,gid';
            $rules['data.'.$key.'.gid'] = 'required|integer';
        }
        return $rules;
    }

    public function messages()
    {
        foreach ($this->request->get('data') as $key => $value) {
            $messages['data.'.$key.'.sub_group_id.required'] = 'Subgroup Id is required';
            $messages['data.'.$key.'.sub_group_id.exists'] = 'Subgroup Id is invalid';
            $messages['data.'.$key.'.gid.required'] = 'Group Id is required';
            $messages['data.'.$key.'.gid.integer'] = 'Group Id must be integer';
            $messages['data.'.$key.'.sub_group_id.integer'] = 'Subgroup Id must be integer';
        }
        return $messages;
    }

    protected function failedValidation(Validator $validator)
    {
        $errorsList = (new ValidationException($validator))->errors();
        $counter = 0;
        foreach($errorsList as $key => $errorList) {
            $data[$counter]['target_element'] = $key;
            $data[$counter]['message'] = $errorList[0];
            $counter ++;
        }
        $errors = ['status' => 'error',
            'data' => $data,
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
    
}