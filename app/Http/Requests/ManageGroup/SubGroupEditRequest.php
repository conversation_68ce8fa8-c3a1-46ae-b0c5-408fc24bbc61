<?php

namespace App\Http\Requests\ManageGroup;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;

class SubGroupEditRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules['data.id'] = 'required|integer';
        $rules['data.status'] = 'required|in:1,0';
        return $rules;
    }

    public function messages()
    {
        $messages['data.id.required'] = 'Group Id is required';
        $messages['data.id.integer'] = 'Group Id must be integer';
        $messages['data.status.required'] = 'Status is required';
        $messages['data.status.in'] = 'Status must be 1 or 0';
        return $messages;
    }

    protected function failedValidation(Validator $validator)
    {
        $errorsList = (new ValidationException($validator))->errors();
        $counter = 0;
        foreach($errorsList as $key => $errorList) {
            $data[$counter]['target_element'] = $key;
            $data[$counter]['message'] = $errorList[0];
            $counter ++;
        }
        $errors = ['status' => 'error',
            'data' => $data,
            'statusCode' => JsonResponse::HTTP_UNPROCESSABLE_ENTITY];

        throw new HttpResponseException(response()->json($errors
            , JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
    
}