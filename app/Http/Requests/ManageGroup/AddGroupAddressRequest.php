<?php

namespace App\Http\Requests\ManageGroup;

use App\GroupAddress;
use App\GroupBillingAddress;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class AddGroupAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $billingType = GroupBillingAddress::GROUP_ADDRESS_TYPE_BILLING;
        $businessType = GroupAddress::GROUP_ADDRESS_TYPE_BUSINESS;
        $billingText = ucfirst($billingType).",".strtolower($billingType).",".strtoupper($billingType);
        $businessText = ucfirst($businessType).",".strtolower($businessType).",".strtoupper($businessType);
        $typeInText = "{$billingText},{$businessText}";
        return [
            "group_id"=>"required|integer|bail|exists:group_info,gid",
            "state" => "required|exists:states,abbrev",
            "address1" => "required",
            "address2" => "nullable",
            "city" => "required",
            "zip" => "required",
            "is_primary" => "nullable|integer",
            "usps_verified"=>"nullable|integer",
            "type" => "required|in:" . $typeInText,
            "loginUserId" => "nullable",
            "loginUserName" => "nullable"
        ];
    }


    public function messages()
    {
        return [
            'group_id.required' => 'Group is required.',
            'group_id.integer' => 'Group Id should be integer.',
            'group_id.exists' => 'Group not found.',
            'state.exists' => 'Invalid state name.',
            'address1.required' => 'Primary address is required.',
            'city.required' => 'City is required.',
            'zip.required' => 'Zip is required.',
            'type.required' => 'Type is required.',
            'type.in' => 'Type should be either business or billing.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
