<?php

namespace App\Http\Requests\ManageGroup;

use App\Rules\NeverBounceRule;
use App\Rules\NumVerifyRule;
use App\Rules\RoutingNumberBankNameRule;
use App\Rules\RoutingNumberRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class UpdateGroupBankingDetailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $routingNumber = Request::get('ach_bank_routing');
        $paymentType = Request::get('payment_type');
        $routingRule =$paymentType == 'eft' ? ['numeric','bail','digits:9',new RoutingNumberRule() ]: '';
        $bankAccountRule = $paymentType == 'eft' ? ['|integer|bail|gt:0|bail|min:10000'] :'';
        return [
            "group_id" => "required|integer|bail|exists:group_info,gid",
            "loginUserId"=>"nullable",
            "loginUserName"=>"nullable",
            "payment_type"=>"required|in:eft,stmt,list,cc",
            "ach_bank_account_name" => "required_if:payment_type,=,eft",
            "ach_bank_name" => ['required_if:payment_type,=,eft',$paymentType == 'eft' ? new RoutingNumberBankNameRule($routingNumber) :''],
            "ach_bank_routing" => ['required_if:payment_type,=,eft',$routingRule],
            "ach_bank_account" => "required_if:payment_type,=,eft".$bankAccountRule,
        ];
    }

    public function messages()
    {
        return [
            'group_id.required' => 'Group is required.',
            'group_id.integer' => 'Group Id should be integer.',
            'group_id.exists' => 'Group not found.',
            'payment_type.required'=>'Payment Type is required.',
            'ach_bank_account_name.required' => 'Account name is required.',
            'ach_bank_name.required' => 'Bank name is required.',
            'ach_bank_routing.required' => 'Routing number is required.',
            'ach_bank_routing.numeric' => 'Routing number should be in number.',
            'ach_bank_routing.digits' => 'Routing number should be of 9 digits.',
            'ach_bank_account.numeric' => 'Account number should be in number.',
            'payment_type.in'=>'Payment type must be either eft or stmt or list or cc.',
            'ach_bank_account.min' => 'Account number must be at least 5 digits.',
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
