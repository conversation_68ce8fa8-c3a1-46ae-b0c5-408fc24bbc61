<?php

namespace App\Http\Requests\ACM;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use App\Rules\NeverBounceRule;
use App\Service\CustomValidationService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Foundation\Http\FormRequest;

class OnboardingNueraRequest extends FormRequest
{
    private $customValidationService;

    public function __construct(CustomValidationService $customValidationService)
    {
        $this->customValidationService = $customValidationService;
    }
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules =  [
            "first_name" => "required",
            "last_name" => "required",
            "email" => ["required",new NeverBounceRule()],
            "mobile_number" => ["nullable","digits:10",
                function ($attribute, $value, $fail) {
                    $mobileValidation = $this->customValidationService->validatePhoneNumVerify($value, true);
                    if (!$mobileValidation) {
                        $fail('Please enter valid mobile number');
                    }else if($mobileValidation['data']['line_type'] !== 'mobile') {
                        $fail('Only mobile numbers are allowed');
                    }
            }],
            'image' => 'nullable|mimes:jpg,jpeg,png|bail|max:20480',
            "state" => "required|exists:states,abbrev",
            "address_1" => "required",
            "city" => "required",
            "zip_code" => "required",

        ];
        return $rules;
    }

    public function messages()
    {
        $messages = [
            "first_name.required" => "First name is required.",
            "last_name.required" => "Last name is required.",
            "email.required" => "Email is required.",
            "mobile_number.integer" => "Mobile number should be number.",
            "mobile_number.digits" => "Mobile number must have 10 digits.",
            'image.mimes' => 'Agent Image must be type of .jpeg,.jpg or .png format.',
            'image.max' => 'Agent Image must be less than 20 MB.',
            'state.exists' => 'Invalid state name.',
            'address_1.required' => 'Primary address is required.',
            'city.required' => 'City is required.',
            'zip_code.required' => 'Zip is required.',
        ];
        return $messages;
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(['errors' => $errors
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY));
    }
}
