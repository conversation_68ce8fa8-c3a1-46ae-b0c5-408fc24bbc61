<?php

namespace App\Http\Resources\ACM;

use Illuminate\Http\Resources\Json\JsonResource;

class DashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->agent_id,
            "first_name" => $this->agent_fname,
            "middle_name" => $this->agent_mname,
            "last_name" => $this->agent_lname,
            "email" => $this->email,
            "phone_number" => $this->agent_phone1,
            "address_1" => $this->agent_address1,
            "state" => $this->agent_state,
            "city" => $this->agent_city,
            "zip_code" => $this->agent_zip,
            "photo" => $this->agent_img,
            "path" => $this->path,
            "signed_in_date" => date('m/d/Y', $this->agent_signup_date),
            "agent_code" => $this->agent_code,
        ];
    }
}
