<?php

namespace App\Http\Controllers\MonthlyReport;

use Exception;
use App\Policy;
use App\AgentInfo;
use App\PlanOverview;
use Illuminate\Http\Request;
use App\Helpers\CommonHelper;
use Illuminate\Support\Carbon;
use App\Service\MessageService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Mail\MonthlyProductionReport;

class MonthlyReportController extends Controller
{
    public function report()
    {
        try {
            $agents = AgentInfo::where('agent_status', 'A')
                ->whereNull('deleted_at')
                ->select('agent_id', 'agent_email')
                ->get();

            foreach ($agents as $agent) {
                $downlineReps = CommonHelper::getDownlineAgentList($agent->agent_id);
                $agentsEmail = $agent->agent_email;
                $direct_business = [
                    'direct_policy' => $this->directReportData($agent->agent_id),
                    'direct_plan' => $this->directPlans($agent->agent_id)
                ];
                $downline_business = [
                    'downline_policy_report' => $this->downlineReportData($downlineReps),
                    'downline_plan_report' => $this->downlinePlans($downlineReps)
                ];
                $new_business = [
                    'new_business_report' => $this->repEnroll($downlineReps)
                ];
                $downline_reps_no_enrollments = [
                    'downline_reps_no_enrollments' => $this->getRepsWithoutEnrollments($downlineReps)
                ];

                $isAllDataEmpty = empty($direct_business['direct_policy']) && empty($direct_business['direct_plan']) &&
                    empty($downline_business['downline_policy_report']) && empty($downline_business['downline_plan_report']) &&
                    empty($new_business['new_business_report']) && empty($downline_reps_no_enrollments['downline_reps_no_enrollments']);

                if (!$isAllDataEmpty) {
                    Log::info("Sending email to: " . $agentsEmail);

                    $content = new MonthlyProductionReport($direct_business, $downline_business, $new_business, $downline_reps_no_enrollments);
                    $template = $content->render();
                    $this->sendEmail($agentsEmail, Carbon::now()->format('F Y'). " - Summary of Enrollments" . " " . Carbon::now()->subMonth()->format('F'), $template);

                    Log::info("Email sent to: " . $agentsEmail);
                } else {
                    Log::info("No data to send for agent: " . $agentsEmail);
                }
            }
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    public function directReportData($agentId)
    {
        return Policy::where('p_agent_num', $agentId)
            ->whereBetween('edate', [Carbon::now()->subMonth()->timestamp, Carbon::now()->timestamp])
            ->select(DB::raw('
                DATE_FORMAT(effective_date, "%M") as effective_date,
                COUNT(policy_id) as total_enrolled,
                COUNT(CASE WHEN (Approval IS NOT NULL AND status = "ACTIVE") OR (status = "TERMED" AND term_date > NOW()) THEN policy_id END) as total_active,
                COUNT(CASE WHEN status = "TERMED" AND term_date <= NOW() THEN policy_id END) as total_termed,
                COUNT(CASE WHEN status = "WITHDRAWN" THEN policy_id END) as total_withdrawn,
                COUNT(CASE WHEN Approval IS NULL AND status = "ACTIVE" THEN policy_id END) as total_pending
            '))
            ->groupByRaw('DATE_FORMAT(effective_date, "%M")')
            ->get()
            ->toArray();
    }

    public function downlineReportData($downlineReps)
    {
        $policies = Policy::whereIn('p_agent_num', $downlineReps)
            ->join('agent_info', 'policies.p_agent_num', '=', 'agent_info.agent_id')
            ->whereBetween('policies.edate', [Carbon::now()->subMonth()->timestamp, Carbon::now()->timestamp])
            ->select(DB::raw('
                policies.p_agent_num,
                CONCAT(agent_info.agent_fname, " ", agent_info.agent_lname) as agent_name,
                COUNT(policies.policy_id) as total_enrolled,
                COUNT(CASE WHEN (policies.Approval IS NOT NULL AND policies.status = "ACTIVE") OR (policies.status = "TERMED" AND policies.term_date > NOW()) THEN policies.policy_id END) as total_active,
                COUNT(CASE WHEN policies.status = "TERMED" AND policies.term_date <= NOW() THEN policies.policy_id END) as total_termed,
                COUNT(CASE WHEN policies.status = "WITHDRAWN" THEN policies.policy_id END) as total_withdrawn,
                COUNT(CASE WHEN policies.Approval IS NULL AND policies.status = "ACTIVE" THEN policies.policy_id END) as total_pending
            '))
            ->groupBy('policies.p_agent_num')
            ->orderBy('total_enrolled', 'desc')
            ->get();
        $getPolicy = [];
        foreach ($policies as $policy) {
            $getPolicy[] = [
                'agent_name' => $policy->agent_name,
                'total_enrolled' => $policy->total_enrolled,
                'total_active' => $policy->total_active,
                'total_termed' => $policy->total_termed,
                'total_withdrawn' => $policy->total_withdrawn,
                'total_pending' => $policy->total_pending,
                'effective_date' => $this->directReportData($policy->p_agent_num)
            ];
        }

        return $getPolicy;
    }

    public function directPlans($agentId)
    {
        return PlanOverview::where('p_agent_num', $agentId)
            ->whereBetween('edate', [Carbon::now()->subMonth()->timestamp, Carbon::now()->timestamp])
            ->where('is_assoc', '!=', 1)
            ->select(DB::raw('
                web_display_name,
                COUNT(p_ai) as total_enrolled,
                COUNT(CASE WHEN pstatus = 1 OR (pstatus = 2 AND pterm_date > NOW()) THEN p_ai END) as total_active,
                COUNT(CASE WHEN pstatus = 2 AND pterm_date <= NOW() THEN p_ai END) as total_termed,
                COUNT(CASE WHEN pstatus = 3 THEN p_ai END) as total_withdrawn
            '))
            ->groupBy('pid')
            ->get()
            ->toArray();
    }

    public function downlinePlans($downlineReps)
    {
        return PlanOverview::whereIn('p_agent_num', $downlineReps)
            ->whereBetween('edate', [Carbon::now()->subMonth()->timestamp, Carbon::now()->timestamp])
            ->where('is_assoc', '!=', 1)
            ->select(DB::raw('
                web_display_name,
                COUNT(p_ai) as total_enrolled,
                COUNT(CASE WHEN pstatus = 1 OR (pstatus = 2 AND pterm_date > NOW()) THEN p_ai END) as total_active,
                COUNT(CASE WHEN pstatus = 2 AND pterm_date <= NOW() THEN p_ai END) as total_termed,
                COUNT(CASE WHEN pstatus = 3 THEN p_ai END) as total_withdrawn
            '))
            ->groupBy('pid')
            ->get()
            ->toArray();
    }

    public function repEnroll($downlineReps)
    {
        $newAgents = AgentInfo::whereIn('agent_id', $downlineReps)
            ->where('agent_status', 'A')
            ->whereBetween('created_at', [Carbon::now()->subMonth(), Carbon::now()])
            ->whereNull('deleted_at')
            ->pluck('agent_id');

        if ($newAgents->isEmpty()) {
            return [];
        }

        $policies = Policy::whereIn('p_agent_num', $newAgents)
            ->join('agent_info', 'policies.p_agent_num', '=', 'agent_info.agent_id')
            ->whereBetween('policies.edate', [Carbon::now()->subMonth()->timestamp, Carbon::now()->timestamp])
            ->select(DB::raw('
                policies.p_agent_num,
                CONCAT(agent_info.agent_fname, " ", agent_info.agent_lname) as agent_name,
                COUNT(policies.policy_id) as total_enrolled,
                COUNT(CASE WHEN (policies.Approval IS NOT NULL AND policies.status = "ACTIVE") OR (policies.status = "TERMED" AND policies.term_date > NOW()) THEN policies.policy_id END) as total_active,
                COUNT(CASE WHEN policies.status = "TERMED" AND policies.term_date <= NOW() THEN policies.policy_id END) as total_termed,
                COUNT(CASE WHEN policies.status = "WITHDRAWN" THEN policies.policy_id END) as total_withdrawn,
                COUNT(CASE WHEN policies.Approval IS NULL AND policies.status = "ACTIVE" THEN policies.policy_id END) as total_pending
            '))
            ->groupBy('policies.p_agent_num')
            ->get();

        return $policies->map(function ($policy) {
            return [
                'agent_name' => $policy->agent_name,
                'total_enrolled' => $policy->total_enrolled,
                'total_active' => $policy->total_active,
                'total_termed' => $policy->total_termed,
                'total_withdrawn' => $policy->total_withdrawn,
                'total_pending' => $policy->total_pending
            ];
        })->toArray();
    }

    public function getRepsWithoutEnrollments($downlineReps)
    {
        $repsWithNoEnrollments = AgentInfo::whereIn('agent_id', $downlineReps)
            ->leftJoin('policies', function ($join) {
                $join->on('policies.p_agent_num', '=', 'agent_info.agent_id')
                    ->whereBetween('policies.edate', [
                        Carbon::now()->subMonth()->timestamp,
                        Carbon::now()->timestamp
                    ]);
            })
            ->select(DB::raw('
            CONCAT(agent_info.agent_fname, " ", agent_info.agent_lname) AS agent_name,
            COUNT(policies.policy_id) AS total_enrolled
        '))
            ->groupBy('agent_info.agent_id', 'agent_name')
            ->having('total_enrolled', '=', 0)
            ->orderBy('agent_name', 'asc')
            ->get()
            ->toArray();

        return $repsWithNoEnrollments;
    }

    public function sendEmail($email_recipients, $subject, $template, $config_attachment = '', $attachmentFileNames = [])
    {
        $toAddress = config("testemail.TEST_EMAIL") ? [config("testemail.TEST_EMAIL")] : [$email_recipients];
        $data = [
            "configuration_name" => 'REP_GENERIC_MESSAGE',
            "to_address" => $toAddress,
            "cc_address" => [],
            "bcc_address" => [],
            "subject" => $subject,
            "content" => $template
        ];
        if (!empty($config_attachment)) {
            $data['configuration_name'] = $config_attachment;
            $data['attached_files'] = $attachmentFileNames ?? [];
        }
        if (!empty($data['to_address'][0])) {
            $post_field = [
                "email_message_configuration_name" => $data['configuration_name'],
                "toAddress" => $data['to_address'],
                "ccAddress" => [],
                "bccAddress" => [],
                "subject" => $data['subject'] ?? '',
                "attachedFiles" => $data['attached_files'] ?? [],
                "contentData" => [
                    "content" => $data['content']
                ]
            ];
            $response = new MessageService();
            $response->sendEmailWithTemplateAndData($post_field);
        }
    }
}
