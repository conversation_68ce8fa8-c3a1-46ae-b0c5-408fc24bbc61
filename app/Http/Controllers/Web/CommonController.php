<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CommonController extends Controller
{
    /**
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function downloadFile(Request $request){

        $fileUrl = str_replace(' ','%20',$request->file_url);
        $ext = pathinfo($fileUrl, PATHINFO_EXTENSION);
        $fileName = $request['file_name'] ? $request['file_name'] . '.' . $ext : basename($fileUrl);
        return response()->streamDownload(function () use ($fileUrl) {
            echo file_get_contents($fileUrl);
        }, $fileName);
    }
}
