<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\PolicyUpdates\AddPolicyBillDateRequest;
use App\Http\Requests\PolicyUpdates\UpdateGroupPolicyRequest;
use App\Http\Requests\PolicyUpdates\UpdateRecurringPaymentStatus;
use App\Http\Requests\PolicyUpdates\UpdateWaiveStatementFeeRequest;
use App\Http\Resources\DataResponse;
use App\Http\Requests\GetPolicyDetails;
use App\Repositories\PolicyFeatures;
use Illuminate\Http\Request;

class PolicyInfoController extends Controller
{
    public function __construct(PolicyFeatures $policyFeatures)
    {
        $this->policyFeature = $policyFeatures;
    }

    public function addPolicyBillingDate(AddPolicyBillDateRequest $request)
    {
        $data = $this->policyFeature->addPolicyBillDate($request);
        return new DataResponse($data);
    }

    public function getPolicyGroupListing()
    {
        $data = $this->policyFeature->getGroupPolicyListing();
        return new DataResponse($data);
    }

    public function updatePolicyGroup(UpdateGroupPolicyRequest $request)
    {
        $data = $this->policyFeature->updateGroupPolicy($request);
        return new DataResponse($data);
    }
    public function updateWaiveStatementFee(UpdateWaiveStatementFeeRequest $request)
    {
        $data = $this->policyFeature->updateWaiveStatementFee($request);
        return new DataResponse($data);
    }
    public function updatePolicyRecurringPaymentStatus(UpdateRecurringPaymentStatus $request) {
        $data = $this->policyFeature->updatePaymentRecurringStatus($request);
        return new DataResponse($data);
    }
    public function showUploadedFiles(GetPolicyDetails $request)
    {
        $data = $this->policyFeature->showUploadedFiles($request);
        return new DataResponse($data);
    }
}

