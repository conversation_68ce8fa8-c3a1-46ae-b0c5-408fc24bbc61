<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\MemberResourceRequest;
use App\Repositories\MemberResource\MemberResourceFeatures;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Http\Resources\MemberFileResource;

class ResourceController extends Controller
{
    private $resourceFeature;

    public function __construct(MemberResourceFeatures $memberResourceFeature)
    {
        $this->resourceFeature = $memberResourceFeature;
    }

    public function getUserResource(MemberResourceRequest $request)
    {
        $data = $this->resourceFeature->getMemberResource($request);
        return new DataResponse($data);
    }

    public function getNewRates($policy_id)
    {
        $data = $this->resourceFeature->getNewRates($policy_id);
        return new DataResponse($data);
    }
    public function getNewRatesRenewal($policy_id)
    {
        $data = $this->resourceFeature->getNewRatesRenewal($policy_id);
        return new DataResponse($data);
    }

    public function getSolisticCardDetail($policy_id)
    {
        $data = $this->resourceFeature->getSolisticCardDetail($policy_id);
        return new DataResponse($data);
    }

    public function getDeltaDentalCardDetail($policy_id)
    {
        $data = $this->resourceFeature->getDeltaDentalCardDetail($policy_id);
        return new DataResponse($data);
    }
}
