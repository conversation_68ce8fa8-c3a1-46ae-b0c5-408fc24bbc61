<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\DependentRemovalRequest;
use Illuminate\Http\Request;
use App\Repositories\DependentApproval;
use App\Repositories\DependentRemovalFeatures;
use App\Http\Resources\DataResponse;
use App\Http\Requests\DependentApprovalRequest;

class DependentApprovalController extends Controller
{
    public function __construct()
    {
        $this->dependentApprovalModel = new DependentApproval();
        $this->dependentRemoval = new DependentRemovalFeatures();
    }
    public function getApprovalList()
    {
        $data = $this->dependentApprovalModel->getApprovalList();
        return new DataResponse($data);
    }
    public function approveDependent(DependentApprovalRequest $request)
    {
        $data = $this->dependentApprovalModel->approvalAction($request);
        return new DataResponse($data);
    }
    public function removeDependent(DependentRemovalRequest $request) {

        $data = $this->dependentRemoval->removeDependent($request);
        return new DataResponse($data);
    }
}
