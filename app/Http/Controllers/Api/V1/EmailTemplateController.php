<?php

namespace App\Http\Controllers\Api\V1;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Repositories\EmailFeatures;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEmailTemplate;
use App\Http\Requests\SaveEmailQueue;
use App\Http\Resources\DataResponse;

class EmailTemplateController extends Controller
{
    public function __construct()
    {
        $this->EmailModel = new EmailFeatures();
    }

    public function getEmailTemplateInfo($id=0)
    {
        $data = $this->EmailModel->getEmailTemplate($id);
        return new DataResponse($data);
    }
    //storing new record
    public function storeTemplate(StoreEmailTemplate $request)
    {
        $categoryName = $request->categoryname;
        $templateDesign = $request->templatedesign;
        $status = '1';
        $subject = $request->subject;

        $data = array('categoryname'=>$categoryName, 'template_design'=>$templateDesign, 'status'=>$status, 'subject'=>$subject);
        if ($request->id != null) {
            //update
            $id = $request->id;
            $value = $this->EmailModel->updateTemplate($data, $id);
        } else {
            //insert
            $value = $this->EmailModel->storeTemplate($data);
        }
        return response()->json(['message'=>$value]);
    }
    //preview email template
    public function templatePreview($id)
    {
        $emailContent = $this->getEmailTemplateInfo($id);
        $template = $this->EmailModel->getEmailOutine(1);
        $template->template = str_replace('#firstname', 'Firstname'.' '.'Lastname', $template->template);
        $template->template = str_replace('#content', $emailContent->template_design, $template->template);
        return response()->json(['message'=>'success','emailPreview'=>$template->template,'emailContent'=>$emailContent]);
    }

    //get email with html outline category wise
    public function getTemplateByCategory($categoryName)
    {
        $data = $this->EmailModel->getEmailByCategory($categoryName);
        $htmlOutline = $this->EmailModel->getEmailOutine(1);
        return response()->json(['message'=>'success','result'=>$data, 'emailOutline'=>$htmlOutline]);
    }

    //save email in queue
    public function saveEmail(SaveEmailQueue $request)
    {
        $emailData = $request->all();
        $result = $this->EmailModel->saveQueue($emailData);
        return response()->json(['message'=>'success','result'=>$result]);
    }
}
