<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\UserUpdate;
use App\GroupUpdate;
use Carbon\Carbon;
use App\Traits\ResponseMessage;
use App\Traits\Paginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\AdminUser;
use App\AgentUpdate;
use App\UserInfoPolicyAddress;


class EmailUpdateLogsController extends Controller
{
    use ResponseMessage, Paginator;

    /**
     * Get user update logs
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function userUpdates(Request $request, $userId)
    {


        $data = UserUpdate::query()
            ->where('user_id', '=', $userId)
            ->where('change_type', '=', UserUpdate::EMAIL_UPDATE)
            ->orderBy('created_at', 'DESC')
            ->paginate($request->query('per_page') ?? 20);

        $result = [];
        foreach ($data as $d) {
            $admin = AdminUser::where('id', $d->changed_by_id)->first();

            // Only look for member if admin is null and change was by a member
            if (!$admin && $d->changed_by_type === 'MEMBER') {
                $member = UserInfoPolicyAddress::where('userid', $d->changed_by_id)->first();
            }

            $result[] = [
                'changed_date' => $d->created_at->format('m/d/Y') . ' ' . $d->created_at->format('h:i A'),
                'comment' => $d->comment ?: 'N/A',
                'changed_type' => $d->change_type ?: 'N/A',
                'changed_by_id' =>  $d->changed_by_id?: 'N/A',
                'changed_by_type' => $d->changed_by_type ?: 'N/A',
                'changed_by_full' => $admin ? $admin->name . " [" . strtoupper($admin->userrole) . "]"
                    : ($member ? ucwords(strtolower($member->cfname . ' ' . $member->clname)) . ' [MEMBER]' : 'N/A'),

                'changed_from' => $d->changed_from ?: 'N/A',
                'changed_to' => $d->changed_to ?: 'N/A'
            ];
        }

        $paginatedData = [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];

        try {
            return $this->successResponse('Success', $paginatedData);
        } catch (\Throwable $th) {
            return $this->failedResponse("Failed to fetch data.");
        }
    }

    /**
     * Get group update logs
     *
     * @param Request $request
     * @param int $groupId
     * @return JsonResponse
     */
    public function groupUpdates(Request $request, $groupId)
    {
        $data = GroupUpdate::query()
            ->where('group_id', '=', $groupId)
            ->where('elgb_act', '=', GroupUpdate::EMAIL_UPDATE)
            ->orderBy('elgb_id', 'DESC')
            ->paginate($request->query('per_page') ?? 20);

        $result = [];
        foreach ($data as $d) {
            $admin = AdminUser::where('id', $d->elgb_agent)->first();
            $result[] = [
                'group_id' => $d->group_id,
                'elgb_act' => $d->elgb_act ?: 'N/A',
                'elgb_act_date' => $d->created_at->format('m/d/Y') . ' ' . $d->created_at->format('h:i A'),
                'elgb_act_date_original' => $d->elgb_act_date,
                'elgb_comment' => $d->elgb_comment ?: 'N/A',
                'elgb_agent' => $d->elgb_agent ?: 'N/A',
                'elgb_agentname' => $d->elgb_agentname ?: 'N/A',
                'changed_by_full' =>  $admin?$admin->name ." [".strtoupper($admin->userrole)."]": 'N/A',
                'changed_from' => $d->changed_from ?: 'N/A',
                'changed_to' => $d->changed_to ?: 'N/A'
            ];
        }

        $paginatedData = [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];

        try {
            return $this->successResponse('Success', $paginatedData);
        } catch (\Throwable $th) {
            return $this->failedResponse("Failed to fetch data.");
        }
    }

    public function agentUpdates(Request $request, $agentId)
    {

        $data = AgentUpdate::query()
            ->where('agent_id', '=', $agentId)
            ->where('elgb_act', '=', AgentUpdate::EMAIL_UPDATE)
            ->orderBy('created_at', 'DESC')
            ->paginate($request->query('per_page') ?? 20);

        $result = [];
        foreach ($data as $d) {
            $admin = AdminUser::where('id', $d->elgb_agent_id)->first();
            $result[] = [
                'agent_id' => $d->agent_id,
                'elgb_act' => $d->elgb_act ?: 'N/A',
                'elgb_act_date' => $d->created_at->format('m/d/Y') . ' ' . $d->created_at->format('h:i A'),
                'elgb_act_date_original' => $d->elgb_act_date,
                'elgb_comment' => $d->elgb_comment ?: 'N/A',
                'elgb_agent' => $d->elgb_agent_id ?: 'N/A',
                'elgb_agentname' => $d->elgb_agent_name ?: 'N/A',
                'changed_from' => $d->changed_from ?: 'N/A',
                'changed_to' => $d->changed_to ?: 'N/A',
                'changed_by_full' =>  $admin?$admin->name ." [".strtoupper($admin->userrole)."]": 'N/A',
            ];
        }

        $paginatedData = [
            'data' => $result,
            'links' => $this->links($data),
            'meta' => $this->meta($data)
        ];

        try {
            return $this->successResponse('Success', $paginatedData);
        } catch (\Throwable $th) {
            return $this->failedResponse("Failed to fetch data.");
        }

    }


}
