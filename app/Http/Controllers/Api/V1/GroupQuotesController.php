<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Groups\GroupQuotesRepository;
use Exception;
use App\Traits\ResponseMessage;

class GroupQuotesController extends Controller
{
    use ResponseMessage;

    private $groupQuotes;

    public function __construct()
    {
        $this->groupQuotes = new GroupQuotesRepository();
    }
    public function getGroupCensusDetails(Request $request)
    {
        try {
            $data = $this->groupQuotes->paginatedFormattedList($request->query->get('per_page') ?? 20, $request->query->all());
            return $this->successResponse('Success', $data);
        } catch (Exception $e) {
            return  $this->failedResponse();
        }
    }
    public function getGroupPlanList()
    {
        try {
            $data = $this->groupQuotes->getPlanList();
            return $this->successResponse('Success', $data);
        } catch (Exception $e) {
            return  $this->failedResponse();
        }
    }
}
