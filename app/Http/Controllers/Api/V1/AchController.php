<?php

namespace App\Http\Controllers\Api\V1;
use App\Repositories\Ach\AchInfo;
use App\Repositories\Ach\GenerateAch;
use App\Repositories\Ach\AchFunctions;
use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;

class AchController extends Controller
{
    public function __construct()
    {
        $this->achModel = new AchInfo();
        $this->generateAchModel = new GenerateAch();
    }

    public function viewAch($achYear, $achMonth)
    {
        $data = $this->achModel->viewAch($achYear,$achMonth);
        return new DataResponse($data);
    }

    //Active termed data having term eff date greater than current date;
    public function policyAT()
    {
        $data = $this->achModel->policyAT();
        return new DataResponse($data);
    }

    //Active termed data having term eff date greater than current date and also payment type eft or elist
    public function policyEftElist($effDate)
    {
        $data = $this->generateAchModel->policyEftElist($effDate);
        return new DataResponse($data);
    }

    //function to generate ACH
    public function generateAch($effDate)
    {
        $data = $this->generateAchModel->generateAch($effDate);
        // return new DataResponse($data);
        return $data;
    }
}
