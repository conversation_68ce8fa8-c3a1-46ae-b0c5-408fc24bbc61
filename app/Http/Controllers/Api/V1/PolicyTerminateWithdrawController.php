<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\PolicyTerminationWithdrawn\BillingInformationCCRequest;
use App\Http\Requests\PolicyTerminationWithdrawn\BillingInformationEFTRequest;
use App\Http\Requests\PolicyTerminationWithdrawn\PolicyReInstateRequest;
use App\Http\Requests\PolicyTerminationWithdrawn\PolicyTerminateRequest;
use App\Http\Requests\PolicyTerminationWithdrawn\PolicyWithdrawRequest;
use App\Http\Requests\PolicyTerminationWithdrawn\PolicyInformationRequest;
use App\Http\Requests\PolicyTerminationWithdrawn\PolicyTierDetailsRequest;
use App\Http\Requests\PolicyTerminationWithdrawn\PolicyTierUpdateRequest;
use App\Http\Resources\DataResponse;
use App\Repositories\PolicyTerminateWithdraw\BillingInformationFeatures;
use App\Repositories\PolicyTerminateWithdraw\PolicyTerminateWithdrawFeatures;
use App\Repositories\PolicyTerminateWithdraw\PolicyTierFeatures;
use Illuminate\Http\Request;
use Illuminate\Http\ResponseTrait;

class PolicyTerminateWithdrawController extends Controller
{
    use ResponseTrait;
    private $policyTerminateWithdrawFeatures;
    private $billingInformationFeatures;
    private $policyTierFeatures;

    public function __construct()
    {
        $this->policyTerminateWithdrawFeatures = new PolicyTerminateWithdrawFeatures();
        $this->billingInformationFeatures = new BillingInformationFeatures();
        $this->policyTierFeatures = new PolicyTierFeatures();
    }
    public function terminateAndWithdrawDetails($policyID)
    {
        $data= $this->policyTerminateWithdrawFeatures->getBriefInfo($policyID);
        return new DataResponse($data);
    }
    public function terminatePolicy(PolicyTerminateRequest $request)
    {
        return $this->policyTerminateWithdrawFeatures->setTerminatePolicy($request);  
    }

    public function withdrawPolicy(PolicyWithdrawRequest $request)
    {
        return $this->policyTerminateWithdrawFeatures->setWithdrawPolicy( $request);      
    }
    public function getUserPolicyInformationDetails($uid)
    {
        $data = $this->policyTerminateWithdrawFeatures->getPolicyInformation($uid);
        return new DataResponse($data);
    }
    public function updateUserPolicyInformationDetails(PolicyInformationRequest $request)
    {
        $data = $this->policyTerminateWithdrawFeatures->updatePolicyInformation($request);
        return new DataResponse($data);
    }
    public function getBillingInformationDetails($uid)
    {
        $data = $this->billingInformationFeatures->getBillingInformation($uid);
        return new DataResponse($data);
    }
    public function updateBillingInformationDetailsCC(BillingInformationCCRequest $request)
    {
        $data = $this->billingInformationFeatures->updateBillingInformationCC($request);
        return new DataResponse($data);
    }
    public function updateBillingInformationDetailsEFT(BillingInformationEFTRequest $request)
    {
        $data = $this->billingInformationFeatures->updateBillingInformationEFT($request);
        return new DataResponse($data);
    }
    public function deleteBillingInformationDetailsEFT(BillingInformationEFTRequest $request): DataResponse
    {
        $data = $this->billingInformationFeatures->deleteBillingInformationEFT($request->validated());
        return new DataResponse($data);
    }
    public function getReinstatePolicyDetails($policyID)
    {
        $data = $this->policyTerminateWithdrawFeatures->getReinstatePolicyDetails($policyID);
        return new DataResponse($data);
    }
    public function setReinstatePolicyDetails(PolicyReInstateRequest $request)
    {
        $data = $this->policyTerminateWithdrawFeatures->setReinstatePolicyDetails($request);
        return new DataResponse($data);
    }
    public function getPolicyTierDetails(PolicyTierDetailsRequest $request)
    {
        $data = $this->policyTierFeatures->getPolicyTierInformation($request);
        return new DataResponse($data);
    }
    public function updatePolicyTierDetails(PolicyTierUpdateRequest $request)
    {
        $data = $this->policyTierFeatures->setPolicyTier($request);
        $response = [
            'status' => $data['status'],
            'statusCode' => ($data['statusCode']) ? $data['statusCode'] : 422
        ];
        if ( isset($data['message']) ) $response['message'] = $data['message'];
        if ( isset($data['data']) ) $response['data'] = $data['data'];

        return response()->json($response, $response['statusCode']);
    }
}
