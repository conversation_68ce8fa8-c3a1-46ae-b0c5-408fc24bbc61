<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\Members\RegisteredMember;

class RegisteredMemberController extends Controller
{
    public function __construct()
    {
        $this->registeredUser = new RegisteredMember();
    }
    public function getRegisteredUsers()
    {
        $userid = 104065;
        $data = $this->registeredUser->getRegisteredMembers($userid);
        return new DataResponse($data);
    }
}
