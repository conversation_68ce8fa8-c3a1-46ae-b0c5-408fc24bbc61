<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\DefaultAndRecurringRequest;
use App\Http\Resources\DataResponse;
use App\Repositories\Payment\DefaultAndRecurringFeatures;

class DefaultAndRecurringController extends Controller
{
    public function __construct()
    {
        $this->defaultAndRecurringFeature = new DefaultAndRecurringFeatures();
    }
    public function defaultRecurringMethodSet(DefaultAndRecurringRequest $request)
    {
       $data = $this->defaultAndRecurringFeature->defaultRecurringMethodSet($request);
        return new DataResponse($data);
    }
}
