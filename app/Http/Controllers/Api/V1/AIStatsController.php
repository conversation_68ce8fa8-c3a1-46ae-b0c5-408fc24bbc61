<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Helpers\GuzzleHelper;
use App\Traits\ResponseMessage;
use App\Traits\Paginator;
use Illuminate\Http\Request;
use App\Policy;
use App\Repositories\PolicyRepository;
use App\Repositories\PlanOverviewRepository;
use App\PlanOverview;

class AIStatsController extends Controller
{
    use ResponseMessage, Paginator;

    protected $policyRepository;

    public function __construct(PolicyRepository $policyRepository)
    {
        $this->policyRepository = $policyRepository;
    }

    public function getAICompare(Request $request)
    {
        try {
            $url = config('app.ai_stats.url') . "/api/compare";
            $headers = ['Content-Type' => 'application/json'];
            $token = config('app.ai_stats.token');
            $response = GuzzleHelper::getApiwithToken($url, $headers, $token);
            $data = @json_decode($response, true);
            if (!empty($data)) {
                return $this->successResponse('Success', $data);
            }
            return $this->failedResponse('Invalid response from service', 422);
            
        } catch (\Exception $e) {
            $statusCode = $e->getCode() ?: 500;
            return $this->failedResponse($e->getMessage(), $statusCode > 0 ? $statusCode : 500);
        }
    }

    public function getPolicyData(Request $request)
    {
        try {
            \Log::info("Entering getPolicyData");
            
            $page = $request->get('page', 1);
            $limit = $request->get('per_page',20);
            $type = $request->get('type');
            $policy_id = $request->get('policy_id');
            
            \Log::debug("Request parameters", ['page' => $page, 'limit' => $limit, 'type' => $type]);

            $url = config('app.ai_stats.url') . "/api/complete-policies?page=" . $page;
            if (!empty($type)) {
                $url .= "&type=" . $type;
            }
            
            if (!empty($limit)) {
                $url .= "&limit=" . $limit;
            }
            if (!empty($policy_id)) {
                $url .= "&policy_id=" . $policy_id;
            }

            \Log::info("AI Stats URL: " . $url);

            $headers = ['Content-Type' => 'application/json'];
            $token = config('app.ai_stats.token');
            $response = GuzzleHelper::getApiWithToken($url, $headers, $token);

            \Log::info("AI Stats Response: " . $response);

            $aiStatsData = json_decode($response, true);

            if (empty($aiStatsData) || !isset($aiStatsData['data'])) {
                \Log::error("Invalid response from AI stats service", ['response' => $response]);
                return $this->failedResponse('Invalid response from AI stats service', 422);
            }

            $aiPolicyIds = array_map(function($item) {
                return $item['policyId'];
            }, $aiStatsData['data']);

            \Log::debug("AI Policy IDs", ['aiPolicyIds' => $aiPolicyIds]);


            if(empty($aiPolicyIds)) {
                \Log::info("AI Policy IDs not found in response");
                return $this->successResponse('Success', []);
            }

            // Remove pagination from internal query to prevent misalignment
            $query = Policy::query()
                ->whereIn('policy_id', $aiPolicyIds)
                ->with(['planOverview', 'agentInfo', 'getMember'])
                ->orderBy('policy_id', 'DESC')
                ->get();

            \Log::debug("Fetched policies", ['policies' => $query]);

            // Create a collection from the results and maintain order from AI service
            $orderedPolicies = collect([]);
            foreach ($aiPolicyIds as $policyId) {
                $policy = $query->firstWhere('policy_id', $policyId);
                if ($policy) {
                    $orderedPolicies->push($policy);
                } else {
                    \Log::warning("Policy not found in internal database", ['policy_id' => $policyId]);
                }
            }

            $formattedData = $this->healthPlanFormattedData($orderedPolicies, $aiStatsData);

            \Log::info("Formatted data prepared");

            return $this->successResponse('Success', $formattedData);
            
        } catch (\Exception $e) {
            \Log::error("Exception in getPolicyData", ['exception' => $e]);
            $statusCode = $e->getCode() ?: 500;
            return $this->failedResponse($e->getMessage(), $statusCode > 0 ? $statusCode : 500);
        }
    }

    protected function healthPlanFormattedData($data, $aiStatsData)
    {
        $result = [];
        foreach ($data as $d) {
            $result[] = $this->healthPlanSingleFormattedItem($d,$aiStatsData);
        }

        $paginator = $this->paginator(
            collect($result),
            $aiStatsData['total'],
            $aiStatsData['limit'],
            $aiStatsData['page'],
            ['path' => request()->url()]
        );


        return [
            'data' => $result,
            'links' => $this->links($paginator),
            'meta' => $this->meta($paginator),
            'hasNext' => $aiStatsData['hasNext'] ?? false,
            'hasPrevious' => $aiStatsData['hasPrevious'] ?? false,
            'total' => $aiStatsData['total'] ?? count($result)
        ];
    }

    protected function healthPlanSingleFormattedItem($d,$aistats)
    {
        $userFullName = isset($d->getMember) ? $d->getMember->fullname : null;
        $planOverviewRepo = new PlanOverviewRepository(new PlanOverview());
        $healthPlan = isset($d->healthPlanOverview) ?
            $planOverviewRepo->singleFormattedItem($d->healthPlanOverview)
            : null;
        $agentName = isset($d->agentInfo) ? $d->agentInfo->fullname : null;
        
        $aiData = $this->getPolicyAIData($aistats['data'], $d->policy_id);
        
        return [
            'policyId' => $d->policy_id,
            'userName' => $userFullName,
            'enrollmentDate' => $d->enrollment_date ? date('m/d/y', strtotime($d->enrollment_date)) : null,
            'effectiveDate' => $d->effective_date ? date('m/d/y', strtotime($d->effective_date)) : null,
            'status' => $d->status,
            'healthPlan' => $healthPlan,
            'agentName' => $agentName,
            'agentCode' => isset($d->agentInfo) ? $d->agentInfo->agent_code : null,
            'agentId' => isset($d->agentInfo) ? $d->agentInfo->agent_id : null,
            'recommendation'=> $aiData['recommendation'],
            'is_present' => $aiData['is_present'],
            'plan_price' => isset($d->healthPlanOverview) && isset($d->healthPlanOverview->price_male_nons) ? 
                number_format($d->healthPlanOverview->price_male_nons, 2, '.', '') : null,
            'ai_status' => $aiData['ai_status'],
            'system_status' => $aiData['system_status']
        ];
    }

    protected function getPolicyAIData($aiStatsData, $policyId) {
        static $policyMap = [];
        
        if (empty($policyMap)) {
            foreach ($aiStatsData as $policy) {
                $policyMap[$policy['policyId']] = [
                    'recommendation' => $policy['result']['critical_analysis']['recommendation'] ?? null,
                    'is_present' => empty($policy['result']) ? 'no' : 'yes',
                    'ai_status' => $policy['ai_status'] ?? null,
                    'system_status' => $policy['system_status'] ?? null
                ];
            }
        }
        
        return $policyMap[$policyId] ?? [
            'recommendation' => null,
            'is_present' => 'no',
            'ai_status' => null,
            'system_status' => null
        ];
    }

    public function getAIIHACompare(Request $request){
        try {
            $url = config('app.ai_iha_stats.url')."api/v1/iha/ai-prediction-report" ;
            $headers = ['Content-Type' => 'application/json'];
            $token = config('app.ai_iha_stats.token');
            $response = GuzzleHelper::getApiwithToken($url, $headers, $token);
            $data = @json_decode($response, true);
            if (!empty($data)) {
                return $this->successResponse('Success', $data);
            }
            return $this->failedResponse('Invalid response from service', 422);
            
        } catch (\Exception $e) {
            $statusCode = $e->getCode() ?: 500;
            return $this->failedResponse($e->getMessage(), $statusCode > 0 ? $statusCode : 500);
        }
    }

    public function getIHAPolicyData(Request $request){

        try{
            $page = $request->get('page', 1);
            $per_page = $request->get('per_page',20);
            $type = $request->get('type');
            $policy_id = $request->get('policy_id');


            $url= config('app.ai_iha_stats.url')."api/v1/iha/member-ai-prediction?page=" . $page;

            if (!empty($type)) {
                $url .= "&type=" . $type;
            }
            if (!empty($per_page)) {
                $url .= "&per_page=" . $per_page;
            }
            if (!empty($policy_id)) {
                $url .= "&policy_id=" . $policy_id;
            }
            $headers = ['Content-Type' => 'application/json'];
            $token = config('app.ai_iha_stats.token');
            $response = GuzzleHelper::getApiwithToken($url, $headers, $token);
            $data = @json_decode($response, true);
            if (!empty($data)) {
                return $this->successResponse('Success', $data['data']);
            }
            return $this->failedResponse('Invalid response from service', 422);

        }
        catch (\Exception $e) {
            $statusCode = $e->getCode() ?: 500;
            return $this->failedResponse($e->getMessage(), $statusCode > 0 ? $statusCode : 500);
        }
        
    }
}
