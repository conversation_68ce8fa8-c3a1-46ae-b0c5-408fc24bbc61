<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\PolicyRider\AddPolicyRiderRequest;
use App\Http\Requests\PolicyRider\TerminateRiderRequest;
use App\Http\Resources\DataResponse;
use App\Repositories\PolicyRider\PolicyRiderFeatures;
use Illuminate\Http\Request;

class PolicyRiderController extends Controller
{
    public function __construct(PolicyRiderFeatures $policyRider)
    {
        $this->policyRider = $policyRider;
    }

    public function getRiderPlanList($policyID)
    {
        $data = $this->policyRider->getRiderList($policyID);
        return new DataResponse($data);
    }

    public function addPolicyRider(AddPolicyRiderRequest $request)
    {
        $data = $this->policyRider->addPolicyRiderPlan($request);
        return new DataResponse($data);
    }

    public function terminateRider(TerminateRiderRequest $request)
    {
        $data = $this->policyRider->terminateWithDrawRiderPolicy($request);
        return new DataResponse($data);
    }
}
