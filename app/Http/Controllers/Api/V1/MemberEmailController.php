<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\Members\MemberEmail;
use Illuminate\Http\Request;

class MemberEmailController extends Controller
{
    //
    public function __construct()
    {
        $this->memberEmailModel = new MemberEmail();
    }
    public function sendReminderEmailMemberApproval($schedule)
    {
        $data = $this->memberEmailModel->sendMemberScheduledEmail($schedule);
        return new DataResponse($data);
    }
    public function sendMemberMedicalPlanEffectiveNotification()
    {
        $data = $this->memberEmailModel->sendMemberMedicalPlanEffectiveNotification();
    }
    public function sendMemberAlertEmailPolicyUpgrade()
    {
        $data = $this->memberEmailModel->sendMemberAlertEmailPolicyUpgrade();
        return new DataResponse($data);
    }
}
