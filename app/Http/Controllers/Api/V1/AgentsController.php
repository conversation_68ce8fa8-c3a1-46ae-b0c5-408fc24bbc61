<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Repositories\Agents\Agents;

use Illuminate\Http\Request;
use App\Http\Requests\Agent\AgentInfoRequest;

class AgentsController extends Controller
{
    private $agentRepo;

    public function __construct(Agents $agentRepo)
    {
        $this->agentRepo = $agentRepo;
    }

    public function getDownlineAgents($uplineRepId)
    {
      return  $this->agentRepo->getDownlineAgents($uplineRepId);
    }

    public function getAdditionalInfoDownlineAgents($uplineRepId)
    {
      return  $this->agentRepo->getDownlineAgents($uplineRepId, 'additionInfo');
    }

    public function totalActiveAgents(Request $request)
    {
      $filters = $request->query->all();
      return $this->agentRepo->totalActiveAgents($filters);
    }
    public function getDownlineAgentsExists($agentID)
    {
      return  $this->agentRepo->getDownlineAgentsExists($agentID);
    }
    // Get an agent info
    public function getAgentInfo(AgentInfoRequest $request){
      return  $this->agentRepo->getAgentInfo($request);
    }
}
