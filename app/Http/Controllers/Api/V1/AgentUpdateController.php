<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\AgentUpdatesHelper;
use App\Http\Requests\AgentUpdates\ChangeAgentUplineRequest;
use App\Http\Requests\ManageAgent\UpdateAgentSessionRequest;
use App\Repositories\Agents\AgentUpdate;
use App\Http\Resources\DataResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\AgentUpdates\UpdateAgentStatusRequest;
use App\Http\Requests\AgentUpdates\UpdateAgentLevelStatusRequest;
use App\Http\Requests\AgentUpdates\UpdateAgentGroupRequest;
use Illuminate\Http\Request;

class AgentUpdateController extends Controller
{
    protected $agentUpdateModel;
    public function __construct(AgentUpdate $agentUpdateModel)
    {
        $this->agentUpdateModel = $agentUpdateModel;
    }
    public function updateAgentStatus(UpdateAgentStatusRequest $request,AgentUpdatesHelper $agentUpdatesHelper)
    {
        $data = $this->agentUpdateModel->editAgentStatus($request,$agentUpdatesHelper);
        return new DataResponse($data);
    }
    public function updateAgentLevel(UpdateAgentLevelStatusRequest $request,AgentUpdatesHelper $agentUpdatesHelper)
    {
        $data = $this->agentUpdateModel->editAgentLevel($request,$agentUpdatesHelper);
        return new DataResponse($data);
    }
    public function activateAgent(Request $request,AgentUpdatesHelper $agentUpdatesHelper)
    {
        $data = $this->agentUpdateModel->activateAgent($request,$agentUpdatesHelper);
        return new DataResponse($data);
    }
    public function updateAgentLevelStatus(UpdateAgentLevelStatusRequest $request,AgentUpdatesHelper $agentUpdatesHelper)
    {
        $data = $this->agentUpdateModel->editAgentLevelStatus($request,$agentUpdatesHelper);
        return new DataResponse($data);
    }
    public function updateAgentGroup(UpdateAgentGroupRequest $request)
    {
        $data = $this->agentUpdateModel->editAgentGroup($request);
        return new DataResponse($data);
    }
    public function updateAgentSession(UpdateAgentSessionRequest $request,AgentUpdatesHelper $agentUpdatesHelper)
    {
        $data = $this->agentUpdateModel->editAgentSession($request,$agentUpdatesHelper);
        return new DataResponse($data);
    }
    public function changeAgentUpline(ChangeAgentUplineRequest $request,AgentUpdatesHelper $agentUpdatesHelper)
    {
        $data = $this->agentUpdateModel->changeAgentUpline($request,$agentUpdatesHelper);
        return new DataResponse($data);
    }
}
