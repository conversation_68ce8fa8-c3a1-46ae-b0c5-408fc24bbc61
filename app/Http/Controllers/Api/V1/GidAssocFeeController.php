<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\AssocFee\DeleteSelectedAssocFeeRequest;
use App\Http\Requests\AssocFee\GroupAssocFeeRequest;
use App\Repositories\AssocFee\GidAssocFeeRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;

class GidAssocFeeController extends Controller
{
    use ResponseMessage;
    /**
     * @var GidAssocFeeRepository
     */
    private $repository;

    /**
     * AssocFeeController constructor.
     * @param GidAssocFeeRepository $repository
     */
    public function __construct(GidAssocFeeRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $filters = $request->query->all();
        $data = $this->repository->paginatedFormattedList(request()->query('per_page') ?? 20, $filters);
        return $this->successResponse('Success', $data);
    }      

    public function getOptions(Request $request)
    {
        $filters = $request->query->all();
        $data = $this->repository->getOptions($filters);
        return $this->successResponse('Success', $data);
    }

    public function create(GroupAssocFeeRequest $request)
    {
        $data = $request->validated();
        return $this->repository->createGroupAssoc($data);
    }

    public function delete($aId)
    {
        return $this->repository->delete($aId);
    }

    public function deleteSelected(DeleteSelectedAssocFeeRequest $request){
        return $this->repository->deleteSelectedGroupAssocFee($request->validated());
    }

}
