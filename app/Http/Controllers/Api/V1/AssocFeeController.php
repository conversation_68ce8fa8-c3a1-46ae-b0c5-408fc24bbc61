<?php

namespace App\Http\Controllers\Api\V1;

use App\AssocFee;
use App\Http\Controllers\Controller;
use App\Http\Requests\AssocFee\DeleteSelectedAssocFeeRequest;
use App\Http\Requests\AssocFee\WebsiteAssocFeeRequest;
use App\Http\Requests\AssocFee\GroupAssocFeeRequest;
use App\Repositories\AssocFee\AssocFeeRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssocFeeController extends Controller
{
    use ResponseMessage;

    /**
     * @var AssocFeeRepository
     */
    private $repository;

    /**
     * AssocFeeController constructor.
     * @param AssocFeeRepository $repository
     */
    public function __construct(AssocFeeRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $filters = $request->query->all();
        $data = $this->repository->paginatedFormattedList(request()->query('per_page') ?? 20, $filters);
        return $this->successResponse('Success', $data);
    }

    public function getOptions()
    {
        $data = $this->repository->getOptions();
        return $this->successResponse('Success', $data);
    }

    public function create(WebsiteAssocFeeRequest $request)
    {
        $data = $request->validated();
        return $this->repository->createWebsiteAssoc($data);
    }

    public function delete($aId)
    {
        return $this->repository->delete($aId);
    }

    public function deleteSelected(DeleteSelectedAssocFeeRequest $request){
        return $this->repository->deleteSelectedWebsiteAssocFee($request->validated());
    }

    public function getAssociationFeesSearch($policy_id){
        return $this->repository->getAssociationFeesSearch($policy_id);
    }

}
