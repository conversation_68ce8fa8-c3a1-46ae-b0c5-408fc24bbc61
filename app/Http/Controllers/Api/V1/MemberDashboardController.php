<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Repositories\Members\MemberDashboardRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;

class MemberDashboardController extends Controller
{
    use ResponseMessage;

    private $memberDashboardModel;

    public function __construct(MemberDashboardRepository $member)
    {
        $this->memberDashboardModel = $member;
    }

    public function totalMemberAnalytics(Request $request,$fdate=null,$tdate=null)
    {
        $filters = $request->query->all();
        $data = $this->memberDashboardModel->totalAnalytics($filters,$fdate,$tdate);
        return $this->successResponse($data['status'], $data['data']);
    }

    public function totalDependentAnalytics(Request $request)
    {
        $filters = $request->query->all();
        $data = $this->memberDashboardModel->totalAnalyticsOfDependents($filters);
        return $this->successResponse($data['status'], $data['data']);
    }

    public function totalPoliciesAnalytics(Request $request)
    {
        $filters = $request->query->all();
        $data = $this->memberDashboardModel->totalAnalyticsOfPolicies($filters);
        return $this->successResponse($data['status'], $data['data']);
    }

    public function totalProgressOfYear(Request $request, $fyear, $tyear)
    {
        $filters = $request->query->all();
        $data = $this->memberDashboardModel->totalYearProgress($fyear, $tyear, $filters);
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function totalProgressOfMonths(Request $request, $fdate, $tdate)
    {
        $filters = $request->query->all();
        $data = $this->memberDashboardModel->totalMonthsProgress($fdate, $tdate, $filters);
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function totalProgressOfWeek(){
        $data = $this->memberDashboardModel->totalWeekProgress();
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function groupByOther(Request $request)
    {
        $filters = $request->query->all();
        $data = $this->memberDashboardModel->groupingByOther($filters);
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }
}
