<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use App\Repositories\UploadPolicyDocument;
use App\Http\Requests\PolicyUpdates\AddPolicyDocument;
use App\Http\Requests\PolicyUpdates\AddNewFiles;
use App\Http\Requests\PolicyUpdates\DeletePolicyDocument;
use App\Http\Requests\PolicyUpdates\AddPolicyLetter;
use App\Http\Requests\PolicyUpdates\EditPolicyLetter;
use App\Http\Requests\PolicyUpdates\AddPolicyTaxDocumentRequest;
use App\Http\Requests\PolicyUpdates\RemovePolicyGroup;
use App\Repositories\PolicyUpdateLog;

class PolicyAppsUploadController extends Controller
{
    //
    public function __construct()
    {
        $this->uploadPolicyDocument = new UploadPolicyDocument();
        $this->policyUpdateLogModel = new PolicyUpdateLog();
    }
    public function addPolicyApps(AddPolicyDocument $request)
    {
        $data = $this->uploadPolicyDocument->addPolicyDocument($request);
        if($data['type'] == 'success')
        {
            $msg = $this->policyUpdateLogModel->addPolicyUpdates($request,'DOC'); 
        }
        return new DataResponse($data);        
    }
    public function viewPolicyApps(Request $request)
    {
        $data = $this->uploadPolicyDocument->viewPolicyDocument($request);
        return new DataResponse($data);
    }
    public function deletePolicyApps(DeletePolicyDocument $request)
    {
        $data = $this->uploadPolicyDocument->deletePolicyDocument($request);
        if($data['type'] == 'success')
        {
            $msg = $this->policyUpdateLogModel->addPolicyUpdates($request,'DOC'); 
        }
        return new DataResponse($data);
    }
    public function uploadPolicyLetter(AddPolicyLetter $request)
    {
        $data = $this->uploadPolicyDocument->addPolicyLetter($request);
        if($data['type'] == 'success')
        {
            $msg = $this->policyUpdateLogModel->addPolicyUpdates($request,'LCUP'); 
        }
        return new DataResponse($data);
    }
    public function updatePolicyLetter(EditPolicyLetter $request)
    {
        $data = $this->uploadPolicyDocument->editPolicyLetter($request);
        if($data['type'] == 'success')
        {
            $msg = $this->policyUpdateLogModel->addPolicyUpdates($request,'LCUP'); 
        }
        return new DataResponse($data);
    }
    public function getPolicyTaxDocument(RemovePolicyGroup $request)
    {
        $data = $this->uploadPolicyDocument->viewPolicyTaxDocument($request);
        return new DataResponse($data);
    }
    public function uploadPolicyTaxDocument(AddPolicyTaxDocumentRequest $request)
    {
        $data = $this->uploadPolicyDocument->addPolicyTaxDocument($request);
        if($data['type'] == 'success')
        {
            $msg = $this->policyUpdateLogModel->addPolicyUpdates($request,'TAX'); 
        }
        return new DataResponse($data);
    }
    public function uploadPolicyTaxDocumentReminderEmail(RemovePolicyGroup $request)
    {
        $data = $this->uploadPolicyDocument->addPolicyTaxDocumentReminderEmail($request);
        return new DataResponse($data);
    }    
    public function uploadNewFiles(AddNewFiles $request)
    {
        $data = $this->uploadPolicyDocument->addNewFiles($request);
        return new DataResponse($data);
    }    

}
