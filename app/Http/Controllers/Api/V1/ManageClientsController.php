<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\GetPolicyDetails;
use App\Http\Requests\ManageClientsList;
use App\Http\Resources\DataResponse;
use App\Repositories\Members\ManageClients;
use App\Traits\ResponseMessage;
use Carbon\Exceptions\InvalidFormatException;
use Illuminate\Http\Request;
use App\Http\Requests\Member\UpdatePlatform;
use App\Http\Requests\Member\DefaultOTPForMemberRequest;
use DB;
use App\Http\Requests\Member\ChangePasswordRequest;

class ManageClientsController extends Controller
{
    use ResponseMessage;

    private $manageClientsModel;

    public function __construct()
    {
        $this->manageClientsModel = new ManageClients();
    }
    public function manageClientsInfo(Request $request)
    {
        try{
        $data = $this->manageClientsModel->paginatedFormattedList($request->query->get('per_page') ?? 20, $request->query->all());
        return $this->successResponse('Success', $data);
//        } catch(InvalidFormatException $exception) {
//            return $this->failedResponse('Date format should be mm/dd/yyyy.');
        } catch (\Exception $exception) {
            $status = $exception->getCode() ?: 400;
            return $this->failedResponse($exception->getMessage(), $status);
//          return $this->failedResponse('Could not get list. Please try again.');
        }
    }

    public function clientsPlansDetail(Request $request){
        return new DataResponse($this->manageClientsModel->clientsPlansDetail($request));
    }

    public function clientsPlansCat(){
        return new DataResponse($this->manageClientsModel->clientsPlansCat());
    }

    public function clientsPlansType(){
        return new DataResponse($this->manageClientsModel->clientsPlansType());
    }

    public function policyDetails(GetPolicyDetails $request)
    {
        $data = $this->manageClientsModel->getClientDetails($request);
        return new DataResponse($data);
    }
    public function resetAppSecurity(GetPolicyDetails $request)
    {
        $data = $this->manageClientsModel->resetAppSecurity($request);
        return new DataResponse($data);
    }
    public function deleteTestPolicy(GetPolicyDetails $request)
    {
        return $this->manageClientsModel->deleteTestPol($request);
    }

    public function calculatePrudPrice(Request $request){
        return $this->manageClientsModel->prudplanprice((array)$request->all());
    }
    public function getAgentPlatform($agentId)
    {
        return $this->manageClientsModel->getAgentPlatform($agentId);
    }
    public function updateAgentPlatform(UpdatePlatform $request){
        return $this->manageClientsModel->updateAgentPlatform($request);
    }
    public function getClientDetail($id){
        return $this->manageClientsModel->getClientDetail($id);
    }
    public function getDefaultOTP(DefaultOTPForMemberRequest $request)
    {
        try {
            $cemail = DB::connection('mysql')->table('userinfo')
                ->where('userid', $request->user_id)
                ->value('cemail');

            $default_otp = null;
            if($cemail)
            {
                $userid = DB::connection('mysql3')->table('sso_users')
    ->where('email', $cemail)
    ->where('user_type', 'M')
    ->value('id');

            if($userid)
            {
                $default_otp = DB::connection('mysql3')->select('
                SELECT otp
                FROM default_otp
                WHERE user_id = ?', [$userid]
            );
        }
            }
            if($default_otp){
                return $this->successResponse('Success', $default_otp);
            }
            else{
                return $this->failedResponse('Could not get list. Please try again.');
            }
        } catch (\Exception $e) {
            return $this->failedResponse($e->getMessage());
        }
    }
    public function changeMemberPassword(ChangePasswordRequest $request)
    {
        return  $this->manageClientsModel->changeMemberPassword($request);
    }

    public function clientPlanPricing(Request $request){
        return $this->successResponse('Plan Details with price has been fetched successfully',
         $this->manageClientsModel->clientPlanPricing($request)
        );
    }
    public function getTermedDates(Request $request){
        return $this->successResponse('Effective date list has been fetched successfully',
            $this->manageClientsModel->getTermedDates($request->effective_date)
       );
    }

}
