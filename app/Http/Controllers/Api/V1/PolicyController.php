<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\PoliciesExport;
use App\Helpers\GuzzleHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Policy\ChangeEffectiveDateRequest;
use App\Http\Requests\Policy\DownloadHealthPlanRequest;
use App\Http\Resources\DataResponse;
use App\Policy;
use App\Repositories\PolicyRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PolicyController extends Controller
{
    use ResponseMessage;
    /**
     * @var PolicyRepository
     */
    private $repository;

    public function __construct(PolicyRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $filters = $request->query->all();
        return $this->repository->paginatedPolicyFormattedList(20, $filters);
        //        return DataResponse::collection($policies);
    }

    public function show($policyId)
    {
        $policy = $this->repository->getPolicyDetail($policyId);
        if ($policy) {
            return new DataResponse($policy);
        } else {
            return $this->failedResponse();
        }
    }


    public function getHealthPlanPolicies(Request $request)
    {
        $filters = $request->query->all();
        $perPage = $request->query('per_page') ?? 20;
        $data = $this->repository->paginatedHealthPlanPolicies($perPage, $filters);
        $policyIds = array_column($data['data'], 'policyId');
        $policyIdParam = implode('&policy_id[]=', $policyIds);
        $url = config('app.ai_recommendation_url'). "api/description?policy_id[]=" . $policyIdParam;
        $headers = ['Content-Type' => 'application/json'];
        $token = config('app.ai_recommendation_token');
        $getData = GuzzleHelper::getApiwithToken($url, $headers, $token);
        $recommendations = json_decode($getData, true);
        if(!isset($recommendations['error'])){
            $recommendationMap = [];
            if ($recommendations) {
                foreach ($recommendations as $recommendation) {
                    $recommendationMap[$recommendation['policyId']] = $recommendation;
                }
            }
            foreach ($data['data'] as &$policyItem) {
                $policyId = $policyItem['policyId'];

                $recommendationData = $recommendationMap[$policyId] ?? null;
                $policyItem['is_present'] = $recommendationData ? 'yes' : 'no';

                if ($recommendationData) {
                    $policyItem['recommendation'] = $recommendationData['result']['critical_analysis']['recommendation'] ?? null;
                    $approvedPolicies = array_column($recommendationData['result']['existing_data']['approved'] ?? [], 'policy_id');
                    $notApprovedPolicies = array_column($recommendationData['result']['existing_data']['not_approved'] ?? [], 'policy_id');
                    $policyItem['approvedPolicyIds'] = $approvedPolicies;
                    $policyItem['notApprovedPolicyIds'] = $notApprovedPolicies;
                } else {
                    $policyItem['recommendation'] = null;
                    $policyItem['approvedPolicyIds'] = [];
                    $policyItem['notApprovedPolicyIds'] = [];
                }
            }

            return $this->successResponse('Success', $data);
        }else{
            return $this->successResponse('Success',$data);
        }
    }

    public function processPolicy($policyId)
    {
        $url = config('app.ai_recommendation_url'). "api/process-policy/{$policyId}";
        $headers = ['Content-Type' => 'application/json'];
        $token = config('app.ai_recommendation_token');
        $responseJson = GuzzleHelper::putApiWithToken($url, $headers, [], $token);
        $response = json_decode($responseJson, true);
        return $response;
    }


    public function getMedicalDetails($policyId)
    {
        $url = config('app.ai_recommendation_url') . "api/description/{$policyId}";

        $headers = ['Content-Type' => 'application/json'];
        $token = config('app.ai_recommendation_token');
        // Fetch data from external API
        $responseJson = GuzzleHelper::getApiwithToken($url, $headers, $token);
        $response = json_decode($responseJson, true);
        return $response;
    }

    public function downloadHealthPlanExcel(DownloadHealthPlanRequest $request)
    {
        $filters = $request->validated();
        $filters['withoutStatus'] = Policy::STATUS_WITHDRAWN;
        $policies = $this->repository->getHealthPlanPoliciesQuery($filters)->get();
        if ($policies->isNotEmpty()) {
            $excel = new PoliciesExport($policies);
            $fileFormat = \Maatwebsite\Excel\Excel::XLS;
            $myFile = Excel::raw($excel, $fileFormat);

            $file = "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64," . base64_encode($myFile);

            return $this->successResponse('Excel Sheet Exported successfully', [
                'filename' => 'policies-' . $filters['effective_date'] . '.' . $fileFormat,
                'file' => $file,
                'downloadStatus' => true
            ]);
        } else {
            return $this->successResponse('No Data to export', [
                'downloadStatus' => false
            ]);
        }
    }

    public function getHealthPlanEffectiveDates()
    {
        $dates = $this->repository->getEffectiveDates(['healthPlanExists' => true]);
        $message = "Total " . count($dates) . " effective dates found.";
        return $this->successResponse($message, $dates);
    }

    public function changeEffectiveDate(ChangeEffectiveDateRequest $request)
    {
        return $this->repository->changePolicyEffectiveDate($request);
    }


    //    public function downloadExcel(Request $request)
    //    {
    //        $filters = $request->query->all();
    //        $policies =$this->repository->getHealthPlanPoliciesQuery($filters)->get();
    //        $excel = new PoliciesExport($policies);
    //        return $excel->download('test.'.\Maatwebsite\Excel\Excel::XLS);
    //    }
    //
    //    public function viewExcel(Request $request)
    //    {
    //        $filters = $request->query->all();
    //        $data['policies'] = Policy::query()->where('policy_id', '=', $filters['policy_id'])->get();
    //        $data['policies'] = $this->repository->getHealthPlanPoliciesQuery($filters)->take(4)->get();
    //        return view('excel.policies-export', $data);
    //    }

}
