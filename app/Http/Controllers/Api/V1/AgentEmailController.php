<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\Agents\AgentEmail;
use Illuminate\Http\Request;

class AgentEmailController extends Controller
{
    //
    protected $agentEmailModel;
    public function __construct()
    {
        $this->agentEmailModel = new AgentEmail();
    }
    public function sendReminderEmailRepresentativeActivationIncomplete()
    {
        $data = $this->agentEmailModel->sendReminderEmailRepresentativeActivationIncomplete();
        return new DataResponse($data);
    }
    public function sendAgentWeeklyReportEmail()
    {
        $this->agentEmailModel->sendAgentRegistrationWeeklyReport();
    }
}
