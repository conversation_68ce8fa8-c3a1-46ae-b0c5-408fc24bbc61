<?php

namespace App\Http\Controllers\Api\V1;

use App\GroupInfo;
use App\Http\Requests\Group\AssocFeeWaiverRequest;
use App\Http\Requests\ManageGroup\DeleteGroupRequest;
use App\Http\Requests\ManageGroup\GroupLogoEditRequest;
use App\Http\Requests\ManageGroup\SuspendGroupRequest;
use App\Http\Requests\ManageGroup\UpdateGroupBillingDetailRequest;
use App\Http\Requests\ManageGroup\UpdateGroupMainAgentRequest;
use App\Http\Requests\ManageGroup\UpdateGroupPaymentEftMethodWithEftBankRequest;
use App\Http\Requests\ManageGroup\UpdateGroupPaymentMethodRequest;
use App\Http\Requests\ManageGroup\UpdateGroupTypeRequest;
use App\Http\Requests\ManageGroup\UpdateWebDisplayRequest;
use App\Http\Requests\ManageGroup\UpdateGroupBasicDetailRequest;
use App\Traits\ResponseMessage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Requests\GetGroupInfo;
use App\Http\Controllers\Controller;
use App\Http\Requests\ManageGroup\ChangeGroupPasswordRequest;
use App\Http\Requests\ManageGroup\groupFilerequest;
use App\Http\Requests\ManageGroup\SubGroupEditRequest;
use App\Http\Requests\ManageGroup\SubGroupRequest;
use App\Http\Resources\DataResponse;
use App\Repositories\Groups\ManageGroups;

class ManageGroupsController extends Controller
{
    use ResponseMessage;

    /**
     * @var ManageGroups
     */
    private $manageGroupsModel;

    /**
     * ManageGroupsController constructor.
     * @param ManageGroups $manageGroups
     */
    public function __construct(ManageGroups $manageGroups)
    {
        $this->manageGroupsModel = $manageGroups;
    }
    public function manageGroupsInfo()
    {
        $data = $this->manageGroupsModel->manageGroups();
        return new DataResponse($data);
    }
    public function viewGroupInfo(GetGroupInfo $request)
    {
        $data = $this->manageGroupsModel->viewGroupDetail($request);
        return new DataResponse($data);
    }

    public function groupList(Request $request){
        $filters = $request->query->all();
        $data =  $this->manageGroupsModel->paginatedFormattedList(request()->query('per_page') ?? 20, $filters);
        return $this->successResponse('Success',$data);
    }

    public function groupSearchList(Request $request){
        return $this->manageGroupsModel->groupSearchList($request);
    }

    public function groupDetail($groupId)
    {
        return $this->manageGroupsModel->groupDetail($groupId);
    }

    public function groupAgents($groupId,Request $request){
        $filters = $request->query->all();
        return $this->manageGroupsModel->groupAgents($groupId,5, $filters);
    }

    public function eligibilityLogs($groupId){
        return $this->manageGroupsModel->groupEligibilityLogs($groupId);
    }

    public function editGroupLogo(GroupLogoEditRequest $request){
        return $this->manageGroupsModel->editGroupLogo($request->validated());
    }

    public function groupAgentList(Request $request, $agentId)
    {
        $filters = $request->query->all();
        return $this->manageGroupsModel->getGroupAgents($agentId, $filters);
    }

    public function updateMainAgent(UpdateGroupMainAgentRequest $request)
    {
        return $this->manageGroupsModel->updateGroupAgent($request->validated());
    }

    public function updateWebDisplay(UpdateWebDisplayRequest $request){
        return $this->manageGroupsModel->updateWebDisplay($request->validated());
    }

    public function getGroupIndustries(){
        return $this->manageGroupsModel->getGroupIndustries();
    }

    public function updateBasicDetail(UpdateGroupBasicDetailRequest $request){
        return $this->manageGroupsModel->updateGroupBasicDetail($request->validated());
    }

    public function updateBillingDetail(UpdateGroupBillingDetailRequest $request){
        return $this->manageGroupsModel->updateBillingDetail($request->validated());
    }


    public function updateEftWithBankDetail(UpdateGroupPaymentEftMethodWithEftBankRequest $request){
        return $this->manageGroupsModel->updateEftWithBankDetail($request->validated());
    }

    public function updatePaymentMethod(UpdateGroupPaymentMethodRequest $request){
        return $this->manageGroupsModel->updatePaymentMethod($request->validated());
    }

    public function suspendAccount(SuspendGroupRequest $request){
        return $this->manageGroupsModel->suspendAccount($request->validated());
    }

    public function deleteGroup(DeleteGroupRequest $request){
        return $this->manageGroupsModel->deleteGroup($request->validated());
    }

    public function groupActivity($groupId){
        return $this->manageGroupsModel->getGroupActivityDetail($groupId);
    }

    public function getGrouptypes(){
        return $this->manageGroupsModel->getGroupTypes();
    }

    public function updateGroupType(UpdateGroupTypeRequest $request){
        return $this->manageGroupsModel->updateGroupType($request->validated());
    }

    public function addGroupFile(groupFilerequest $request){
        return $this->manageGroupsModel->addGroupFile($request);
    }

    public function deleteGroupFile($id){
        return $this->manageGroupsModel->deleteGroupFile($id);
    }

    public function getGroupFiles(Request $request){
        return $this->manageGroupsModel->getGroupFiles($request);
    }

    public function addSubGroups(SubGroupRequest $request){
        return $this->manageGroupsModel->addSubGroups($request);
    }

    public function deleteSubGroups(Request $request){
        return $this->manageGroupsModel->deleteSubGroups($request);
    }

    public function getSubGroups(Request $request){
        return $this->manageGroupsModel->getSubGroups($request->gid);
    }

    public function editSubGroups(SubGroupEditRequest $request){
        return $this->manageGroupsModel->editSubGroups($request);
    }

    public function getGroupDetail($id){
        return $this->manageGroupsModel->getGroupDetail($id);
    }

    public function changeGroupPassword(ChangeGroupPasswordRequest $request){
        return $this->manageGroupsModel->changeGroupPassword($request);
    }

    public function waiveGroupAssociationFee(AssocFeeWaiverRequest $request): JsonResponse
    {
        $groupId = $request->validated()['group_id'];
        $scheduleMonth = $request->validated()['schedule_month'];
        $group = GroupInfo::find($groupId);

        return $this->manageGroupsModel->waiveGroupAssociationFee($group, $scheduleMonth);
    }

    public function getGroupList(Request $request){
        return new DataResponse($this->manageGroupsModel->getGroupList($request));
    }

    public function updateGroupLevelStatus(Request $request){
        return $this->manageGroupsModel->updateGroupLevelStatus($request);
    }

    public function updateGroupGuaranteedStatus(Request $request){
        return $this->manageGroupsModel->updateGroupGuaranteedStatus($request);
    }

    public function getGroupPhLevels(){
        return $this->manageGroupsModel->getGroupPhLevels();
    }

    public function updateGroupLumpSumPay(Request $request){
        return $this->manageGroupsModel->updateGroupLumpSumPay($request);
    }
}
