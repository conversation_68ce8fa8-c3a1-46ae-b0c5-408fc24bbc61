<?php

namespace App\Http\Controllers\Api\V1;

use App\Traits\ResponseMessage;
use App\Repositories\Groups\GroupDashboard;
use App\Http\Resources\DataResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\GroupDashboard\MemberGroup;

class GroupDashboardController extends Controller
{
    use ResponseMessage;

    /**
     * @var GroupDashboard
     */
    private $groupDashboardModel;

    public function __construct(GroupDashboard $groupDashboard)
    {
        $this->groupDashboardModel = $groupDashboard;
    }

    public function totalGroupAnalytics($from_date = null,$to_date = null)
    {
        $data = $this->groupDashboardModel->totalAnalytics($from_date,$to_date);
        return $this->successResponse($data['status'], $data['data']);
    }

    public function totalProgressOfYears($fyear, $tyear)
    {
        $data = $this->groupDashboardModel->totalYearProgress($fyear, $tyear);
        if ($data['status'] == 'success') {
            return  $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function totalProgressOfMonths($from_date, $to_date)
    {
        $data = $this->groupDashboardModel->totalMonthsProgress($from_date, $to_date);
        if ($data['status'] == 'success') {
            return  $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function totalProgressOfWeek()
    {
        $data = $this->groupDashboardModel->totalWeekProgress();
        if ($data['status'] == 'success') {
            return  $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function groupByOther()
    {
        $data = $this->groupDashboardModel->groupByOtherCondition();
        if ($data['status'] == 'success') {
            return  $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function groupByMemberRange(MemberGroup $request)
    {
        $data = $this->groupDashboardModel->getGroupsByMemberRange($request);
        if ($data['status'] == 'success') {
            return  $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }
}
