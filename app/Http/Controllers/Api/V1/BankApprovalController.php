<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Repositories\BankInfo;
use App\Http\Controllers\Controller;
use App\Http\Requests\ApproveBankInfo;
use App\Http\Resources\DataResponse;

class BankApprovalController extends Controller
{
    public function __construct()
    {
        $this->bankinfoModel = new BankInfo();
    }

    public function filterBankInfo()
    {
        $data = $this->bankinfoModel->getBankInfo();
        return new DataResponse($data);
    }

    public function approveBankInfo(ApproveBankInfo $request)
    {
        $bankid = $request->bank_id;
        $update = $this->bankinfoModel->updateBankInfo($bankid, '1',$request->reason);
        return new DataResponse($update);
    }

    public function rejectBankInfo(ApproveBankInfo $request)
    {
        $bankid = $request->bank_id;
        $update = $this->bankinfoModel->updateBankInfo($bankid, '2',$request->reason);
        return new DataResponse($update);
    }
}
