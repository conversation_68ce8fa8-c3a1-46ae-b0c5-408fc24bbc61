<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\Plans\ManagePlans;
use App\Repositories\Plans\PlanDetails;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    public function __construct()
    {
        $this->PlanModel = new ManagePlans();
        $this->PlanDetail = new PlanDetails();
    }
    public function getPlans()
    {
        $data = $this->PlanDetail->allPlans();
        return new DataResponse($data);
    }
    public function newPlan(Request $request)
    {
        $data = $this->PlanModel->newPlan($request);
        return new DataResponse($data);
    }
    public function getPlanDetail($id)
    {
        $data = $this->PlanDetail->getPlanDetail($id);
        return new DataResponse($data);
    }
    public function getPlanPricing($id)
    {
        $data = $this->PlanDetail->getPlanPricing($id);
        return new DataResponse($data);
    }
    public function setPlanPricing(Request $request)
    {
        $data = $this->PlanDetail->setPlanPricing($request);
        return new DataResponse($data);
    }
    public function setPlanPricingAgeBased(Request $request)
    {
        $data = $this->PlanDetail->setPlanPricingAgeBased($request);
        return new DataResponse($data);
    }
    public function setPlanPricingZip(Request $request)
    {
        $data = $this->PlanDetail->setPlanPricingZip($request);
        return new DataResponse($data);
    }
    public function setPlanPricingAgeBasedZip(Request $request)
    {
        $data = $this->PlanDetail->setPlanPricingAgeBasedZip($request);
        return new DataResponse($data);
    }
}
