<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\EnrollmentQuestion\EnrollmentQuestionRequest;
use App\Repositories\EnrollmentQuestionRepository;
use Illuminate\Http\Request;

class EnrollmentQuestionController extends Controller
{
    /**
     * @var EnrollmentQuestionRepository
     */
    private $repository;

    /**
     * EnrollmentQuestionController constructor.
     * @param EnrollmentQuestionRepository $repository
     */
    public function __construct(EnrollmentQuestionRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $filters = $request->query->all();
        $filters['deleted'] = false;
        return $this->repository->paginatedFormattedList(request()->query('per_page') ?? 20, $filters);
    }

    public function show($qId)
    {
        return $this->repository->show($qId);
    }

    public function create(EnrollmentQuestionRequest $request)
    {
        return $this->repository->create($request->validated());
    }

    public function update(EnrollmentQuestionRequest $request, $qId)
    {
        return $this->repository->update($request->validated(), $qId);
    }

    public function delete($qId)
    {
        return $this->repository->delete($qId);
    }
}
