<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\GroupUpdates\UpdateWaiveFeeRequest;
use App\Http\Resources\DataResponse;
use App\Repositories\Groups\GroupFeatures;
use App\Repositories\Groups\GroupRepository;
use Illuminate\Http\Request;

class GroupInfoController extends Controller
{
    private $groupInfo;
    private $groupFeatures;

    public function __construct(GroupFeatures $groupFeatures, GroupRepository $groups)
    {
        $this->groupFeatures = $groupFeatures;
        $this->groupInfo = $groups;
    }

    public function updateWaiveFee(UpdateWaiveFeeRequest $request)
    {
        return $this->groupFeatures->updateFee($request);
    }

    public function totalActiveGroups(Request $request)
    {
        $filters = $request->query->all();
        return $this->groupInfo->totalActiveGroups($filters);
    }

    public function getMemberList(Request $request){
        $filters = $request->query->all();
        return $this->groupInfo->getGroupMemberList($filters);
    }

    public function terminateGroup(Request $request)
    {
        return $this->groupFeatures->terminateGroup($request);
    }

}

