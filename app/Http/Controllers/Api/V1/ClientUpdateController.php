<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Http\Requests\PolicyUpdates\UpdatePolicyAgent;
use App\Http\Requests\PolicyUpdates\UpdatePolicyGroup;
use App\Http\Requests\PolicyUpdates\UpdatePolicyBen;
use App\Http\Requests\PolicyUpdates\RemovePolicyGroup;
use App\Http\Requests\PolicyUpdates\InsertPolicyDependent;
use App\Http\Requests\PolicyUpdates\ChangePolicyDependent;
use App\Repositories\Members\ClientDetailUpdate;

class ClientUpdateController extends Controller
{
    protected $clientUpdateModel;
    
    public function __construct()
    {
        $this->clientUpdateModel = new ClientDetailUpdate();
    }
    public function agentUpdate(UpdatePolicyAgent $request)
    {
        return $this->clientUpdateModel->UpdatePolicyAgent($request);
    }
    public function listGroupInfo()
    {
        $data = $this->clientUpdateModel->listGroupDetail();
        return new DataResponse($data);
    }
    public function editPolicyGroup(UpdatePolicyGroup $request)
    {
        $data = $this->clientUpdateModel->UpdatePolicyGroup($request);
        return new DataResponse($data);
    }
    public function beneficiaryRelation()
    {
        return $this->clientUpdateModel->GetPolicyBenRelation();
    }
    public function beneficiaryCreate(UpdatePolicyBen $request)
    {
        return $this->clientUpdateModel->CreatePolicyBen($request->validated());
    }
    public function beneficiaryUpdate(UpdatePolicyBen $request)
    {
        return $this->clientUpdateModel->UpdatePolicyBen($request->validated());
    }
    public function deletePolicyGroup(RemovePolicyGroup $request)
    {
        $data = $this->clientUpdateModel->RemovePolicyGroup($request);
        return new DataResponse($data);
    }
    public function addPolicyDependent(InsertPolicyDependent $request)
    {
        $data = $this->clientUpdateModel->InsertPolicyDependent($request);
        return new DataResponse($data);
    }
    public function editPolicyDependent(ChangePolicyDependent $request)
    {
        $data = $this->clientUpdateModel->ChangePolicyDependent($request);
        $response = [
            'status' => $data['status'],
            'statusCode' => $data['statusCode']
        ];
        if ( isset($data['message']) ) $response['message'] = $data['message'];
        if ( isset($data['data']) ) $response['data'] = $data['data'];

        return response()->json($response, $response['statusCode']);
    }
}
