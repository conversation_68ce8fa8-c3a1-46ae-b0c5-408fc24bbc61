<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\Members\HealthEnrollment;

class ManageHealthEnrollment extends Controller
{
    public function __construct()
    {
        $this->healthEnrollModel = new HealthEnrollment();
    }
    public function getHealthEnrollment(Request $request)
    {
        $data = $this->healthEnrollModel->getHealthEnrollment($request);
        return new DataResponse($data);
    }
    //details of policy
    public function getPolicyDetails(Request $request)
    {
        $data = $this->healthEnrollModel->getHealthDetail($request);
        return new DataResponse($data);
    }
}
