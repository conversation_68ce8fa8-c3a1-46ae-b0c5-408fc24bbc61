<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use Illuminate\Http\Request;
use App\Http\Requests\PolicyUpdates\DeletePolicyRequest;
use App\Repositories\DeleteFeatures;
use App\Repositories\PolicyUpdateLog;

class DeleteController extends Controller
{
    //
    public function __construct()
    {
        $this->deleteFeaturesModel = new DeleteFeatures();
        $this->policyUpdateLogModel = new PolicyUpdateLog();
    }
    public function removePolicy(DeletePolicyRequest $request)
    {
        $data = $this->deleteFeaturesModel->deletePolicy($request);
        foreach($request->policy_id as $policy)
        {
            $requestNew = 
            [
                'policy_id' => $policy,
                'aid' => $request->aid,
                'reason' => $request->reason, 
            ];
            $req = (object) $requestNew;
            foreach($data as $d)
            {
            	//Check whether the policy has been deleted if so then update log table.
                if($d['type'] == 'success' and $d['policy_id'] == $policy)
                {
                    $msg = $this->policyUpdateLogModel->addPolicyUpdates($req,'PDEL'); 
                }
            }
        }
        return new DataResponse($data);
    }
}
