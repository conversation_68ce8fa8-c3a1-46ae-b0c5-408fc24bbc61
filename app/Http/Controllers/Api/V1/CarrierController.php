<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\Carrier;
use App\Http\Requests\StoreCarrier;
use Illuminate\Http\Request;

class CarrierController extends Controller
{
    public function __construct()
    {
        $this->carrierModel = new Carrier();
    }
    public function getCarrierInfo($cid=0)
    {
        $data = $this->carrierModel->carrierInfo($cid);
        return new DataResponse($data);
    }
    public function newCarrier(StoreCarrier $request)
    {
        $data = $this->carrierModel->newCarrier($request);
        return new DataResponse($data);
    }
    public function getCarrierData()
    {
        $data = $this->carrierModel->carrierData();
        return $data;
    }
}
