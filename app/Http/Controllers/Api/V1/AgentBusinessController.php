<?php

namespace App\Http\Controllers\Api\V1;

use App\AgentBusiness;
use App\Http\Controllers\Controller;
use App\Http\Requests\AgentBusinessRequest;
use App\Repositories\AgentBusinessRepository;

class AgentBusinessController extends Controller
{
    private $agentBusinessRepo;

    public function __construct(AgentBusinessRepository $agentBusinessRepo)
    {
        $this->agentBusinessRepo = $agentBusinessRepo;
    }

    public function addAgentBusiness(AgentBusinessRequest $request)
    {
        $agentBusiness = AgentBusiness::where('business_agent_id',$request->business_agent_id)->count();
        if ($agentBusiness){
            return $this->agentBusinessRepo->update($request);
        }
        else{
           return $this->agentBusinessRepo->add($request);
        }
    }
    public function listAgentBusiness()
    {
        return $this->agentBusinessRepo->list();
    }
    public function agentDetailBusiness($id)
    {
        return $this->agentBusinessRepo->agentDetail($id);
    }

}
