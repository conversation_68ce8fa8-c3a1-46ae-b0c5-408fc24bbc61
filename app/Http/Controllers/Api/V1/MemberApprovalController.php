<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Http\Requests\ApproveMemberInfo;
use App\Http\Requests\GetPolicyDetails;
use App\Repositories\MemberApproval;

class MemberApprovalController extends Controller
{
    private $memberApprovalModel;

    public function __construct()
    {
        $this->memberApprovalModel = new MemberApproval();
    }
    public function getMember(Request $request)
    {
        return $this->memberApprovalModel->getApprovalList($request);
    }
    public function approveMember(ApproveMemberInfo $request)
    {
        $result = $this->memberApprovalModel->changeMemberStatus($request);
        return new DataResponse($result);
    }

    public function bulkApproveMember(Request $request)
    {
        foreach ($request->policy_id as $key => $policy) {
            if($request->type == "approve") {
                $data['type'] = "approve";
            } else {
                $data['type'] = "reject";
            }
            $data['policy_id'] = $policy;
            $result[$key] = $this->memberApprovalModel->changeMemberStatus($data);

        }
        return new DataResponse($result);
    }
    public function receivedSignature(GetPolicyDetails $request)
    {
        $result = $this->memberApprovalModel->setSignatureReceived($request);
        return new DataResponse($result);
    }
    public function receivedTaxDocument(GetPolicyDetails $request)
    {
        $result = $this->memberApprovalModel->setTaxDocumentReceived($request);
        return new DataResponse($result);
    }
}
