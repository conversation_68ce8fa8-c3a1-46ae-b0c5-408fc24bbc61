<?php

namespace App\Http\Controllers\Api\V1;


use App\Http\Resources\DataResponse;
use App\Repositories\UserInfoFeatures;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class UserInfoController extends Controller
{
    public function __construct(UserInfoFeatures $userInfoFeatures)
    {
        $this->userInfoFeatures = $userInfoFeatures;
    }
    public function getUserAddressList(Request $request, $userID)
    {
        $data = $this->userInfoFeatures->getUserAddress($userID);
        return new DataResponse($data);
    }

    public function getUserActivity($userID): DataResponse
    {
        $data = $this->userInfoFeatures->fetchUserActivity($userID);
        return new DataResponse($data);
    }
}
