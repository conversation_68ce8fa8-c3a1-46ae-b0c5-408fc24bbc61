<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Http\Resources\PlanOverviewResource;
use App\PlanOverview;
use App\Repositories\PlanOverviewRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;

class PlanOverviewController extends Controller
{
    use ResponseMessage;
    /**
     * @var PlanOverviewRepository
     */
    private $repository;

    public function __construct(PlanOverviewRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $filters = $request->query->all();
        return $this->repository->paginatedFormattedList(20, $filters);
    }

    public function getByPolicyId($policyId)
    {
        $filters['policy_id'] = $policyId;
        $planOverviews = $this->repository->listAll($filters);
        return $this->repository->formattedItems($planOverviews);
    }

    public function getHealthPlans(Request $request)
    {
        $filters = $request->query->all();
        $plans = $this->repository->getHealthPlansQuery($filters)->paginate(20)->appends($filters);
        return $this->repository->formattedData($plans);
    }

}
