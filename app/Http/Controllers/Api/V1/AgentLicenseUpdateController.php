<?php

namespace App\Http\Controllers\Api\V1;

use App\Repositories\Agents\AgentLicenseUpdate;
use App\Http\Resources\DataResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\AgentUpdates\ViewAgentLicenseRequest;
use App\Http\Requests\AgentUpdates\AddAgentLicenseRequest;
use App\Http\Requests\AgentUpdates\EditAgentLicenseRequest;
use App\Http\Requests\AgentUpdates\DeleteAgentLicenseRequest;
use Illuminate\Http\Request;

class AgentLicenseUpdateController extends Controller
{
    //
    public function __construct()
    {
        $this->agentLicenseUpdateModel = new AgentLicenseUpdate();
    }
    public function viewAgentLicense(ViewAgentLicenseRequest $request)
    {
        $data = $this->agentLicenseUpdateModel->listAgentLicense($request);
        return new DataResponse($data);
    }
    public function addAgentLicense(AddAgentLicenseRequest $request)
    {
        $data = $this->agentLicenseUpdateModel->saveAgentLicense($request);
        return new DataResponse($data);
    }
    public function updateAgentLicense(EditAgentLicenseRequest $request)
    {
        $data = $this->agentLicenseUpdateModel->editAgentLicense($request);
        return new DataResponse($data);
    }
    public function removeAgentLicense(DeleteAgentLicenseRequest $request)
    {
        $data = $this->agentLicenseUpdateModel->deleteAgentLicense($request);
        return new DataResponse($data);
    }    
}
