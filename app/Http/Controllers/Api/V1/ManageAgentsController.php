<?php

namespace App\Http\Controllers\Api\V1;

use App\AgentInfo;
use App\Http\Controllers\Controller;
use App\Http\Requests\ManageAgent\AddAgentGroupRequest;
use App\Http\Requests\ManageAgent\DeleteAgentRequest;
use App\Http\Requests\ManageAgent\EditAgentImageRequest;
use App\Http\Requests\ManageAgent\EditPersonalInfoRequest;
use App\Http\Requests\ManageAgent\RemoveGroupRequest;
use App\Http\Requests\ManageAgent\ScheduleUpdateUplineRepRequest;
use App\Http\Requests\ManageAgent\UpdatePaymentMethodRequest;
use App\Http\Requests\ManageAgent\SendContractEmailRequest;
use App\Http\Requests\ManageAgent\UpdateUplineRepRequest;
use App\Http\Requests\ManageAgent\UpdateWebsiteRequest;
use App\Http\Resources\DataResponse;
use App\Http\Requests\GetAgentDetails;
use App\Http\Requests\ManageAgent\ChangePasswordRequest;
use App\Http\Requests\ManageAgent\UpdateBusinessProfileRequest;
use App\Http\Requests\ManageAgent\UpdateDisplaySettingRequest;
use App\Repositories\Agents\ManageAgents;
use App\Traits\ResponseMessage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ManageAgentsController extends Controller
{
    use ResponseMessage;
    /**
     * @var ManageAgents
     */
    private $manageAgentsModel;

    /**
     * ManageAgentsController constructor.
     */
    public function __construct(ManageAgents $manageAgentsModel)
    {
        $this->manageAgentsModel = $manageAgentsModel;
    }

    public function manageAgentsInfo()
    {
        $data = $this->manageAgentsModel->manageAgents();
        return new DataResponse($data);
    }

    public function agentDetails(GetAgentDetails $request)
    {
        $data = $this->manageAgentsModel->getAgentDetails($request);
        return new DataResponse($data);
    }

    public function clientDetails(GetAgentDetails $request)
    {
        $data = $this->manageAgentsModel->getClientDetails($request);
        return new DataResponse($data);
    }

    public function agentList(Request $request)
    {
        try{
        $filters = $request->query->all();
        $data = $this->manageAgentsModel->paginatedFormattedList(request()->query('per_page') ?? 20, $filters);
        return $this->successResponse('Success', $data);
        } catch (\Exception $exception) {
            $status = $exception->getCode() ?: 400;
            return $this->failedResponse($exception->getMessage(), $status);
        }
    }

    public function contractActions($agentId)
    {
        $data = $this->manageAgentsModel->getContractActions($agentId);
        return $this->successResponse('Success', $data);
    }

    public function contractLevels($agentId)
    {
        $data = $this->manageAgentsModel->getContractLevels($agentId);
        return $this->successResponse('Success', $data);
    }

    public function getSignedContractLevels($agent_id, $type){
        $data = $this->manageAgentsModel->getSignedContractLevels($agent_id, $type);
        return $this->successResponse('Success', $data);
      }

    public function sendContractEmail(SendContractEmailRequest $request)
    {
        return $this->manageAgentsModel->sendContractEmail($request);
    }

    public function agentDetail($agentId)
    {
        return $this->manageAgentsModel->agentDetail($agentId);
    }

    public function agentDownlineReps(Request $request, $agentId)
    {
        $filters = $request->query->all();
        return $this->manageAgentsModel->getDownlineReps($agentId, 5, $filters);
    }

    public function agentLicense(Request $request, $agentId)
    {
        $filters = $request->query->all();
        return $this->manageAgentsModel->getAgentLicenses($agentId, 10, $filters);
    }

    public function updatePaymentMethod(UpdatePaymentMethodRequest $request)
    {
        return $this->manageAgentsModel->updatePaymentMethod($request->validated());
    }

    public function uplineReps($agentId)
    {
        return $this->manageAgentsModel->getUplineReps($agentId);
    }

    public function updateUplineRep(UpdateUplineRepRequest $request)
    {
        return $this->manageAgentsModel->updateUplineRep($request->validated());
    }

    public function scheduleUpdateUplineRep(ScheduleUpdateUplineRepRequest $request): JsonResponse
    {
        return $this->manageAgentsModel->scheduleUpdateUplineRep($request->validated());
    }

    public function getScheduledUpdateUplineRep(ScheduleUpdateUplineRepRequest $request)
    {
        return $this->manageAgentsModel->getScheduledUpdateUplineRep($request->validated());
    }

    public function agentGroupList(Request $request, $agentId)
    {
        $filters = $request->query->all();
        return $this->manageAgentsModel->getAgentGroups($agentId, $filters);
    }

    public function agentAddGroup(AddAgentGroupRequest $request)
    {
        return $this->manageAgentsModel->addAgentGroup($request->validated());
    }

    public function agentActivity($agentId){
        return $this->manageAgentsModel->getAgentUserActivityDetail($agentId);
    }

    public function editAgentImage(EditAgentImageRequest $request){
        return $this->manageAgentsModel->editAgentImage($request->validated());
    }

    public function checkIfAgentHaveActiveDownlines($agentId){
        return $this->manageAgentsModel->checkIfAgentHaveActiveDownlines($agentId);
    }

    public function changeAgentUpline($agent_ga,Request $request){
        return $this->manageAgentsModel->changeAgentUpline($agent_ga,$request->agent_ids);
    }

    public function deleteAgent(DeleteAgentRequest $request){
        return $this->manageAgentsModel->deleteAgent($request->validated());
    }

    public function editPersonalInfo(EditPersonalInfoRequest $request){
        return $this->manageAgentsModel->editPersonalInfo($request->validated());
    }

    public function getDownlineAgentsExists($agentsId)
    {
        return $this->manageAgentsModel->getDownlineAgentsExists($agentsId);
    }

    public function agentUpdates($agentId){
        return $this->manageAgentsModel->getAgentUpdates($agentId);
    }

    public function websiteUpdate(UpdateWebsiteRequest $request){
        return $this->manageAgentsModel->updateWebsite($request->validated());
    }

    public function removeGroup(RemoveGroupRequest $request){
        return $this->manageAgentsModel->removeGroup($request->validated());
    }

    public function updateDisplaySetting(UpdateDisplaySettingRequest $request){
        return $this->manageAgentsModel->updateDisplaySetting($request->validated());
    }

    public function saveBusinessInfo(UpdateBusinessProfileRequest $request){
        return $this->manageAgentsModel->saveBusinessInfo($request);
    }

    public function agentNotes(Request $request){
        return $this->manageAgentsModel->getAgentNotes($request);
    }

    public function getAgentDetail($id){
        return $this->manageAgentsModel->getAgentDetail($id);
    }

    public function restoreTrashedAgent($agentId)
    {
        return $this->manageAgentsModel->restoreTrashedAgent($agentId);
    }

    public function getAgentCommissionDetails($agentId,Request $request)
    {
        return  $this->manageAgentsModel->getAgentCommissionDetails($agentId,$request);
    }

    public function changeAgentPassword(ChangePasswordRequest $request)
    {
        return  $this->manageAgentsModel->changeAgentPassword($request);
    }
    
    public function getContractNameList(){
        return $this->successResponse('Success', $this->manageAgentsModel->getContractNameList());
    }

}
