<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Plan\ActivePlanTermedRequest;
use App\Http\Requests\TermRequest\ApproveTermRequest;
use App\Http\Requests\TermRequest\RejectTermRequest;
use App\Http\Resources\DataResponse;
use App\Repositories\TermRequestRepository;
use Illuminate\Http\Request;

class TermRequestController extends Controller
{
    /**
     * @var TermRequestRepository
     */
    private $repository;

    public function __construct(TermRequestRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $filters = $request->query->all();
        return $this->repository->paginatedFormattedList(request()->query('per_page') ?? 20, $filters);
    }

    public function approve(ApproveTermRequest $request)
    {
        $data = $request->validated();
        return $this->repository->approveTermRequest($data);
    }

    public function reject(RejectTermRequest $request)
    {
        $data = $request->validated();
        return $this->repository->rejectTermRequest($data);
    }

    public function show($termId){
        return $this->repository->getTermDetail($termId);
    }

    public function activePlanTermed(ActivePlanTermedRequest $request){
        $data = $request->validated();
        return $this->repository->activePlanTermed($data);
    }

    public function updatePlanTermDate(Request $request){
        $data = $request->all();
        return $this->repository->updateTermDate($data);
    }
}
