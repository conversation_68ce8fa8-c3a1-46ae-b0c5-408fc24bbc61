<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Http\Requests\DetailQuestion;
use App\Http\Requests\AddQuestion;
use App\Http\Requests\AddEnrollText;
use App\Http\Requests\DetailEnrollText;
use App\Repositories\EnrollmentFeature;

class EnrollmentFeatureController extends Controller
{
    public function __construct()
    {
        $this->enrollmentModel = new EnrollmentFeature();
    }
    public function getQuestion()
    {
        $data = $this->enrollmentModel->getQuestionList();
        return new DataResponse($data);
    }
    public function detailQuestion(DetailQuestion $request)
    {
        $data = $this->enrollmentModel->getQuestionDetail($request);
        return new DataResponse($data);
    }
    public function addQuestion(AddQuestion $request)
    {
        $data = $this->enrollmentModel->addQuestion($request);
        return new DataResponse($data);
    }
    public function deleteQuestion(DetailQuestion $request)
    {
        $data = $this->enrollmentModel->deleteData($request->qid, 'questions');
        return new DataResponse($data);
    }
    public function getEnrollmentText()
    {
        $enrollmentText = $this->enrollmentModel->getTextList();
        return new DataResponse($enrollmentText);
    }
    public function detailEnrollmentText(DetailEnrollText $request)
    {
        $detail = $this->enrollmentModel->getEnrollmentTextDetail($request);
        return new DataResponse($detail);
    }
    public function addEnrollmentText(AddEnrollText $request)
    {
        $data = $this->enrollmentModel->addEnrollText($request);
        return new DataResponse($data);
    }
     public function deleteEnrollText(DetailEnrollText $request)
    {
        $data = $this->enrollmentModel->deleteData($request->etid, 'enrolltext');
        return new DataResponse($data);
    }
}
