<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\PaymentBankAccountRequest;
use App\Http\Requests\GetPaymentMethodDataRequest;
use App\Http\Requests\TogglePaymentStatusRequest;
use App\Http\Resources\DataResponse;
use App\Repositories\Payment\PaymentFeatures;
use Facade\Ignition\Http\Requests;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public function __construct()
    {
       $this->paymentFeatures = new PaymentFeatures();
    }
    public function addBankInfo(PaymentBankAccountRequest $request)
    {
       $data = $this->paymentFeatures->addUpdateBankAccount($request);
        return new DataResponse($data);
    }
    public function getBankDetailsByID($id)
    {
        $data = $this->paymentFeatures->getBankDetailsByID($id);
        return new DataResponse($data);
    }
    public function getPaymentMethodDetails(GetPaymentMethodDataRequest $request)
    {
        $data = $this->paymentFeatures->fetchPaymentMethod($request);
        return new DataResponse($data);
    }

    public function togglePaymentCcStatus(TogglePaymentStatusRequest $request){
        return $this->paymentFeatures->togglePaymentStatus($request);
    }
}
