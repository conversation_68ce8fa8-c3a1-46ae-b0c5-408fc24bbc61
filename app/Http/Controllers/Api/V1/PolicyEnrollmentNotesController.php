<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\PolicyEnrollmentNotes;
use App\Http\Resources\DataResponse;
use App\Http\Requests\PolicyUpdates\ViewEnrollmentNotes;
use App\Http\Requests\PolicyUpdates\AddPolicyEnrollmentNotes;
use App\Http\Requests\PolicyUpdates\EditPolicyEnrollmentNotes;
use App\Http\Requests\PolicyUpdates\DeletePolicyEnrollmentNotes;

class PolicyEnrollmentNotesController extends Controller
{
	//
    public function __construct()
    {
        $this->policyEnrollmentNotes = new PolicyEnrollmentNotes();
    }
    public function getPolicyEnrollmentNotes(ViewEnrollmentNotes $request)
    {
        $data = $this->policyEnrollmentNotes->viewPolicyEnrollmentNotes($request);
        return new DataResponse($data);
    }
    public function addPolicyEnrollmentNotes(AddPolicyEnrollmentNotes $request)
    {
        $data = $this->policyEnrollmentNotes->insertPolicyEnrollmentNotes($request);
        return new DataResponse($data);
    }
    public function updatePolicyEnrollmentNotes(EditPolicyEnrollmentNotes $request)
    {
        $data = $this->policyEnrollmentNotes->editPolicyEnrollmentNotes($request);
        return new DataResponse($data);
    }
    public function removePolicyEnrollmentNotes(DeletePolicyEnrollmentNotes $request)
    {
        $data = $this->policyEnrollmentNotes->deletePolicyEnrollmentNotes($request);
        return new DataResponse($data);
    }
}
