<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Plan\ApprovePlanPolicyRequest;
use App\Http\Requests\Plan\DeclinePlanPolicyRequest;
use App\Repositories\PlanPolicyRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;

class PlanPolicyController extends Controller
{
    use ResponseMessage;

    /**
     * @var PlanPolicyRepository
     */
    private $repository;

    /**
     * PlanPolicyController constructor.
     * @param PlanPolicyRepository $repository
     */
    public function __construct(PlanPolicyRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $filters = $request->query->all();
        return $this->repository->paginatedFormattedList(20, $filters);
    }


    public function approvePlanPolicy(ApprovePlanPolicyRequest $request)
    {
        $data = $request->validated();
        return $this->repository->approvePlanPolicy($data);
    }

    public function declinePlanPolicy(DeclinePlanPolicyRequest $request){
        $data = $request->validated();
        return $this->repository->declinePlanPolicy($data);
    }

    public function updateAIStatus(Request $request){
        $data = $request->all();
        return $this->repository->updateAIStatus($data);
    }
}
