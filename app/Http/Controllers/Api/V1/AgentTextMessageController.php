<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Repositories\Agents\AgentTextMessage;
use Illuminate\Http\Request;

class AgentTextMessageController extends Controller
{
    //
    public function __construct()
    {
        $this->agentTextMessageModel = new AgentTextMessage();
    }
    public function sendScheduledDailyMessageOnAgentStatus()
    {
        $data = $this->agentTextMessageModel->sendScheduledDailyMessageOnAgentStatus();
    }
    public function sendScheduledMonthlyReportsForRepresentative()
    {
        $data = $this->agentTextMessageModel->sendScheduledMonthlyReportsForRepresentative();
    }
}
