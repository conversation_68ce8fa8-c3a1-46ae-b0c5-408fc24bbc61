<?php

namespace App\Http\Controllers\Api\V1;

use App\Helper\AgentHelper;
use App\Helper\PlanHelper;
use App\Http\Requests\Agent\AddAgentGroupConfiguration;
use App\Http\Requests\Agent\AddRepLicenseRequest;
use App\Http\Requests\Agent\EditRepLicenseRequest;
use App\Http\Requests\Agent\License\AddNewAgentLicenseRequest;
use App\Http\Requests\Agent\RemoveRepLicenseRequest;
use App\Repositories\Agents\RepLicenseRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Storage;
use Illuminate\Http\JsonResponse;

class RepLicenseController extends Controller
{
    use ResponseMessage;
    private $repLicense;

    public function __construct(RepLicenseRepository $repLicense)
    {
        $this->repLicense = $repLicense;
    }

    public function getRepLicense(Request $request): JsonResponse
    {
       return $this->repLicense->getAgentLicense($request);
    }
    public function removeRepLicense(RemoveRepLicenseRequest $request): JsonResponse
    {
        return $this->repLicense->removerepLicense($request->validated());
    }

    public function addRepLicense(AddRepLicenseRequest $request): JsonResponse
    {
        return $this->repLicense->addRepLicense($request->validated());
    }

    public function getLicenseDetail(Request $request, int $licenseID): JsonResponse
    {
        return $this->repLicense->getLicenseDetail($request, $licenseID);
    }

    public function editRepLicense(EditRepLicenseRequest $request): JsonResponse
    {
        return $this->repLicense->editLicenseDetail($request->validated());
    }
}
