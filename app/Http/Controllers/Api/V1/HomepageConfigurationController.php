<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\HomepageConfiguration\DeleteSelectedHomepageConfigurationRequest;
use App\Http\Requests\HomepageConfiguration\HomepageAgentPlanRequest;
use App\Http\Requests\HomepageConfiguration\HomepageGroupPlanRequest;
use App\Repositories\HomepageConfigurationRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;

class HomepageConfigurationController extends Controller
{
    use ResponseMessage;
    /**
     * @var HomepageConfigurationRepository
     */
    private $repository;

    /**
     * HomepageConfigurationController constructor.
     * @param HomepageConfigurationRepository $repository
     */
    public function __construct(HomepageConfigurationRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getOptions()
    {
        return $this->repository->getOptions();
    }

    public function getAgentPlans($agentId, Request $request)
    {
        $filters = $request->query->all();
        return $this->repository->paginatedAgentPlans($agentId, 25, $filters);
    }

    public function getGroupPlans($groupId, Request $request)
    {
        $filters = $request->query->all();
        return $this->repository->paginatedGroupPlans($groupId, 25, $filters);
    }

    public function createAgentPlan(HomepageAgentPlanRequest $request)
    {
        return $this->repository->createAgentPlan($request->validated());
    }

    public function createGroupPlan(HomepageGroupPlanRequest $request)
    {
        return $this->repository->createGroupPlan($request->validated());
    }

    public function toggleIsFeatured($id)
    {
        return $this->repository->toggleIsFeatured($id);
    }

    public function delete($id)
    {
        return $this->repository->delete($id);
    }

    public function agentOptions($agentId){
        return $this->repository->getAgentOptions($agentId);

    }

    public function groupOptions(){
        return $this->repository->getGroupOptions();
    }

    public function deleteSelected(DeleteSelectedHomepageConfigurationRequest $request){
        return $this->repository->deleteSelectedHomepageConfiguration($request->validated());
    }
}
