<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Repositories\Members\ViewEnrollmentReceipt;
use App\Http\Resources\DataResponse;
use Illuminate\Http\Request;
use App\Http\Requests\GetPolicyDetails;

class EnrollmentReceiptController extends Controller
{
    //
     public function __construct()
    {
        $this->viewEnrollmentReceipt = new ViewEnrollmentReceipt();
    }
    public function getEnrollmentReceipt(GetPolicyDetails $request)
    {
        $data = $this->viewEnrollmentReceipt->detailEnrollmentReceipt($request);
        return new DataResponse($data);
    }
}
