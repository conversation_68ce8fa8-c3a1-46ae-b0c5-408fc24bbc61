<?php

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\Reports\HealthReport;

class HealthReportController extends Controller
{
    public function __construct()
    {
        $this->healthReportModel = new HealthReport();
    }
    public function getHealthReport(Request $request)
    {
        $newDate = strtotime('04/20/2018');
        $data = $this->healthReportModel->getHealthReport($request, $newDate);
        return new DataResponse($data);
    }
}
