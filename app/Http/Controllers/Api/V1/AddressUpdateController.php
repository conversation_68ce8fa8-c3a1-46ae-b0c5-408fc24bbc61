<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use Illuminate\Http\Request;
use App\Repositories\Members\ManageAddress;
use App\Repositories\PolicyUpdateLog;
use App\Http\Requests\PolicyUpdates\InsertPolicyAddress;
use App\Http\Requests\PolicyUpdates\ChangePolicyAddress;

class AddressUpdateController extends Controller
{
    //
    public function __construct()
    {
        $this->manageAddressModel = new ManageAddress();
        $this->policyUpdateLogModel = new PolicyUpdateLog();
    }
    public function listStates()
    {
        $data = $this->manageAddressModel->listStateDetail();
        return new DataResponse($data);
    }
    public function addPolicyAddress(InsertPolicyAddress $request)
    {
        $data = $this->manageAddressModel->insertPolicyAddress($request);
//        if(isset($data['type']) && $data['type'] == 'success')
//        {
//            $msg = $this->policyUpdateLogModel->addPolicyUpdates($request,'ADR');
//        }
        return new DataResponse($data);
    }
    public function updatePolicyAddress(ChangePolicyAddress $request)
    {
        $data = $this->manageAddressModel->changePolicyAddress($request);
        if($data['type'] == 'success')
        {
            $msg = $this->policyUpdateLogModel->addPolicyUpdates($request,'ADR');
        }
        return new DataResponse($data);
    }
}
