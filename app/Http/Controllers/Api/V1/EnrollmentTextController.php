<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\EnrollmentText\EnrollmentTextRequest;
use App\Repositories\EnrollmentTextRepository;
use Illuminate\Http\Request;

class EnrollmentTextController extends Controller
{
    /**
     * @var EnrollmentTextRepository
     */
    private $repository;

    /**
     * EnrollmentQuestionController constructor.
     * @param EnrollmentTextRepository $repository
     */
    public function __construct(EnrollmentTextRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $filters = $request->query->all();
        $filters['deleted'] = false;
        return $this->repository->paginatedFormattedList(request()->query('per_page') ?? 20, $filters);
    }

    public function show($etId)
    {
        return $this->repository->show($etId);
    }

    public function create(EnrollmentTextRequest $request)
    {
        return $this->repository->create($request->validated());
    }

    public function update(EnrollmentTextRequest $request, $etId)
    {
        return $this->repository->update($request->validated(), $etId);
    }

    public function delete($etId)
    {
        return $this->repository->delete($etId);
    }
}
