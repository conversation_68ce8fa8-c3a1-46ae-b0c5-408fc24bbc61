<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\PolicyFeatures;

class FilterUserController extends Controller
{
    public function __construct()
    {
        $this->policyfeatureModel = new PolicyFeatures();
    }
    public function filterChild26()
    {
        $data = $this->policyfeatureModel->filterChild26();
        return new DataResponse($data);
    }

    public function filterSpouse65()
    {
        $data = $this->policyfeatureModel->filterSpouse65();
        return new DataResponse($data);
    }

    public function filterMember65()
    {
        $data = $this->policyfeatureModel->filterMember65();
        return new DataResponse($data);
    }

    //aging off new deps for email
    public function agingOffDeps()
    {
        return $this->policyfeatureModel->agingOffDeps();
        // return new DataResponse($data);
    }
}
