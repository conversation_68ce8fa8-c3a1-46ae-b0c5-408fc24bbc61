<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\AgentNotesRequest;
use App\Http\Requests\NotesRequest;
use App\Http\Resources\DataResponse;

use App\Repositories\Notes\NotesFeatures;
use Illuminate\Http\Request;

class NotesController extends Controller
{
    public function __construct(NotesFeatures $noteFeatures)
    {
        $this->noteFeatures = $noteFeatures;
    }

    public function addNote(NotesRequest $request)
    {
        return $this->noteFeatures->addNotes($request);
    }

    public function addAgentNote(AgentNotesRequest $request)
    {
        return $this->noteFeatures->addAgentNote($request);
    }

    public function deleteNotes(Request $request, $id)
    {
        $data = $this->noteFeatures->deleteNotes($request, $id);
        return new DataResponse($data);
    }

    public function deleteAgentNotes($id)
    {
        return $this->noteFeatures->deleteAgentNotes($id);
    }

    public function getLatestNote(Request $request, $policyID)
    {
        $data = $this->noteFeatures->getLatestNote($request, $policyID);
        return new DataResponse($data);
    }
    public function getAllNotes(Request $request, $policyID)
    {
        $data = $this->noteFeatures->getAllNotes($request, $policyID);
        return new DataResponse($data);
    }
    public function downloadFile(Request $request,$filename) {
        return $this->noteFeatures->downloadNoteAttachment($filename);
    }
}
