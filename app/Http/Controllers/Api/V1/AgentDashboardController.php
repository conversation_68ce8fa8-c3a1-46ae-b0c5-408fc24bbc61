<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Repositories\Agents\AgentDashboard;
use App\Traits\ResponseMessage;
use App\AgentInfo;

class AgentDashboardController extends Controller
{
    use ResponseMessage;

    /**
     * @var AgentDashboard
     */
    private $agentDashboardModel;

    public function __construct(AgentDashboard $agentDashboard)
    {
        $this->agentDashboardModel = $agentDashboard;
    }

    public function totalAgentAnalytics($fdate = null,$tdate = null)
    {
        $data = $this->agentDashboardModel->totalAnalytics($fdate,$tdate);
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function totalProgressOfYears($fyear, $tyear)
    {
        $data = $this->agentDashboardModel->totalYearProgress($fyear, $tyear);
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function totalProgressOfMonths($fdate, $tdate)
    {
        $data = $this->agentDashboardModel->totalMonthsProgress($fdate, $tdate);
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function totalProgressOfWeek(){
        $data = $this->agentDashboardModel->totalWeekProgress();
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }
    public function agentGroupByOther()
    {
        $data = $this->agentDashboardModel->agentGroupByOtherFactor();
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }
    public function productInfo()
    {
        $data = $this->agentDashboardModel->productsSection();
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }

    public function topAgentsByCategory(){
        $data = $this->agentDashboardModel->topAgentsByCategory();
        if ($data['status'] === 'success') {
            return $this->successResponse($data['status'], $data['data']);
        } else {
            return $this->failedResponse($data['message']);
        }
    }
}