<?php
namespace App\Http\Controllers\Api\V3\Archive;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\ValidateAgentIdRequest;
use App\Http\Requests\ValidateGroupIdRequest;
use App\Http\Requests\ValidateMemberIdRequest;
use App\Repositories\V3\Archive\ArchiveRepository;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Resources\SuccessResource;
use App\Http\Resources\ErrorResource;

class ArchiveController extends Controller
{
    protected $archiveRepository;

    public function __construct(ArchiveRepository $archiveRepository)
    {
        $this->archiveRepository = $archiveRepository;
    }

    public function deleteAgent(ValidateAgentIdRequest $request)
    {
        $agent_id = $request->agent_id;
        try {
            $data = $this->archiveRepository->deleteAgent($agent_id);
            $response = [
                'statusCode' => Response::HTTP_OK,
                'message' => 'Agent deleted successfully.'
            ];
            if (is_object($data) || is_array($data) || $data instanceof \Illuminate\Support\Collection) {
                $response['data'] = $data;
            }
            return response()->json(new SuccessResource($response), Response::HTTP_OK);
        } catch (\Exception $exception) {
            return $this->handleException($exception, 'Failed to delete agent.');
        }
    }

    public function restoreAgent(ValidateAgentIdRequest $request)
    {
        $agent_id = $request->agent_id;
        try {
            $data = $this->archiveRepository->restoreAgent($agent_id);
            $response = [
                'statusCode' => Response::HTTP_OK,
                'message' => 'Agent restored successfully.'
            ];
            if (is_object($data) || is_array($data) || $data instanceof \Illuminate\Support\Collection) {
                $response['data'] = $data;
            }
            return response()->json(new SuccessResource($response), Response::HTTP_OK);
        } catch (\Exception $exception) {
            return $this->handleException($exception, 'Failed to restore agent.');
        }
    }

    public function deleteMember(ValidateMemberIdRequest $request)
    {
        $member_id = $request->member_id;
        try {
            $data = $this->archiveRepository->deleteMember($member_id);
            $response = [
                'statusCode' => Response::HTTP_OK,
                'message' => 'Member deleted successfully.'
            ];
            if (is_object($data) || is_array($data) || $data instanceof \Illuminate\Support\Collection) {
                $response['data'] = $data;
            }
            return response()->json(new SuccessResource($response), Response::HTTP_OK);
        } catch (\Exception $exception) {
            return $this->handleException($exception, 'Failed to delete member.');
        }
    }

    public function restoreMember(ValidateMemberIdRequest $request)
    {
        $member_id = $request->member_id;
        try {
            $data = $this->archiveRepository->restoreMember($member_id);
            $response = [
                'statusCode' => Response::HTTP_OK,
                'message' => 'Member restored successfully.'
            ];
            if (is_object($data) || is_array($data) || $data instanceof \Illuminate\Support\Collection) {
                $response['data'] = $data;
            }
            return response()->json(new SuccessResource($response), Response::HTTP_OK);
        } catch (\Exception $exception) {
            return $this->handleException($exception, 'Failed to restore member.');
        }
    }

    public function deleteGroup(ValidateGroupIdRequest $request)
    {
        $group_id = $request->group_id;
        try {
            $data = $this->archiveRepository->deleteGroup($group_id);
            $response = [
                'statusCode' => Response::HTTP_OK,
                'message' => 'Group deleted successfully.'
            ];
            if (is_object($data) || is_array($data) || $data instanceof \Illuminate\Support\Collection) {
                $response['data'] = $data;
            }
            return response()->json(new SuccessResource($response), Response::HTTP_OK);
        } catch (\Exception $exception) {
            return $this->handleException($exception, 'Failed to delete group.');
        }
    }

    public function restoreGroup(ValidateGroupIdRequest $request)
    {
        $group_id = $request->group_id;
        try {
            $data = $this->archiveRepository->restoreGroup($group_id);
            $response = [
                'statusCode' => Response::HTTP_OK,
                'message' => 'Group restored successfully.'
            ];
            if (is_object($data) || is_array($data) || $data instanceof \Illuminate\Support\Collection) {
                $response['data'] = $data;
            }
            return response()->json(new SuccessResource($response), Response::HTTP_OK);
        } catch (\Exception $exception) {
            return $this->handleException($exception, 'Failed to restore group.');
        }
    }

    private function handleException(\Exception $exception, $defaultMessage)
    {
        $errorCode = array_key_exists($exception->getCode(), Response::$statusTexts)
            ? $exception->getCode()
            : Response::HTTP_UNPROCESSABLE_ENTITY;
        return response()->json(
            new ErrorResource([
                'statusCode' => $errorCode,
                'error' => $exception->getMessage(),
                'message' => $defaultMessage
            ]), $errorCode
        );
    }
}
