<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\CustomValidation\RoutingNumberRequest;
use App\Service\CustomValidationService;
use Illuminate\Http\Request;

class CustomValidationController extends Controller
{
    /**
     * @var CustomValidationService
     */
    private $service;

    /**
     * CustomValidationController constructor.
     * @param CustomValidationService $service
     */
    public function __construct(CustomValidationService $service)
    {
        $this->service = $service;
    }

    public function routingValidation(RoutingNumberRequest $request){
        $data = $request->validated();
        return $this->service->validateRoutingNumberWithResponse($data['routing_number']);
    }
}
