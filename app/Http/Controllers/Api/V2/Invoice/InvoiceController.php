<?php

namespace App\Http\Controllers\Api\V2\Invoice;

use App\Http\Controllers\Controller;
use App\NbInvoice;
use App\PlanOverview;
use App\Policy;
use Illuminate\Http\Request;
use App\Repositories\Invoice\SingleInvoiceRepository;
use App\UserInfoPolicyAddress;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class InvoiceController extends Controller
{
    private $invoiceService;

    public function __construct(SingleInvoiceRepository $service)
    {
        $this->invoiceService = $service;
    }

    public function payInvoicesAlert(Request $request)
    {
        if (!isset($request->userId)) {
            return response()->json(['status' => 'error', 'message' => 'User ID is required.'], 422);
        }
        $user_id = $request->userId;
        $response = $this->invoiceService->payInvoicesAlert($user_id);
        return response()->json(['status' => $response['status'], 'alert' => $response['alert'], 'message' => $response['message']], $response['code']);
    }

    public function getInvoiceInfo(Request $request)
    {
        if (!isset($request->policy_id)) {
            return response()->json(['status' => 'error', 'message' => 'Policy ID is required.', 'code' => 422], 422);
        }
        $policy_id = $request->policy_id;
        $response = $this->invoiceService->getInvoiceInfo($policy_id);
        return response()->json($response, $response['code']);
    }

    public function getPaymentSummaryDetails($invoice_id)
    {
        $response = $this->invoiceService->getInvoicePaymentDetails($invoice_id);
        return response()->json($response, $response['code']);
    }

    public function getInvoiceHistory(Request $request)
    {
        $date = null;
        if (!isset($request->policy_id)) {
            return response()->json(['status' => 'error', 'message' => 'Policy ID is required.', 'code' => 422], 422);
        }
        $response = $this->invoiceService->getInvoiceHistory($request);
        return response()->json($response, $response['code']);
    }

    public function getInvoiceHistoryDetail($invoice_id)
    {
        $response = $this->invoiceService->getInvoiceHistoryDetail($invoice_id);
        return response()->json($response, $response['code']);
    }

    public function sendPushNotification(Request $request) {
        if (!isset($request->invoice_id)) {
            return response()->json(['status' => 'error', 'message' => 'Invoice ID is required.', 'code' => 422], 422);
        }
        $invoice_id = $request->invoice_id;
        $message = $request->message;
        $status = $request->status;
        $response = $this->invoiceService->sendPushNotification($invoice_id, $status, $message);
        return $response;
    }



        public function getPolicyInvoiceSummary(Request $request)
    {
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', '0');

        try {
            $yearMonth = $request->input('year_month', Carbon::now()->format('Y-m'));

            if (!preg_match('/^\d{4}-\d{2}$/', $yearMonth)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid format. Use YYYY-MM.',
                ], 422);
            }

            $monthlyAnalytics = self::getMonthlyAnalytics($yearMonth);

            return response()->json([
                'success' => true,
                'year_month' => $yearMonth,
                'monthly_analytics' => $monthlyAnalytics
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in getPolicyInvoiceSummary: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public static function getMissingInvoicePolicyIds($yearMonth)
    {
        $termDate = Carbon::parse($yearMonth . '-01')->endOfMonth()->format('Y-m-d');
        $invMonthStart = Carbon::parse($termDate)->startOfMonth()->format('Y-m-d');

        $policyIdsWithInvoices = NbInvoice::whereDate('invoice_start_date', $invMonthStart)
            ->pluck('invoice_policy_id');

        $missingPolicyIds = Policy::join('plan_overview', 'plan_overview.policy_id', '=', 'policies.policy_id')
            ->join('userinfo_policy_address', 'userinfo_policy_address.policy_id', '=', 'policies.policy_id')
            ->where('policies.effective_date', '<=', $invMonthStart)
            ->where(function ($query) use ($termDate) {
                $query->whereNull('plan_overview.term_date')
                    ->orWhere('plan_overview.term_date', '')
                    ->orWhere('plan_overview.term_date', '>', $termDate);
            })
            ->whereRaw("LOWER(userinfo_policy_address.cfname) != 'test'")
            ->where('policies.effective_date', 'LIKE', '%-01')
            ->whereNotIn('policies.policy_id', $policyIdsWithInvoices)
            ->orderByDesc('policies.policy_id')
            ->pluck('policies.policy_id')
            ->toArray();

        return $missingPolicyIds;
    }

    public static function getMonthlyAnalytics($yearMonth)
    {
        $invMonth = Carbon::parse("$yearMonth-01")->format('Y-m-d');

        $base = NbInvoice::join('userinfo_policy_address AS upa', 'nb_invoices.invoice_policy_id', '=', 'upa.policy_id')
            ->whereDate('nb_invoices.invoice_start_date', $invMonth)
            ->whereRaw("LOWER(upa.cfname) != 'test'");

        $activeInvoices   = (clone $base)->where('nb_invoices.invoice_status',   'ACTIVE')
            ->distinct('nb_invoices.invoice_policy_id')
            ->count('nb_invoices.invoice_policy_id');

        $invoicePremium   = (clone $base)->where('nb_invoices.invoice_status',   'ACTIVE')
            ->sum('nb_invoices.invoice_total');

        $paidInvoices     = (clone $base)->where('nb_invoices.invoice_status',   'ACTIVE')
            ->where('nb_invoices.invoice_payment_status', 'PAID')
            ->distinct('nb_invoices.invoice_policy_id')
            ->count('nb_invoices.invoice_policy_id');

        $unpaidProcessing = (clone $base)
            ->where('nb_invoices.invoice_status',   'ACTIVE')
            ->where('nb_invoices.invoice_payment_status', 'UNPAID')

            ->where('payment_party_status', 'PROCESSING')
            ->distinct('nb_invoices.invoice_policy_id')
            ->count('nb_invoices.invoice_policy_id');

        $unpaidUnprocessed = (clone $base)->where('nb_invoices.invoice_status',   'ACTIVE')
            ->where('nb_invoices.invoice_payment_status', 'UNPAID')
            ->where(function ($q) {
                $q->where('nb_invoices.payment_party_status', '')
                    ->orWhereNull('nb_invoices.payment_party_status');
            })
            ->distinct('nb_invoices.invoice_policy_id')
            ->count('nb_invoices.invoice_policy_id');

        $unpaidFailed = (clone $base)->where('nb_invoices.invoice_status',   'ACTIVE')
            ->where('nb_invoices.invoice_payment_status', 'UNPAID')
            ->where('payment_party_status', 'PROCESSED')
            ->where('failed_fl', 1)
            ->distinct('nb_invoices.invoice_policy_id')
            ->count('nb_invoices.invoice_policy_id');

        $unpaidDisputed = (clone $base)->where('nb_invoices.invoice_status',   'ACTIVE')
            ->where('nb_invoices.invoice_payment_status', 'UNPAID')
            ->where('payment_party_status', 'PROCESSED')
            ->where('dispute_fl', 1)
            ->distinct('nb_invoices.invoice_policy_id')
            ->count('nb_invoices.invoice_policy_id');

        $partialPremium   = (clone $base)->where('nb_invoices.invoice_status',   'ACTIVE')
            ->where('nb_invoices.invoice_payment_status', 'PARTIAL')
            ->sum('nb_invoices.invoice_total');

        $partialPrevPremium   = (clone $base)->where('nb_invoices.invoice_status',   'ACTIVE')
            ->where('nb_invoices.invoice_payment_status', 'PARTIAL')
            ->sum('nb_invoices.invoice_prev_due');

        $processingDateDistribution = (clone $base)
            ->where('nb_invoices.invoice_status', 'ACTIVE')
            ->groupBy('nb_invoices.processing_date')
            ->selectRaw("
            nb_invoices.processing_date as date, 
            COUNT(DISTINCT nb_invoices.invoice_policy_id) as invoices_count")
            ->orderBy('nb_invoices.processing_date')
            ->get()
            ->mapWithKeys(function ($row) {
                return [$row->date => $row->invoices_count];
            })
            ->toArray();

        $paymentMethodDistribution = (clone $base)
            ->where('nb_invoices.invoice_status', 'ACTIVE')
            ->groupBy('nb_invoices.payment_method')
            ->selectRaw("
        nb_invoices.payment_method AS label,
        COUNT(DISTINCT nb_invoices.invoice_policy_id) AS value")
            ->orderBy('nb_invoices.payment_method')
            ->get()
            ->pluck('value', 'label')
            ->toArray();

        $paymentPartyDistribution = (clone $base)
            ->where('nb_invoices.invoice_status', 'ACTIVE')
            ->groupBy('nb_invoices.payment_party_status')
            ->selectRaw("
        nb_invoices.payment_party_status AS label,
        COUNT(DISTINCT nb_invoices.invoice_policy_id) AS value
        ")
            ->orderBy('nb_invoices.payment_party_status')
            ->get()
            ->pluck('value', 'label')
            ->toArray();

        $merchantTypeDistribution = NbInvoice::leftJoin('userinfo_policy_address AS upa', 'nb_invoices.invoice_policy_id', '=', 'upa.policy_id')
            ->whereDate('nb_invoices.invoice_start_date', $invMonth)
            ->where('nb_invoices.invoice_status', 'ACTIVE')
            ->where(function ($q) {
                $q->whereNull('upa.cfname')
                    ->orWhereRaw("LOWER(upa.cfname) != 'test'");
            })
            ->groupBy('nb_invoices.merchant')
            ->selectRaw("
        nb_invoices.merchant AS label,
        COUNT(DISTINCT nb_invoices.invoice_policy_id) AS value
    ")
            ->orderBy('nb_invoices.merchant')
            ->get()
            ->pluck('value', 'label')
            ->toArray();

        $carrierWiseInvoices = NbInvoice::join('plan_overview AS po', 'nb_invoices.invoice_policy_id', '=', 'po.policy_id')
            ->join('carrier_info AS ci', 'po.cid', '=', 'ci.cid')
            ->join('userinfo_policy_address AS upa', 'nb_invoices.invoice_policy_id', '=', 'upa.policy_id')
            ->whereDate('nb_invoices.invoice_start_date', $invMonth)
            ->where('nb_invoices.invoice_status', 'ACTIVE')
            ->whereRaw("LOWER(upa.cfname) != 'test'")
            ->select(
                'ci.cid',
                'ci.carrier_name as carrier_name',
                'nb_invoices.invoice_policy_id'
            )
            ->get()
            ->groupBy('invoice_policy_id')
            ->map(function ($items) {
                $first = $items->first();
                return [
                    'cid' => $first->cid,
                    'carrier_name' => $first->carrier_name,
                ];
            })
            ->groupBy('cid')
            ->map(function ($group, $cid) {
                return [
                    'cid' => $cid,
                    'carrier_name' => $group->first()['carrier_name'],
                    'invoice_count' => count($group),
                ];
            })
            ->values()
            ->toArray();

        $totalCarrierInvoices = array_sum(array_column($carrierWiseInvoices, 'invoice_count'));

        $monthDate = Carbon::parse($yearMonth . '-01')->format('Y-m-d');
        $termDate  = Carbon::parse($yearMonth . '-01')->endOfMonth()->format('Y-m-d');

        $analytics = self::getPolicies()
            ->selectRaw("
                COUNT(DISTINCT policy_id) as total_policy_count,
                COUNT(DISTINCT CASE 
                    WHEN 
                        (
                            (status = 'ACTIVE' AND Approval = 1 AND (term_date IS NULL OR term_date = ''))
                            OR
                            (status = 'TERMED' AND (Approval IS NULL OR Approval = '' OR Approval = 1) AND term_date > ?)
                            OR
                            (status = 'WITHDRAWN' AND (Approval IS NULL OR Approval = '' OR Approval = 1) AND term_date > ?)
                        )
                    THEN policy_id 
                END) AS active_policy_count,
                COUNT(DISTINCT CASE WHEN status = 'TERMED' AND term_date <= ? THEN policy_id END) AS termed_policy_count,
                COUNT(DISTINCT CASE WHEN status = 'WITHDRAWN' AND term_date <= ? THEN policy_id END) AS withdrawn_policy_count
            ", [$termDate, $monthDate, $termDate, $monthDate])
            ->first();

        $missing_invoice_policies = self::getMissingInvoicePolicyIds($yearMonth);
        $priceMissMatchInvoices = self::getMismatchedInvoicePolicyAmounts($yearMonth);
        $termedPolicyInvoice = self::getTermedPolicyInvCount($yearMonth);

        return (object) [

            'total_policy_count'     => (int) $analytics->total_policy_count,
            'active_policy_count'    => (int) $analytics->active_policy_count,
            'termed_policy_count'    => (int) $analytics->termed_policy_count,
            'withdrawn_policy_count' => (int) $analytics->withdrawn_policy_count,
            'termed_policy_invoices' => $termedPolicyInvoice,
            'missing_invoice_count' => count($missing_invoice_policies),
            'active_invoices' => $activeInvoices,
            'paid_invoices'   => $paidInvoices,
            'Unpaid_invoices' => [
                'proceesing' => $unpaidProcessing,
                'unprocessed' => $unpaidUnprocessed,
                'failed' => $unpaidFailed,
                'disputed' =>  $unpaidDisputed
            ],
            'processing_date_wise' => $processingDateDistribution,
            'payment_method_distribution'       => $paymentMethodDistribution,
            'payment_party_distribution'        => $paymentPartyDistribution,
            'merchant_type_distribution'        => $merchantTypeDistribution,
            'carrier_distribution' => $carrierWiseInvoices,
            // 'total_invoices_by_carrier' => $totalCarrierInvoices,
            'price_mismatch_invoices' => $priceMissMatchInvoices,

        ];
    }

    public static function getActivePolicyByMonth($yearMonth)
    {
        $monthDate = Carbon::parse($yearMonth . '-01')->format('Y-m-d');
        $termDate  = Carbon::parse($yearMonth . '-01')->endOfMonth()->format('Y-m-d');

        $test = self::getPolicies()
            ->where(function ($query) use ($termDate, $monthDate) {
                $query->where(function ($q) {
                    $q->where('status', 'ACTIVE')
                        ->where('Approval', 1)
                        ->where(function ($inner) {
                            $inner->whereNull('term_date')->orWhere('term_date', '');
                        });
                })->orWhere(function ($q) use ($termDate) {
                    $q->where('status', 'TERMED')
                        ->where(function ($inner) {
                            $inner->whereNull('Approval')->orWhere('Approval', '')->orWhere('Approval', 1);
                        })
                        ->where('term_date', '>', $termDate);
                })->orWhere(function ($q) use ($monthDate) {
                    $q->where('status', 'WITHDRAWN')
                        ->where(function ($inner) {
                            $inner->whereNull('Approval')->orWhere('Approval', '')->orWhere('Approval', 1);
                        })
                        ->where('term_date', '>', $monthDate);
                });
            })
            ->select('policy_id', 'status', 'Approval', 'term_date')
            ->distinct()
            ->get();

        return $test;
    }

    private static function getTermedPolicyInvCount($yearMonth)
    {
        $invMonthStart = Carbon::parse($yearMonth . '-01')->format('Y-m-d');
        $termDate = Carbon::parse($yearMonth . '-01')->endOfMonth()->format('Y-m-d');

        return NbInvoice::join('userinfo_policy_address', 'nb_invoices.invoice_policy_id', '=', 'userinfo_policy_address.policy_id')
            ->whereDate('invoice_start_date', $invMonthStart)
            ->where('invoice_payment_status', 'UNPAID')
            ->where('invoice_status', 'ACTIVE')
            ->where(function ($query) use ($invMonthStart, $termDate) {
                $query->whereNotNull('userinfo_policy_address.term_date')
                    ->where(function ($q) use ($invMonthStart, $termDate) {
                        $q->where('userinfo_policy_address.term_date', '<', $invMonthStart)
                            ->orWhere('userinfo_policy_address.term_date', '<', $termDate);
                    });
            })
            ->where(function ($query) {
                $query->whereNull('payment_party_status')
                    ->orWhere('payment_party_status', '');
            })
            ->whereRaw("LOWER(userinfo_policy_address.cfname) != 'test'")
            ->distinct('invoice_policy_id')
            ->count('invoice_policy_id');
    }

    private static function getPolicies()
    {
        return UserInfoPolicyAddress::whereRaw("LOWER(cfname) != 'test'");
    }

    private static function getMismatchedInvoicePolicyAmounts($yearMonth)
    {
        $invMonthStart = Carbon::parse($yearMonth . '-01')->format('Y-m-d');

        $invoices = NbInvoice::where('invoice_start_date', $invMonthStart)
            ->where('invoice_payment_status', 'UNPAID')
            ->where(function ($query) {
                $query->whereNull('payment_party_status')
                    ->orWhere('payment_party_status', '');
            })
            ->select(['invoice_id', 'invoice_policy_id', 'invoice_total', 'pay_type', 'invoice_start_date'])
            ->get();

        if ($invoices->isEmpty()) {
            return [];
        }

        $policyDateMap = [];
        foreach ($invoices as $invoice) {
            $key = $invoice->invoice_policy_id . '|' . $invoice->invoice_start_date;
            $policyDateMap[$key][] = strtolower($invoice->pay_type);
        }

        $policyIds = array_unique(array_map(function ($key) {
            return explode('|', $key)[0];
        }, array_keys($policyDateMap)));

        $planData = PlanOverview::whereIn('policy_id', $policyIds)
            ->where(function ($query) use ($invMonthStart) {
                $query->where('peffective_date', '<=', $invMonthStart)
                    ->where(function ($q) use ($invMonthStart) {
                        $q->where('pstatus', 1)
                            ->orWhere('pterm_date', '>', $invMonthStart);
                    });
            })
            ->get();

        $policyTotals = [];

        foreach ($planData as $plan) {
            $key = $plan->policy_id . '|' . $invMonthStart;

            if (!isset($policyTotals[$key])) {
                $policyTotals[$key] = 0;
            }

            $policyTotals[$key] += (float) $plan->price_male_nons;
        }

        $mismatchedInvoices = [];

        foreach ($invoices as $invoice) {
            $key = $invoice->invoice_policy_id . '|' . $invoice->invoice_start_date;
            $total = $policyTotals[$key] ?? 0;

            if (strtolower($invoice->pay_type) === 'annual') {
                $total *= 12;
            }

            $total = round($total, 2);
            $invoiceTotal = round((float) $invoice->invoice_total, 2);

            if ($total !== $invoiceTotal) {
                $mismatchedInvoices[] = [
                    'invoice_id'     => $invoice->invoice_id,
                    'policy_total'   => $total,
                    'invoice_total'  => $invoiceTotal,
                ];
            }
        }
        return $mismatchedInvoices;
    }

    public static function formatAmount($amount)
    {
        $pos = strpos($amount, '.');
        if ($pos !== false) {
            $amount = substr($amount, 0, $pos + 3);
        }
        return floatval($amount);
    }
}
