<?php

namespace App\Http\Controllers\Api\V2\MedicalTypes;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repositories\MedicalTypes\MedicalTypesRepository;

class MedicalTypesController extends Controller
{
    protected $medicalTypes;

    public function __construct(MedicalTypesRepository $medicalTypes)
    {
        $this->medicalTypes = $medicalTypes;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $filters = $request->query->all();
        return $this->medicalTypes->index($filters);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        return $this->medicalTypes->store($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\MedicalTypes  $medicalTypes
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return $this->medicalTypes->show($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\MedicalTypes  $medicalTypes
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        return $this->medicalTypes->update($id, $request->all());
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\MedicalTypes  $medicalTypes
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return $this->medicalTypes->delete($id);
    }

    public function addMedicalTypesToAgents(Request $request)
    {
        return $this->medicalTypes->addMedicalTypesToAgents($request->all());
    }

    public function addMedicalTypeToPlan(Request $request)
    {
        return $this->medicalTypes->addMedicalTypeToPlan($request->all());
    }

    public function getAgentList()
    {
        return $this->medicalTypes->getAgentList();
    }

    public function getPlanList()
    {
        return $this->medicalTypes->getPlanList();
    }

    public function getPlanListFromMedicalType($medical_id)
    {
        return $this->medicalTypes->getPlanListFromMedicalType($medical_id);
    }

    public function getAgentListFromMedicalType($medical_id)
    {
        return $this->medicalTypes->getAgentListFromMedicalType($medical_id);
    }
}
