<?php

namespace App\Http\Controllers\Api\V2\Report;

use App\Http\Controllers\Controller;
use App\Repositories\Agents\AgentReportRepository;
use Illuminate\Http\Request;

class AgentReportController extends Controller
{
    /**
     * @var AgentReportRepository
     */
    private $repository;

    public function __construct(AgentReportRepository $repository)
    {
        $this->repository = $repository;
    }

    public function export(Request $request){
        $filters = $request->query->all();
        return $this->repository->generate_export($filters);
    }

    public function getAgentReport(Request $request){
        return $this->repository->getAgentReport($request);
    }
}