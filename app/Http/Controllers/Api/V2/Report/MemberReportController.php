<?php

namespace App\Http\Controllers\Api\V2\Report;

use App\Http\Controllers\Controller;
use App\Repositories\Members\MemberReportRepository;
use Illuminate\Http\Request;

class MemberReportController extends Controller
{

    /**
     * @var MemberReportRepository
     */
    private $repository;

    public function __construct(MemberReportRepository $repository)
    {
        $this->repository = $repository;
    }

    public function export(Request $request){
        $filters = $request->query->all();
       return $this->repository->generate_export($filters);
    }

    public function getMemberReport(Request $request){
        return $this->repository->getMemberReport($request);
    }
}
