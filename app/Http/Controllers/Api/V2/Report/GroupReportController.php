<?php

namespace App\Http\Controllers\Api\V2\Report;

use App\Http\Controllers\Controller;
use App\Repositories\Groups\GroupReportRepository;
use App\UserInfoPolicyAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class GroupReportController extends Controller
{
    /**
     * @var GroupReportRepository
     */
    private $repository;

    public function __construct(GroupReportRepository $repository)
    {
        $this->repository = $repository;
    }

    public function export(Request $request){
        $filters = $request->query->all();
        return $this->repository->generate_export($filters);
    }

    public function getGroupReport(Request $request){
        return $this->repository->getGroupReport($request);
    }
    public function generateAdminGroupReport(Request $request){
        $filters = $request->query->all();
        return $this->repository->generateAdminGroupreport($request);
    }
    public function getAdminGroupReport(Request $request){
        $filters = $request->query->all();
        return $this->repository->getAdminGroupreport($request);
    }
}