<?php

namespace App\Http\Controllers\Api\V2\Report;

use App\Http\Controllers\Controller;
use App\Repositories\Payment\ExtraHealthPaymentRepository;
use Illuminate\Http\Request;

class ExtraHealthPaymentReportController extends Controller
{
    /**
     * @var ExtraHealthPaymentRepository
     */
    private $repository;

    public function __construct(ExtraHealthPaymentRepository $repository)
    {
        $this->repository = $repository;
    }

    public function export(Request $request){
        $this->repository->generate_export($request);
    }

    public function getExtraHealthPaymentReport(Request $request): array
    {
        return $this->repository->getPaymentReport($request);
    }
}
