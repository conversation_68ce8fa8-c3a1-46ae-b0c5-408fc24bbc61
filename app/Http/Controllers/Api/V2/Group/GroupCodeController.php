<?php

namespace App\Http\Controllers\Api\V2\Group;

use App\GroupInfo;
use App\GroupUpdate;
use App\Http\Controllers\Controller;
use App\Http\Requests\Group\GroupCodeRequest;
use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use App\Service\MessageService;
use Carbon\Carbon;



class GroupCodeController extends Controller
{

    public function update(GroupCodeRequest $request): JsonResponse
    {
        DB::beginTransaction();
        \Log::info('Transaction started for group code update.');

        $validatedRequest = $request->validated();
        $groupId = $validatedRequest['group_id'];
        $groupCode = $validatedRequest['group_code'];
        \Log::info('Validated request data.', ['group_id' => $groupId, 'group_code' => $groupCode]);

        try {
            $group = GroupInfo::find($groupId);
            if (!$group) {
                throw new Exception("Group not found for ID: {$groupId}");
            }
            \Log::info('Group found.', ['group_id' => $groupId]);

            $oldGroupCode = $group->gcode;
            $group->update(['gcode' => $groupCode]);
            \Log::info('Group code updated.', ['old_group_code' => $oldGroupCode, 'new_group_code' => $groupCode]);

            GroupUpdate::create([
                'group_id' => $groupId,
                'elgb_act' => "CODE",
                'elgb_act_date' => time(),
                'elgb_comment' => "Group Code Updated from Old: {$oldGroupCode}. New: {$groupCode}",
                'elgb_agent' => request()->header('id'),
                'elgb_agentname' => request()->header('name')
            ]);
            \Log::info('Group update record created.', ['group_id' => $groupId]);

            DB::commit();
            \Log::info('Transaction committed successfully.');

            $emailData = $this->emailContextBasedOnCodeChange($group, $oldGroupCode, $groupCode);
            \Log::info('Group code change Email data prepared.', ['email_data' => $emailData]);

            $messageService = new MessageService();
            $messageService->sendEmailWithContentData($emailData);
            \Log::info('Email sent successfully.');
        }
        catch (Exception $exception) {
            DB::rollBack();
            \Log::error('Transaction rollback due to exception.', ['exception' => $exception]);

            $errorCode = in_array($exception->getCode(), Response::$statusTexts)
                ? $exception->getCode()
                : Response::HTTP_UNPROCESSABLE_ENTITY;
            return response()->json(
                new ErrorResource([
                    'statusCode' => $errorCode,
                    'error' => $exception->getMessage(),
                    'message' => 'Failed to update Group Code.'
                ]), $errorCode
            );
        }

        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Group Code updated.',
            ]), Response::HTTP_OK
        );
    }

    public function isUnique(GroupCodeRequest $request): JsonResponse
    {
        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Group Code Unique.',
            ]), Response::HTTP_OK
        );
    }

    public function hasConflict(GroupCodeRequest $request): JsonResponse
    {
        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Group Code does not conflict with others.',
            ]), Response::HTTP_OK
        );
    }

    protected function emailContextBasedOnCodeChange($groupInfo, $oldGroupCode, $newGroupCode)
    {
        $toAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $groupInfo->gemail;
        $ccAddress = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $groupInfo->gemail;
        $date = Carbon::now()->format('m/d/Y');
        $message = "This is an automated notice to notify that your group code has been updated on " . $date . ".";
        $contentData = [
            'Group Id' => $groupInfo->gid,
            'Old Group Code' => $oldGroupCode,
            'New Group Code' => $newGroupCode,
        ];
        

        $middleMessage = '<div style="padding-top:20px;padding-bottom:10px;">
            <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;">
                Old Group Code: ' . $oldGroupCode . '<br>
            </p>
        </div>
        <div style="padding-top:20px;padding-bottom:10px;">
            <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;">
                New Group Code: ' . $newGroupCode . '<br>
            </p>
        </div>';


        $emailConfigurationName = "GROUP_CODE_CHANGE"; //create a new email template
        $subject = 'Group Code Changes';
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => $subject,
            'message' => $message,
            'data' => $contentData, 
            'middleMessage'=>$middleMessage,
        ];
        return $emailData;
    }

}
