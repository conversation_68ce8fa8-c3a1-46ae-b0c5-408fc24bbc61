<?php

namespace App\Http\Controllers\Api\V2\Group;

use App\Http\Controllers\Controller;
use App\Http\Requests\Group\CreateGroupNoteRequest;
use App\Repositories\Notes\NotesFeatures;
use Illuminate\Http\Request;

class GroupNoteController extends Controller
{
    /**
     * @var NotesFeatures
     */
    private $repository;

    /**
     * GroupNoteController constructor.
     * @param NotesFeatures $repository
     */
    public function __construct(NotesFeatures $repository)
    {
        $this->repository = $repository;
    }

    public function addGroupNote(CreateGroupNoteRequest $request){
        return $this->repository->addGroupNotes($request);
    }

    public function listNotes($groupId){
        return $this->repository->listGroupNotes($groupId);
    }

    public function deleteGroupNote(CreateGroupNoteRequest $request)
    {
        return $this->repository->deleteGroupNote($request);
    }
}
