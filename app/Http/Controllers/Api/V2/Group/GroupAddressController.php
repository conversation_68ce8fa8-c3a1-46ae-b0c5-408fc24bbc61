<?php

namespace App\Http\Controllers\Api\V2\Group;

use App\Http\Controllers\Controller;
use App\Http\Requests\ManageGroup\AddGroupAddressRequest;
use App\Http\Requests\ManageGroup\DeleteGroupAddressRequest;
use App\Http\Requests\ManageGroup\SetGroupPrimaryAddressRequest;
use App\Repositories\Groups\GroupAddressRepository;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;

class GroupAddressController extends Controller
{
    use ResponseMessage;
    /**
     * @var GroupAddressRepository
     */
    private $repository;

    /**
     * GroupAddressController constructor.
     * @param GroupAddressRepository $repository
     */
    public function __construct(GroupAddressRepository $repository)
    {
        $this->repository = $repository;
    }

    public function groupAddressList($groupId)
    {
        return $this->repository->getAddressByGroupId($groupId);
    }

    public function create(AddGroupAddressRequest $request){
        return $this->repository->createGroupAddress($request->validated());
    }

    public function setPrimaryGroupAddress(SetGroupPrimaryAddressRequest $request){
        return $this->repository->setPrimaryGroupAddress($request->validated());
    }

    public function deleteGroupAddress(DeleteGroupAddressRequest $request){
        return $this->repository->deleteGroupAddress($request->validated());
    }
}
