<?php

namespace App\Http\Controllers\Api\V2\Group;

use App\Http\Controllers\Controller;
use App\Http\Requests\Group\CreateGroupNoteRequest;
use App\Http\Requests\Group\GroupNameUpdateRequest;
use App\Repositories\Groups\GroupRepository;
use Illuminate\Http\Request;

class GroupInfoController extends Controller
{
    /**
     * @var NotesFeatures
     */
    private $repository;

    /**
     * @param GroupRepository $repository
     */
    public function __construct(GroupRepository $repository)
    {
        $this->repository = $repository;
    }

    public function updateGroupName(GroupNameUpdateRequest $request){
        return $this->repository->updateGroupName($request);
    }

}
