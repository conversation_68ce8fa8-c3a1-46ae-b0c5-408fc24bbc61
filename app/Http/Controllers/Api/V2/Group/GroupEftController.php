<?php

namespace App\Http\Controllers\Api\V2\Group;

use App\GroupInfo;
use App\Http\Controllers\Controller;
use App\Http\Requests\GroupEft\CreateGroupEftBankRequest;
use App\Http\Requests\GroupEft\DeleteGroupEftBankRequest;
use App\Http\Requests\GroupEft\SetPrimaryGroupEftBankRequest;
use App\Repositories\Groups\GroupEftRepository;
use Illuminate\Http\Request;

class GroupEftController extends Controller
{
    /**
     * @var GroupEftRepository
     */
    private $repository;

    /**
     * AchPaymentController constructor.
     * @param GroupEftRepository $repository
     */
    public function __construct(GroupEftRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getEftBanks($groupId){
        return $this->repository->getEftBankByGroupId($groupId);
    }

    public function create(CreateGroupEftBankRequest $request){
        return $this->repository->createGroupEft($request->validated());
    }

    public function setPrimaryEft(SetPrimaryGroupEftBankRequest $request){
        return $this->repository->setPrimaryEftBank($request->validated());
    }

    public function deleteEft(DeleteGroupEftBankRequest $request){
        return $this->repository->deleteEftBank($request->validated());
    }

    public function checkEftBankExists($groupId){
        return $this->repository->checkBank($groupId);
    }
}
