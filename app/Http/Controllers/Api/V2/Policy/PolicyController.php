<?php

namespace App\Http\Controllers\Api\V2\Policy;

use App\Http\Controllers\Controller;
use App\Http\Requests\Policy\CreateDependentRequest;
use App\Http\Requests\Policy\GetHealthQuestionsRequest;
use App\Http\Requests\Policy\UpdateEmailRequest;
use App\Http\Requests\Policy\UpdateAltEmailRequest;
use App\Http\Requests\Policy\UpdateEmployerInfoRequest;
use App\Http\Requests\Policy\UpdatePersonalInfoRequest;
use App\Http\Requests\Policy\UpdatePhoneRequest;
use App\Repositories\PolicyRepository;
use Illuminate\Http\Request;
use App\Traits\ResponseMessage;


class PolicyController extends Controller
{
    use ResponseMessage;
    /**
     * @var PolicyRepository
     */
    private $repository;

    /**
     * PolicyController constructor.
     * @param PolicyRepository $repository
     */
    public function __construct(PolicyRepository $repository)
    {

        $this->repository = $repository;
    }

    public function getHealthQuestions(GetHealthQuestionsRequest $request){
        $query = $request->validated();
        return $this->repository->getHealthQuestions($query);
    }

    public function getDistinctBillDate() {

        return $this->successResponse('Success', $this->repository->getDistinctBillDate());

    }

    public function createDependent(CreateDependentRequest $request){
        $data = $request->validated();
        return $this->repository->createDependent($data);
    }

    public function getDependentList($policy_id){
        return $this->repository->getDependentList($policy_id);
    }

    public function addDependentToPolicy(Request $request){
        return $this->repository->addDependentToPolicy($request);
    }

    public function updateDependent(CreateDependentRequest $request){
        $data = $request->validated();
        return $this->repository->updateDependent($data);
    }

    public function deleteDependent(CreateDependentRequest $request){
        $data = $request->validated();
        return $this->repository->deleteDependent($data);
    }

    public function getEmployerInfo($policyId){
        return $this->repository->getEmployerInfo($policyId);
    }

    public function getEmployerInfoOptions(){
        return $this->repository->getEmployerInfoOptions();
    }

    public function updateEmployerInfo(UpdateEmployerInfoRequest $request){
        return $this->repository->updateEmployerInfo($request->validated());
    }

    public function updatePersonalInfo(UpdatePersonalInfoRequest $request){
        return $this->repository->updatePersonalInfo($request->validated());
    }

    public function updateEmailInfo(UpdateEmailRequest $request){
        return $this->repository->updateEmailInfo($request->validated());
    }

    public function updateAltEmailInfo(UpdateAltEmailRequest $request)
    {
        return $this->repository->updateAltEmailInfo($request->validated());
    }
    public function updatePhoneInfo(UpdatePhoneRequest $request){
        return $this->repository->updatePhoneInfo($request->validated());
    }

    public function getEftPaymentDetails($paymentId){
        return $this->repository->getEftPaymentDetail($paymentId);
    }

    public function getCreditCardDetails($paymentId){
        return $this->repository->getCCPaymentDetail($paymentId);
    }

    public function getDependentNames(Request $request){
        return $this->successResponse('Success', $this->repository->getDependentNames($request->limit,$request->name));
    }
}
