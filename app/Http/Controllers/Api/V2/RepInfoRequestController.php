<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\RepInfoRequest\ApproveRequestRequest;
use App\Http\Requests\RepInfoRequest\RejectRequestRequest;
use App\RepInfoRequest;
use App\Repositories\RepInfoRequestRepository;
use Illuminate\Http\Request;

class RepInfoRequestController extends Controller
{
    /**
     * @var RepInfoRequestRepository
     */
    private $repository;

    /**
     * EnrollmentQuestionController constructor.
     * @param RepInfoRequestRepository $repository
     */
    public function __construct(RepInfoRequestRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $request->validate([
            'email' => 'nullable|email',
            'date' => 'nullable|date_format:m/d/Y', 

        ]);
        $filters = $request->query->all();
        return $this->repository->paginatedFormattedList($request->per_page ?? 25, $filters);
    }

    public function approveRequest(Request $request){
        return $this->repository->updateStatus($request->all(),RepInfoRequest::STATUS_IN_PROGRESS);
    }

    public function completeRequest(Request $request){
        return $this->repository->updateStatus($request->all(),RepInfoRequest::STATUS_COMPLETED);
    }

    public function rejectRequest(RejectRequestRequest $request){
        return $this->repository->rejectRequest($request->validated());
    }

    public function getOptions(){
        return $this->repository->getOptions();
    }

    public function getPaymentDetails($id){
        return $this->repository->getPaymentDetails($id);
    }

    public function getPolicyPaymentDetails($policy_id){
        return $this->repository->getPolicyPaymentDetails($policy_id);
    }
    
}
