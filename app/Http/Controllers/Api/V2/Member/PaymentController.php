<?php

namespace App\Http\Controllers\Api\V2\Member;

use App\Http\Controllers\Controller;
use App\Http\Requests\Payment\CreateCcPaymentRequest;
use App\Http\Requests\Payment\VerifyCCPaymentRequest;
use App\Repositories\Payment\CcPaymentRepository;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    /**
     * @var CcPaymentRepository
     */
    private $repository;

    /**
     * PaymentController constructor.
     * @param CcPaymentRepository $repository
     */
    public function __construct(CcPaymentRepository $repository)
    {
        $this->repository = $repository;
    }

//    public function checkPayer($userId){
//        return $this->repository->checkPayerByUserId($userId);
//    }

    public function create(CreateCcPaymentRequest $request){
        return $this->repository->addCreditInfo($request->validated());
    }

    public function verify(VerifyCCPaymentRequest $request)
    {
        return $this->repository->verifyExistingCardWithMerchant($request->validated());
    }
}
