<?php

namespace App\Http\Controllers\Api\V2\Member;

use App\Helpers\EmailSendHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Member\MemberApproveRequest;
use App\MemberReferral;
use App\Repositories\MemberApproval;
use App\Repositories\Members\ReferralListRepository;
use App\Traits\ResponseMessage;
use App\UserInfoPolicyAddress;
use Illuminate\Http\Request;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class MemberController extends Controller
{
    use ResponseMessage;

    /**
     * @var MemberApproval
     */
    private $repository;

    public function __construct(MemberApproval $repository, ReferralListRepository $referralListRepository)
    {
        $this->repository = $repository;
        $this->referralListRepository = $referralListRepository;
    }

    public function approveRejectMember(MemberApproveRequest $request)
    {
        return $this->repository->approveRejectMember($request);
    }

    public function referralList(int $refId, Request $request)
    {
        try {
            $perPage = $request->query->get('per_page', 20);
            $filters = $request->query->all();

            $data = $this->referralListRepository->paginatedList($refId, $perPage, $filters);

            return $this->successResponse('Member referral list fetched successfully', $data);

        } catch (\Exception $e) {
            return $this->failedResponse('Failed to fetch referral list: ' . $e->getMessage());
        }
    }
    public function mainReferralList(Request $request)
    {
        try {
            $perPage = $request->query->get('per_page', 20);
            $filters = $request->query->all();

            $data = $this->referralListRepository->paginatedList((int)null, $perPage, $filters);

            return $this->successResponse('Member referral list fetched successfully', $data);

        } catch (\Exception $e) {
            return $this->failedResponse('Failed to fetch referral list: ' . $e->getMessage());
        }
    }

    public function resendEmail(Request $request)
    {
        try {
            $memberReferral = MemberReferral::query()
                ->where('id', '=', $request->id)
                ->where('ref_by', '=', $request->ref_by)
                ->first();
            if (!$memberReferral instanceof MemberReferral) {
                throw new \Exception('Failed sending referral email.');
            }
            $this->preparePayLoad($memberReferral);
            return $this->successResponse('Referral email sent successfully.');
        } catch (\Exception $e) {
            return $this->failedResponse($e);
        }
    }

    private function preparePayLoad($memberReferral)
    {
        $title = null;
        $updatedMessage = null;

        if ($memberReferral) {
             $userInfoPolicyAddress = UserInfoPolicyAddress::query()
                ->select('cfname', 'cmname', 'clname')
                ->where('userid', $memberReferral->ref_by)
                ->first();

             $title = $this->getFullName($userInfoPolicyAddress) . " has referred you to us, please open.";
             $message = $memberReferral->message;

            // Replace whatever is inside <b>...</b> with the full name
            $updatedMessage = preg_replace("/<b>.*?<\/b>/", "<b>{$this->getFullName($userInfoPolicyAddress)}</b>", $message);

            $data = [
                'name' => $memberReferral->name,
                'title' => $title,
                'message' => $updatedMessage,
                'email' => $memberReferral->email,
                'ref_by' => $memberReferral->ref_by,
                'sent_count' => $memberReferral->sent_count + 1,
                'referred_platform' => 'admin-dashboard'
            ];

            MemberReferral::query()->where('ref_by', '=',$memberReferral->ref_by)->update($data);
        }

        $emailService = new EmailSendHelper();
        if (!$emailService->sendReferralEmail($memberReferral->email, $title, $memberReferral->name, $updatedMessage, [], '', 'MEMBER_REFERRAL')) {
            throw new \Exception('Failed sending referral email.');
        }
    }

    private function getFullName($userInfoAddress): ?string
    {
        if (is_null($userInfoAddress->cmname)) {
            return trim("{$userInfoAddress->cfname} {$userInfoAddress->clname}");
        }

        return trim("{$userInfoAddress->cfname} {$userInfoAddress->cmname} {$userInfoAddress->clname}");
    }
}
