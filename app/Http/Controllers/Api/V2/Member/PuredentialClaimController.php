<?php

namespace App\Http\Controllers\Api\V2\Member;

use App\Http\Controllers\Controller;
use App\Http\Requests\PrudentialClaimCreateRequest;
use App\Http\Requests\UpdatePrudentialClaimRequest;
use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use App\Repositories\Members\PrudentialClaimRepository;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Resources\DataResponse;


class PuredentialClaimController extends Controller
{
    //

    private $prudClaimRepo;

    public function __construct(PrudentialClaimRepository $prudClaimRepo)
    {
        $this->prudClaimRepo = $prudClaimRepo;
    }

    public function getPrudentialList(Request $request)
    {
        ini_set('max_execution_time', 0);
        try {
            $data = $this->prudClaimRepo->getPrudentialList($request->policy_id);
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Prudential claim fetched sucessfully.',
                    'data' => $data
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {
            $errorCode = in_array($exception->getCode(), Response::$statusTexts)
            ? $exception->getCode()
            : Response::HTTP_UNPROCESSABLE_ENTITY;
        return response()->json(
            new ErrorResource([
                'statusCode' => $errorCode,
                'error' => $exception->getMessage(),
                'message' => 'Failed to fetch prudential claim.'
            ]), $errorCode
        );
        }
    }

    public function getPrudentialAllList(Request $request)
    {
        ini_set('max_execution_time', 0);
        try {
            $filters = $request->query->all();
            $data = $this->prudClaimRepo->getPrudentialAllList($request->all(), $filters);
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Prudential claim fetched sucessfully.',
                    'data' => $data
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {
            $errorCode = in_array($exception->getCode(), Response::$statusTexts)
            ? $exception->getCode()
            : Response::HTTP_UNPROCESSABLE_ENTITY;
        return response()->json(
            new ErrorResource([
                'statusCode' => $errorCode,
                'error' => $exception->getMessage(),
                'message' => 'Failed to fetch prudential claim.'
            ]), $errorCode
        );
        }
    }

    public function getAllMembersWithSpecialPlans(Request $request){
        ini_set('max_execution_time', 0);
        $data = $this->prudClaimRepo->getAllMembersWithSpecialPlans($request->query('query'),$request->query('limit')??10);
        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Prudential members fetched sucessfully.',
                'data' => $data
            ]), Response::HTTP_OK
        );
    }

    public function getPrudentialPlanList(Request $request){
        return new DataResponse($this->prudClaimRepo->getPrudentialPlanList($request));
    }

    public function getPlanList(Request $request)
    {
        ini_set('max_execution_time', 0);
        try {
            $data = $this->prudClaimRepo->plansByPolicy($request->policy_id);
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Plan List fetched sucessfully.',
                    'data' => $data
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {
            $errorCode = in_array($exception->getCode(), Response::$statusTexts)
            ? $exception->getCode()
            : Response::HTTP_UNPROCESSABLE_ENTITY;
        return response()->json(
            new ErrorResource([
                'statusCode' => $errorCode,
                'error' => $exception->getMessage(),
                'message' => 'Failed to fetch plans.'
            ]), $errorCode
        );
        }
    }

    public function getPrudentialForm(Request $request)
    {
        ini_set('max_execution_time', 0);
        try {
            $data = $this->prudClaimRepo->getPrudentialForm($request->policy_id);
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Prudential Form fetched sucessfully.',
                    'data' => $data
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {
            $errorCode = in_array($exception->getCode(), Response::$statusTexts)
            ? $exception->getCode()
            : Response::HTTP_UNPROCESSABLE_ENTITY;
        return response()->json(
            new ErrorResource([
                'statusCode' => $errorCode,
                'error' => $exception->getMessage(),
                'message' => 'Failed to fetch Prudential Forms.'
            ]), $errorCode
        );
        }
    }

    public function updatePrudentialClaim(UpdatePrudentialClaimRequest $request)
    {
        ini_set('max_execution_time', 0);
        try {
            $data = $this->prudClaimRepo->update($request);
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Prudential Form Updated sucessfully.',
                    'data' => $data
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {
            $errorCode = in_array($exception->getCode(), Response::$statusTexts)
            ? $exception->getCode()
            : Response::HTTP_UNPROCESSABLE_ENTITY;
        return response()->json(
            new ErrorResource([
                'statusCode' => $errorCode,
                'error' => $exception->getMessage(),
                'message' => 'Failed to fetch Prudential Forms.'
            ]), $errorCode
        );
        }
    }

    public function createPrudentialClaim(PrudentialClaimCreateRequest $request)
    {
        ini_set('max_execution_time', 0);
        try {
            $data = $this->prudClaimRepo->create($request);
            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Prudential Form Created sucessfully.',
                    'data' => $data
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {
            $errorCode = in_array($exception->getCode(), Response::$statusTexts)
            ? $exception->getCode()
            : Response::HTTP_UNPROCESSABLE_ENTITY;
        return response()->json(
            new ErrorResource([
                'statusCode' => $errorCode,
                'error' => $exception->getMessage(),
                'message' => 'Failed to fetch Prudential Forms.'
            ]), $errorCode
        );
        }
    }
}
