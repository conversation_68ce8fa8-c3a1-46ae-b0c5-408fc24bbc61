<?php

namespace App\Http\Controllers\Api\V2\Member;

use App\Address;
use App\BundelPlan;
use App\Http\Controllers\Controller;
use App\Http\Requests\Member\SwitchPlanRequest;
use App\PlanOverview;
use App\PlanPolicy;
use App\PlanPricingDisplay;
use App\PlanZip;
use App\PolicyUpdate;
use App\PlanState;
use App\Service\MessageService;
use App\Traits\ResponseMessage;
use App\UserInfo;
use Carbon\Carbon;
use App\Helpers\PolicyUpdateHelper;
use App\Plan;
use App\PlanPolicyMember;
use App\Repositories\MemberCard\MemberCardRepository;
use App\Repositories\MemberResource\MemberResourceFeatures;
use App\UserInfoPolicyAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SwitchPlanController extends Controller
{
    use ResponseMessage;

    /**
     * @param $planPolicyId
     * @return object|null
     * Fetching current plan through plan policy id - p_ai in plan_overview
     */
    protected function fetchCurrentActivePlan($planPolicyId)
    {
        return PlanOverview::query()
            ->where('p_ai','=',$planPolicyId)
            ->first();
    }

    /**
     * @param $memberId
     * @return object|null
     * Fetching user address
     */
    protected function memberAddress($memberId)
    {
        return Address::query()
            ->where([
                ['a_userid', $memberId],
                ['status', 1],
                ['type', 'R'],
            ])
            ->first();
    }

    protected function planStates($planId)
    {
        return PlanState::query()
            ->where([
                ['pid', $planId],
            ])
            ->count();
    }

    /**
     * @param Request $request
     * @param $planPolicyId
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * Fetch plans from plan overview in formatted list
     */
    public function getPlans(Request $request, $planPolicyId)
    {
        $filters = $request->query->all();
        /**
         * @var $currentPlan PlanOverview
         */
        $currentPlan = $this->fetchCurrentActivePlan($planPolicyId);
        $memberInfo = $currentPlan->userInfo;
        $isAssociation = $currentPlan->is_assoc;
        $checkAssoc = $isAssociation ? "=" : "!=";

        try {
            $query = PlanPricingDisplay::query()
                ->where([
                    // ['pricing_status', 1],
                    ['forsale', 1],
                    ['tier', $currentPlan->tier],
                    ['is_assoc',$checkAssoc, 1],
                ]);


            $bundledPlanPids = BundelPlan::where('bundle_pid', $currentPlan->pid)->pluck('pid')->toArray();

            if (count($bundledPlanPids) > 0) {
                $bundledPlTypes = Plan::whereIn('pid',$bundledPlanPids)->pluck('pl_type')->toArray();
                $query->WhereIn('pl_type', $bundledPlTypes);
            }
            else{
                $query->where('pl_type', $currentPlan->pl_type);
            }

            if (isset($memberInfo)) {
                $memberAddress = $this->memberAddress($memberInfo->userid);
                $planStates = $this->planStates($currentPlan->pid);
                if ($memberAddress && $planStates) {
                    $query->whereHas('planStates', function ($q) use ($memberAddress) {
                        $q->where('state', '=', $memberAddress->state);
                    });
                }
            }

            if ($currentPlan->plan_pricing_type == 2 && $memberInfo) {
                $query->where([
                    ['age1', '<=', $memberInfo->age],
                    ['age2', '>=', $memberInfo->age],
                ]);
            }
            // Checking Med Life
            $userInfoPolicyAddress = $currentPlan->userInfoPolicyAddress;
            $entZip = substr($userInfoPolicyAddress->zip, 0, 3);
            if ($currentPlan->cid == PlanPricingDisplay::MET_LIFE_CID) {
                $query->whereHas('planZips', function ($q) use ($entZip) {
                    $q->where('zip', 'LIKE', '%' . $entZip . '%');
                });
            }

            if (isset($filters['query'])) {
                $query->where(function ($subQuery) use ($filters) {
                    $subQuery->orWhere('web_display_name', 'LIKE', '%' . $filters['query'] . '%');
                    $subQuery->orWhere('plan_name_system', 'LIKE', '%' . $filters['query'] . '%');
                });
            }

            $plans = $query->orderBy('plan_name_system', 'asc')->get();
            // $plans = $query->get();
            $data = [];
            foreach ($plans as $p) {
                $pricingTimeStamp = $pricingDate = $formattedPricingDate = '';
                if ($p->pricing_date) {
                    $pricingTimeStamp = $p->pricing_date;
                    $pricingDate = Carbon::createFromTimestamp($pricingTimeStamp)->format('m/d/Y H:i:s');
                    $formattedPricingDate = Carbon::createFromTimestamp($pricingTimeStamp)->format('M - Y');
                }
                $date = $formattedPricingDate ? " - [{$formattedPricingDate}]" : null;
                $oldPrice = $p->pricing_status === 0 ? '| old price' : '';
                $formattedPlanName = "{$p->plan_name_system} | {$p->tier} | $ {$p->price_male_nons}{$date} {$oldPrice}";
                $data[] = [
                    'pid' => $p->pid,
                    'plan_pricing_id' => $p->plan_pricing_id,
                    'plan_name' => $p->plan_name_system,
                    'web_display_name' => $p->web_display_name,
                    'tier' => $p->tier,
                    'price' => $p->price_male_nons,
                    'pl_type' => $p->pl_type,
                    'formatted_plan_name' => $formattedPlanName,
                    'pricing_timestamp' => $pricingTimeStamp,
                    'pricing_date' => $pricingDate,
                    'formatted_pricing_date' => $formattedPricingDate,
                    'is_assoc' => $p->is_assoc
                ];
            }

            $message = "Success - Plan list for {$currentPlan->plan_name_system} | {$currentPlan->tier} | $ {$currentPlan->price_male_nons}";
            return $this->successResponse($message, $data);
        } catch (\Throwable $th) {
            return $this->failedResponse("Failed to fetch plans.");
        }
    }

    /**
     * @param $p
     * @param $price
     * @param null $formattedPricingDate
     * @return string
     */
    protected function formattedPlanName($p, $price,$formattedPricingDate=null)
    {
        $date = $formattedPricingDate ? " - [{$formattedPricingDate}]" : null;
        return "{$p->web_display_name} | {$p->tier} | $ {$price}{$date}";
    }

    /**
     * @param $planPolicyId
     * @return mixed
     * fetch plan policy by plan policy id
     */
    protected function getPlanPolicy($planPolicyId)
    {
        return PlanPolicy::find($planPolicyId);
    }

    /**
     * @param $request
     * @param $policyId
     * @return mixed
     * creating new plan policy
     */
    protected function createNewPlanPolicy($request, $policyId)
    {
        $data = [
            'policy_num' => $policyId,
            'plan_id' => $request['plan_pricing_id'],
            'pstatus' => 1,
            'peffective_date' => $request['effective_date'],
            'date' => time(),
            'ppptype' => 'price_male_nons',
            'is_approved' => 1
        ];
        /**
         * @var $planPolicy PlanPolicy
         */
        $planPolicy = new PlanPolicy();
        return $planPolicy->create($data);
    }

    /**
     * @param SwitchPlanRequest $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function switchPlan(SwitchPlanRequest $request){
        $currentPlanPolicyId = $request['current_plan_policy_id'];
        $planId = $request['plan_id'];
        $currentPlanId = $request['current_plan_id'];
        $oldPlanPolicy = $this->getPlanPolicy($currentPlanPolicyId);
        $policy = $oldPlanPolicy->getPlanPolicyDetail;
        $policyId = $policy->policy_id;
        $loginUserId = $request['login_user_id'] ?: null;
        $currentPlan = $this->fetchCurrentActivePlan($currentPlanPolicyId);
        if ($currentPlan->pid != $currentPlanId) return $this->failedResponse('Current plan does not exist.');
        /**
         * checking date if current plan is same as new plan ignore date else check date
         * required effective date must be greater than current effective date
         */
        $checkSwitchPlanDate = $this->checkSwitchPlanRequestedEffectiveDateGreaterThanCurrentDate($oldPlanPolicy->peffective_date,$request['effective_date']);
        if ($currentPlanId != $planId && !$checkSwitchPlanDate){
            return $this->failedResponse("Requested effective date - {$request['effective_date']} must be greater than current effective date - {$oldPlanPolicy->peffective_date}");
        }
        if ($policy) {
            DB::beginTransaction();
            try {
                /**
                 * @var $oldPlanPolicy PlanPolicy
                 * here current plan policy means old/ previous plan policy
                 * update current plan policy
                 * if current plan_policy id is same as requested current_plan_policy_id - withdrawn else termed
                 * if requested effective_date and current plan policy effective date similar - withdrawn
                 */
                if($currentPlanId == $planId || $request['effective_date'] == $oldPlanPolicy->peffective_date){
                    $policyUpdateAction = 'wdr';
                    $oldPlanPolicy->update([
                        'pstatus' => PlanPolicy::STATUS_WITHDRAWN_VALUE,
                        'pterm_date' => $oldPlanPolicy->peffective_date
                    ]);
                    $oldPlanPolicyMessage = "Plan withdrawn and switched.";
                }else{
                    $policyUpdateAction = 'tmo';
                    $oldPlanPolicy->update([
                        'pstatus' => PlanPolicy::STATUS_TERMED_VALUE,
                        'pterm_date' => Carbon::parse($request['effective_date'])->subDay()->format('Y-m-d')
                    ]);
                    $oldPlanPolicyMessage = "Plan termed and switched.";
                }


                //update current plan policy in policy updates
                $this->updatePolicyLog($policy, $policyUpdateAction, $oldPlanPolicyMessage,$loginUserId, $currentPlanId);

                //create new plan policy
                $newPlanPolicy = $this->createNewPlanPolicy($request, $policyId);

                //update current plan policy in policy updates
                $newPlanPolicyMessage = "New plan switched.";
                $this->updatePolicyLog($policy, 'SPC', $request['reason'],$loginUserId, $currentPlanId, $planId);

                //send email
                $newCurrentPlan = $this->fetchCurrentActivePlan($newPlanPolicy->p_ai);
                $previousPlan = $this->fetchCurrentActivePlan($currentPlanPolicyId);
                $this->sendSwitchPlanEmail($policy, $newPlanPolicy, $previousPlan, $newCurrentPlan);
                $existingPlanPoliciesMember = PlanPolicyMember::where('policy_id', $policy->policy_id)->where('pid',$currentPlanId)->where('is_updateMemberCard','0')->where('email_sent_at',null)->first();
                if($existingPlanPoliciesMember) {
                    $planOverview = PlanOverview::where('policy_id', $policy->policy_id)->where('pid',$planId)->select('brand','cid','p_ai','pid','policy_id','policy_userid')->first();
                    $existingPlanPoliciesMember->pid = $planId;
                    $existingPlanPoliciesMember->p_ai = $planOverview->p_ai;
                    $existingPlanPoliciesMember->brand = $planOverview->brand;
                    $existingPlanPoliciesMember->cid = $planOverview->cid;
                    $existingPlanPoliciesMember->save();
                } else {
                    $planOverview = PlanOverview::where('policy_id', $policy->policy_id)->where('pid',$planId)->select('brand','cid','p_ai','pid','policy_id','policy_userid')->first();
                    if(in_array($planOverview->cid, ["11", "77", "59", "65", "67", "47"])){
                        PolicyUpdateHelper::addPlanPoliciesMember($policy->policy_id, $planId);
                    }
                    else{
                        $plans = PlanPolicy::where('policy_num',$policy->policy_id)->get();
                        foreach($plans as $plan){
                            if(($plan->pstatus == 1 || ($plan->pterm_date && (int)$plan->pstatus === 2 && strtotime($plan->pterm_date) > strtotime(date('Y-m-d')) ))){
                                $activePlans [] = $plan->p_ai;
                            }
                        }
                        $plans_null_member_id_email_sent = [];
                        $plans_not_null_member_id_email_sent = [];
                        $plan_policy_members = PlanPolicyMember::where('policy_id',$policy->policy_id)->whereIn('p_ai',$activePlans)->get();
                        foreach($plan_policy_members as $plan_policy_member){
                            if(in_array($plan_policy_member->cid,["11", "77", "59", "65", "67", "47"])){
                                if(!empty($plan_policy_member->member_id)){
                                    $plans_not_null_member_id_email_sent[$plan_policy_member->p_ai] = $plan_policy_member->email_sent;
                                }else{
                                    $plans_null_member_id_email_sent[$plan_policy_member->p_ai] = $plan_policy_member->email_sent;
                                }
                            }
                        }
                        if(!empty($plans_null_member_id_email_sent)){
                            PolicyUpdateHelper::addPlanPoliciesMember($policy->policy_id, $planId);
                        }else if(empty($plans_not_null_member_id_email_sent)){
                            self::sendSwitchPlanMemberCardEmail($policy->policy_id);
                        }else{
                            self::sendSwitchPlanMemberCardEmail($policy->policy_id);
                        }
                    }
                }
                DB::commit();
                return $this->successResponse($newPlanPolicyMessage);
            } catch (\Throwable $th) {
                DB::rollBack();
                return $this->failedResponse('Failed to switch plan.');
            }
        } else {
            return $this->failedResponse('Current Plan  Not Found.');
        }
    }

    /**
     * @param $policy
     * @param $action
     * @param $comment
     * @param $loginUserId
     * @param $oldPId
     * @param null $newPId
     * @return PolicyUpdate
     * updating policy logs
     */
    public function updatePolicyLog($policy, $action, $comment,$loginUserId, $oldPId, $newPId = null)
    {
        $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
        $update = [
            'elgb_policyid' => $policy->policy_id,
            'elgb_act' => $action,
            'elgb_act_date' => time(),
            'elgb_comment' => $comment,
            'elgb_agent' => $loginUserId,
            'elgb_file_date' => date('Y-m-d'),
            'elgb_old_pid' => $oldPId,
            'elgb_new_pid' => $newPId,
            'origin' => $origin
        ];
        $policyUpdateLog = new PolicyUpdate();
        $policyUpdateLog->create($update);
        return $policyUpdateLog;
    }

    public function planFormattedData($d)
    {
        $formattedPlanName = $this->formattedPlanName($d, $d->price_male_nons);
        return [
            'policy_id' => $d->policy_id,
            'pid' => $d->pid,
            'plan_id' => $d->plan_id,
            'plan_pricing_id' => $d->plan_pricing_id,
            'plan_policy_id' => $d->p_ai,
            'plan_name' => $d->plan_name_system,
            'web_display_name' => $d->web_display_name,
            'tier' => $d->tier,
            'price' => $d->plan_new_price,
            'formatted_plan_name' => $formattedPlanName
        ];
    }

    /**
     * @param $policy
     * @param $planPolicy
     * @param $previousPlan
     * @param $newPlan
     * @return bool|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    protected function sendSwitchPlanEmail($policy, $planPolicy, $previousPlan, $newPlan)
    {
        $member = $policy->getMember;
        if(isset($agentInfo->agentCustomeHomepage)) {
            $agent = $policy->agentInfo->agentCustomeHomepage->email;
        } else {
            $agent = $policy->agentInfo;
        }
        $toAddress = config("testemail.TEST_EMAIL") ?: $member->cemail;
        $ccAddress = config("testemail.TEST_EMAIL") ?: $member->cemail_alt;
        $message = "Your plan has been switched.";
        $previousPlanData = $this->planFormattedData($previousPlan);
        $newPlanData = $this->planFormattedData($newPlan);
        $effectiveDate = Carbon::parse($planPolicy->peffective_date)->format('m/d/Y');
        $data = [
            'Confirmation #' => $policy->policy_id,
            'Member Name' => isset($member) ? $member->fullname : '',
            'Plan Name' => $newPlanData['formatted_plan_name'],
            'Effective Date' => $effectiveDate,
        ];

        $data1 = [
            'Plan Name' => $previousPlanData['formatted_plan_name'],
            'Termination Date' => Carbon::parse($previousPlan->pterm_date)->format('m/d/Y')
        ];
        $messageBottom = $this->switchPlanEmailMessageBottom($effectiveDate, $agent);
        $emailConfigurationName = "OPEN_ENROLLMENT";
        $emailData = [
            'email_message_configuration_name' => $emailConfigurationName,
            'toAddress' => $toAddress,
            'ccAddress' => $ccAddress,
            'subject' => "Switch Plan",
            'message' => $message,
            'dataName' => $member->fullname,
            'data' => $data,
            'data1' => $data1,
            'countData1' => 1,
            'messageBottom' => $messageBottom,
            'middleMessage' => 'Switched Plan',
            'middleMessage1' => 'Current Plan'
        ];
        $messageService = new MessageService();
        return $messageService->sendEmailWithContentData($emailData);
    }

    protected function switchPlanEmailMessageBottom($effectiveDate, $agent)
    {
        $html = "";
        $html .= "<p>";
        $html .= "As a member , you have access to special programs with exclusive pricing and benefits. ";
        $html .= "Let our tremendous buying power work for you.";
        $html .= "</p>";
        $html .= "<p>";
        $html .= "There is still time to add-on benefits for an effective of {$effectiveDate} through your Member Mobile App.";
        $html .= "</p>";
        $html .= "<p>";
        $html .= "If you should have any questions, ";
        $html .= "please contact your Broker-Rep {$agent->rep_full_name} [{$agent->agent_email}]";
        $html .= "</p>";
        $html .= "<p>";
        $html .= "Being a member has its privileges.";
        $html .= "</p>";
        $html .= "</br>";
        $html .= "<h3>The Elevate Wellness Team</h3>";
        return $html;
    }

    public function checkSwitchPlanRequestedEffectiveDateGreaterThanCurrentDate($oldEffectiveDate,$newRequestedEffectiveDate){
         return Carbon::parse($newRequestedEffectiveDate)->gte(Carbon::parse($oldEffectiveDate));
    }

    public function sendSwitchPlanMemberCardEmail($policy){
                $policyDetails = UserInfoPolicyAddress::where('policy_id', $policy)->select('policy_id','userid','agent_email','cemail','cfname','cmname','clname','effective_date','agent_id')->first();
                if(!$policyDetails instanceof UserInfoPolicyAddress) {
                    Log::channel('memberCard')->info(PHP_EOL ."Policy Detail Not Found.");
                }
                $member = UserInfo::where('userid',$policyDetails->userid)->first();
                $t_array = array();

                if ($member instanceof UserInfo) {
                    foreach ($member->getPolicyList as $singlePolicy) {
                        if ($policy != $singlePolicy->policy_id) {
                            continue;
                        }
                        $plans = $singlePolicy->getPolicyPlan;
                        $memberResource = new MemberResourceFeatures();
                        $t_array = $memberResource->getPlanCards($plans, $singlePolicy, $t_array, $member);
                    }
                }
                $tempArray = array();
                $uniqueData = array();
                foreach ($t_array as $val) {
                    if (isset($val['filename'])) {
                        if (in_array($val['filename'], $tempArray) || empty($val)) {
                            continue;
                        }
                        array_push($tempArray, $val['filename']);
                        array_push($uniqueData, $val);
                    }
                }
                $t_array = $uniqueData;

                if(!empty($t_array)){

                    $p_ai_values = array_unique(array_column($t_array, 'p_ai'));
                            $cards = "";
                            foreach($t_array as $list) {
                                $cards .= "<li><a href=\"" . $list['url'] . "\" target=\"_blank\" rel=\"noopener noreferrer\">" . $list['filename'] . "</a></li>";
                            }
                            $userEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policyDetails->cemail;
                            $agentEmail = config("testemail.TEST_EMAIL") ? config("testemail.TEST_EMAIL") : $policyDetails->agent_email;
                            $member_name = $policyDetails->getFullNameAttribute();
                            if(self::checkFirstEmail($policy)) {
                                $subject = "New Digital Documents Available ";
                            } else {
                                $subject = "Digital Documents Available ";
                            }
                            $data = [
                                'email' => $userEmail,
                                'agent_email' => $agentEmail,
                                'member_name' => $member_name,
                                'cards' => $cards,
                                'subject' => $subject,
                                'agent_id' => $policyDetails['agent_id']
                            ];
                            $memberCardRepository = new MemberCardRepository();
                            $mailStatus = $memberCardRepository->sendEmailWithMessageCenter($data);
                            if($mailStatus) {
                                $origin = request()->header('Origin') ?? request()->header('Referer') ?? null;
                                $today = date('Y-m-d');
                                $plan_policy_member = PlanPolicyMember::where('policy_id',$policy)->update(['email_sent' => 'Y','email_sent_at' => $today]);
                                PlanPolicy::where('policy_num',$policy)->update(['is_email_sent' => 'Y']);
                                    $time = time();
                                    $policyUpdateData = [
                                        'elgb_act' => "MEMCARD",
                                        'elgb_act_date' => $time,
                                        'elgb_comment' => "Member Card Send Successfully.",
                                        'elgb_file_date' => $today,
                                        'elgb_term_date' => null,
                                        'elgb_policyid' => $policy,
                                        'elgb_agent' => 'auto',
                                        'origin' => $origin
                                    ];
                                    PolicyUpdateHelper::updateEligibility($policyUpdateData);
                                Log::channel('memberCard')->info(PHP_EOL ."Member Card Send Successfully for Policy Id " . $policyDetails->policy_id . ".");
                            } else {
                                PlanPolicyMember::whereIn('p_ai', $p_ai_values)->whereNot('email_sent','Y')->update(['email_sent' => 'N']);
                                PlanPolicy::where('policy_num',$policy)->update(['is_email_sent' => 'N']);
                                Log::channel('memberCard')->info(PHP_EOL ."Mail Sending Failed for Policy Id " . $policyDetails->policy_id . ".");
                            }
                        }
        }
    public static function checkFirstEmail($policy) {
            $fetchPolicy = PolicyUpdate::where('elgb_policyid',$policy)->where('elgb_act','MEMCARD')->first();
            if(isset($fetchPolicy)){
                return true;
            } else {
                return false;
            }
    }
}
