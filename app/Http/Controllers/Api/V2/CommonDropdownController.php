<?php

namespace App\Http\Controllers\Api\V2;

use App\Helpers\CommonHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Payment;
use App\Traits\ResponseMessage;
use Illuminate\Http\Request;

class CommonDropdownController extends Controller
{
    use ResponseMessage;

    private $payment;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    public function getPaymentType()
    {
        return $this->successResponse('Success', CommonHelper::mapDropdownNameValue(Payment::$paymentTypes));
    }

    public function getPlatforms()
    {
        return $this->successResponse('Success', CommonHelper::mapDropdownNameValue(Payment::$platforms));
    }

    public function getPaymentStatuses()
    {
        return $this->successResponse('Success', CommonHelper::mapDropdownNameValue(Payment::$paymentStatuses));
    }

}
