<?php

namespace App\Http\Controllers\Api\V2\Plans;

use App\Http\Controllers\Controller;
use App\Http\Resources\DataResponse;
use App\Repositories\Plans\PlanRequestRepository;
use Illuminate\Http\Request;

class PlanRequestController extends Controller
{
    //

    private  $PlanRequestRepository;

    public function __construct()
    {
        $this->PlanRequestRepository = new PlanRequestRepository();
    }

    public function getPlanRequest()
    {
        $data = $this->PlanRequestRepository->getPlanRequest();
        return new DataResponse($data);
    }

    public function planRequestAction(Request $request)
    {
        $data = $this->PlanRequestRepository->planRequestAction($request);
        return new DataResponse($data);
    }
}
