<?php

namespace App\Http\Controllers\Api\V2\Agent;

use App\Http\Controllers\Controller;
use App\Http\Requests\Agent\AgentDateSpecificCommissionRequest;
use App\Http\Resources\SuccessResource;
use App\NbCommissionDataSummary;
use App\Repositories\Agents\AgentCommissionRepository;

class AgentCommissionController extends Controller
{
    private $repository;

    /**
     * @param AgentCommissionRepository $repository
     */
    public function __construct(AgentCommissionRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getAgentDateSpecificCommission(AgentDateSpecificCommissionRequest $request)
    {
        $agent_id = $request->agentId;
        $year = $request->year;
        $month = $request->month;
        $user_id = isset($request->userid) ? $request->userid : '';
        if($agent_id == 605 && $user_id == config('commission.user_id')){
            $response = [];
        }else{
            $response = $this->repository->formatAgentYearlyCommissionSummaryForMobile($agent_id, $year, $month);
        }

        return response()->json(
            new SuccessResource([
                'statusCode' => 200,
                'data' => $response
            ]),
            200
        );
    }
}
