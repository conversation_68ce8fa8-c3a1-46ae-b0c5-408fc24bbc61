<?php

namespace App\Http\Controllers\Api\V2\Agent;

use App\Http\Controllers\Controller;
use App\Http\Requests\ManageAgent\SendContractEmailRequest;
use App\Repositories\Agents\v2\ManageAgents;
use Illuminate\Http\Request;

class ManageAgentController extends Controller
{
    private $manageAgentsModel;

    public function __construct(ManageAgents $manageAgentsModel)
    {
        $this->manageAgentsModel = $manageAgentsModel;
    }
    //
    public function saveContractLevels(SendContractEmailRequest $request){
        return $this->manageAgentsModel->saveContractLevels($request);
    }

    public function contractActionsNew($agentId)
    {
        return $this->manageAgentsModel->getContractActionsNew($agentId);
    }

    public function contractLevelsNew($agentId){
        return $this->manageAgentsModel->contractLevelsNew($agentId);
    }

    public function contractLevelsNewAdmin($agentId){
        return $this->manageAgentsModel->contractLevelsNewAdmin($agentId);
    }
}
