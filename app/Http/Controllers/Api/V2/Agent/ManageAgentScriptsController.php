<?php

namespace App\Http\Controllers\Api\V2\Agent;

use App\Http\Controllers\Controller;
use App\Repositories\Agents\ManageAgentScriptsRepository;
use Illuminate\Http\Request;

class ManageAgentScriptsController extends Controller
{
    protected $manageAgentScriptsRepository;

    public function __construct(ManageAgentScriptsRepository $manageAgentScriptsRepository)
    {
        $this->manageAgentScriptsRepository = $manageAgentScriptsRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $filters = $request->query->all();
        return $this->manageAgentScriptsRepository->index($filters);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        return $this->manageAgentScriptsRepository->store($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\manageAgentScriptsRepository  $manageAgentScriptsRepository
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return $this->manageAgentScriptsRepository->show($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\manageAgentScriptsRepository  $manageAgentScriptsRepository
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        return $this->manageAgentScriptsRepository->update($id, $request->all());
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\manageAgentScriptsRepository  $manageAgentScriptsRepository
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return $this->manageAgentScriptsRepository->delete($id);
    }

    public function getAgentDownlinesListFromAgent($id)
    {
        return $this->manageAgentScriptsRepository->getAgentDownlinesListFromAgent($id);
    }

    public function getAgentList(Request $request)
    {
        return $this->manageAgentScriptsRepository->getAgentList($request);
    }

    public function addManageAgentScriptsToAgents(Request $request)
    {
        return $this->manageAgentScriptsRepository->addManageAgentScriptsToAgents($request->all());
    }
}
