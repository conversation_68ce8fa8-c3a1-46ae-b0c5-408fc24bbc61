<?php

namespace App\Http\Controllers\Api\V2\Agent;

use App\Http\Controllers\Controller;
use App\Http\Requests\ManageAgent\AddAgentAddressRequest;
use App\Http\Requests\ManageAgent\DeleteAgentAddressRequest;
use App\Http\Requests\ManageAgent\SetAgentPrimaryAddressRequest;
use App\Http\Requests\ManageGroup\DeleteGroupAddressRequest;
use App\Repositories\Agents\AgentAddressRepository;
use Illuminate\Http\Request;

class AgentAddressController extends Controller
{
    /**
     * @var AgentAddressRepository
     */
    private $repository;

    /**
     * AgentAddressController constructor.
     * @param AgentAddressRepository $repository
     */
    public function __construct(AgentAddressRepository $repository)
    {
        $this->repository = $repository;
    }

    public function agentAddressList($agentId)
    {
        return $this->repository->getAddressByAgentId($agentId);
    }

    public function create(AddAgentAddressRequest $request){
        return $this->repository->createAgentAddress($request->validated());
    }
    public function setPrimaryAgentAddress(SetAgentPrimaryAddressRequest $request){
        return $this->repository->setPrimaryAgentAddress($request->validated());
    }

    public function deleteAgentAddress(DeleteAgentAddressRequest $request){
        return $this->repository->deleteAgentAddress($request->validated());
    }
}
