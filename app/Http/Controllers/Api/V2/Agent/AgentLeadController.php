<?php

namespace App\Http\Controllers\Api\V2\Agent;

use App\AgentLead;
use App\Http\Controllers\Controller;
use App\Http\Requests\Agent\AgentLeadRequest;
use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use App\Service\MessageService;
use App\Traits\Paginator;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class AgentLeadController extends Controller
{
    use Paginator;

    /**
     * Display a listing of the Leads Details.
     *
     * @query agent - fuzzy search by agent_fullname | agent_code
     * @query device - fuzzy search by device_id
     * @query limit - result per_page
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $leads_collection =
            AgentLead::with([
                'question:id,question,alias',
                'agent:agent_id,agent_code,agent_fname,agent_mname,agent_lname'
            ])
                ->select([
                    'id', 'question_id', 'agent_id', 'device_id',
                    'answer', 'web_url', 'created_at', 'updated_at'
                ])
                ->orderByDesc('updated_at')
                ->get();

        if (request()->has('search')) {
            $leads_collection = $leads_collection
                ->filter(function ($each_lead, $value) {
                    if (!empty($each_lead['agent'])) {
                        if ( preg_match('/' . request('search') . '/i', $each_lead->agent->fullname)
                            || preg_match('/' . request('search') . '/i', $each_lead['agent']['agent_code']) ) {
                            return $each_lead;
                        }
                    }
                    if ( preg_match('/' . request('search') . '/i', $each_lead['device_id']) ) {
                        return $each_lead;
                    }
            });
        }

        $leads_paginated = $this->paginateCollection(
            $leads_collection,
            request('limit') ?? 15
        );

        $leads_mapped = $this->mapLeadData($leads_paginated);

        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => empty($leads_mapped)
                    ? 'Leads not found.'
                    : 'Leads successfully fetched.',
                'data' => [
                    'data' => $leads_mapped,
                    'links' => $this->links($leads_paginated),
                    'meta' => $this->meta($leads_paginated)
                ]
            ]), Response::HTTP_OK
        );
    }

    /**
     * Create a new Lead entry.
     *
     * @param AgentLeadRequest $request
     * @return JsonResponse
     */
    public function create(AgentLeadRequest $request): JsonResponse
    {
        try {
            $request_data = $request->validated();
            $agent_lead = AgentLead::create($request_data);

            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Leads successfully fetched.',
                    'data' => $agent_lead
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {

            return response()->json(
                new ErrorResource([
                    'statusCode' => $exception->getCode(),
                    'error' => $exception->getMessage(),
                    'message' => 'Failed to create new Agent Lead entry.'
                ]), $exception->getCode()
            );
        }
    }

    /**
     * Link Lead to an Agent
     *
     * @query lead_id
     * @query agent_id
     * @param AgentLeadRequest $request
     * @return JsonResponse
     */
    public function update(AgentLeadRequest $request): JsonResponse
    {
        try {
            $lead_id = $request->validated()['lead_id'];
            $agent_id = $request->validated()['agent_id'];

            $agent_lead = AgentLead::find($lead_id);
            $agent_lead->agent_id = $agent_id;
            $send_email = $agent_lead->isDirty();
            $agent_lead->updated_at = Carbon::now()->timezone('EST')->format('Y-m-d H:i:s');
            $agent_lead->save();

            if ($send_email) {
                $agent_name = $agent_lead->agent->full_name;
                $agent_email = $agent_lead->agent->agent_email;
                $messageService = new MessageService();
                $template_body = "Dear $agent_name,<br/>";
                $template_body .= "<strong>This is to inform you that a new Lead has been assigned to your account.</strong><br>";

                $toAddress = config("testemail.TEST_EMAIL") ?: $agent_email;
                $post = [
                    'email_message_configuration_name' => "NUERA_API_GENERIC",
                    'toAddress' => $toAddress,
                    'subject' => "New Lead Assigned",
                    'generalTemplateData' => [],
                    'contentTemplate' => $template_body
                ];
                $messageService->sendEmailWithContentTemplate($post);
            }

            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => ($send_email)
                        ? 'Agent and Lead successfully linked. Agent notified via Email.'
                        : 'Agent and Lead successfully linked.',
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {

            return response()->json(
                new ErrorResource([
                    'statusCode' => $exception->getCode(),
                    'error' => $exception->getMessage(),
                    'message' => 'Failed to unlink Agent from Lead.'
                ]), $exception->getCode()
            );
        }
    }

    /**
     * Unlink Agent from a Lead.
     *
     * @query lead_id
     * @param AgentLeadRequest $request
     * @return void
     */
    public function destroy(AgentLeadRequest $request): JsonResponse
    {
        try {
            $lead_id = $request->validated()['lead_id'];
            $agent_lead = AgentLead::find($lead_id);
            $agent_lead->agent_id = null;
            $agent_lead->updated_at = Carbon::now()->timezone('EST')->format('Y-m-d H:i:s');
            $agent_lead->save();

            return response()->json(
                new SuccessResource([
                    'statusCode' => Response::HTTP_OK,
                    'message' => 'Agent and Lead successfully unlinked.',
                ]), Response::HTTP_OK
            );
        } catch (\Exception $exception) {

            return response()->json(
                new ErrorResource([
                    'statusCode' => $exception->getCode(),
                    'error' => $exception->getMessage(),
                    'message' => 'Failed to unlink Agent from Lead.'
                ]), $exception->getCode()
            );
        }
    }

    /**
     * Format the lead collection.
     *
     * @param $collection
     * @return array
     */
    private function mapLeadData($collection): array
    {
        return $collection->isNotEmpty()
            ? $collection->map(function ($each_lead) {
                return [
                    'id' => $each_lead['id'],
                    'agent' => isset($each_lead['agent'])
                        ? [
                            'id' => $each_lead['agent_id'],
                            'code' => $each_lead['agent']['agent_code'],
                            'name' => $each_lead->agent->fullname
                        ]
                        : null,
                    'question' => [
                        'id' => $each_lead['question_id'],
                        'alias' => $each_lead['question']['alias'],
                        'question' => $each_lead['question']['question']
                    ],
                    'device_id' => $each_lead['device_id'] ?? null,
                    'answer' => $each_lead['answer'] ?? null,
                    'url' => isset($each_lead['web_url']) ? 'https://' . $each_lead['web_url'] : null,
                    'created_at' => Carbon::parse($each_lead['created_at'])->format('m-d-Y H:i:s'),
                    'updated_at' => Carbon::parse($each_lead['updated_at'])->format('m-d-Y H:i:s')
                ];
            })->all()
            : [];
    }
}
