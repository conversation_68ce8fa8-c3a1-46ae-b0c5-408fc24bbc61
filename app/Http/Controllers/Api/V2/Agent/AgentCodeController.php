<?php

namespace App\Http\Controllers\Api\V2\Agent;

use App\AgentInfo;
use App\AgentUpdate;
use App\Helpers\AgentUpdatesHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Agent\AgentCodeRequest;
use App\Http\Resources\ErrorResource;
use App\Http\Resources\SuccessResource;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class AgentCodeController extends Controller
{
    private $agentEligibilityLog;

    public function __construct(AgentUpdatesHelper $agentEligibilityLog)
    {
        $this->agentEligibilityLog = $agentEligibilityLog;
    }

    public function update(AgentCodeRequest $request): JsonResponse
    {
        DB::beginTransaction();

        $validatedRequest = $request->validated();
        $agentId = $validatedRequest['agent_id'];
        $agentCode = $validatedRequest['agent_code'];

        try {
            $agent = AgentInfo::find($agentId);
            $oldAgentCode = $agent->agent_code;
            $agent->update(['agent_code' => $agentCode]);
            DB::commit();

            $this->agentEligibilityLog
                ->createAgentUpdateLog([
                    'agent_id' => $agentId,
                    'elgb_act' => AgentUpdate::ACT_PERSONAL_INFO,
                    'elgb_act_date' => time(),
                    'elgb_comment' => "Agent Code Updated. Old: {$oldAgentCode}. New: {$agentCode}",
                    'elgb_agent_id' => request()->header('id'),
                    'elgb_agent_name' => request()->header('name'),
                    'changed_from' => $oldAgentCode,
                    'changed_to' => $agentCode
                ]);
        }
        catch (Exception $exception) {
            DB::rollBack();

            $errorCode = in_array($exception->getCode(), Response::$statusTexts)
                ? $exception->getCode()
                : Response::HTTP_UNPROCESSABLE_ENTITY;
            return response()->json(
                new ErrorResource([
                    'statusCode' => $errorCode,
                    'error' => $exception->getMessage(),
                    'message' => 'Failed to update Agent Code.'
                ]), $errorCode
            );
        }

        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Agent Code updated.',
            ]), Response::HTTP_OK
        );
    }

    public function isUnique(AgentCodeRequest $request): JsonResponse
    {
        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Agent Code Unique.',
            ]), Response::HTTP_OK
        );
    }

    public function hasConflict(AgentCodeRequest $request): JsonResponse
    {
        return response()->json(
            new SuccessResource([
                'statusCode' => Response::HTTP_OK,
                'message' => 'Agent Code does not conflict with others.',
            ]), Response::HTTP_OK
        );
    }
}
