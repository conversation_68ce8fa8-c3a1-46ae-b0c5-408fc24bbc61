<?php

namespace App\Http\Controllers\Api\V2\Agent;

use App\Http\Controllers\Controller;
use App\Http\Requests\AgentAchPayment\AddAchPaymentRequest;
use App\Http\Requests\AgentAchPayment\DeleteAchPaymentRequest;
use App\Http\Requests\AgentAchPayment\SetPrimaryAchPaymentRequest;
use App\Http\Requests\AgentAchPayment\UpdatePaymentMethodWithAchRequest;
use App\Repositories\Agents\AgentAchRepository;
use Illuminate\Http\Request;

class AchPaymentController extends Controller
{
    /**
     * @var AgentAchRepository
     */
    private $repository;

    /**
     * AchPaymentController constructor.
     * @param AgentAchRepository $repository
     */
    public function __construct(AgentAchRepository $repository)
    {
        $this->repository = $repository;
    }

    public function agentAchPayments($agentId){
        return $this->repository->getAgentAchPayments($agentId);
    }

    public function create(AddAchPaymentRequest $request){
        return $this->repository->createAchPayment($request->validated());
    }
    public function setPrimaryAchPayment(SetPrimaryAchPaymentRequest $request){
        return $this->repository->setPrimaryAchPayment($request->validated());
    }

    public function deleteAchPayment(DeleteAchPaymentRequest $request){
        return $this->repository->deleteAchPayment($request->validated());
    }

    public function updatePaymentWithAch(UpdatePaymentMethodWithAchRequest $request){
        return $this->repository->updatePaymentMethodWithAchPayment($request->validated());
    }
}
