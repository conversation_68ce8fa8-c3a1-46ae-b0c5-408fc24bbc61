<?php

namespace App\Http\Controllers\Api\V2\HomeConfig;

use App\Http\Controllers\Controller;
use App\Repositories\HomepageConfigurationRepository;
use Illuminate\Http\Request;

class HomepageConfigurationController extends Controller
{
    //

    protected $repository;

    public function __construct(HomepageConfigurationRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getHomepageConfigurationWebsite(Request $request)
    {
        return $this->repository->getHomepageConfigurationWebsite($request);
    }

    public function getHomepageConfigurationPlanList(Request $request){
        return $this->repository->getHomepageConfigurationPlanList($request);
    }

    public function removeBulkHomepagePlans(Request $request){
        return $this->repository->removeBulkHomepagePlans($request);
    }
}
