<?php

namespace App\Http\Controllers\Api\ACM;

use App\AgentInfo;
use App\Http\Controllers\Controller;
use App\Http\Requests\ACM\OnboardingNueraRequest;
use App\Http\Resources\ACM\DashboardResource;
use App\Http\Resources\SuccessResource;
use App\Repositories\Agents\ManageAgents;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
        /**
     * @var ManageAgents
     */
    private $manageAgentsModel;
    public function __construct()
    {
        $this->manageAgentsModel = new ManageAgents();
    }
    public function getSyncNeuraAcm(Request $request){
        $agent = AgentInfo::where('agent_email',$request->email)->first();
        $agent->path = $agent->getFullImageAttribute();
        return response()->json(
            new SuccessResource([
                'statusCode' => 200,
                'data' => new DashboardResource($agent),
                'message' => 'Neura dashboard data fetched successfully.',
            ]),
            200
        );
    }

    public function onboardingSyncNeuraAcm(OnboardingNueraRequest $request){
        return $this->manageAgentsModel->onboardingSyncNeuraAcm($request);
    }
}
