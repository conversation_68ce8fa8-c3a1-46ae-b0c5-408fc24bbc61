<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\BundelPlan;
use App\DependentInPolicy;
use App\Helpers\PlanHelper;
use App\Service\PrudentialPlanPricingService;
use Carbon\Carbon;

class PlanOverview extends Model
{
    protected $table = 'plan_overview';
    protected $primaryKey = null;
    public $incrementing = false;
    public $timestamps = false;

    protected $appends = ['formatted_price_male_nons'];

    protected $attributes = ['plan_new_price'];
    static $CC = 'cc';

    const PLAN_OVERVIEW_SMMR_CID = 78;
    const PLAN_CARRIER_ID = 80;
    const PLAN_PHCS_IHA_CARRIER_ID = 77;
    const EXTRA_HEALTH_CARRIER_ID = 92;
    const EXTRA_HEALTH_ADDED_PLAN_IDS = [1894,1895];

    public function userInfo()
    {
        return $this->hasOne(UserInfo::class, 'userid', 'userid');
    }

    public function policy()
    {
        return $this->belongsTo(Policy::class, 'policy_id');
    }

    public function planPolicy()
    {
        return $this->belongsTo(PlanPolicy::class, 'p_ai');
    }

    public function getEnrollmentDateAttribute()
    {
        return date('Y-m-d', $this->edate);
    }

    public function getFormattedPriceMaleNonsAttribute(){
        return number_format((float)$this->price_male_nons, 2, '.', '');
    }

    public function userInfoPolicyAddress()
    {
        return $this->hasOne(UserInfoPolicyAddress::class, 'userid', 'policy_userid');
    }

    public function signature(){
        return $this->hasOne(Signature::class,'pid','policy_id');
    }

    public function getTierDetails()
    {
        switch ($this->tier) {
            case 'IO':
                return 'Member';
            case 'IS':
                return 'Member + Spouse';
            case 'IC2':
            case 'IC':
                return 'Member + Child(ren)';
            case 'IF':
                return 'Family';
        }
    }

    public function getIsAssociationFeeAttribute(): bool
    {
        return $this->attributes['is_assoc'] == 1
            || in_array($this->attributes['pid'], [886, 887, 888, 890]);
    }

    public function bundledPlan(){
        return $this->hasOne(BundelPlan::class, 'pid', 'pid');
    }

    public function hasExtraHelthPlans()
    {
        return $this->hasMany(Plan::class, 'carrier', 'cid')
        ->where(function ($q) {
            $q->where('carrier', self::EXTRA_HEALTH_CARRIER_ID)
            ->orWhereIn('pid', self::EXTRA_HEALTH_ADDED_PLAN_IDS);
        });
    }

    public function ihaDashboardStatus(){
        return $this->hasOne(VwIhaMemberStatusInfo::class,'p_ai','p_ai');
    }


    public function getPlanNewPriceAttribute()
    {
      //return (float)$this->price_male_nons;
     // if (!in_array($this->pid, Plan::PRUDENTIAL_PLAN) || !$this->user_dob) return $this->price_male_nons;
       // return PlanHelper::calculatePrudentialPlanPrice($this);
       if (!(PlanHelper::isHospitalOrCriticalPrudentialPlan($this->pid) || PlanHelper::isBundleHasPrudentailPlan($this->pid)) ) {
           return (float)$this->price_male_nons;
       }

        $dependentCount = 0;
        if(in_array($this->tier,['IF','IC'])) {
            $dependentCount = DependentInPolicy::where([
                'policy_id' => $this->policy_id,
                'userid' => $this->userid
            ])->count();
        }

        $spouseDob = DependentInPolicy::where('policy_id', $this->policy_id)
            ->where('d_relate','S')
            ->pluck('d_dob')
            ->first();

        try {
            $prudentialPlanPricingService = new PrudentialPlanPricingService(
                $this->tier,
                Carbon::parse($this->user_dob)->age,
                $dependentCount - (int)isset($spouseDob),
                Carbon::parse($spouseDob)->age
            );
            $planPrice = $prudentialPlanPricingService->calculatePlanPrice($this->pid);
            if ($this->payment_type == self::$CC)  {
               // return $planPrice +  (($planPrice * self::$CCFee) / 100);
            }
            return $planPrice;
        } catch(\Exception $e) {
            return 0.00;
        }

    }
}
