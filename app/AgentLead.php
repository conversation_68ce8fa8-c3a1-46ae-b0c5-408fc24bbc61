<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AgentLead extends Model
{
    protected $connection = 'mysql';
    protected $table = 'assistant_question_answer';
    protected $fillable = [
        'question_id', 'agent_id', 'device_id', 'answer'
    ];
    protected $casts = [
        'created_at' => 'datetime:m-d-Y H:i:s',
        'updated_at' => 'datetime:m-d-Y H:i:s'
    ];

    public function question()
    {
        return $this->belongsTo(LeadQuestion::class, 'question_id', 'id');
    }

    public function device_detail()
    {
        return $this->hasMany(LeadInfo::class, 'device_id', 'device_id');
    }

    public function agent()
    {
        return $this->belongsTo(AgentInfo::class, 'agent_id', 'agent_id');
    }
}
