<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GroupCensusReport extends Model
{
    protected $table = 'group_census_report';

    public function groupCensus()
    {
        return $this->belongsTo(GroupCensus::class, 'id');
    }
    public function groupCensusDependent()
    {
        return $this->hasMany(GroupCensusDependent::class, 'primary_member_id');
    }
    public function plan()
    {
        return $this->belongsTo(Plan::class, 'plan');
    }
}
