<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class TermRequest extends Model
{
    protected $table = 'term_request';

    protected $primaryKey = 'term_id';

    protected $guarded = [];

    public $timestamps = false;

    public const STATUS_APPROVE = 'APPROVED';
    public const STATUS_REJECTED = 'REJECTED';

    static $statuses = [
        self::STATUS_APPROVE => 'A',
        self::STATUS_REJECTED => 'R',
    ];


    public function policy()
    {
        return $this->belongsTo(Policy::class, 'policy_id');
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class, 'pid');
    }

    public function getSignatureUrlAttribute()
    {
        return "https://corenroll.com/bank_sign/" . $this->signature;
    }

    public function getbase64EncodedSignatureImageUrlAttribute(){
        return 'data:image/png;base64,'.base64_encode(file_get_contents($this->signature_url));
    }
}
