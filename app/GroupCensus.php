<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class GroupCensus extends Model
{
    protected $table = 'group_census';

    // get groupcencusreport
    public function groupCensusReports()
    {
        return $this->hasMany(GroupCensusReport::class, 'group_census_id');
    }
}
