<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentUplineScheduler extends Model
{
    use SoftDeletes;

    protected $table = 'agent_upline_scheduler';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    protected $casts = [
        'effective_date_start' => 'datetime:Y-m-d'
    ];

    public function agent()
    {
        return $this->hasOne(AgentInfo::class, 'agent_id', 'agent_id')->withTrashed();
    }

    public function upline()
    {
        return $this->hasOne(AgentInfo::class, 'agent_id', 'agent_ga')->withTrashed();
    }
}
