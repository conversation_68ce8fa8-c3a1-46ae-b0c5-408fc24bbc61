<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentCheckpayment extends Model
{
    use SoftDeletes;

    protected $table = 'agent_checkpayment';
    protected $primaryKey = 'checkpayment_id';
    protected $guarded = ['checkpayment_id'];
    protected $dates = ['deleted_at'];


    public function restore()
    {
      parent::restore();
    }

}
