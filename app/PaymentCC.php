<?php

namespace App;
use App\UserInfo;
use App\Policy;
use Illuminate\Database\Eloquent\Model;

class PaymentCC extends Model
{
    protected $table = 'creditcard_info';
    protected $primaryKey = 'creditcard_id';

    // get user from userinfo table
    public function getUser(){
        return $this->hasOne('App\UserInfo','userid','credit_userid');
    }

    // get policy information from policies table
    public function getPolicy(){
        return $this->hasOne('App\Policy','payment_id','creditcard_id');
    }
}
