<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class LeadQuestion extends Model
{
    protected $connection = 'mysql';
    protected $table = 'assistant_question';
    protected $fillable = [
        'question', 'category', 'order', 'type', 'alias', 'message'
    ];
    public $timestamps = false;

    protected $casts = [
        'message' => 'array'
    ];

    public function agent_lead()
    {
        return $this->hasMany(AgentLead::class, 'question_id', 'id');
    }
}
