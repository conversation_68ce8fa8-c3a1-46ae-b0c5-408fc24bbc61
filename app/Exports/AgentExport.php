<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class AgentExport implements FromView, WithEvents, ShouldAutoSize
{
    use Exportable, RegistersEventListeners;

    private $agents;

    /**
     * PoliciesExport constructor.
     * @param $agents
     */
    public function __construct($agents)
    {
        $this->agents = $agents;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        $data['agents'] = $this->agents;
        return view('excel.report.agent', $data);
    }


    public static function afterSheet(AfterSheet $event)
    {
        $headingStyle = [
            'font' => ['bold' => true, 'size' => 12],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];
        $event->sheet->getDelegate()->getColumnDimension('H')->setWidth(80)->setAutoSize(false);
        $spreadsheet = $event->sheet->getDelegate();
        // heading group of cells (A1 to f1)
        $spreadsheet->getStyle('A1:S1')->applyFromArray($headingStyle)->getAlignment()->setHorizontal('center');
        $spreadsheet->getStyle('A:S')->getAlignment()->setHorizontal('center');


    }

}