<?php

namespace App\Exports\Prudential;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class PrudentialPlanMonthlyExport implements FromView
{
    private $plans;

    /**
     * PrudentialPlanMonthlyExport constructor.
     * @param $plans
     */
    public function __construct($plans)
    {
        $this->plans = $plans;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        $data['header'] = $this->plans['header'];
        $data['body'] = $this->plans['body'];
        return view('excel.prudential.plans-export', $data);
    }
}
