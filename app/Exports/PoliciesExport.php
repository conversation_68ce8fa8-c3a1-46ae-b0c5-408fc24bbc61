<?php

namespace App\Exports;

use App\Policy;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class PoliciesExport implements FromView, WithEvents, ShouldAutoSize
{
    use Exportable, RegistersEventListeners;

    private $policies;

    /**
     * PoliciesExport constructor.
     * @param $policies
     */
    public function __construct($policies)
    {
        $this->policies = $policies;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        $data['policies'] = $this->policies;
        return view('excel.policies-export', $data);
    }


    public static function afterSheet(AfterSheet $event)
    {

        $headingStyle = [
            'font' => ['bold' => true, 'size' => 12],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];
        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];
        // Get Worksheet
        $spreadsheet = $event->sheet->getDelegate();
        foreach ($spreadsheet->getRowIterator() as $row) {
            $index = $row->getRowIndex();

            if ($index !== 1) {
                // Get the value from cell C
                $cellValue = $spreadsheet->getCellByColumnAndRow(3, $index)->getValue();
                $cellRange = 'A' . $index . ':W' . $index;

                $color = $cellValue % 2 == 0 ? 'CCCCCC' : 'd6e2ba';

                $spreadsheet->getStyle($cellRange)
                    ->applyFromArray($styleArray)
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setRGB($color);
            }
        }
        // heading group of cells (A1 to w1)
        $spreadsheet->getStyle('A1:W1')->applyFromArray($headingStyle)->getAlignment()->setHorizontal('center');
        $spreadsheet->getStyle('A:W')->getAlignment()->setHorizontal('center');


    }


    public static function getMedicals($depMedBex)
    {
        $medicals = [];
        if (isset($depMedBex)) {
            foreach ($depMedBex as $item) {
                $medAnswers = $item->getMedCondition;
                foreach ($medAnswers as $medAnswer) {
                    if ($item['question_id'] != 113) {
                        $medicals[] = [
                            'question_id' => $item['question_id'] ?? '',
                            'health_condition' => $medAnswer['health_condition'] ?? '',
                            'date_of_onset' => $medAnswer['date_of_onset'] ?? '',
                            'date_of_recovery' => $medAnswer['date_of_recovery'] ?? '',
                            'is_treatment' => isset($medAnswer['is_treatment']) ? ($medAnswer['is_treatment'] == 1) ? 'Yes' : 'No' : '',
                            'is_medicate' => isset($medAnswer['is_medicate']) ? ($medAnswer['is_medicate'] == 1) ? 'Yes' : 'No' : '',
                            'd_last_seen' => $medAnswer['d_last_seen'] ?? '',
                            'symptoms' => $medAnswer['symptoms'] ?? '',
                            'additional_notes' => $item['additional_notes']
                        ];
                    }
                }
            }
        }
        return $medicals;
    }

    public static function getMedications($getMedications)
    {
        $medications = [];
        if (isset($getMedications)) {
            foreach ($getMedications as $medication) {
                $medications[] = [
                    'medication' => $medication->medication ?? '',
                    'dosage' => $medication->dosage ?? '',
                    'medical_condition' => $medication->medical_condition ?? ''
                ];
            }
        }

        return $medications;
    }


    public static function finalResult($medicals, $medications)
    {
        $medConditionCount = count($medicals);
        $medicalInfosCount = count($medications);
        $finalResult = [];
        if ($medConditionCount >= $medicalInfosCount) {
            $counter = $medConditionCount;
        } else {
            $counter = $medicalInfosCount;
        }
        if ($counter == 0) {
            $counter = 0;
        } else {
            $counter = $counter - 1;
        }
        for ($i = 0; $i <= $counter; $i++) {
            $finalResult[] = [
                'question_id' => isset($medicals[0]['question_id']) ? $medicals[0]['question_id'] : '',
                'health_condition' => $medicals[$i]['health_condition'] ?? '',
                'date_of_onset' => isset($medicals[$i]['date_of_onset']) ? self::checkDate($medicals[$i]['date_of_onset']): '',
                'date_of_recovery' => isset($medicals[$i]['date_of_recovery']) ? self::checkDate($medicals[$i]['date_of_recovery']) : '',
                'is_treatment' => isset($medicals[$i]['is_treatment']) ? ($medicals[$i]['is_treatment'] == 1) ? 'Yes' : 'No' : '',
                'is_medicate' => isset($medicals[$i]['is_medicate']) ? ($medicals[$i]['is_medicate'] == 1) ? 'Yes' : 'No' : '',
                'd_last_seen' => isset($medicals[$i]['d_last_seen']) ? $medicals[$i]['d_last_seen'] : '',
                'symptoms' => isset($medicals[$i]['symptoms']) ? $medicals[$i]['symptoms'] : '',
                'medication' => isset($medications[$i]['medication']) ? $medications[$i]['medication'] : '',
                'dosage' => isset($medications[$i]['dosage']) ? $medications[$i]['dosage'] : '',
                'medical_condition' => isset($medications[$i]['medical_condition']) ? $medications[$i]['medical_condition'] : '',
                'additional_notes' => isset($medicals[$i]['additional_notes']) ? $medicals[$i]['additional_notes'] : '',
            ];
        }
        return $finalResult;
    }

    public static function checkDate($stringDate){
        try {
            $date = \Carbon\Carbon::parse($stringDate)->format('m/Y');
        }catch (\Throwable $th){
            $date = $stringDate ?? '';
        }
        return $date;
    }

}
