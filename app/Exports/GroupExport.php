<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class GroupExport implements FromView, WithEvents, ShouldAutoSize
{
    use Exportable, RegistersEventListeners;

    private $groups;

    /**
     * PoliciesExport constructor.
     * @param $groups
     */
    public function __construct($groups)
    {
        $this->groups = $groups;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        $data['groups'] = $this->groups;
        return view('excel.report.group', $data);
    }



    public static function afterSheet(AfterSheet $event)
    {

        $headingStyle = [
            'font' => ['bold' => true, 'size' => 12],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];

        // Get Worksheet
        $spreadsheet = $event->sheet->getDelegate();
        // heading group of cells (A1 to f1)
        $spreadsheet->getStyle('A1:U1')->applyFromArray($headingStyle)->getAlignment()->setHorizontal('center');
        $spreadsheet->getStyle('A:U')->getAlignment()->setHorizontal('center');

    }

}