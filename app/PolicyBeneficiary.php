<?php

namespace App;

use App\Helpers\DecryptEcryptHelper;
use Illuminate\Database\Eloquent\Model;

class PolicyBeneficiary extends Model
{
    protected $table = 'policy_benificiery';
    protected $primaryKey = 'ben_id';
    protected $fillable = ['bfname', 'blname', 'brelate', 'bdob', 'bssn', 'ben_percentage', 'bpolicy_id'];

    const RELATION_SPOUSE = 'Spouse';
    const RELATION_UNCLE_AUNT = 'Uncle or Aunt';
    const RELATION_NEPHEW_NIECE = 'Nephew or Niece';
    const RELATION_BROTHER_SISTER = 'Brother or Sister';
    const RELATION_CHILD = 'Child';
    const RELATION_EX_SPOUSE = 'Ex Spouse';
    const RELATION_MOTHER = 'Mother';
    const RELATION_FATHER = 'Father';
    const RELATION_LIFE_PARTNER = 'Life Partner';
    const RELATION_TRUST = 'Trust';
    const RELATION_FRIEND = 'Friend';
    const RELATION_OTHER = 'Other';

    static $beneficiaryRelations = [
        self::RELATION_SPOUSE,
        self::RELATION_UNCLE_AUNT,
        self::RELATION_NEPHEW_NIECE,
        self::RELATION_BROTHER_SISTER,
        self::RELATION_CHILD,
        self::RELATION_EX_SPOUSE,
        self::RELATION_MOTHER,
        self::RELATION_FATHER,
        self::RELATION_LIFE_PARTNER,
        self::RELATION_TRUST,
        self::RELATION_FRIEND,
        self::RELATION_OTHER,
    ];

    /*
     * join with beneficiary table based on ssn and dob
     */
    public function scopeBeneficiary($query)
    {
        return $query->join('beneficiaries', function($join)
        {
            $join->on('beneficiaries.ssn', '=', 'policy_benificiery.bssn')
                ->on('beneficiaries.dob', '=', 'policy_benificiery.bdob');
        });
    }

    public function getBSsnAttribute(): string
    {
        return DecryptEcryptHelper::decryptInfo($this->attributes['bssn']);
    }

    public function policy(){
        return $this->belongsTo(Policy::class,'bpolicy_id','policy_id');
    }

    public function getFullNameAttribute()
    {
        $firstName = $this->bfname ? ucwords($this->bfname) : '';
        $lastName = $this->blname ? ucwords($this->blname) : '';
        return $firstName . ' ' . $lastName;
    }
}
