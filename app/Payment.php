<?php

namespace App;

//This class is to store payment related static values.
//If any better place to move these value, please do so.
class Payment
{
    const PAYMENT_TYPE_EFT = "eft";
    const PAYMENT_TYPE_STMT = "stmt";
    const PAYMENT_TYPE_list = "list";
    const PAYMENT_TYPE_CC = "cc";
    const PAYMENT_TYPE_CHCK = "chck";
    const PAYMENT_TYPE_ELIST = "elist";

    const PAYMENT_STATUS_UNPAID = "UNPAID";
    const PAYMENT_STATUS_PAID = "PAID";
    const PAYMENT_STATUS_PARTIAL = "PARTIAL";

    static $paymentTypes = [
        self::PAYMENT_TYPE_EFT,
        self::PAYMENT_TYPE_STMT,
        self::PAYMENT_TYPE_list,
        self::PAYMENT_TYPE_CC,
        self::PAYMENT_TYPE_CHCK,
        self::PAYMENT_TYPE_ELIST,
    ];

    static $paymentStatuses = [
        self::PAYMENT_STATUS_UNPAID,
        self::PAYMENT_STATUS_PAID,
        self::PAYMENT_STATUS_PARTIAL,
    ];

    static $platforms = [
        "enaeasychoice.com",
        "nuerabenefits.com",
        "nueradentalplans.com",
        "nueramedical.com",
        "enamembership.com",
        "www.enamembership.com",
        "www.idilustotalsolution.com",
        "enabenecare.com",
        "vebaenroll.com",
        "www.vebaenroll.com",
        "chamberhealthplans.com",
        "mec99.com",
        "eliteenroll.com",
        "www.mec99.com",
        "tdahealthplans.com",
        "www.eliteenroll.com",
        "www.tdahealthplans.com",
        "brokerexchanges.com",
        "echelonhealthplans.com",
        "corenroll.com",
        "goenroll123.com",
        "premierenroll.com",
        "mhtmedicalplans.com",
        "ifphealthplans.com",
        "fpmedicalplans.com",
        "goenrolldisability.com",
        "goenrolldental.com",
        "goenrolllife.com",
        "mybenefitbuilder.com",
        "teletherapy123.com",
        "goenrollgap.com",
        "enroll.purenroll.com"
    ];
}
