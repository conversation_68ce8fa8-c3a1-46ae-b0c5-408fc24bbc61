<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class InvoiceHistory extends Model
{
    protected $table = 'nb_invoice_history';

    protected $fillable = [
        'nb_invoice_history_id',
        'invoice_id',
        'invoice_type',
        'payment_type',
        'invoice_policy_id',
        'payment_method',
        'invoice_date',
        'invoice_due_date',
        'invoice_start_date',
        'invoice_end_date',
        'invoice_efee',
        'invoice_status',
        'invoice_payment_status',
        'invoice_total',
        'invoice_late_fee',
        'invoice_bounce_fee',
        'invoice_stmt_fee',
        'invoice_user_id',
        'invoice_prev_due',
        'invoice_due_amount',
        'invoice_due_amount_paid',
        'invoice_agent_id',
        'invoice_group_id',
        'ena_bill2',
        'invoice_created_user_id',
        'invoice_create_dttm',
        'security_token',
        'dispute_fl',
        'dispute_notes',
        'prev_dispute_fl',
        'failed_fl',
        'failed_at',
        'payment_party',
        'payment_party_status',
        'payment_party_processing_date',
        'payment_processing_count',
        'processing_amount',
        'payment_id',
        'processing_date',
        'clearing_date',
        'bank_id',
        'payer_id',
        'card_id',
        'comments',
        'comments_by',
        'bank_payment_type',
        'bank_payment_id',
        'onetime_fl',
        'onetime_type',
        'updated_at',
        'created_at',
        'is_paystand_processed',
        'group_payment_notes',
        'group_payment_status',
        'invoice_notes',
        'is_recurring_self_payment',
        'bulk_payment_notification_email_send',
        'stmt_payment_notification_email_send',
        'ccfees',
        'email_log_id',
        'merchant',
        'pay_type',
        'updated_by',
        'lump_sum_pay_fl',
    ];
    
}
