<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class RepInfoRequest extends Model
{
    protected $table = 'rep_info_requests';
    protected $guarded = [];

    const UPLOAD_DIR= "/rep_request_doc";

    const TYPE_INFO = 'info';
    const TYPE_TIER = 'tier';
    const TYPE_PLAN = 'plan';
    const TYPE_DEPENDENT = 'dependent';
    const TYPE_DOCUMENT = 'document';
    const TYPE_BILLING = 'billing';
    const TYPE_TERMINATION = 'termination';
    const TYPE_PAPER_ENROLLMENT = 'paper_enrollment';
    const TYPE_REPRESENTATIVE = 'representative';
    const TYPE_GROUP = 'group';
    const TYPE_OTHER = 'other';
    const TYPE_MEMBER_CARD = 'member_card';

    static $types = [
        'Information Update' => self::TYPE_INFO,
        'Plan' => self::TYPE_PLAN,
        'Tier' => self::TYPE_TIER,
        'Dependent' => self::TYPE_DEPENDENT,
        'Document' => self::TYPE_DOCUMENT,
        'Billing' => self::TYPE_BILLING,
        'Termination' => self::TYPE_TERMINATION,
        'Paper Enrollment' => self::TYPE_PAPER_ENROLLMENT,
        'Representative' => self::TYPE_REPRESENTATIVE,
        'Group' => self::TYPE_GROUP,
        'Other' => self::TYPE_OTHER,
        'Member Card' => self::TYPE_MEMBER_CARD,
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELED = 'canceled';
    const STATUS_REJECTED = 'rejected';

    static $statuses = [
        'Pending' => self::STATUS_PENDING,
        'In Progress' => self::STATUS_IN_PROGRESS,
        'Completed' => self::STATUS_COMPLETED,
        'Canceled' => self::STATUS_CANCELED,
        'Rejected' => self::STATUS_REJECTED,
    ];

    public function userInfo()
    {
        return $this->belongsTo(UserInfo::class,'userid');
    }

    public function policy()
    {
        return $this->belongsTo(Policy::class,'policy_id');
    }

    public function agentInfo(){
        return $this->belongsTo(AgentInfo::class,'agent_id');
    }

    public function getTypeNameAttribute(){
        return array_search($this->type, self::$types);
    }

    public function getStatusNameAttribute(){
        return array_search($this->status, self::$statuses);
    }

    public function getDocumentUrlAttribute()
    {
        if (isset($this->document) and $this->document != '') {
            return config('filesystems.disks.s3-third.url') . self::UPLOAD_DIR . "/{$this->document}";
        } else {
            return null;
        }
    }

    public function repInfoPaymentDetails()
    {
        return $this->hasMany(RepInfoRequestsDetail::class,'info_request_id','id');
    }
}
