<?php

namespace App\Helper;

use App\EmailCCLogs;
use App\EmailDomain;
use Illuminate\Support\Facades\Auth;

class EmailCCHelper
{
    /**
     * Fetch CC emails based on the email template (configuration).
     *
     * @param string $template
     * @return array
     */
    public static function fetchCcEmails(string $template, ?string $context = null, $passedAgentId = null): array
    {
        // Check if a user is authenticated
        $loggedInUser = Auth::user();

        // Determine the agent ID to use: prioritize logged-in user, fallback to passed agent ID
        $agentID = $loggedInUser && $loggedInUser->agentInfo()
            ? $loggedInUser->agentInfo()->agent_id
            : $passedAgentId;

        if (!$agentID) {
            return []; // Return an empty array if no valid agent ID is available
        }

        // Build query based on the presence of context (Usage of RAW_TEMPLATE config for different Email Categories)
        $query = EmailDomain::whereJsonContains('email_configurations', $template);
        if ($context) {
            $query->where('email_category', $context);
        }

        // Fetch the email domain ID
        $emailDomainId = $query->value('id');

        // Handle the case where the context or template is invalid
        if (!$emailDomainId) {
            return []; // Return an empty array if the email domain ID is not found
        }

        // Fetch enabled CC emails for this email domain ID
        return EmailCCLogs::where('rep_id', $agentID)
            ->where('email_domain_id', $emailDomainId) // Updated to use email_domain_id
            ->where('cc_status', 1)
            ->pluck('rep_admin_email')
            ->filter(function ($email) {
                return filter_var($email, FILTER_VALIDATE_EMAIL); // Validate email format
            })
            ->toArray();
    }
}
