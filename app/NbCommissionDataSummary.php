<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class NbCommissionDataSummary extends Model
{
    protected $connection = 'mysql2';
    protected $table = 'nb_commission_data_summary';
    protected $primaryKey = 'id';

    public static $commission_category = [
        'direct', 'downline', 'special', 'association', 'onetime', 'override', 'affiliate', 'enrollercode'
    ];

    public static $commission_attachment_category = [
        'summary', 'special', 'association', 'onetime', 'affiliates'
    ];

    public static $commission_to_attachment_map = [
        'direct'        => 'summary', 'downline'    => 'summary', 'special'       => 'special',
        'association'   => 'association', 'onetime' => 'onetime', 'affiliate'   => 'affiliates'
    ];

    public function scopeOfYear($query, $year)
    {
        return $query->where('paid_through_date', 'LIKE', "{$year}%");
    }

    public function scopeOfMonth($query, $month)
    {
        return $query->where('paid_through_date', 'LIKE', "%-{$month}-%");
    }
}
