<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AgentChequeAddress extends Model
{
    use SoftDeletes;

    protected $table = 'agent_checkpayment_address';
    protected $primaryKey = 'id';
    protected $appends = ['type'];
    protected $guarded = ['id'];
    protected $dates = ['deleted_at'];

    const AGENT_ADDRESS_TYPE_PAYMENT = 'payment';

    public function getTypeAttribute()
    {
        return self::AGENT_ADDRESS_TYPE_PAYMENT;
    }

    public function restore()
    {
       parent::restore();
    }
}
