<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AgentUpdate extends Model
{
    //
    protected $table = 'agent_updates';
    protected $primaryKey = 'elgb_id';
    protected $guarded = ['elgb_id'];

    const ACT_PERSONAL_INFO = "PICHG";
    const ACT_BANKINGS_INFO = "BICHG";
    const ACT_ADDRESS_INFO = "ADCHG";
    const ACT_UPLINE = "UPCHG";
    const ACT_ADD_GROUP = "GPCHG";
    const ACT_IMAGE = "PICHG";
    const ACT_BUSINESS_INFO = "BINCHG";
    const AGENT_LICENSE = "AGTLC";
    const AGENT_DISPLAY_SETTING = "ADSCHG";
    const AGENT_NOTES_UPDATES = "ANCHG";
    const AGENT_NOTES_DELETE = "ANDLT";
    CONST EMAIL_UPDATE = "EMCHG";
}
