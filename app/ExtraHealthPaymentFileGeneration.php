<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ExtraHealthPaymentFileGeneration extends Model
{

    protected $table = 'extra_health_payment_file_generations';
    protected $primaryKey = 'id';
    protected $fillable = ['file_path', 'file_name', 'file_generation_started', 'file_generation_ended', 'no_of_record', 'added_by', 'added_by_id'];


    public static function fetchPaymentReport($id)
    {
        return self::changeLink(ExtraHealthPaymentFileGeneration::query()->where('added_by_id', $id)->orderBy('id', 'DESC')->paginate(10));
    }


    public static function changeLink($data)
    {
        $data->map(function ($data) {
            if ($data['file_path'] != null && $data['file_name'] != null) {
                $data['file_download_link'] = config('filesystems.disks.s3-third.url') . '/' . $data['file_path'] . $data['file_name'];
            }
        });
        return $data;
    }
}
