<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PolicyUpdate extends Model
{
    protected $table = 'policy_updates';
    protected $primaryKey = 'elgb_id';
    protected $guarded = [];

    CONST POLICY_NOTE_UPDATE = 'MNCHG';
    CONST POLICY_NOTE_DELETE = 'MNDLT';
    CONST REQUESTED_PLAN_APPROVE = 'RPA';
    CONST REQUESTED_PLAN_REJECT = 'RPR';
    CONST POLICY_AGENT_CHANGE = 'agc';
}
