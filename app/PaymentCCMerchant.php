<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PaymentCCMerchant extends Model
{
    protected $table = 'payment_cc_merchant';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
//    protected $with = ['creditCard:cc_id,cc_contactfname,cc_contactlname,cc_type,cc_status,cc_expmm,cc_expyyyy,cc_userid,cc_addressid'];

    protected $casts = [
        'created_at' => 'datetime:m/d/Y H:i:s',
        'updated_at' => 'datetime:m/d/Y H:i:s',
        'verified_at' => 'datetime:m/d/Y H:i:s'
    ];

    public function creditCard()
    {
        $this->belongsTo(PaymentTypeCc::class, 'cc_id', 'cc_id');
    }
}
