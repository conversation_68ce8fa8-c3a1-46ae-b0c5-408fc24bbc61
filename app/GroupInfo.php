<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\GroupUpdate;

class GroupInfo extends Model
{
    protected $table = 'group_info';
    protected $primaryKey = 'gid';
    protected $guarded = ['gid'];
    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 0;
    public const STATUS_TERMED = 2;
    public const GROUP_TYPE_EMPLOYER = 'employer';

    static $statuses = [
        'Active' => self::STATUS_ACTIVE,
        'Inactive' => self::STATUS_INACTIVE,
        'Termed' => self::STATUS_TERMED
    ];

    const PAYMENT_TYPE_EFT = 'geft';
    const PAYMENT_TYPE_CC = 'gcc';
    const PAYMENT_TYPE_LIST = 'glist';
    const PAYMENT_TYPE_STMT = 'gstmt';

    static $paymentTypes = [
        self::PAYMENT_TYPE_EFT,
        self::PAYMENT_TYPE_CC,
        self::PAYMENT_TYPE_LIST,
        self::PAYMENT_TYPE_STMT,
    ];

    const MOBILE_APP_ANDROID = 'android';
    const MOBILE_APP_IOS = 'iOS';

    static $mobileAppDevices = [self::MOBILE_APP_ANDROID, self::MOBILE_APP_IOS];

    const ENROLLMENT_NOTIFICATION_GROUP_ID = [3275 => '<EMAIL>']; //enrollment receuipt email to be sent to these group having these ids


    public function memberCount()
    {
        return $this->hasMany('App\GroupTotals', 'eid', 'gid');
    }

    public function mainAgent()
    {
        return $this->hasOne('App\AgentInfo', 'agent_id', 'gagent_code');
    }

    public function groupAgents()
    {
        return $this->hasMany('App\AgentInGroup', 'gid', 'gid')->with('agentDetail');
    }

    public function getContactFullNameAttribute()
    {
        return ucwords($this->gcontact_fname . ' ' . $this->gcontact_lname);
    }

    public function userActivity()
    {
        return $this->hasOne(UserActivityDetail::class, 'user_id', 'gid')
            ->where([
                'user_type' => 'group',
                'action' => 'login'
            ]);
    }

    public function infoQuick()
    {
        return $this->belongsTo(GroupInfoQuick::class, 'gid', 'gid');
    }


    public function agents()
    {
        return $this->hasMany(AgentInGroup::class, 'gid', 'gid');
    }

    /**
     * @return string|null
     * currently fetching image from corenroll server
     * s3 only used to store image
     */
    public function getLogoUrlAttribute()
    {
        return $this->group_logo != "" ? "https://corenroll.com/biz_image.php?file=" . $this->group_logo : null;
    }

    public function groupUser()
    {
        return $this->hasOne(GroupUser::class, 'gid', 'gid')
            ->where('is_root', '=', 1);
    }

    public function generalAddress()
    {
        return $this->hasMany(GroupAddress::class, 'gid', 'gid');
    }

    public function billingAddress()
    {
        return $this->hasMany(GroupBillingAddress::class, 'gid', 'gid');
    }

    public function eftBanks()
    {
        return $this->hasMany(GroupEft::class, 'bank_gid', 'gid');
    }

    public function gidAssocFees()
    {
        return $this->hasMany(GidAssocFee::class, 'gid', 'gid');
    }

    public function groupPlanFees()
    {
        return $this->hasMany(GroupPlanFee::class, 'group_id', 'gid');
    }

    public function groupPlans()
    {
        return $this->hasMany(GroupPlans::class, 'group_id', 'gid');
    }

    public function members(): HasMany
    {
        return $this->hasMany(Policy::class, 'eid');
    }


    public static function boot()
    {
        parent::boot();
        
        static::updated(function ($model) {
            if (array_key_exists('gemail', $model->getDirty())) {
                \Log::channel('email_activity_log')->info('Group Info: gemail Updated' );
                $id = request()->header('id');
                $name = request()->header('name');
                \Log::channel('email_activity_log')->info('Request', [
                    'data' => request()->all(),
                    'headers' => request()->headers->all()
                ]);
                \Log::channel('email_activity_log')->info('Header User Data', [
                    'id' => $id,
                    'name' => $name,
                ]);

                $data = [
                    'group_id' => $model->gid,
                    'elgb_act' => GroupUpdate::EMAIL_UPDATE,
                    'elgb_comment' => 'Group Email updated from ' .$model->getOriginal('gemail') . ' to ' . $model->gemail,
                    'elgb_act_date' => time(),
                    'elgb_agent' => $id,
                    'elgb_agentname' => $name,
                    'changed_from' => $model->getOriginal('gemail'),
                    'changed_to' => $model->gemail 
                ];
                GroupUpdate::create($data);
                \Log::channel('email_activity_log')->info('GroupInfo:Email updated', $data);
            }
        });
    }

    public function groupPlansView()
    {
        return $this->hasMany(GroupPlansView::class, 'gid', 'gid');
    }
}
