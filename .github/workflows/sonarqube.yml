name: Sonar scan 
run-name: '[Weekly Scan - ${{ github.ref_name }}]'

on:
  schedule:
    - cron: '15 0 * * 1'  # Run every Monday at 6:00 AM NPT

jobs:
  sonarqube:
    name: sonarqube build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for better relevancy of analysis

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Cache SonarQube packages
        id: cache-sonarqube
        uses: actions/cache@v4
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      - name: SonarQubeScan
        uses: SonarSource/sonarqube-scan-action@v4
        if: steps.cache-sonarqube.outputs.cache-hit != 'true'
        env: 
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }} 
        with:
          args: >
            -Dsonar.projectKey=${{ secrets.SONAR_PROJECT_KEY }}
            -Dsonar.sources=.
            -Dsonar.test.exclusions=**/vendor/**,**/node_modules/**,**/dist/**,**/build/**            