{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2", "bruli/php-git-hooks": "^5.8", "fideloper/proxy": "^4.0", "guzzlehttp/guzzle": "7.1.1", "laravel/framework": "^6.2", "laravel/tinker": "^1.0", "league/flysystem-aws-s3-v3": "~1.0", "league/flysystem-sftp": "^1.0", "maatwebsite/excel": "^3.1"}, "require-dev": {"facade/ignition": "^1.4", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^7"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "bin-dir": "bin"}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-install-cmd": ["PhpGitHooks\\Infrastructure\\Composer\\ConfiguratorScript::buildConfig"], "post-update-cmd": ["PhpGitHooks\\Infrastructure\\Composer\\ConfiguratorScript::buildConfig"]}}