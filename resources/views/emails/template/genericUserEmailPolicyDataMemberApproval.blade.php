

<table width="100%" cellspacing="0" cellpadding="0" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
    <tr style="border-collapse:collapse;">
        <td width="518" valign="top" align="center" style="font-size:12px;padding:0;Margin:0;">
            <table width="100%" cellspacing="0" cellpadding="0" style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-position:left top;">
                <tr style="border-collapse:collapse;">

                    <td colspan="{{ $countData }}" align="left" style="padding:0;Margin:0;padding-top:5px;padding-bottom:15px;"><h2 style="font-size:18px;margin:0;line-height:24px;mso-line-height-rule:exactly;font-style:normal;font-weight:bold;color:#333333;">Hi,</h2></td>
                </tr>
                @if(isset($messageTop) && $messageTop !='')
                    <tr style="border-collapse:collapse;">
                        <td colspan="{{ $countData }}" align="left" style="padding:0;Margin:0;padding-top:20px;padding-bottom:10px;">
                            <p style="margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;">Congratulations! You are now a member of Elevate Wellness Association. We are truly excited and look forward to helping you be as healthy as you can be, physically, mentally, and financially.
                                </p><br>
                        </td>
                    </tr>
                @endif
                @if(isset($reason) && $reason !='')
                    <tr style="border-collapse:collapse;">
                        <td colspan="{{ $countData }}" align="left" style="padding:0;Margin:0;padding-top:20px;padding-bottom:10px;">
                            <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;">
                                <b>Reason :</b> <br>{!! $reason !!}</p><br><br>

                        </td>
                    </tr>
                @endif
                @if($countData > 0)
                    <tr class="dataPCScreen" style="border-collapse:collapse;">
                        @foreach($data as $key=>$value)
                            <?php  $width = ($key == 'Plan Name') ? '27%' : '20%';
                            $paddingLeft = ($key == 'Eff. Date') ? '15px' : '0px';
                            ?>
                            <td align="left" style="width:{{$width}};padding:0;Margin:0;padding-left:{{$paddingLeft}};">
                                <strong>{{$key}} </strong>
                            </td>
                        @endforeach
                    </tr>
                    <tr class="dataPCScreen" style="border-collapse:collapse;vertical-align: top;">
                        @foreach($data as $key=>$value)
                            <?php    $paddingLeft = ($key == 'Eff. Date') ? '15px' : '0px';  ?>
                            <td align="left" style="padding:0;Margin:0;padding-bottom:10px;padding-top: 5px;padding-left:{{$paddingLeft}};">
                                {!! $value !!}
                                <br><br>
                            </td>
                        @endforeach
                    </tr>
                    @endif

                            <!-- for mobile screen -->

                    @if($countData > 0)

                        @foreach($data as $key=>$value)
                            <tr class="dataMobileScreen" style="border-collapse:collapse;vertical-align:baseline;margin-top: 14px!important;">
                                <td align="left" style="padding:0;Margin:0;width: 110px">
                                    <strong>{!! $key !!}</strong>

                                </td>
                                <td align="left" style="padding:0;Margin:0;">
                                   {!! $value !!}

                                </td>
                            </tr>
                            @endforeach

                            @endif
                                    <!-- for mobile screen ends -->

                            @if(isset($middleMessage) && $middleMessage !='')
                                <tr style="border-collapse:collapse;">
                                    <td colspan="{{ $countData }}" align="left" style="padding:0;Margin:0;padding-top:10px;padding-bottom:10px;">
                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;color:#555555;">
                                            <strong>{!! $middleMessage  !!}</strong>
                                            <br><br>
                                        </p>

                                    </td>
                                </tr>
                            @endif
                            @if(isset($groupInfo) && $groupInfo !='')
                                <tr style="border-collapse:collapse;">
                                    <td colspan="{{ $countData }}" align="left" style="padding:0;Margin:0;padding-top:10px;padding-bottom:10px;">
                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;color:#555555;">
                                            <strong><b>Group Info</b><br>{!! $groupInfo  !!}</strong>
                                            <br><br>
                                        </p>
                                    </td>
                                </tr>
                            @endif
                            @if(isset($companyPhone) && $companyPhone !='')
                                <tr style="border-collapse:collapse;">
                                    <td colspan="{{ $countData }}" align="left" style="padding:0;Margin:0;padding-top:10px;padding-bottom:10px;">
                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;line-height:23px;color:#555555;">

                                        <p><strong></strong></p>

                                        <p>
                                        <p>You can register for online access to your Member Dashboard, on your effective date {{$effectiveDate}}.</p><br>
                                        <p> <a style="text-decoration: none;font-width: 700" href="https://corenroll.com/client_reg.php?p=client_register"> <strong>Click here to register </strong> </a> </p><br>

                                        <p>Your Member Dashboard contains resources such as, but not limited to: </p>

                                          <p>  Summary Plan Documents <br>
                                            View/Print Cards (cards are only available on or after effective date) and more. <br><br>

                                            Thank you, <br><br>

                                           The Elevate Wellness Team </p>
                                        </p>


                                      <!--  <p style="line-height:23px">Please feel free to contact us, if you have questions regarding your benefits or billing, <br/>we have customer support specialists on hand ready to assist you, Monday-Friday 9am - 5pm (Eastern). <br/>
                                            Customer Service:{{ $companyPhone }}</p>
                                        </p> -->
                                    </td>
                                </tr>
                                @endif
                                        <!-- commented out as it suport asked -->
                                <!-- <tr style="border-collapse:collapse;">
                    <td colspan="{{ $countData }}" align="left" style="padding:0;Margin:0;padding-top:10px;"><p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:15px;font-family:helvetica, 'helvetica neue', arial, verdana, sans-serif;line-height:23px;color:#555555;">Yours sincerely,</p></td>
                </tr> -->
            </table>
        </td>
    </tr>
</table>
