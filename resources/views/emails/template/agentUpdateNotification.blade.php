@if($type == 'A')
<!--<div class='col-md-12' style='background:#f2f2f2;border: 4px solid #f2f2f2;padding:2em;padding-bottom:0em;'>-->
    <table style='background:#f2f2f2;'>
    	<tr>
    		<td></td>
    		<td style='width:100%;'>
    			<center>
                    <p>
                    	<strong>
                    		<font size=7 color=#6699CC>ACCOUNT ACTIVATED</font> 
                    	</strong>
                    </p>
                </center>
            </td>
            <td></td>
        </tr>
    </table>
<table style='padding:2em;'>
	<tr>
		<td></td>
		<td style='width:100%;background:#ffffff;padding:1em;'>
            <div style="width:750px;padding:5px; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px;">
  
  
                <p><strong><font size=3 color=#6699CC>Your account has been activated!</font> </strong></p>
                <p>You are now ready to <strong><em>submit new business</em></strong>!</p>
                <table border=1 cellspacing=0 cellpadding=10 bgcolor=#6699CC><tr><td><font size=5 color=#ffffff>Your Representative Code</em>: <strong>{{$agentCode}}</strong> </font><em></td></tr></table>

                <p><strong>Representative Enrollment Portal: </strong><a href='{{$url}}' target=_blank>{{ $url }}</a></P>	

                <p><strong>Representative Name: </strong>{{ $agentFirstName }} {{ $agentLastName }}</P>
                <p><strong>State:</strong>{{ $agentState }}</p>
                <p><strong>Group:</strong>{{ $agentInGroupFinal }}<br />
                <p><strong>Your Phone:</strong>{{ $agentPhone }}</p>
                <p><strong>Your Email:</strong>{{ $agentEmail }} (Broker portal username)</p>
                <p><strong>Your Temp Password:</strong>{{ $agentPwd }}</p>
	            <p><strong>Your GA:</strong>{{ $agentFirstNameGa }} {{ $agentLastNameGa }}(GA Phone: {{ $agentPhoneGa }})</p>
	
	            <p><strong>Functions of the Representative dashboard include but not limited to:</strong></p>
                <ul>
                    <li><u></u>Change Password</li>
                    <li><u></u>Upload Logo for broker landing pages</li>
                    <li><u></u>Upload Personal Picture for your broker landing pages</li>
                    <li><u></u>View Your Business</li>
                    <li><u></u>Manage Your Business</li>
                    <li>Run Reports</li>
                    <li><u></u>Request ID Cards</li>
                </ul>
                <p><h3>Note: Do not use the Reply button to respond to this message</h3></p><br><BR>

            </div>

        </td>
        <td></td>
    </tr>
</table>
<!--<table style='background:#f2f2f2;padding-bottom:1em;'>
	<tr>
		<td></td>
		<td style='width:100%;'>
	        <center>
                <a href=https://corenroll.com target=_blank /><img src=https://corenroll.com/corenroll-logo-pb.png width=150 height=29/><br><br>
            </center>
        </td>
        <td></td>
    </tr>
</table>-->
<!--</div>	-->
@endif
@if($type == 'S')
<div style="width:750px;padding:5px; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px;">
    <div> 	
        <div style="border-top:1px solid #6699CC;"></div>
    </div>
    <p>&nbsp;</p>
    <p><strong>Your Account has been deactivated.</strong></p>
    <p>Please contact Support at {{$supportPhone}}.</p>
    <div style=\&quot;margin-left:40px;\&quot;>
        <p>Note: Do not use the Reply button to respond to this message</p><BR><br>
    </div>
</div>
@endif
@if($type == 'Level')
<div style=\width:750px;padding:5px; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px;\>
  
  <br />
  <p>Your Level has been changed to <strong>{{$level}}</strong> on <strong>{{$date}}</strong>.</p><br>

  <br />
  <p>Thank you.<br />
  </p>
  <p>Note: Do not use the Reply button to respond to this message</p><BR><BR>
@endif