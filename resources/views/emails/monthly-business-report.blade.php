@extends('emails.layouts.report-layout')
@section('content')
    <h2>Direct Business Summary for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp</h2>

    <h3 style="margin-bottom:0.5rem; margin-top:1rem">Policy Summary</h3>
    <div class="table-responsive">
        <table class="table" style="border:1px solid #ccc; border-collapse:collapse">
            <tr>
                <th>Effective Date</th>
                <th>Total Enrolled</th>
                <th>Total Active</th>
                <th>Total Pending</th>
                <th>Total Withdrawn</th>
                <th>Total Termed</th>
            </tr>
            @forelse ($directCount['direct_policy'] ?? [] as $policy)
                <tr>
                    <td>{{ $policy['effective_date'] }}</td>
                    <td>{{ $policy['total_enrolled'] }}</td>
                    <td>{{ $policy['total_active'] }}</td>
                    <td>{{ $policy['total_pending'] }}</td>
                    <td>{{ $policy['total_withdrawn'] }}</td>
                    <td>{{ $policy['total_termed'] }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" style="padding:0.8rem; border:1px solid #ccc; text-align:center;">
                        No data available for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp
                    </td>
                </tr>
            @endforelse
        </table>
    </div>

    <h3 style="margin-bottom:0.5rem; margin-top:1rem">Active Plans Summary</h3>
    <div class="table-responsive">
        <table class="table" style="border:1px solid #ccc; border-collapse:collapse">
            <tr>
                <th>Web Display Name</th>
                <th>Total Enrolled</th>
                <th>Total Active</th>
                <th>Total Termed</th>
                <th>Total Withdrawn</th>
            </tr>
            @forelse ($directCount['direct_plan'] ?? [] as $plan)
                <tr>
                    <td>{{ $plan['web_display_name'] }}</td>
                    <td>{{ $plan['total_enrolled'] }}</td>
                    <td>{{ $plan['total_active'] }}</td>
                    <td>{{ $plan['total_termed'] }}</td>
                    <td>{{ $plan['total_withdrawn'] }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="5" style="padding:0.8rem; border:1px solid #ccc; text-align:center;">
                        No data available for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp
                    </td>
                </tr>
            @endforelse
        </table>
    </div>

    <hr style="margin-top:2rem">
    <h2>Downline Brokers/Reps Business Summary for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp</h2>
    <h3 style="margin-bottom:0.5rem; margin-top:1rem">Policy Business Summary</h3>
    <div class="table-responsive">
        @forelse ($downlineCount['downline_policy_report'] ?? [] as $plan)
            <table class="table" style="border:1px solid #ccc; border-collapse:collapse">
                <thead>
                    <tr>
                        <th style="min-width: 96px">Broker/Rep Name</th>
                        <th style="min-width: 96px">Total Enrolled</th>
                        <th style="min-width: 96px">Total Active</th>
                        <th style="min-width: 96px">Total Pending</th>
                        <th style="min-width: 96px">Total Withdrawn</th>
                        <th style="min-width: 96px">Total Termed</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="min-width: 96px">{{ $plan['agent_name'] }}</td>
                        <td style="min-width: 96px">{{ $plan['total_enrolled'] }}</td>
                        <td style="min-width: 96px">{{ $plan['total_active'] }}</td>
                        <td style="min-width: 96px">{{ $plan['total_pending'] }}</td>
                        <td style="min-width: 96px">{{ $plan['total_withdrawn'] }}</td>
                        <td style="min-width: 96px">{{ $plan['total_termed'] }}</td>
                    </tr>
                </tbody>
            </table>

            @if (isset($plan['effective_date']) && is_array($plan['effective_date']))
                <table class="table" style="border:1px solid #ccc; border-collapse:collapse; margin-top: 10px;">
                    <thead>
                        <tr>
                            <th style="min-width: 96px">Effective Date</th>
                            <th style="min-width: 96px">Total Enrolled</th>
                            <th style="min-width: 96px">Total Active</th>
                            <th style="min-width: 96px">Total Pending</th>
                            <th style="min-width: 96px">Total Withdrawn</th>
                            <th style="min-width: 96px">Total Termed</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($plan['effective_date'] as $effective)
                            <tr>
                                <td style="min-width: 96px">{{ $effective['effective_date'] }}</td>
                                <td style="min-width: 96px">{{ $effective['total_enrolled'] }}</td>
                                <td style="min-width: 96px">{{ $effective['total_active'] }}</td>
                                <td style="min-width: 96px">{{ $effective['total_pending'] }}</td>
                                <td style="min-width: 96px">{{ $effective['total_withdrawn'] }}</td>
                                <td style="min-width: 96px">{{ $effective['total_termed'] }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                <br />
            @endif
        @empty
            <table class="table" style="border:1px solid #ccc; border-collapse:collapse">
                <thead>
                    <tr>
                        <th style="min-width: 96px">Broker/Rep Name</th>
                        <th style="min-width: 96px">Total Enrolled</th>
                        <th style="min-width: 96px">Total Active</th>
                        <th style="min-width: 96px">Total Pending</th>
                        <th style="min-width: 96px">Total Withdrawn</th>
                        <th style="min-width: 96px">Total Termed</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" style="padding:0.8rem; border:1px solid #ccc; text-align:center;">
                            No data available for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp
                        </td>
                    </tr>
                </tbody>
            </table>
        @endforelse
    </div>

    <h3 style="margin-bottom:0.5rem; margin-top:1rem">Downline Plans Summary</h3>
    <div class="table-responsive">
        <table class="table" style="border:1px solid #ccc; border-collapse:collapse">
            <tr>
                <th>Web Display Name</th>
                <th>Total Enrolled</th>
                <th>Total Active</th>
                <th>Total Termed</th>
                <th>Total Withdrawn</th>
            </tr>
            @forelse ($downlineCount['downline_plan_report'] ?? [] as $plan)
                <tr>
                    <td>{{ $plan['web_display_name'] }}</td>
                    <td>{{ $plan['total_enrolled'] }}</td>
                    <td>{{ $plan['total_active'] }}</td>
                    <td>{{ $plan['total_termed'] }}</td>
                    <td>{{ $plan['total_withdrawn'] }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" style="padding:0.8rem; border:1px solid #ccc; text-align:center;">
                        No data available for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp
                    </td>
                </tr>
            @endforelse
        </table>
    </div>

    <hr style="margin-top:2rem">
    <h2>New Business Count for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp</h2>
    <h3 style="margin-bottom:0.5rem; margin-top:1rem">Overall Summary</h3>
    <div class="table-responsive">
        <table class="table" style="border:1px solid #ccc; border-collapse:collapse">
            <tr>
                <th>Broker/Rep Name</th>
                <th>Total Enrolled</th>
                <th>Total Active</th>
                <th>Total Pending</th>
                <th>Total Withdrawn</th>
                <th>Total Termed</th>
            </tr>
            @forelse ($newBusinessCount['new_business_report'] ?? [] as $business)
                <tr>
                    <td>{{ $business['agent_name'] }}</td>
                    <td>{{ $business['total_enrolled'] }}</td>
                    <td>{{ $business['total_active'] }}</td>
                    <td>{{ $business['total_pending'] }}</td>
                    <td>{{ $business['total_withdrawn'] }}</td>
                    <td>{{ $business['total_termed'] }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" style="padding:0.8rem; border:1px solid #ccc; text-align:center;">
                        No data available for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp
                    </td>
                </tr>
            @endforelse
        </table>
    </div>

    <!-- Downline Brokers/Reps with No Business -->

    <hr style="margin-top:2rem">
    <h2>Downline Brokers/Reps with No Business for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp</h2>
    <div class="table-responsive">
        <table class="table" style="border:1px solid #ccc; border-collapse:collapse">
            <tr>
                <th>Broker/Rep Name</th>
                <th>Total Enrollments</th>
            </tr>
            @forelse ($repsWithNoEnrollments['downline_reps_no_enrollments'] ?? [] as $rep)
                <tr>
                    <td>{{ $rep['agent_name'] }}</td>
                    <td>{{ $rep['total_enrolled'] }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" style="padding:0.8rem; border:1px solid #ccc; text-align:center;">
                        No downline brokers/reps with zero enrollments for @php echo \Carbon\Carbon::now()->subMonth()->format('F Y'); @endphp.
                    </td>
                </tr>
            @endforelse
        </table>
    </div>

@endsection
