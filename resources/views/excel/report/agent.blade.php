<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>Agent Report</title>
    <style>
        table, th, td {
            border: 1px solid black;
            border-collapse: collapse;
            padding: 5px;
        }
    </style>
<body>
<table>
    <thead>
    <tr>
        <th>Sign Up Date</th>
        <th>Level</th>
        <th>Ancillary Level</th>
        <th>Premier Level</th>
        <th>Status</th>
        <th>Agent Name</th>
        <th>Agent Code</th>
        <th>Group(s)</th>
        <th>Upline</th>
        <th>Upline Agent Code</th>
        <th>Upline Agent Level</th>
        <th>Phone 1</th>
        <th>Phone 2</th>
        <th>Email</th>
        <th>Resident License</th>
         <th>Active</th>
        <th>Withdrawn</th>
        <th>Termed</th>
        <th>Total</th>
    </tr>
    </thead>
    <tbody>
    @foreach($agents as $agent)
        <tr>
            <td>{{ $agent['agent_signup_date'] ?? '' }}</td>
            <td>{{ $agent['agent_level'] ?? '' }}</td>
            <td>{{ $agent['contracts']['alc'] ?? '' }}</td>
            <td>{{ $agent['contracts']['pec'] ?? '' }}</td>
            <td>{{ $agent['formatted_agent_status'] ?? '' }}</td>
            <td>{{ $agent['agent_fullname'] ?? '' }}</td>
            <td>{{ $agent['agent_code'] ?? '' }}</td>
            <td>
                @if(!empty($agent['groups']))
                    @foreach($agent['groups'] as $group)
                        {{ $loop->first ? '' : ', ' }}{{ $group->gname }}
                    @endforeach
                @endif
            </td>
            <td>{{ $agent['upline']['agent_fullname'] ?? '' }}</td>
            <td>{{ $agent['upline']['agent_code'] ?? '' }}</td>
            <td>{{ $agent['upline']['agent_level'] ?? '' }}</td>
            <td>{{ $agent['agent_phone1'] ?? 'N/A' }}</td>
            <td>{{ $agent['agent_phone2'] ?? 'N/A' }}</td>
            <td>{{ $agent['agent_email'] ?? 'N/A' }}</td>
            <td>
                @if(!empty($agent['licenses']))
                    @foreach($agent['licenses'] as $license)
                        {{ $license['license_number'] ?? '' }}<br>
                        Exp: {{ $license['license_exp_date'] ?? '' }}<br>
                        Status: {{ $license['formatted_license_status'] ?? '' }}<br><br>
                    @endforeach
                @else
                    {{ $agent['admin_only'] ?? false ? 'Administration' : 'No License Defined' }}
                @endif
            </td>
            <td>{{ $agent['totals']['values']['active'] ?? 0 }}</td>
            <td>{{ $agent['totals']['values']['withdrawn'] ?? 0 }}</td>
            <td>{{ $agent['totals']['values']['termed'] ?? 0 }}</td>
            <td>{{ $agent['totals']['total'] ?? 0 }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
</body>
</html>
