<td>{{$policyInfo->enrollment_date}}</td>
<td>{{$policyInfo->effective_date}}</td>
<td>
    <a target="_blank" href=""> {{$policyInfo->policy_id}}</a>
</td>
<td>{{ isset($policyInfo->agentInfo) ?  ucwords($policyInfo->agentInfo->fullname) : ''}}</td>
<td>{{ isset($policyInfo->getGroup) ?  ucwords($policyInfo->getGroup->gname) : ''}}</td>

@if(isset($dependent))
    <td>
        {{ isset($dependent) ?  ucwords($dependent->fullname) : ''}}<br/>
        <strong>
            {{ '('.\App\Helpers\FamilyRelationFinderHelper::getFullRelation($dependent->d_relate).')' }}
        </strong>
    </td>
    <td>{{ isset($dependent) ? ($dependent->age > 0 ? $dependent->age :''): ''}}</td>
    <td>{{ isset($dependent) ?  array_search($dependent->d_gender,\App\DependentInPolicy::$gender) : ''}}</td>
    <td>{{ isset($dependent) ?  $dependent->formatted_height.' '. $dependent->formatted_weight: ''}}</td>
    <td>{{ucfirst($d->d_relate)}}</td>
@else
    <td>{{ isset($policyInfo->getMember) ?  ucwords($policyInfo->getMember->fullname) : ''}}</td>
    <td>{{ isset($policyInfo->getMember) ?  ($policyInfo->getMember->age > 0  ? $policyInfo->getMember->age : '') :''}}</td>
    <td>{{ isset($policyInfo->getMember) ?  array_search($policyInfo->getMember->cgender,\App\UserInfo::$gender) : ''}}</td>
    <td>{{ isset($policyInfo->getMember) ?  $policyInfo->getMember->formatted_height.' '. $policyInfo->getMember->formatted_weight: ''}}</td>
    <td>P</td>
@endif
<td>{{ isset($policyInfo->healthPlanOverview) ?  $policyInfo->healthPlanOverview->plan_name_system  : ''}}</td>
