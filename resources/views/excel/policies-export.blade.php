<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>Export</title>
</head>
<body>
<table>
    <thead>
    <tr>
        <th>Enrollment Date</th>
        <th>Effective Date</th>
        <th>POLICY ID</th>
        <th>REP</th>
        <th>GROUP</th>
        <th>MEMBER</th>
        <th>AGE</th>
        <th>GENDER</th>
        <th>HEIGHT/WEIGHT</th>
        <th>RELATE</th>
        <th>PLAN</th>
        <th>Question</th>
        <th>
            Condition/Disease/<br>
            Diagnosis/Treatment
        </th>
        <th>
            Date of Onset
            (MM/YYYY)
        </th>
        <th>
            Date of Recovery
            (MM/YYYY)
        </th>
        <th>
            Current<br>
            Treatment
        </th>
        <th>
            Taking Medication
        </th>
        <th>
            Date Last Seen <br>By
            Physician (MM/DD/YYYY)
        </th>
        <th>
            Remaining Symptoms<br> or Problems? Surgery or Hospitalization?
        </th>
        <th>
            Medications
        </th>
        <th>
            Dosage
        </th>
        <th>
            Medical Conditions
        </th>
        <th>
            Additional Notes
        </th>
    </tr>
    </thead>
    <tbody>

    @foreach($policies as $p)
        @php
            $selectedDepMedBex = $p->userInfoDepMedBex->where('selected_answer','=',true);
            $medicals = \App\Exports\PoliciesExport::getMedicals($selectedDepMedBex);
            $getMedications = $p->getMember->getMedication;
            $medications = \App\Exports\PoliciesExport::getMedications($getMedications);
            $finalResult = \App\Exports\PoliciesExport::finalResult($medicals,$getMedications);
        @endphp
        @if(!empty($finalResult))
            @foreach($finalResult as $medBex)
                <tr class="plan-{{$p->policy_id}}">
                    @include('excel.health-policies.primary-info-table-data',['policyInfo'=>$p])
                    @include('excel.health-policies.user-dep-med-bex-table-data',['depMedBex'=>$medBex])
                </tr>
            @endforeach
        @else
            <tr class="plan-{{$p->policy_id}}">
                @include('excel.health-policies.primary-info-table-data',['policyInfo'=>$p])
                @include('excel.health-policies.user-dep-med-bex-table-data')
            </tr>
        @endif

        @if(isset($p->dependentInPolicy))
            @foreach($p->dependentInPolicy as $d)
                @php
                    $dependentSelectedDepMedBox = $d->healthAnswers->where('selected_answer','=',true);
                    $depMedicals = \App\Exports\PoliciesExport::getMedicals($dependentSelectedDepMedBox);
                    $depGetMedications = $d->getDepMedication;
                    $depMedications = \App\Exports\PoliciesExport::getMedications($depGetMedications);
                    $depFinalResult = \App\Exports\PoliciesExport::finalResult($depMedicals,$depMedications);
                @endphp

                @if(!empty($depFinalResult))
                    @foreach($depFinalResult as $depMedBex)
                        <tr class="plan-{{$p->policy_id}}" >
                            @include('excel.health-policies.primary-info-table-data',['policyInfo'=>$p,'dependent'=>$d])
                            @include('excel.health-policies.user-dep-med-bex-table-data',['depMedBex'=>$depMedBex])
                        </tr>
                    @endforeach
                @else
                    <tr class="plan-{{$p->policy_id}}">
                        @include('excel.health-policies.primary-info-table-data',['policyInfo'=>$p,'dependent'=>$d])
                        @include('excel.health-policies.user-dep-med-bex-table-data')
                    </tr>
                @endif
            @endforeach
        @endif
    @endforeach
    </tbody>
</table>
</body>
</html>
