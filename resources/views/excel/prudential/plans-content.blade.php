<table border='1' style="border-collapse:collapse; margin-left: auto;margin-right: auto">
    <thead>
    <tr style="background: lightgrey">
        <th>{{$header['plan']}}</th>
        <th>{{$header['col_first']}}</th>
        <th>{{$header['col_second']}}</th>
        <th>{{$header['col_third']}}</th>
        <th>{{$header['col_current']}}</th>
    </tr>
    </thead>
    <tbody>
    @foreach($body as $p)
        <tr>
            <td align="center">{{$p['plan_name_system']}}</td>
            <td align="center">
                {{$p['row_first']}}
                @if($p['row_first_price'])
                    <br/>
                    {{ "Total : $" . sprintf('%01.2f', $p['row_first_price']) }}
                @endif
            </td>
            <td align="center">
                {{$p['row_second']}}
                @if($p['row_second_price'])
                    <br/>
                    {{ "Total : $" . sprintf('%01.2f', $p['row_second_price']) }}
                @endif
            </td>
            <td align="center">
                {{$p['row_third']}}
                @if($p['row_third_price'])
                    <br/>
                    {{ "Total : $" . sprintf('%01.2f', $p['row_third_price']) }}
                @endif
            </td>
            <td align="center">
                {{$p['row_current']}}
                @if($p['row_current_price'])
                    <br/>
                    {{ "Total : $" . sprintf('%01.2f', $p['row_current_price']) }}
                @endif
            </td>
        </tr>
    @endforeach
    <tr>
        <td align="center">Total</td>
        <td align="center">{{$total['total_row_first']}}</td>
        <td align="center">{{$total['total_row_second']}}</td>
        <td align="center">{{$total['total_row_third']}}</td>
        <td align="center">{{$total['total_row_current']}}</td>
    </tr>
    </tbody>
</table>
