
## Nuera Benefits
## Developers Guide
Production Branch: master

Development QA Branch: qa

## Basic Instruction for development
Clone repository in local machine.<br>

Create an feature branch from master branch<br>

_git checkout -b {feature_branch_name}_<br>

push in your specific branch and open pull request in qa


## Pre Hook Git setup
Steps:
1. composer install

2. Copy config file to location .git/hooks

    cp php-git-hooks.yml .git/hooks
    
3. Copy these 3 files to .git/hooks

    cp vendor/bruli/php-git-hooks/src/PhpGitHooks/Infrastructure/Hook/commit-msg .git/hooks
    
    cp vendor/bruli/php-git-hooks/src/PhpGitHooks/Infrastructure/Hook/pre-push .git/hooks
    
    cp vendor/bruli/php-git-hooks/src/PhpGitHooks/Infrastructure/Hook/pre-commit .git/hooks


## DEPLOYMENT GUIDE
[CLICK HERE](https://github.com/CloudTechService/nuerabenefits/blob/qa/deployment.md)


## License

This product is licensed to [PURENROLL](https://purenroll.com).


