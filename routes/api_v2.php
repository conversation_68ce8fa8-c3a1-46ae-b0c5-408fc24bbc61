<?php

use Illuminate\Support\Facades\Route;

Route::group(['prefix' => '/v2', 'namespace' => 'Api\V1', 'as' => 'api.'], function () {
    //Agents
    Route::get('get-agents', 'ManageAgentsController@agentList');
    Route::get('get-contract-actions/{agentId}', 'ManageAgentsController@contractActions');
    Route::get('get-contract-levels/{agentId}', 'ManageAgentsController@contractLevels');
    Route::post('agent/send-contract-email', 'ManageAgentsController@sendContractEmail');
    Route::get('getSignedContractLevels/{id}/{type}', 'ManageAgentsController@getSignedContractLevels');
    Route::get('getContractNameList', 'ManageAgentsController@getContractNameList');

    Route::get('agent/{agentId}/detail', 'ManageAgentsController@agentDetail');
    Route::get('agent/{agentId}/downline-reps', 'ManageAgentsController@agentDownlineReps');
    Route::get('agent/{agentId}/licenses', 'ManageAgentsController@agentLicense');
    Route::put('update-payment-method','ManageAgentsController@updatePaymentMethod');
    Route::get('agent/{agentId}/upline-reps', 'ManageAgentsController@uplineReps');
    Route::put('agent/update-upline-rep', 'ManageAgentsController@updateUplineRep');
    Route::post('agent/schedule-update-upline-rep', 'ManageAgentsController@scheduleUpdateUplineRep');
    Route::get('agent/{agentId}/get-scheduled-update-upline-rep', 'ManageAgentsController@getScheduledUpdateUplineRep');
    Route::get('agent/{agentId}/get-groups', 'ManageAgentsController@agentGroupList');
    Route::post('agent/add-group', 'ManageAgentsController@agentAddGroup');
    Route::get('agent/{agentId}/activity', 'ManageAgentsController@agentActivity');
    Route::put('edit-agent-image', 'ManageAgentsController@editAgentImage');
    Route::delete('delete-agent', 'ManageAgentsController@deleteAgent');
    Route::get('active-downlines-list/{agentId}','ManageAgentsController@checkIfAgentHaveActiveDownlines');
    Route::put('change-agent-upline/{agentGa}','ManageAgentsController@changeAgentUpline');
    Route::get('restore-agent/{agent_id}', 'ManageAgentsController@restoreTrashedAgent');
    Route::put('edit-agent-personal-info', 'ManageAgentsController@editPersonalInfo');
    Route::put('change-agent-password', 'ManageAgentsController@changeAgentPassword');

    //update business info
    Route::put('save-business-info','ManageAgentsController@saveBusinessInfo');

    //Groups
    Route::get('get-groups', 'ManageGroupsController@groupList');
    Route::get('get-all-groups', 'ManageGroupsController@getGroupList');
    Route::get('get-groups-search-list', 'ManageGroupsController@groupSearchList');
    Route::post('add-sub-groups','ManageGroupsController@addSubGroups');
    Route::post('edit-sub-groups','ManageGroupsController@editSubGroups');
    Route::delete('delete-sub-groups','ManageGroupsController@deleteSubGroups');
    Route::get('get-sub-groups','ManageGroupsController@getSubGroups');
    Route::post('update-group-level-status','ManageGroupsController@updateGroupLevelStatus');
    Route::post('update-group-guaranteed-status','ManageGroupsController@updateGroupGuaranteedStatus');
    Route::get('get-group-ph-levels','ManageGroupsController@getGroupPhLevels');
    Route::post('update-group-lump-sum-pay','ManageGroupsController@updateGroupLumpSumPay');
    //Group Detail
    Route::get('group/{groupId}/detail', 'ManageGroupsController@groupDetail');
    Route::get('group/{groupId}/agents', 'ManageGroupsController@groupAgents');
    Route::get('group/{groupId}/eligibility-logs', 'ManageGroupsController@eligibilityLogs');
    Route::put('edit-group-logo', 'ManageGroupsController@editGroupLogo');
    Route::get('group/{groupId}/get-agents', 'ManageGroupsController@groupAgentList');
    Route::put('group/update-main-agent', 'ManageGroupsController@updateMainAgent');
    Route::put('group/update-web-display-details', 'ManageGroupsController@updateWebDisplay');
    Route::get('group/get-industries','ManageGroupsController@getGroupIndustries');
    Route::put('group/update-basic-detail','ManageGroupsController@updateBasicDetail');
    Route::put('group/update-eft-with-bank-details','ManageGroupsController@updateEftWithBankDetail');
    Route::put('group/update-payment-method','ManageGroupsController@updatePaymentMethod');
    Route::put('group/update-billing-details','ManageGroupsController@updateBillingDetail');
    Route::put('group/suspend-account', 'ManageGroupsController@suspendAccount');
    Route::delete('delete-group', 'ManageGroupsController@deleteGroup');
    Route::get('group/{groupId}/activity', 'ManageGroupsController@groupActivity');
    Route::put('change-group-password', 'ManageGroupsController@changeGroupPassword');
    Route::post('group/waive-association-fee', 'ManageGroupsController@waiveGroupAssociationFee');

    //group file
    Route::post('add-group-file','ManageGroupsController@addGroupFile');
    Route::delete('delete-group-file/{id}','ManageGroupsController@deleteGroupFile');
    Route::get('get-group-file','ManageGroupsController@getGroupFiles');

    // adminMessageCenter
    Route::get('admin/get-client-detail/{id}','ManageClientsController@getClientDetail');
    Route::get('admin/get-agent-detail/{id}','ManageAgentsController@getAgentDetail');
    Route::get('admin/get-group-detail/{id}','ManageGroupsController@getGroupDetail');


    //agent logs
    Route::get('agent/{agentId}/logs', 'ManageAgentsController@agentUpdates');
    Route::put('agent/update-personalized-site', 'ManageAgentsController@websiteUpdate');
    Route::delete('agent/remove-group', 'ManageAgentsController@removeGroup');

    //Member
    Route::get('manage-clients', 'ManageClientsController@manageClientsInfo');
    Route::get('clients-plan-pricing', 'ManageClientsController@clientPlanPricing');
    Route::get('get-user-otp', 'ManageClientsController@getDefaultOTP');
    Route::get('clients-plans-detail', 'ManageClientsController@clientsPlansDetail');
    Route::get('clients-plans-cat', 'ManageClientsController@clientsPlansCat');
    Route::get('clients-plans-type', 'ManageClientsController@clientsPlansType');
    Route::put('change-member-password', 'ManageClientsController@changeMemberPassword');
    Route::get('get-termed-dates', 'ManageClientsController@getTermedDates');

    //Group Quotes
    Route::get('get-group-census', 'GroupQuotesController@getGroupCensusDetails');
    Route::get('get-group-plans', 'GroupQuotesController@getGroupPlanList');

    //homepage configuration
    Route::get('/homepage-configuration/{agentId}/get-agent-options', 'HomepageConfigurationController@agentOptions');
    Route::get('/homepage-configuration/get-group-options', 'HomepageConfigurationController@groupOptions');
    Route::get('group/get-group-types','ManageGroupsController@getGroupTypes');
    Route::put('group/update-group-type','ManageGroupsController@updateGroupType');
    Route::delete('/homepage-configuration/delete-selected-plan', 'HomepageConfigurationController@deleteSelected');

    Route::delete('/delete-website-selected-association-fees', 'AssocFeeController@deleteSelected');
    Route::delete('/delete-group-selected-association-fees', 'GidAssocFeeController@deleteSelected');
    // Get the agent groups for portal
    Route::get('agent/{agentId}/platform', 'ManageClientsController@getAgentPlatform');
    Route::post('agent/platform/update', 'ManageClientsController@updateAgentPlatform');
    Route::get('agent-commission-overview/{agentId}', 'ManageAgentsController@getAgentCommissionDetails');


});
//Version 2 api
Route::group(['prefix' => '/v2', 'namespace' => 'Api\V2', 'as' => 'api.'], function () {
    //Common dropdown values like payment types, statuses, platforms
    Route::get('payment-types', 'CommonDropdownController@getPaymentType');
    Route::get('platforms', 'CommonDropdownController@getPlatforms');
    Route::get('payment-statuses', 'CommonDropdownController@getPaymentStatuses');

    //group address
    Route::get('group/{groupId}/addresses', 'Group\GroupAddressController@groupAddressList');
    Route::post('group/add-address', 'Group\GroupAddressController@create');
    Route::put('group/set-primary-address', 'Group\GroupAddressController@setPrimaryGroupAddress');
    Route::delete('group/delete-address', 'Group\GroupAddressController@deleteGroupAddress');

    //agent address
    Route::get('agent/{agentId}/addresses', 'Agent\AgentAddressController@agentAddressList');
    Route::post('agent/add-address', 'Agent\AgentAddressController@create');
    Route::put('agent/set-primary-address', 'Agent\AgentAddressController@setPrimaryAgentAddress');
    Route::delete('agent/delete-address', 'Agent\AgentAddressController@deleteAgentAddress');

    //agent commission mobile api
    Route::get('agent/{agentId}/commission/{year}/{month}', 'Agent\AgentCommissionController@getAgentDateSpecificCommission');

    //ach payment
    Route::get('agent/{agentId}/ach-payments', 'Agent\AchPaymentController@agentAchPayments');
    Route::post('agent/add-ach-payment', 'Agent\AchPaymentController@create');
    Route::put('agent/set-primary-ach-payment', 'Agent\AchPaymentController@setPrimaryAchPayment');
    Route::delete('agent/delete-ach-payment', 'Agent\AchPaymentController@deleteAchPayment');
    Route::post('agent/update-payment-method-with-ach-payment', 'Agent\AchPaymentController@updatePaymentWithAch');

    Route::post('validate-routing-number', 'CustomValidationController@routingValidation');

    Route::post('/funding-source-webhook-response','Agent\AchPaymentController@sendWebhookResponse');

    //Member
    Route::put('approve-reject-member', 'Member\MemberController@approveRejectMember');
    Route::get('member-referral/{refId}', 'Member\MemberController@referralList');
    Route::get('member-referral', 'Member\MemberController@mainReferralList');
    Route::post('resend-member-referral', 'Member\MemberController@resendEmail');
    //group payment
    Route::get('group/{groupId}/check-eft-bank-exists', 'Group\GroupEftController@checkEftBankExists');
    Route::get('group/{groupId}/eft-banks', 'Group\GroupEftController@getEftBanks');
    Route::post('group/add-eft-bank', 'Group\GroupEftController@create');
    Route::put('group/set-primary-eft-bank', 'Group\GroupEftController@setPrimaryEft');
    Route::delete('group/delete-eft-bank', 'Group\GroupEftController@deleteEft');

    Route::post('group/add-note','Group\GroupNoteController@addGroupNote');
    Route::get('group/{groupId}/notes','Group\GroupNoteController@listNotes');
    Route::delete('group-delete-notes/{noteId}','Group\GroupNoteController@deleteGroupNote');

    //Member Credit Card
    Route::post('create-credit-card-info','Member\PaymentController@create');
    Route::get('verify-credit-card/{merchant}/{id}', 'Member\PaymentController@verify');

    //Member Get Plans
    Route::get('member/{planPolicyId}/plans','Member\SwitchPlanController@getPlans');
    Route::put('switch-member-plan','Member\SwitchPlanController@switchPlan');

    //Get Health Questions
    Route::get('policy/get-health-questions','Policy\PolicyController@getHealthQuestions');
    Route::get('bill-date', 'Policy\PolicyController@getDistinctBillDate');
    Route::post('policy/create-dependent','Policy\PolicyController@createDependent');
    Route::get('policy/get-dependent-list/{policy_id}','Policy\PolicyController@getDependentList');
    Route::post('policy/add-dependent-to-policy','Policy\PolicyController@addDependentToPolicy');
    Route::put('policy/update-dependent','Policy\PolicyController@updateDependent');
    Route::delete('policy/delete-dependent','Policy\PolicyController@deleteDependent');

    //Leads Questions
    Route::get('agent/leads', 'Agent\AgentLeadController@index');
    Route::post('agent/leads', 'Agent\AgentLeadController@create');
    Route::put('agent/leads/{leadId}', 'Agent\AgentLeadController@update');
    Route::delete('agent/leads/{leadId}', 'Agent\AgentLeadController@destroy');

    // agent code
    Route::put('agent/code', 'Agent\AgentCodeController@update');
    Route::get('agent/code/check/{agentCode}', 'Agent\AgentCodeController@isUnique');
    Route::post('agent/code/check', 'Agent\AgentCodeController@hasConflict');

    //Policy - Employer Information
    Route::get('policy/{policyId}/get-employer-info','Policy\PolicyController@getEmployerInfo');
    Route::get('policy/get-employer-info-options','Policy\PolicyController@getEmployerInfoOptions');
    Route::put('policy/update-employer-info','Policy\PolicyController@updateEmployerInfo');

    Route::put('policy/update-user-info','Policy\PolicyController@updatePersonalInfo');
    Route::put('policy/update-email-info','Policy\PolicyController@updateEmailInfo');
    Route::put('policy/update-alt-email-info', 'Policy\PolicyController@updateAltEmailInfo');
    Route::put('policy/update-phone-info','Policy\PolicyController@updatePhoneInfo');
    Route::get('policy/{paymentId}/eft-payment-details','Policy\PolicyController@getEftPaymentDetails');
    Route::get('policy/{paymentId}/credit-card-details','Policy\PolicyController@getCreditCardDetails');


    // plan request approval
    Route::get('plans/plan-request-approval', 'Plans\PlanRequestController@getPlanRequest');
    Route::post('plans/plan-request-action', 'Plans\PlanRequestController@planRequestAction');

    //Homepage Configuration Plans
    Route::get('/get-homepage-configuration-website', 'HomeConfig\HomepageConfigurationController@getHomepageConfigurationWebsite');
    Route::get('/get-homepage-configuration-plan-list', 'HomeConfig\HomepageConfigurationController@getHomepageConfigurationPlanList');
    Route::delete('/remove-bulk-homepage-plans', 'HomeConfig\HomepageConfigurationController@removeBulkHomepagePlans');

    // Contrct new APIs
    Route::post('save-contract-levels','Agent\ManageAgentController@saveContractLevels');
    Route::get('get-contract-actions-new/{agentId}', 'Agent\ManageAgentController@contractActionsNew');
    Route::get('get-contract-levels-new/{agentId}', 'Agent\ManageAgentController@contractLevelsNew');


    Route::group([

        'prefix' => 'admin/claim/',
    ], function () {
        Route::get('get-claim-list','Member\PuredentialClaimController@getPrudentialList');
        Route::get('get-plan-list','Member\PuredentialClaimController@getPlanList');
        Route::get('get-prudential-form','Member\PuredentialClaimController@getPrudentialForm');
        Route::post('create-prudential-claim','Member\PuredentialClaimController@createPrudentialClaim');
        Route::post('update-prudential-claim','Member\PuredentialClaimController@updatePrudentialClaim');
        Route::get('get-claim-list-all','Member\PuredentialClaimController@getPrudentialAllList');
        Route::get('get-members-with-special-plan','Member\PuredentialClaimController@getAllMembersWithSpecialPlans');
        Route::get('get-prudential-plan-list','Member\PuredentialClaimController@getPrudentialPlanList');
    });

    Route::group([
        'prefix' => '/rep-info-request',
    ], function () {
        Route::get('list','RepInfoRequestController@index');
        Route::put('approve-request', 'RepInfoRequestController@approveRequest');
        Route::put('complete-request', 'RepInfoRequestController@completeRequest');
        Route::put('reject-request', 'RepInfoRequestController@rejectRequest');
        Route::get('get-options', 'RepInfoRequestController@getOptions');
        Route::get('get-payment-details/{id}', 'RepInfoRequestController@getPaymentDetails');
        Route::get('get-policy-payment-details/{policy_id}', 'RepInfoRequestController@getPolicyPaymentDetails');
    });
    Route::get('migrate-plan','SyncPlanToPlanModuleController@index');
    Route::get('migrate-plan-pricing','SyncPlanToPlanModuleController@planPriceMigrate');

    Route::group([
        'namespace'=>'Report'
    ], function () {
        Route::get('export-member-report','MemberReportController@export');
        Route::get('get-member-report','MemberReportController@getMemberReport');
        Route::get('export-agent-report','AgentReportController@export');
        Route::get('get-agent-report','AgentReportController@getAgentReport');
        Route::get('export-group-report','GroupReportController@export');
        Route::get('get-group-report','GroupReportController@getGroupReport');
        Route::get('get-admin-group-report','GroupReportController@generateAdminGroupReport');
        Route::get('get-john-group-report','GroupReportController@getAdminGroupReport');
        Route::get('export-payment-report','ExtraHealthPaymentReportController@export');
        Route::get('get-payment-report','ExtraHealthPaymentReportController@getExtraHealthPaymentReport');
    });

    // Member Invoice APIs (mobile)
    Route::group([
        'namespace' => 'Invoice'
    ], function () {
        // for unpaid invoice alert
        Route::get('/unpaid-invoice-alert', 'InvoiceController@payInvoicesAlert');
        Route::get('/get-invoice-details', 'InvoiceController@getInvoiceInfo');
        Route::get('/get-payment-summary/{invoice_id}', 'InvoiceController@getPaymentSummaryDetails');
        Route::get('/get-invoice-history', 'InvoiceController@getInvoiceHistory');
        Route::get('/get-invoice-payment-history/{invoice_id}', 'InvoiceController@getInvoiceHistoryDetail');
        Route::post('/send-push-notification', 'InvoiceController@sendPushNotification');
        Route::get('/invoices/summary', 'InvoiceController@getPolicyInvoiceSummary');
    });

    Route::group([
        'namespace' => 'MedicalTypes'
    ], function () {
    //medical types
    Route::resource('/medical-types', 'MedicalTypesController');
    Route::post('/add-medical-type-plan', 'MedicalTypesController@addMedicalTypeToPlan');
    Route::post('/add-medical-type-agent', 'MedicalTypesController@addMedicalTypesToAgents');
    Route::get('/agent-list', 'MedicalTypesController@getAgentList');
    Route::get('/plan-list', 'MedicalTypesController@getPlanList');
    Route::get('/medical-plan-list/{medical_id}', 'MedicalTypesController@getPlanListFromMedicalType');
    Route::get('/medical-agent-list/{medical_id}', 'MedicalTypesController@getAgentListFromMedicalType');
    });

    Route::group(['namespace' => 'Agent'], function () {
        //manage agent script
        Route::resource('/manage-agent-script', 'ManageAgentScriptsController');
        // Route::post('/add-medical-type-plan', 'ManageAgentScriptsController@addMedicalTypeToPlan');
        Route::post('/add-agent-to-tree', 'ManageAgentScriptsController@addManageAgentScriptsToAgents');
        Route::post('/agent-lists', 'ManageAgentScriptsController@getAgentList');
        // Route::get('/plan-list', 'ManageAgentScriptsController@getPlanList');
        Route::get('/agent-downlines/{id}', 'ManageAgentScriptsController@getAgentDownlinesListFromAgent');
    });

    Route::put('/group/code', 'Group\GroupCodeController@update');
    Route::get('/group/code/check/{groupCode}', 'Group\GroupCodeController@isUnique');
    Route::post('/group-code/check', 'Group\GroupCodeController@hasConflict');


    Route::get('dependent-names', 'Policy\PolicyController@getDependentNames');
});
