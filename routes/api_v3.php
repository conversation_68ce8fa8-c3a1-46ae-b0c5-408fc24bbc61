<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V3\ArchiveController;

Route::group(['prefix' => '/v3', 'namespace' => 'Api\V3', 'as' => 'api.'], function () {
    //agent commission mobile api
    Route::get('agent/{agentId}/commission/{year}/{month}', 'Agent\AgentCommissionController@getAgentDateSpecificCommission');

    //archive for member, agent and group to delete and restore respective data
    Route::delete('agent/delete', 'Archive\ArchiveController@deleteAgent');
    Route::post('agent/restore', 'Archive\ArchiveController@restoreAgent');
    Route::delete('member/delete', 'Archive\ArchiveController@deleteMember');
    Route::post('member/restore', 'Archive\ArchiveController@restoreMember');
    Route::delete('group/delete', 'Archive\ArchiveController@deleteGroup');
    Route::post('group/restore', 'Archive\ArchiveController@restoreGroup');
});

Route::group(['prefix' => '/v3', 'namespace' => 'Api\V2', 'as' => 'api.'], function () {
    // This New Contract Level fetching api is for ADMIN only
    Route::get('get-contract-levels-new/{agentId}', 'Agent\ManageAgentController@contractLevelsNewAdmin');
});
