<?php

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

use App\Http\Controllers\Api\V1\AgentsController;
use App\Http\Controllers\Api\V2\Group\GroupInfoController;
use App\Http\Controllers\Api\V1\RepLicenseController;


Route::group(['prefix' => '/v1', 'namespace' => 'Api\V1', 'as' => 'api.'], function () {
    // filter members
    Route::get('filter-child-26', 'FilterUserController@filterChild26');
    Route::get('filter-spouse-65', 'FilterUserController@filterSpouse65');
    Route::get('filter-member-65', 'FilterUserController@filterMember65');

    //ach routes
    Route::get('view-ach/{achYear}/{achMonth}', 'AchController@viewAch');
    Route::get('policy-at', 'AchController@policyAT');
    Route::get('policy-eftlist/{effDate}', 'AchController@policyEftElist');
    Route::get('generate-ach/{effDate}', 'AchController@generateAch');

    //bank info
    Route::get('bank-info', 'BankApprovalController@filterBankInfo');
    Route::post('approve-bank-info', 'BankApprovalController@approveBankInfo');
    Route::post('reject-bank-info', 'BankApprovalController@rejectBankInfo');

    //email template
    Route::get('email-template', 'EmailTemplateController@getEmailTemplateInfo');
    Route::get('email-template/{id}', 'EmailTemplateController@getEmailTemplateInfo');
    Route::post('store-template', 'EmailTemplateController@storeTemplate');
    Route::post('update-template/{id}', 'EmailTemplateController@storeTemplate');
    Route::get('preview-email/{id}', 'EmailTemplateController@templatePreview');
    Route::get('email-category/{id}', 'EmailTemplateController@getTemplateByCategory');
    Route::post('email-queue', 'EmailTemplateController@saveEmail');

    // dependent approval
    Route::get('dependent-approval', 'DependentApprovalController@getApprovalList');
    Route::post('dependent-approval-request', 'DependentApprovalController@approveDependent');
    Route::post('dependent-removal-request', 'DependentApprovalController@removeDependent');

    //get list of registered Users
    Route::get('registered-users', 'RegisteredMemberController@getRegisteredUsers');

    // health policy report
    Route::post('health-policy-report', 'HealthReportController@getHealthReport');

    //health enrollment
    Route::post('health-enrollment', 'ManageHealthEnrollment@getHealthEnrollment');
    Route::get('health-enrollment-detail', 'ManageHealthEnrollment@getPolicyDetails');

    // manage clients info
    Route::post('manage-clients', 'ManageClientsController@manageClientsInfo');
    Route::post('policy-details', 'ManageClientsController@policyDetails');
    Route::post('calculate-prud-price', 'ManageClientsController@calculatePrudPrice');

    // member approval
    Route::get('member-approval', 'MemberApprovalController@getMember');
    Route::post('member-request', 'MemberApprovalController@approveMember');
    Route::post('bulk-member-request', 'MemberApprovalController@bulkApproveMember');

    //enrollment feature
    Route::get('enrollment-question', 'EnrollmentFeatureController@getQuestion');
    Route::post('question-detail', 'EnrollmentFeatureController@detailQuestion');
    Route::post('define-question', 'EnrollmentFeatureController@addQuestion');
    Route::post('delete-question', 'EnrollmentFeatureController@deleteQuestion');
    Route::get('enrollment-text', 'EnrollmentFeatureController@getEnrollmentText');
    Route::post('enrollment-text-detail', 'EnrollmentFeatureController@detailEnrollmentText');
    Route::post('define-enrollment-text', 'EnrollmentFeatureController@addEnrollmentText');
    Route::post('delete-enrollment-text', 'EnrollmentFeatureController@deleteEnrollText');

    //carrier management
    Route::get('carrier-detail/{cid?}', 'CarrierController@getCarrierInfo');
    Route::get('carrier-data', 'CarrierController@getCarrierData');
    Route::post('add-carrier', 'CarrierController@newCarrier');
    Route::post('update-carrier', 'CarrierController@newCarrier');

    // manage groups info
    Route::get('manage-groups', 'ManageGroupsController@manageGroupsInfo');
    Route::post('view-group-detail', 'ManageGroupsController@viewGroupInfo');

    // manage agents
    Route::get('manage-agents', 'ManageAgentsController@manageAgentsInfo');
    Route::post('agent-details', 'ManageAgentsController@agentDetails');
    Route::post('agent-clients', 'ManageAgentsController@clientDetails');
    Route::post('update-display-setting','ManageAgentsController@updateDisplaySetting');

    Route::post('update-signature', 'MemberApprovalController@receivedSignature');

    //Plan Management
    Route::get('plans', 'PlanController@getPlans');
    Route::post('new-plan', 'PlanController@newPlan');
    Route::get('get-plan-detail/{id}', 'PlanController@getPlanDetail');
    Route::post('update-plan', 'PlanController@newPlan');
    Route::get('get-plan-pricing/{id}', 'PlanController@getPlanPricing');
    Route::post('set-plan-pricing', 'PlanController@setPlanPricing');
    Route::post('set-plan-pricing-age-based', 'PlanController@setPlanPricingAgeBased');
    Route::post('set-plan-pricing-zip', 'PlanController@setPlanPricingZip');
    Route::post('set-plan-pricing-age-based-zip', 'PlanController@setPlanPricingAgeBasedZip');

    /* for adding Bank Account */
    Route::post('bank-info-add-update', 'PaymentController@addBankInfo');

    /* for setting Default Method & Recurring Method */
    Route::post('set-default-recurring-method', 'DefaultAndRecurringController@defaultRecurringMethodSet');
    Route::post('get-payment-page-details', 'PaymentController@getPaymentMethodDetails');
    Route::get('get-bank-details/{id}', 'PaymentController@getBankDetailsByID');

    Route::post('client-agent-update', 'ClientUpdateController@agentUpdate');
    Route::get('list-group-info', 'ClientUpdateController@listGroupInfo');
    Route::post('edit-policy-group', 'ClientUpdateController@editPolicyGroup');
    Route::get('get-ben-relation', 'ClientUpdateController@beneficiaryRelation');
    Route::post('client-ben-create', 'ClientUpdateController@beneficiaryCreate');
    Route::put('client-ben-update', 'ClientUpdateController@beneficiaryUpdate');
    Route::post('delete-policy-group', 'ClientUpdateController@deletePolicyGroup');
    Route::post('add-policy-dependent', 'ClientUpdateController@addPolicyDependent');
    Route::post('edit-policy-dependent', 'ClientUpdateController@editPolicyDependent');

    //address updates
    Route::get('list-states', 'AddressUpdateController@listStates');
    Route::post('add-policy-address',['middleware' => 'address_form', 'uses' => 'AddressUpdateController@addPolicyAddress']);
    Route::post('update-policy-address', 'AddressUpdateController@updatePolicyAddress');

    Route::post('get-enrollment-receipt', 'EnrollmentReceiptController@getEnrollmentReceipt');

    /* for policy termination & withdraw */
    Route::post('terminate-and-withdraw-details/{policyID}', 'PolicyTerminateWithdrawController@terminateAndWithdrawDetails');

    Route::post('terminate-policy', 'PolicyTerminateWithdrawController@terminatePolicy');
    Route::post('withdraw-policy', 'PolicyTerminateWithdrawController@withdrawPolicy');
    Route::post('get-user-policy-information-change-details/{id}', 'PolicyTerminateWithdrawController@getUserPolicyInformationDetails');
    Route::post('update-user-policy-information', 'PolicyTerminateWithdrawController@updateUserPolicyInformationDetails');

    Route::post('get-billing-information-change-details/{uid}', 'PolicyTerminateWithdrawController@getBillingInformationDetails');
    Route::post('change-billing-information-cc', 'PolicyTerminateWithdrawController@updateBillingInformationDetailsCC');
    Route::post('change-billing-information-eft', 'PolicyTerminateWithdrawController@updateBillingInformationDetailsEFT');
    Route::delete('delete-billing-information-eft/{id}', 'PolicyTerminateWithdrawController@deleteBillingInformationDetailsEFT');

    /* Policy Reinstate */
    Route::post('get-policy-reinstate-details/{policyID}', 'PolicyTerminateWithdrawController@getReinstatePolicyDetails');
    Route::post('set-policy-reinstate-details', 'PolicyTerminateWithdrawController@setReinstatePolicyDetails');

    /* Policy Tier Change */
    Route::post('get-policy-tier-details', 'PolicyTerminateWithdrawController@getPolicyTierDetails');
    Route::post('update-policy-tier-details', 'PolicyTerminateWithdrawController@updatePolicyTierDetails');

    //Enrollment Notes
    Route::post('get-policy-enrollment-notes', 'PolicyEnrollmentNotesController@getPolicyEnrollmentNotes');
    Route::post('add-policy-enrollment-notes', 'PolicyEnrollmentNotesController@addPolicyEnrollmentNotes');
    Route::post('update-policy-enrollment-notes', 'PolicyEnrollmentNotesController@updatePolicyEnrollmentNotes');
    Route::post('remove-policy-enrollment-notes', 'PolicyEnrollmentNotesController@removePolicyEnrollmentNotes');

    //Manage Agent Policy Actions: Update Agent License
    Route::post('view-agent-license', 'AgentLicenseUpdateController@viewAgentLicense');
    Route::post('add-agent-license', 'AgentLicenseUpdateController@addAgentLicense');
    Route::post('update-agent-license', 'AgentLicenseUpdateController@updateAgentLicense');
    Route::post('remove-agent-license', 'AgentLicenseUpdateController@removeAgentLicense');

    // Agent Policy Actions to change status and level information
    Route::post('update-agent-status', 'AgentUpdateController@updateAgentStatus');
    Route::post('update-agent-level', 'AgentUpdateController@updateAgentLevel');
    Route::post('activate-agent', 'AgentUpdateController@activateAgent');
    Route::post('update-agent-level-status', 'AgentUpdateController@updateAgentLevelStatus');
    Route::post('/change-agent-upline', 'AgentUpdateController@changeAgentUpline');

    // Edit Group Info for Agent
    Route::post('update-agent-group', 'AgentUpdateController@updateAgentGroup');

    //Upload Policy Apps/Docs
    Route::post('add-policy-apps', 'PolicyAppsUploadController@addPolicyApps');
    Route::post('view-policy-apps', 'PolicyAppsUploadController@viewPolicyApps');
    Route::post('delete-policy-apps', 'PolicyAppsUploadController@deletePolicyApps');

    //Upload Letter of Credible Coverage
    Route::post('upload-policy-letter', 'PolicyAppsUploadController@uploadPolicyLetter');
    Route::post('update-policy-letter', 'PolicyAppsUploadController@updatePolicyLetter');

    //Delete Policy
    Route::post('remove-policy', 'DeleteController@removePolicy');

    //Upload Tax Document
    Route::post('get-policy-tax-document', 'PolicyAppsUploadController@getPolicyTaxDocument');
    Route::post('upload-policy-tax-document', 'PolicyAppsUploadController@uploadPolicyTaxDocument');
    Route::post('upload-policy-tax-document-reminder-email', 'PolicyAppsUploadController@uploadPolicyTaxDocumentReminderEmail');

    //get user Address list
    Route::get('get-user-addresses/{userID}', 'UserInfoController@getUserAddressList');

    //get user activity history
    Route::get('get-user-activities-history/{userID}', 'UserInfoController@getUserActivity');

    // set policy billing date
    Route::post('add-policy-billing-date', 'PolicyInfoController@addPolicyBillingDate');

    //Policy Rider
    Route::get('get-policy-rider-plan/{policyID}', 'PolicyRiderController@getRiderPlanList');
    Route::post('add-policy-rider', 'PolicyRiderController@addPolicyRider');
    Route::post('terminate-withdraw-policy-rider', 'PolicyRiderController@terminateRider');

    // set policy billing date
    Route::get('get-policy-group-listing', 'PolicyInfoController@getPolicyGroupListing');
    Route::post('update-policy-group', 'PolicyInfoController@updatePolicyGroup');

    Route::post('add-notes', 'NotesController@addNote');
    Route::delete('delete-notes/{id}', 'NotesController@deleteNotes');
    Route::get('get-latest-notes/{policyID}', 'NotesController@getLatestNote');
    Route::get('get-all-notes/{policyID}', 'NotesController@getAllNotes');
    Route::get('download-attachment/{filename}', 'NotesController@downloadFile');

    // Agent note
    Route::post('add-agent-notes', 'NotesController@addAgentNote');
    Route::delete('delete-agent-notes/{id}', 'NotesController@deleteAgentNotes');
    Route::get('agent-notes', 'ManageAgentsController@agentNotes');

    Route::post('update-waive-statement-fee', 'PolicyInfoController@updateWaiveStatementFee');
    Route::post('update-recurring-self-payment-status', 'PolicyInfoController@updatePolicyRecurringPaymentStatus');

    Route::fallback(function () {
        return response()->json([
            'message' => 'Page Not Found. If error persists, contact <EMAIL>'], 404);
    });

    //manageclient info
    Route::post('update-tax-doc', 'MemberApprovalController@receivedTaxDocument');
    Route::post('reset-app-security', 'ManageClientsController@resetAppSecurity');
    Route::post('upload-new-files', 'PolicyAppsUploadController@uploadNewFiles');
    Route::post('delete-test-policy', 'ManageClientsController@deleteTestPolicy');
    Route::post('show-uploaded-files', 'PolicyInfoController@showUploadedFiles');
    Route::post('update-agent-session', 'AgentUpdateController@updateAgentSession');

    //PlanOverView Info
    Route::get('get-plan-overviews', 'PlanOverviewController@index');
    Route::get('get-plan-overview/{policyId}', 'PlanOverviewController@getByPolicyId');
    Route::get('get-health-plans', 'PlanOverviewController@getHealthPlans');

    //Polices
    Route::get('get-policies', 'PolicyController@index');

    //Change Policy Effective date
    Route::post('change-policy-effective-date', 'PolicyController@changeEffectiveDate');

    //listing policies which have only health plans
    Route::get('get-health-plan-policies', 'PolicyController@getHealthPlanPolicies');
    Route::get('process-policy/{policyId}', 'PolicyController@processPolicy');
    Route::get('get-medical-details/{policyId}','PolicyController@getMedicalDetails');


    Route::get('get-policy-report/{policyId}', 'PolicyController@show');

    //PlanPolicy
    Route::get('get-plan-policies', 'PlanPolicyController@index');
    Route::post('approve-health-plan-policy', 'PlanPolicyController@approvePlanPolicy');
    Route::post('decline-health-plan-policy', 'PlanPolicyController@declinePlanPolicy');

    //Term Request
    Route::get('get-term-requests', 'TermRequestController@index');
    Route::post('approve-term-request', 'TermRequestController@approve');
    Route::post('reject-term-request', 'TermRequestController@reject');
    Route::get('get-term-request/{termId}/detail', 'TermRequestController@show');

    Route::get('get-user-resources', 'ResourceController@getUserResource');
    Route::get('get-new-rates/{policy_id}', 'ResourceController@getNewRates');

    Route::post('/download-health-plans-excel', 'PolicyController@downloadHealthPlanExcel');
    Route::get('/get-policies-effective-dates', 'PolicyController@getHealthPlanEffectiveDates');

    //group updates
    Route::post('update-waive-fee', 'GroupInfoController@updateWaiveFee');

    //Associationn Configuration
    Route::get('get-website-association-fees', 'AssocFeeController@index');
    Route::get('get-group-association-fees', 'GidAssocFeeController@index');
    Route::get('get-association-fees-search/{policy_id}', 'AssocFeeController@getAssociationFeesSearch');

    Route::get('get-website-association-options', 'AssocFeeController@getOptions');
    Route::get('get-group-association-options', 'GidAssocFeeController@getOptions');

    Route::post('/create-website-association-fee', 'AssocFeeController@create');
//    Route::put('/website-association-fee/{aId}/update', 'AssocFeeController@update');
    Route::delete('/website-association-fee/{aId}/delete', 'AssocFeeController@delete');

    Route::post('/create-group-association-fee', 'GidAssocFeeController@create');
//    Route::put('/group-association-fee/{aId}/update', 'GidAssocFeeController@update');
    Route::delete('/group-association-fee/{aId}/delete', 'GidAssocFeeController@delete');

    //Active Plan Termination
    Route::post('active-plan-termed', 'TermRequestController@activePlanTermed');
    Route::post('change-plan-term-date', 'TermRequestController@updatePlanTermDate');



    //Enrollment Question
    Route::get('/get-enrollment-questions', 'EnrollmentQuestionController@index');
    Route::get('/enrollment-question/{qId}/show', 'EnrollmentQuestionController@show');
    Route::post('/enrollment-question/create', 'EnrollmentQuestionController@create');
    Route::put('/enrollment-question/{qId}/update', 'EnrollmentQuestionController@update');
    Route::put('/enrollment-question/{qId}/delete', 'EnrollmentQuestionController@delete');

    //Enrollment Text
    Route::get('/get-enrollment-texts', 'EnrollmentTextController@index');
    Route::get('/enrollment-text/{etId}/show', 'EnrollmentTextController@show');
    Route::post('/enrollment-text/create', 'EnrollmentTextController@create');
    Route::put('/enrollment-text/{etId}/update', 'EnrollmentTextController@update');
    Route::put('/enrollment-text/{etId}/delete', 'EnrollmentTextController@delete');

    //Homepage Configuration Plans
    Route::get('/get-homepage-configuration-options', 'HomepageConfigurationController@getOptions');
    Route::get('/agent-homepage-configuration/{agentId}/plans', 'HomepageConfigurationController@getAgentPlans');
    Route::get('/group-homepage-configuration/{groupId}/plans', 'HomepageConfigurationController@getGroupPlans');
    Route::post('/create-agent-homepage-configuration', 'HomepageConfigurationController@createAgentPlan');
    Route::post('/create-group-homepage-configuration', 'HomepageConfigurationController@createGroupPlan');
    Route::put('/homepage-configuration/{id}/toggle-is-featured', 'HomepageConfigurationController@toggleIsFeatured');
    Route::delete('/homepage-configuration/{id}/delete', 'HomepageConfigurationController@delete');

    //aging off deps
    Route::get('aging-deps', 'FilterUserController@agingOffDeps');
    Route::put('toggle-payment-cc-status', 'PaymentController@togglePaymentCcStatus');

    // Get downline for Agents
    Route::get('get-downline-reps/{uplineRepId}', 'AgentsController@getDownlineAgents');
    // Get Downline Reps with additional info
    Route::get('get-downline-reps-additional-info/{uplineRepId}', 'AgentsController@getAdditionalInfoDownlineAgents');
    // Get If downline exists
    Route::get('check-downline-exists/{uplineRepId}', 'ManageAgentsController@getDownlineAgentsExists');

    //Group Dashboard
    Route::get('total-group-analytics/{fdate?}/{tdate?}', 'GroupDashboardController@totalGroupAnalytics');
    Route::get('total-progress-of-years/{fyear}/{tyear}', 'GroupDashboardController@totalProgressOfYears');
    Route::get('total-progress-of-months/{fdate}/{tdate}', 'GroupDashboardController@totalProgressOfMonths');
    Route::get('total-progress-of-week', 'GroupDashboardController@totalProgressOfWeek');
    Route::get('group-by-other', 'GroupDashboardController@groupByOther');
    Route::get('group-by-member-range', 'GroupDashboardController@groupByMemberRange');

    //Rep Dashboard
    Route::get('total-agent-analytics/{fdate?}/{tdate?}', 'AgentDashboardController@totalAgentAnalytics');
    Route::get('agent-total-progress-of-years/{fyear}/{tyear}', 'AgentDashboardController@totalProgressOfYears');
    Route::get('agent-total-progress-of-months/{fdate}/{tdate}', 'AgentDashboardController@totalProgressOfMonths');
    Route::get('agent-total-progress-of-week', 'AgentDashboardController@totalProgressOfWeek');
    Route::get('agent-group-by-other', 'AgentDashboardController@agentGroupByOther');
    Route::get('top-agents-by-Category','AgentDashboardController@topAgentsByCategory');

    Route::get('get-productinfo','AgentDashboardController@productInfo');
    //Member Dashboard
    Route::get('/total-members-analytics/{fdate?}/{tdate?}', 'MemberDashboardController@totalMemberAnalytics');
    Route::get('/total-dependents-analytics', 'MemberDashboardController@totalDependentAnalytics');
    Route::get('/total-policies-analytics', 'MemberDashboardController@totalPoliciesAnalytics');
    Route::get('/member-total-progress-of-years/{fyear}/{tyear}', 'MemberDashboardController@totalProgressOfYear');
    Route::get('/member-total-progress-of-months/{fdate}/{tdate}', 'MemberDashboardController@totalProgressOfMonths');
    Route::get('/member-total-progress-of-week', 'MemberDashboardController@totalProgressOfWeek');
    Route::get('/member-group-by-other', 'MemberDashboardController@groupByOther');

    //total active agents
    Route::get('total-active-agents', [AgentsController::class, 'totalActiveAgents']);
    //total active groups
    Route::get('total-active-groups', 'GroupInfoController@totalActiveGroups');
    // get group member
    Route::get('get-member-list', 'GroupInfoController@getMemberList');
    Route::get('/send-member-alert-email-policy-upgrade', 'MemberEmailController@sendMemberAlertEmailPolicyUpgrade');

    //Agent Business
    Route::post('agent-business/add', 'AgentBusinessController@addAgentBusiness');
    Route::get('agent-business/list', 'AgentBusinessController@listAgentBusiness');
    Route::get('agent-business/{id}','AgentBusinessController@agentDetailBusiness');

    Route::put('update-group-name',[GroupInfoController::class, 'updateGroupName']);

    // terminate group
    Route::post('group/terminate-group','GroupInfoController@terminateGroup');

    Route::group([
        'prefix' => 'license',
    ], function () {
        Route::post('add', [RepLicenseController::class, 'addRepLicense']);
        Route::get('get', [RepLicenseController::class, 'getRepLicense']);
        Route::get('get/{id}', [RepLicenseController::class, 'getLicenseDetail']);
        Route::put('edit', [RepLicenseController::class, 'editRepLicense']);
        Route::delete('remove', [RepLicenseController::class, 'removeRepLicense']);
    });
    // Get rep info
    Route::post('get-rep-info','AgentsController@getAgentInfo');

    // AISTATS and status
    Route::get('get-ai-compare', 'AIStatsController@getAICompare');
    Route::get('get-complete-policies', 'AIStatsController@getPolicyData');
    Route::post('update-ai-status','PlanPolicyController@updateAIStatus');

    Route::get('get-new-rates-renewal/{policy_id}', 'ResourceController@getNewRatesRenewal');
    Route::get('get-solistic-card-detail/{policy_id}', 'ResourceController@getSolisticCardDetail');
    Route::get('get-delta-dental-card-detail/{policy_id}', 'ResourceController@getDeltaDentalCardDetail');

    Route::get('get-ai-iha-compare', 'AIStatsController@getAIIHACompare');
    Route::get('get-iha-policies', 'AIStatsController@getIHAPolicyData');

    // Email Update logs
    Route::get('user/{userId}/email-logs', 'EmailUpdateLogsController@userUpdates');
    Route::get('group/{groupId}/email-logs', 'EmailUpdateLogsController@groupUpdates');
    Route::get('agent/{agentId}/email-logs', 'EmailUpdateLogsController@agentUpdates');
});

include 'api_v2.php';
include 'api_v3.php';
include 'api_acm.php';
