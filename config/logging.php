<?php

use Monolog\Handler\NullHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['daily'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => 'critical',
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => 'debug',
            'handler' => SyslogUdpHandler::class,
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
            ],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => 'debug',
        ],

        'null' => [
            'driver' => 'monolog',
            'handler' => NullHandler::class,
        ],

        'invoiceDeletion' => [
            'driver' => 'daily',
            'path' => storage_path('logs/invoice/Invoice-deletion.log'),
            'level' => 'debug',
        ],

        'healthPlanApprovalLog' => [
            'driver' => 'daily',
            'path' => storage_path('logs/health-policy-approval.log'),
            'level' => 'debug'
        ],

        'tierUpdate' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tier_update/tier-update.log'),
            'level' => 'debug',
        ],

        'planReactivation' => [
            'driver' => 'daily',
            'path' => storage_path('logs/plan_reactivation/plan-reactivation.log'),
            'level' => 'debug',
        ],

        'memberCard' => [
            'driver' => 'daily',
            'path' => storage_path('logs/member_card/member_card.log'),
            'level' => 'debug',
        ],

        'memberCardUpdate' => [
            'driver' => 'daily',
            'path' => storage_path('logs/member_card_update/member_card_update.log'),
            'level' => 'debug',
        ],

        'groupAssociationFeeWaiverLog' => [
            'driver' => 'daily',
            'path' => storage_path('logs/group-association-waiver.log'),
            'level' => 'debug'
        ],
        'planReplacedAlternative'=> [
            'driver' => 'daily',
            'path' => storage_path('logs/plan_replaced_alternative/plan_replaced_alternative.log'),
            'level' => 'debug',
        ],
        'agent_expiry_logs' => [
            'driver' => 'daily',
            'path' => storage_path('logs/agent_contract_expiry/agent_contract_expiry.log'),
            'level' => 'info',
        ],
        'email_activity_log' => [
            'driver' => 'daily',
            'path' => storage_path('logs/email_activity_log/email_activity_log.log'),
            'level' => 'debug',
         ],
        'root_group'=>[
            'driver' => 'daily',
            'path' => storage_path('logs/rootgroup/rootgroup.log'),
            'level' => 'debug',
        ],
        'payment_change'=>[
            'driver' => 'daily',
            'path' => storage_path('logs/paymentChange/payment_change.log'),
            'level' => 'debug',
        ],
        'archive' => [
            'driver' => 'single',
            'path' => storage_path('logs/archive/archive.log'),
            'level' => 'info',
        ],
        'repcontactlog'=>[
            'driver' => 'daily',
            'path' => storage_path('logs/contract/rep-contract-send.log'),
            'level' => 'info',
        ],
        'refund_withdrawn_extra_plan_payments_log' => [
            'driver' => 'daily',
            'path' => storage_path('logs/refund/refund_withdrawn_extra_plan_payments.log'),
            'level' => 'debug',
        ],
        'agent_group_assignment' => [
            'driver' => 'daily',
            'path' => storage_path('logs/contract/agent_group_assignment.log'),
            'level' => 'info',
        ],
    ],
];
