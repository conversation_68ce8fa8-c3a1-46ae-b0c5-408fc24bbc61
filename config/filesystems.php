<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DRIVER', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Default Cloud Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Many applications store files both locally and in the cloud. For this
    | reason, you may specify a default "cloud" driver here. This driver
    | will be bound as the Cloud disk implementation in the container.
    |
    */

    'cloud' => env('FILESYSTEM_CLOUD', 's3'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been setup for each driver as an example of the required options.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
        ],

        'nuerabenefits' => [
            'driver' => 'sftp',
            'host' => env('CORENROLL_HOST'),
            'username' => env('CORENROLL_USERNAME'),
            'privateKey' => env('CORENROLL_KEY'),
            'port' => env('CORENROLL_PORT'),
            'root' => env('NUERA_ROOT'),
            'timeout' => 10,
        ],

        'corenroll' => [
            'driver' => 'sftp',
            'host' => env('CORENROLL_HOST'),
            'username' => env('CORENROLL_USERNAME'),
            'privateKey' => env('CORENROLL_KEY'),
            'port' => env('CORENROLL_PORT'),
            'root' => env('CORENROLL_ROOT'),
            'timeout' => 10,

        ],
        's3-second' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID_SECOND'),
            'secret' => env('AWS_SECRET_ACCESS_KEY_SECOND'),
            'region' => env('AWS_DEFAULT_REGION_SECOND'),
            'bucket' => env('AWS_BUCKET_SECOND'),
            'url' => env('AWS_URL_SECOND'),
        ],
        's3-third' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID_THIRD'),
            'secret' => env('AWS_SECRET_ACCESS_KEY_THIRD'),
            'region' => env('AWS_DEFAULT_REGION_THIRD'),
            'bucket' => env('AWS_BUCKET_THIRD'),
            'url' => env('AWS_URL_THIRD'),
        ],
        'export' => [
            'driver' => 'local',
            'root' => storage_path(),
            'url' => '/exports',
            'visibility' => 'public',
        ],
    ],
    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

];
