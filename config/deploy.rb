# config valid for current version and patch releases of Capistrano
lock "~> 3.11.2"

set :application, "nuerabenefits-api"
set :repo_url, "**************:CloudTechService/nuerabenefits.git"


# Default value for keep_releases is 5
set :keep_releases, 5

namespace :deploy do
    after :updated, "permission:fix"
    after :updated, "laravel:configure_dot_env"
    after :updated, "composer:install"
    after :updated, "laravel:migrate"
end

set :slackistrano, {
   klass: Slackistrano::CustomMessaging,
   channel: '#deployment-alerts',
   webhook: '*****************************************************************************',
   icon_emoji: ':thumbsup:',
}